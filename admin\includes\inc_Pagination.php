<?php
/**
 * مكون ترقيم الصفحات القابل لإعادة الاستخدام
 * 
 * @param int $currentPage الصفحة الحالية
 * @param int $totalPages إجمالي عدد الصفحات
 * @param string $baseUrl الرابط الأساسي (اختياري)
 * @param array $queryParams معاملات إضافية للرابط (اختياري)
 * @return void
 */
function renderPagination($currentPage, $totalPages, $baseUrl = '', $queryParams = []) {
    // التحقق من صحة المعاملات
    if ($totalPages <= 1) {
        return;
    }
    
    $currentPage = max(1, min($currentPage, $totalPages));
    
    // بناء الرابط الأساسي
    if (empty($baseUrl)) {
        $baseUrl = $_SERVER['PHP_SELF'];
    }
    
    // دالة لبناء الرابط مع المعاملات
    function buildUrl($baseUrl, $page, $queryParams = []) {
        $params = array_merge($queryParams, ['page' => $page]);
        return $baseUrl . '?' . http_build_query($params);
    }
?>

<!-- ترقيم الصفحات -->
<div class="bg-white px-4 py-3 flex items-center justify-center border-t border-gray-200 sm:px-6">
    <nav aria-label="Page navigation example">
        <ul class="flex items-center -space-x-px h-10 text-base">
            <!-- زر السابق -->
            <li>
                <?php if ($currentPage > 1): ?>
                    <a href="<?php echo buildUrl($baseUrl, $currentPage - 1, $queryParams); ?>" class="flex items-center justify-center px-4 h-10 ms-0 leading-tight text-gray-500 bg-white border border-e-0 border-gray-300 rounded-s-lg hover:bg-gray-100 hover:text-gray-700">
                        <span class="sr-only">Previous</span>
                        <svg class="w-3 h-3 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 1 1 5l4 4"/>
                        </svg>
                    </a>
                <?php else: ?>
                    <span class="flex items-center justify-center px-4 h-10 ms-0 leading-tight text-gray-300 bg-gray-100 border border-e-0 border-gray-300 rounded-s-lg cursor-not-allowed">
                        <span class="sr-only">Previous</span>
                        <svg class="w-3 h-3 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 1 1 5l4 4"/>
                        </svg>
                    </span>
                <?php endif; ?>
            </li>
            
            <!-- أرقام الصفحات -->
            <?php
            $startPage = max(1, $currentPage - 2);
            $endPage = min($totalPages, $currentPage + 2);
            
            for ($i = $startPage; $i <= $endPage; $i++):
            ?>
                <li>
                    <?php if ($i == $currentPage): ?>
                        <a href="<?php echo buildUrl($baseUrl, $i, $queryParams); ?>" aria-current="page" class="z-10 flex items-center justify-center px-4 h-10 leading-tight text-blue-600 border border-blue-300 bg-blue-50 hover:bg-blue-100 hover:text-blue-700"><?php echo $i; ?></a>
                    <?php else: ?>
                        <a href="<?php echo buildUrl($baseUrl, $i, $queryParams); ?>" class="flex items-center justify-center px-4 h-10 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700"><?php echo $i; ?></a>
                    <?php endif; ?>
                </li>
            <?php endfor; ?>
            
            <!-- زر التالي -->
            <li>
                <?php if ($currentPage < $totalPages): ?>
                    <a href="<?php echo buildUrl($baseUrl, $currentPage + 1, $queryParams); ?>" class="flex items-center justify-center px-4 h-10 leading-tight text-gray-500 bg-white border border-gray-300 rounded-e-lg hover:bg-gray-100 hover:text-gray-700">
                        <span class="sr-only">Next</span>
                        <svg class="w-3 h-3 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                        </svg>
                    </a>
                <?php else: ?>
                    <span class="flex items-center justify-center px-4 h-10 leading-tight text-gray-300 bg-gray-100 border border-gray-300 rounded-e-lg cursor-not-allowed">
                        <span class="sr-only">Next</span>
                        <svg class="w-3 h-3 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                        </svg>
                    </span>
                <?php endif; ?>
            </li>
        </ul>
    </nav>
</div>

<?php
}

/**
 * مكون ترقيم الصفحات المبسط (أزرار السابق والتالي فقط)
 * 
 * @param int $currentPage الصفحة الحالية
 * @param int $totalPages إجمالي عدد الصفحات
 * @param string $baseUrl الرابط الأساسي (اختياري)
 * @param array $queryParams معاملات إضافية للرابط (اختياري)
 * @return void
 */
function renderSimplePagination($currentPage, $totalPages, $baseUrl = '', $queryParams = []) {
    // التحقق من صحة المعاملات
    if ($totalPages <= 1) {
        return;
    }
    
    $currentPage = max(1, min($currentPage, $totalPages));
    
    // بناء الرابط الأساسي
    if (empty($baseUrl)) {
        $baseUrl = $_SERVER['PHP_SELF'];
    }
    
    // دالة لبناء الرابط مع المعاملات
    function buildSimpleUrl($baseUrl, $page, $queryParams = []) {
        $params = array_merge($queryParams, ['page' => $page]);
        return $baseUrl . '?' . http_build_query($params);
    }
?>

<!-- ترقيم الصفحات البسيط -->
<div class="bg-white px-4 py-3 flex items-center justify-center border-t border-gray-200 sm:px-6">
    <div class="flex items-center space-x-4 rtl:space-x-reverse">
        <?php if ($currentPage > 1): ?>
            <a href="<?php echo buildSimpleUrl($baseUrl, $currentPage - 1, $queryParams); ?>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                السابق
            </a>
        <?php else: ?>
            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-100 cursor-not-allowed">
                السابق
            </span>
        <?php endif; ?>
        
        <div class="text-sm text-gray-700 flex items-center px-4">
            صفحة <?php echo $currentPage; ?> من <?php echo $totalPages; ?>
        </div>
        
        <?php if ($currentPage < $totalPages): ?>
            <a href="<?php echo buildSimpleUrl($baseUrl, $currentPage + 1, $queryParams); ?>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                التالي
            </a>
        <?php else: ?>
            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-100 cursor-not-allowed">
                التالي
            </span>
        <?php endif; ?>
    </div>
</div>

<?php
}

/**
 * حساب معلومات ترقيم الصفحات
 * 
 * @param int $totalRecords إجمالي عدد السجلات
 * @param int $recordsPerPage عدد السجلات في الصفحة الواحدة
 * @param int $currentPage الصفحة الحالية
 * @return array معلومات ترقيم الصفحات
 */
function calculatePagination($totalRecords, $recordsPerPage = 10, $currentPage = 1) {
    $totalPages = ceil($totalRecords / $recordsPerPage);
    $currentPage = max(1, min($currentPage, $totalPages));
    $offset = ($currentPage - 1) * $recordsPerPage;
    
    return [
        'totalRecords' => $totalRecords,
        'recordsPerPage' => $recordsPerPage,
        'totalPages' => $totalPages,
        'currentPage' => $currentPage,
        'offset' => $offset,
        'startRecord' => $offset + 1,
        'endRecord' => min($offset + $recordsPerPage, $totalRecords),
        'hasNextPage' => $currentPage < $totalPages,
        'hasPrevPage' => $currentPage > 1
    ];
}
?>

<!-- 
طريقة الاستخدام:

1. تضمين الملف:
include_once 'includes/inc_Pagination.php';

2. حساب معلومات ترقيم الصفحات:
$pagination = calculatePagination($totalRecords, 10, $currentPage);

3. عرض ترقيم الصفحات الكامل:
renderPagination($pagination['currentPage'], $pagination['totalPages']);

4. أو عرض ترقيم الصفحات البسيط:
renderSimplePagination($pagination['currentPage'], $pagination['totalPages']);

5. مع معاملات إضافية:
$queryParams = ['search' => $_GET['search'] ?? '', 'status' => $_GET['status'] ?? ''];
renderPagination($pagination['currentPage'], $pagination['totalPages'], '', $queryParams);

6. مع رابط مخصص:
renderPagination($pagination['currentPage'], $pagination['totalPages'], '/admin/users.php', $queryParams);

// تضمين الملف
include_once 'includes/inc_Pagination.php';

// حساب معلومات ترقيم الصفحات
$pagination = calculatePagination($totalRecords, 10, $currentPage);

// عرض ترقيم الصفحات
renderPagination($pagination['currentPage'], $pagination['totalPages']);


### العبارة المطلوبة:
"أريد إضافة نظام ترقيم الصفحات إلى صفحة [اسم الصفحة] مثل صفحة المنتجات"

### أمثلة عملية:
- "أريد إضافة نظام ترقيم الصفحات إلى صفحة المستخدمين مثل صفحة المنتجات"
- "أريد إضافة نظام ترقيم الصفحات إلى صفحة الفئات مثل صفحة المنتجات"
- "أريد إضافة نظام ترقيم الصفحات إلى صفحة الموزعين مثل صفحة المنتجات"

-->