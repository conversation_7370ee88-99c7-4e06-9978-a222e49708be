<?php
/**
 * سكريبت تحديث المستخدمين لربطهم بجدول الأدوار الجديد
 * Migrate Users to New Roles System
 */

require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !hasPermission('admin')) {
    redirect('/admin/login.php');
}

require_once '../config/database.php';
$database = new Database();

$message = '';
$error = '';
$migrationResults = [];

// معالجة طلب التحديث
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['migrate'])) {
    try {
        // بدء المعاملة
        $database->query("START TRANSACTION");
        
        // التحقق من وجود جدول roles
        $rolesExist = false;
        try {
            $database->query("SELECT COUNT(*) FROM roles")->fetch();
            $rolesExist = true;
        } catch (Exception $e) {
            throw new Exception("جدول الأدوار غير موجود. يرجى إنشاؤه أولاً.");
        }
        
        // جلب جميع المستخدمين الذين لديهم role ولكن ليس لديهم role_id
        $usersToMigrate = $database->query("
            SELECT id, name, email, role 
            FROM users 
            WHERE role IS NOT NULL 
            AND (role_id IS NULL OR role_id = 0)
        ")->fetchAll();
        
        $migrationResults['total_users'] = count($usersToMigrate);
        $migrationResults['updated_users'] = 0;
        $migrationResults['errors'] = [];
        
        foreach ($usersToMigrate as $user) {
            try {
                // البحث عن الدور المطابق في جدول roles
                $role = $database->query("
                    SELECT id FROM roles 
                    WHERE name = :role_name 
                    LIMIT 1
                ", ['role_name' => $user['role']])->fetch();
                
                if ($role) {
                    // تحديث role_id للمستخدم
                    $database->query("
                        UPDATE users 
                        SET role_id = :role_id 
                        WHERE id = :user_id
                    ", [
                        'role_id' => $role['id'],
                        'user_id' => $user['id']
                    ]);
                    
                    // إضافة الدور إلى جدول user_roles إذا كان موجوداً
                    try {
                        // التحقق من عدم وجود الدور مسبقاً
                        $existingRole = $database->query("
                            SELECT id FROM user_roles 
                            WHERE user_id = :user_id AND role_id = :role_id
                        ", [
                            'user_id' => $user['id'],
                            'role_id' => $role['id']
                        ])->fetch();
                        
                        if (!$existingRole) {
                            $database->query("
                                INSERT INTO user_roles (user_id, role_id, assigned_by, assigned_at, is_active) 
                                VALUES (:user_id, :role_id, :assigned_by, NOW(), 1)
                            ", [
                                'user_id' => $user['id'],
                                'role_id' => $role['id'],
                                'assigned_by' => getCurrentUser()['id'] ?? 1
                            ]);
                        }
                    } catch (Exception $roleException) {
                        // إذا لم يوجد جدول user_roles، تجاهل الخطأ
                        $migrationResults['errors'][] = "تعذر إضافة الدور لجدول user_roles للمستخدم {$user['name']}: " . $roleException->getMessage();
                    }
                    
                    $migrationResults['updated_users']++;
                } else {
                    $migrationResults['errors'][] = "لم يتم العثور على الدور '{$user['role']}' للمستخدم {$user['name']}";
                }
            } catch (Exception $userException) {
                $migrationResults['errors'][] = "خطأ في تحديث المستخدم {$user['name']}: " . $userException->getMessage();
            }
        }
        
        // تأكيد المعاملة
        $database->query("COMMIT");
        
        $message = "تم تحديث {$migrationResults['updated_users']} من أصل {$migrationResults['total_users']} مستخدم بنجاح.";
        
    } catch (Exception $e) {
        // إلغاء المعاملة في حالة الخطأ
        $database->query("ROLLBACK");
        $error = "خطأ في عملية التحديث: " . $e->getMessage();
    }
}

// جلب إحصائيات المستخدمين
$stats = [];
try {
    $stats['total_users'] = $database->query("SELECT COUNT(*) as count FROM users")->fetch()['count'];
    $stats['users_with_role_id'] = $database->query("SELECT COUNT(*) as count FROM users WHERE role_id IS NOT NULL AND role_id > 0")->fetch()['count'];
    $stats['users_without_role_id'] = $database->query("SELECT COUNT(*) as count FROM users WHERE role_id IS NULL OR role_id = 0")->fetch()['count'];
    $stats['users_with_old_role'] = $database->query("SELECT COUNT(*) as count FROM users WHERE role IS NOT NULL AND (role_id IS NULL OR role_id = 0)")->fetch()['count'];
} catch (Exception $e) {
    $stats = ['error' => $e->getMessage()];
}

// جلب المستخدمين الذين يحتاجون تحديث
$usersNeedingMigration = [];
try {
    $usersNeedingMigration = $database->query("
        SELECT u.id, u.name, u.email, u.role, u.role_id, r.display_name as role_display_name
        FROM users u 
        LEFT JOIN roles r ON u.role_id = r.id
        WHERE u.role IS NOT NULL 
        AND (u.role_id IS NULL OR u.role_id = 0)
        ORDER BY u.name
    ")->fetchAll();
} catch (Exception $e) {
    // تجاهل الخطأ
}

$pageTitle = 'تحديث نظام الأدوار';
$currentPage = 'migrate_users';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?> - Green Line</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-white shadow">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-6">
                    <h1 class="text-3xl font-bold text-gray-900"><?= $pageTitle ?></h1>
                    <a href="users.php" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-arrow-right ml-2"></i>
                        العودة لإدارة المستخدمين
                    </a>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            <!-- Messages -->
            <?php if ($message): ?>
                <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                    <i class="fas fa-check-circle ml-2"></i>
                    <?= htmlspecialchars($message) ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    <i class="fas fa-exclamation-circle ml-2"></i>
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>

            <!-- Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-blue-100 rounded-lg">
                            <i class="fas fa-users text-blue-600"></i>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm font-medium text-gray-600">إجمالي المستخدمين</p>
                            <p class="text-2xl font-semibold text-gray-900"><?= $stats['total_users'] ?? 0 ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-green-100 rounded-lg">
                            <i class="fas fa-check text-green-600"></i>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm font-medium text-gray-600">محدثين للنظام الجديد</p>
                            <p class="text-2xl font-semibold text-gray-900"><?= $stats['users_with_role_id'] ?? 0 ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-yellow-100 rounded-lg">
                            <i class="fas fa-exclamation-triangle text-yellow-600"></i>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm font-medium text-gray-600">يحتاجون تحديث</p>
                            <p class="text-2xl font-semibold text-gray-900"><?= $stats['users_with_old_role'] ?? 0 ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-red-100 rounded-lg">
                            <i class="fas fa-times text-red-600"></i>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm font-medium text-gray-600">بدون أدوار</p>
                            <p class="text-2xl font-semibold text-gray-900"><?= $stats['users_without_role_id'] ?? 0 ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Migration Form -->
            <?php if (!empty($usersNeedingMigration)): ?>
                <div class="bg-white rounded-lg shadow mb-8">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">
                            <i class="fas fa-sync-alt ml-2"></i>
                            تحديث المستخدمين للنظام الجديد
                        </h3>
                        <p class="mt-1 text-sm text-gray-600">
                            سيتم ربط المستخدمين التاليين بجدول الأدوار الجديد بناءً على أدوارهم الحالية
                        </p>
                    </div>
                    <div class="px-6 py-4">
                        <form method="POST" class="space-y-4">
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المستخدم</th>
                                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">البريد الإلكتروني</th>
                                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الدور الحالي</th>
                                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <?php foreach ($usersNeedingMigration as $user): ?>
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                    <?= htmlspecialchars($user['name']) ?>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    <?= htmlspecialchars($user['email']) ?>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                        <?= htmlspecialchars($user['role']) ?>
                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                                        يحتاج تحديث
                                                    </span>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <div class="flex justify-end pt-4">
                                <button type="submit" name="migrate" value="1" 
                                        class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-colors"
                                        onclick="return confirm('هل أنت متأكد من تحديث جميع المستخدمين؟')">
                                    <i class="fas fa-sync-alt ml-2"></i>
                                    تحديث جميع المستخدمين
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            <?php else: ?>
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-8 text-center">
                        <i class="fas fa-check-circle text-green-500 text-6xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">تم التحديث بنجاح!</h3>
                        <p class="text-gray-600">جميع المستخدمين محدثين للنظام الجديد</p>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Migration Results -->
            <?php if (!empty($migrationResults)): ?>
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">
                            <i class="fas fa-chart-bar ml-2"></i>
                            نتائج التحديث
                        </h3>
                    </div>
                    <div class="px-6 py-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-medium text-gray-900 mb-2">الإحصائيات</h4>
                                <ul class="space-y-1 text-sm text-gray-600">
                                    <li>إجمالي المستخدمين: <?= $migrationResults['total_users'] ?></li>
                                    <li>تم تحديثهم: <?= $migrationResults['updated_users'] ?></li>
                                    <li>الأخطاء: <?= count($migrationResults['errors']) ?></li>
                                </ul>
                            </div>
                            
                            <?php if (!empty($migrationResults['errors'])): ?>
                                <div>
                                    <h4 class="font-medium text-gray-900 mb-2">الأخطاء</h4>
                                    <ul class="space-y-1 text-sm text-red-600">
                                        <?php foreach ($migrationResults['errors'] as $error): ?>
                                            <li>• <?= htmlspecialchars($error) ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </main>
    </div>
</body>
</html>