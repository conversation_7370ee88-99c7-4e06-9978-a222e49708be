<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "=== اختبار نموذج الاتصال ===\n\n";

try {
    // اختبار 1: إدراج رسالة جديدة باستخدام مصفوفة
    echo "1. اختبار إدراج رسالة باستخدام مصفوفة...\n";
    $testData1 = [
        'name' => 'أحمد محمد',
        'email' => '<EMAIL>',
        'phone' => '0501234567',
        'subject' => 'استفسار عن المكيفات',
        'message' => 'أريد معرفة أسعار المكيفات المتوفرة لديكم'
    ];
    
    $messageId1 = saveContactMessage($testData1);
    if ($messageId1) {
        echo "✓ تم إدراج الرسالة بنجاح. المعرف: $messageId1\n";
        
        // التحقق من البيانات المدرجة
        $inserted = $database->fetch("SELECT * FROM contact_messages WHERE id = ?", [$messageId1]);
        echo "  - الاسم: " . $inserted['name'] . "\n";
        echo "  - البريد: " . $inserted['email'] . "\n";
        echo "  - الموضوع: " . $inserted['subject'] . "\n";
        echo "  - الحالة: " . $inserted['status'] . "\n";
        echo "  - تاريخ الإنشاء: " . $inserted['created_at'] . "\n";
    } else {
        echo "✗ فشل في إدراج الرسالة\n";
    }
    
    echo "\n";
    
    // اختبار 2: إدراج رسالة باستخدام معاملات منفصلة (كما في API)
    echo "2. اختبار إدراج رسالة باستخدام معاملات منفصلة...\n";
    $messageId2 = saveContactMessage('فاطمة علي', '<EMAIL>', '0509876543', 'طلب عرض سعر', 'أحتاج عرض سعر لمكيف 18000 وحدة');
    
    if ($messageId2) {
        echo "✓ تم إدراج الرسالة بنجاح. المعرف: $messageId2\n";
        
        $inserted2 = $database->fetch("SELECT * FROM contact_messages WHERE id = ?", [$messageId2]);
        echo "  - الاسم: " . $inserted2['name'] . "\n";
        echo "  - البريد: " . $inserted2['email'] . "\n";
        echo "  - الموضوع: " . $inserted2['subject'] . "\n";
        echo "  - الحالة: " . $inserted2['status'] . "\n";
    } else {
        echo "✗ فشل في إدراج الرسالة\n";
    }
    
    echo "\n";
    
    // اختبار 3: عرض جميع الرسائل
    echo "3. عرض جميع الرسائل في قاعدة البيانات...\n";
    $allMessages = $database->fetchAll("SELECT * FROM contact_messages ORDER BY created_at DESC");
    echo "عدد الرسائل الإجمالي: " . count($allMessages) . "\n";
    
    foreach ($allMessages as $msg) {
        echo "  - رسالة #" . $msg['id'] . ": " . $msg['name'] . " - " . $msg['subject'] . " (" . $msg['status'] . ")\n";
    }
    
    echo "\n";
    
    // اختبار 4: تحديث حالة الرسالة
    if ($messageId1) {
        echo "4. اختبار تحديث حالة الرسالة...\n";
        $updateResult = $database->update('contact_messages', 
            ['status' => 'read'], 
            'id = ?', 
            [$messageId1]
        );
        
        if ($updateResult) {
            echo "✓ تم تحديث حالة الرسالة إلى 'مقروءة'\n";
            
            $updated = $database->fetch("SELECT status FROM contact_messages WHERE id = ?", [$messageId1]);
            echo "  - الحالة الجديدة: " . $updated['status'] . "\n";
        } else {
            echo "✗ فشل في تحديث حالة الرسالة\n";
        }
    }
    
    echo "\n";
    
    // اختبار 5: إحصائيات الرسائل
    echo "5. إحصائيات الرسائل...\n";
    $stats = [
        'total' => $database->fetch("SELECT COUNT(*) as count FROM contact_messages")['count'],
        'new' => $database->fetch("SELECT COUNT(*) as count FROM contact_messages WHERE status = 'new'")['count'],
        'read' => $database->fetch("SELECT COUNT(*) as count FROM contact_messages WHERE status = 'read'")['count'],
        'replied' => $database->fetch("SELECT COUNT(*) as count FROM contact_messages WHERE status = 'replied'")['count']
    ];
    
    echo "  - إجمالي الرسائل: " . $stats['total'] . "\n";
    echo "  - رسائل جديدة: " . $stats['new'] . "\n";
    echo "  - رسائل مقروءة: " . $stats['read'] . "\n";
    echo "  - رسائل تم الرد عليها: " . $stats['replied'] . "\n";
    
    echo "\n";
    
    // تنظيف البيانات التجريبية
    echo "6. تنظيف البيانات التجريبية...\n";
    if ($messageId1) {
        $database->delete('contact_messages', 'id = ?', [$messageId1]);
        echo "✓ تم حذف الرسالة الأولى\n";
    }
    if ($messageId2) {
        $database->delete('contact_messages', 'id = ?', [$messageId2]);
        echo "✓ تم حذف الرسالة الثانية\n";
    }
    
    echo "\n=== انتهى الاختبار بنجاح ===\n";
    
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage() . "\n";
    echo "تفاصيل الخطأ: " . $e->getTraceAsString() . "\n";
}
?>