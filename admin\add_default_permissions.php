<?php
/**
 * إضافة الصلاحيات الافتراضية للنظام
 * Add Default System Permissions
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/PermissionManager.php';

// التحقق من تسجيل الدخول وصلاحية الإدارة
if (!isLoggedIn() || !hasPermission($_SESSION['user_id'], 'manage-permissions')) {
    header('Location: login.php');
    exit;
}

$permissionManager = new PermissionManager($database);
$message = '';
$error = '';

// إضافة الصلاحيات الافتراضية
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_permissions'])) {
    try {
        // صلاحيات النظام العامة
        $systemPermissions = [
            // صلاحيات الوصول
            ['admin-access', 'الوصول للوحة التحكم', 'النظام'],
            ['frontend-access', 'الوصول للموقع الأمامي', 'النظام'],
            ['api-access', 'الوصول لواجهة البرمجة', 'النظام'],
            
            // صلاحيات المستخدمين
            ['manage-users', 'إدارة المستخدمين', 'المستخدمين'],
            ['view-users', 'عرض المستخدمين', 'المستخدمين'],
            ['create-users', 'إنشاء مستخدمين', 'المستخدمين'],
            ['edit-users', 'تعديل المستخدمين', 'المستخدمين'],
            ['delete-users', 'حذف المستخدمين', 'المستخدمين'],
            ['manage-user-permissions', 'إدارة صلاحيات المستخدمين', 'المستخدمين'],
            
            // صلاحيات الأدوار
            ['manage-roles', 'إدارة الأدوار', 'الأدوار'],
            ['view-roles', 'عرض الأدوار', 'الأدوار'],
            ['create-roles', 'إنشاء أدوار', 'الأدوار'],
            ['edit-roles', 'تعديل الأدوار', 'الأدوار'],
            ['delete-roles', 'حذف الأدوار', 'الأدوار'],
            ['assign-roles', 'تعيين الأدوار', 'الأدوار'],
            
            // صلاحيات الصلاحيات
            ['manage-permissions', 'إدارة الصلاحيات', 'الصلاحيات'],
            ['view-permissions', 'عرض الصلاحيات', 'الصلاحيات'],
            ['create-permissions', 'إنشاء صلاحيات', 'الصلاحيات'],
            ['edit-permissions', 'تعديل الصلاحيات', 'الصلاحيات'],
            ['delete-permissions', 'حذف الصلاحيات', 'الصلاحيات'],
            
            // صلاحيات المنتجات
            ['manage-products', 'إدارة المنتجات', 'المنتجات'],
            ['view-products', 'عرض المنتجات', 'المنتجات'],
            ['create-products', 'إضافة منتجات', 'المنتجات'],
            ['edit-products', 'تعديل المنتجات', 'المنتجات'],
            ['delete-products', 'حذف المنتجات', 'المنتجات'],
            ['manage-categories', 'إدارة الفئات', 'المنتجات'],
            
            // صلاحيات الطلبات
            ['manage-orders', 'إدارة الطلبات', 'الطلبات'],
            ['view-orders', 'عرض الطلبات', 'الطلبات'],
            ['edit-orders', 'تعديل الطلبات', 'الطلبات'],
            ['process-orders', 'معالجة الطلبات', 'الطلبات'],
            ['cancel-orders', 'إلغاء الطلبات', 'الطلبات'],
            
            // صلاحيات التقارير
            ['view-reports', 'عرض التقارير', 'التقارير'],
            ['export-reports', 'تصدير التقارير', 'التقارير'],
            ['advanced-reports', 'تقارير متقدمة', 'التقارير'],
            
            // صلاحيات المحتوى
            ['manage-content', 'إدارة المحتوى', 'المحتوى'],
            ['create-content', 'إنشاء محتوى', 'المحتوى'],
            ['edit-content', 'تعديل المحتوى', 'المحتوى'],
            ['delete-content', 'حذف المحتوى', 'المحتوى'],
            ['publish-content', 'نشر المحتوى', 'المحتوى'],
            
            // صلاحيات الإعدادات
            ['manage-settings', 'إدارة الإعدادات', 'الإعدادات'],
            ['view-settings', 'عرض الإعدادات', 'الإعدادات'],
            ['edit-settings', 'تعديل الإعدادات', 'الإعدادات'],
            ['system-settings', 'إعدادات النظام', 'الإعدادات'],
            ['backup-system', 'النسخ الاحتياطي', 'الإعدادات'],
            
            // صلاحيات الملفات
            ['manage-files', 'إدارة الملفات', 'الملفات'],
            ['upload-files', 'رفع الملفات', 'الملفات'],
            ['delete-files', 'حذف الملفات', 'الملفات'],
            
            // صلاحيات التعليقات
            ['manage-comments', 'إدارة التعليقات', 'التعليقات'],
            ['moderate-comments', 'مراجعة التعليقات', 'التعليقات'],
            ['delete-comments', 'حذف التعليقات', 'التعليقات'],
            
            // صلاحيات الإحصائيات
            ['view-analytics', 'عرض الإحصائيات', 'الإحصائيات'],
            ['advanced-analytics', 'إحصائيات متقدمة', 'الإحصائيات'],
            
            // صلاحيات الأمان
            ['security-logs', 'سجلات الأمان', 'الأمان'],
            ['manage-security', 'إدارة الأمان', 'الأمان'],
            ['audit-trail', 'مسار التدقيق', 'الأمان'],
        ];
        
        $addedCount = 0;
        $skippedCount = 0;
        
        foreach ($systemPermissions as $perm) {
            $result = $permissionManager->createPermission($perm[0], $perm[1], $perm[2]);
            if ($result) {
                $addedCount++;
            } else {
                $skippedCount++;
            }
        }
        
        $message = "تم إضافة {$addedCount} صلاحية جديدة. تم تخطي {$skippedCount} صلاحية (موجودة مسبقاً).";
        
    } catch (Exception $e) {
        $error = 'خطأ في إضافة الصلاحيات: ' . $e->getMessage();
    }
}

// إضافة الأدوار الافتراضية
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_roles'])) {
    try {
        // الأدوار الافتراضية
        $defaultRoles = [
            [
                'name' => 'super-admin',
                'display_name' => 'مدير عام',
                'description' => 'مدير عام للنظام مع جميع الصلاحيات',
                'permissions' => 'all' // جميع الصلاحيات
            ],
            [
                'name' => 'admin',
                'display_name' => 'مدير',
                'description' => 'مدير النظام',
                'permissions' => [
                    'admin-access', 'manage-users', 'view-users', 'create-users', 'edit-users',
                    'manage-products', 'view-products', 'create-products', 'edit-products',
                    'manage-orders', 'view-orders', 'edit-orders', 'process-orders',
                    'view-reports', 'export-reports', 'manage-content', 'create-content',
                    'edit-content', 'publish-content', 'view-settings', 'edit-settings'
                ]
            ],
            [
                'name' => 'manager',
                'display_name' => 'مدير قسم',
                'description' => 'مدير قسم مع صلاحيات محدودة',
                'permissions' => [
                    'admin-access', 'view-users', 'manage-products', 'view-products',
                    'create-products', 'edit-products', 'manage-orders', 'view-orders',
                    'edit-orders', 'process-orders', 'view-reports', 'manage-content',
                    'create-content', 'edit-content'
                ]
            ],
            [
                'name' => 'employee',
                'display_name' => 'موظف',
                'description' => 'موظف عادي',
                'permissions' => [
                    'admin-access', 'view-products', 'view-orders', 'edit-orders',
                    'view-reports', 'create-content'
                ]
            ],
            [
                'name' => 'customer',
                'display_name' => 'عميل',
                'description' => 'عميل عادي',
                'permissions' => [
                    'frontend-access'
                ]
            ]
        ];
        
        $addedRoles = 0;
        $skippedRoles = 0;
        
        foreach ($defaultRoles as $role) {
            // التحقق من وجود الدور
            $stmt = $database->prepare("SELECT id FROM roles WHERE name = ?");
            $stmt->execute([$role['name']]);
            
            if (!$stmt->fetch()) {
                // إنشاء الدور
                $roleId = $permissionManager->createRole(
                    $role['name'],
                    $role['display_name'],
                    $role['description']
                );
                
                if ($roleId) {
                    // إضافة الصلاحيات للدور
                    if ($role['permissions'] === 'all') {
                        // إضافة جميع الصلاحيات
                        $stmt = $database->prepare("SELECT id FROM permissions");
                        $stmt->execute();
                        $permissions = $stmt->fetchAll(PDO::FETCH_COLUMN);
                        
                        foreach ($permissions as $permId) {
                            $permissionManager->assignPermissionToRole($roleId, $permId);
                        }
                    } else {
                        // إضافة صلاحيات محددة
                        foreach ($role['permissions'] as $permName) {
                            $stmt = $database->prepare("SELECT id FROM permissions WHERE name = ?");
                            $stmt->execute([$permName]);
                            $permId = $stmt->fetchColumn();
                            
                            if ($permId) {
                                $permissionManager->assignPermissionToRole($roleId, $permId);
                            }
                        }
                    }
                    
                    $addedRoles++;
                }
            } else {
                $skippedRoles++;
            }
        }
        
        $message = "تم إضافة {$addedRoles} دور جديد. تم تخطي {$skippedRoles} دور (موجود مسبقاً).";
        
    } catch (Exception $e) {
        $error = 'خطأ في إضافة الأدوار: ' . $e->getMessage();
    }
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة الصلاحيات الافتراضية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .btn-success {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border: none;
        }
        .permission-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 10px;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="fas fa-shield-alt me-2"></i>
                            إضافة الصلاحيات والأدوار الافتراضية
                        </h4>
                    </div>
                    <div class="card-body">
                        <?php if ($message): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="fas fa-check-circle me-2"></i>
                                <?php echo $message; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($error): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                <?php echo $error; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0">
                                            <i class="fas fa-key me-2"></i>
                                            إضافة الصلاحيات الافتراضية
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <p class="text-muted">
                                            سيتم إضافة جميع الصلاحيات الأساسية للنظام مثل:
                                        </p>
                                        <div class="permission-item">
                                            <strong>صلاحيات النظام:</strong> الوصول للوحة التحكم، واجهة البرمجة
                                        </div>
                                        <div class="permission-item">
                                            <strong>صلاحيات المستخدمين:</strong> إدارة، عرض، إنشاء، تعديل، حذف
                                        </div>
                                        <div class="permission-item">
                                            <strong>صلاحيات المنتجات:</strong> إدارة المنتجات والفئات
                                        </div>
                                        <div class="permission-item">
                                            <strong>صلاحيات الطلبات:</strong> إدارة ومعالجة الطلبات
                                        </div>
                                        <div class="permission-item">
                                            <strong>صلاحيات أخرى:</strong> التقارير، المحتوى، الإعدادات، الأمان
                                        </div>
                                        
                                        <form method="POST" class="mt-3">
                                            <button type="submit" name="add_permissions" class="btn btn-primary btn-lg w-100">
                                                <i class="fas fa-plus me-2"></i>
                                                إضافة الصلاحيات الافتراضية
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0">
                                            <i class="fas fa-users-cog me-2"></i>
                                            إضافة الأدوار الافتراضية
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <p class="text-muted">
                                            سيتم إضافة الأدوار الأساسية مع صلاحياتها:
                                        </p>
                                        <div class="permission-item">
                                            <strong>مدير عام:</strong> جميع الصلاحيات
                                        </div>
                                        <div class="permission-item">
                                            <strong>مدير:</strong> صلاحيات إدارية أساسية
                                        </div>
                                        <div class="permission-item">
                                            <strong>مدير قسم:</strong> صلاحيات محدودة
                                        </div>
                                        <div class="permission-item">
                                            <strong>موظف:</strong> صلاحيات أساسية
                                        </div>
                                        <div class="permission-item">
                                            <strong>عميل:</strong> الوصول للموقع الأمامي
                                        </div>
                                        
                                        <form method="POST" class="mt-3">
                                            <button type="submit" name="add_roles" class="btn btn-success btn-lg w-100">
                                                <i class="fas fa-plus me-2"></i>
                                                إضافة الأدوار الافتراضية
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-center mt-4">
                            <a href="permissions.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-2"></i>
                                العودة لإدارة الصلاحيات
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>