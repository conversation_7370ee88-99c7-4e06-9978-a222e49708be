<?php
/**
 * Fix contact_info table structure and function usage
 */
require_once __DIR__ . '/config/config.php';

try {
    echo "Checking contact_info table structure...\n";
    
    // Check if contact_info table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'contact_info'");
    if ($stmt->rowCount() == 0) {
        echo "Creating contact_info table...\n";
        $sql = "CREATE TABLE contact_info (
            id INT AUTO_INCREMENT PRIMARY KEY,
            section_key VARCHAR(100) NOT NULL,
            field_name VARCHAR(100) NOT NULL,
            field_value TEXT,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_section_field (section_key, field_name)
        )";
        $pdo->exec($sql);
        echo "✓ contact_info table created successfully!\n";
    } else {
        echo "contact_info table exists. Checking structure...\n";
        
        // Check if the table has the correct columns
        $stmt = $pdo->query("DESCRIBE contact_info");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $required_columns = ['section_key', 'field_name', 'field_value'];
        $missing_columns = array_diff($required_columns, $columns);
        
        if (!empty($missing_columns)) {
            echo "Adding missing columns...\n";
            
            // Add missing columns
            if (in_array('field_name', $missing_columns)) {
                $pdo->exec("ALTER TABLE contact_info ADD COLUMN field_name VARCHAR(100) NOT NULL DEFAULT ''");
                echo "✓ Added field_name column\n";
            }
            
            if (in_array('field_value', $missing_columns)) {
                $pdo->exec("ALTER TABLE contact_info ADD COLUMN field_value TEXT");
                echo "✓ Added field_value column\n";
            }
            
            if (in_array('section_key', $missing_columns) && in_array('section', $columns)) {
                // Rename section to section_key if it exists
                $pdo->exec("ALTER TABLE contact_info CHANGE section section_key VARCHAR(100) NOT NULL");
                echo "✓ Renamed section column to section_key\n";
            } elseif (in_array('section_key', $missing_columns)) {
                $pdo->exec("ALTER TABLE contact_info ADD COLUMN section_key VARCHAR(100) NOT NULL DEFAULT ''");
                echo "✓ Added section_key column\n";
            }
            
            // If we have content column but no field_value, copy content to field_value
            if (in_array('content', $columns) && in_array('field_value', $missing_columns)) {
                $pdo->exec("UPDATE contact_info SET field_value = content WHERE field_value IS NULL OR field_value = ''");
                echo "✓ Copied content to field_value\n";
            }
        }
    }
    
    // Insert some default data if table is empty
    $stmt = $pdo->query("SELECT COUNT(*) FROM contact_info");
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        echo "Inserting default contact info data...\n";
        
        $default_data = [
            ['header', 'main_title', 'الدعم الفني'],
            ['header', 'sub_title', 'نحن هنا لمساعدتك في أي وقت'],
            ['contact_methods', 'show_section', '1'],
            ['contact_methods', 'main_title', 'طرق التواصل معنا'],
            ['contact_methods', 'sub_title', 'تواصل معنا بالطريقة التي تناسبك'],
            ['services', 'show_section', '1'],
            ['services', 'main_title', 'خدماتنا'],
            ['services', 'sub_title', 'نقدم مجموعة شاملة من الخدمات'],
            ['why_choose', 'show_section', '1'],
            ['why_choose', 'main_title', 'لماذا تختارنا'],
            ['why_choose', 'sub_title', 'نحن الخيار الأفضل لك'],
            ['testimonials', 'show_section', '1'],
            ['testimonials', 'main_title', 'آراء عملائنا'],
            ['testimonials', 'sub_title', 'ماذا يقول عملاؤنا عنا'],
            ['faqs', 'show_section', '1'],
            ['faqs', 'main_title', 'الأسئلة الشائعة'],
            ['faqs', 'sub_title', 'إجابات على الأسئلة الأكثر شيوعاً'],
            ['download_center', 'show_section', '1'],
            ['download_center', 'main_title', 'مركز التحميل'],
            ['download_center', 'sub_title', 'حمل الملفات والبرامج المساعدة']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO contact_info (section_key, field_name, field_value, is_active, created_at, updated_at) VALUES (?, ?, ?, 1, NOW(), NOW())");
        
        foreach ($default_data as $data) {
            $stmt->execute($data);
        }
        
        echo "✓ Default data inserted successfully!\n";
    }
    
    echo "\n✅ contact_info table structure fixed successfully!\n";
    echo "The getSupportContactInfo function should now work correctly.\n";
    
} catch (Exception $e) {
    echo "❌ Error fixing contact_info structure: " . $e->getMessage() . "\n";
}
?>