<?php
/**
 * API لجلب وتحديث معلومات التواصل
 * Contact Information API
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/config.php';
require_once '../includes/functions.php';

$method = $_SERVER['REQUEST_METHOD'];
$response = ['success' => false, 'message' => '', 'data' => null];

try {
    switch ($method) {
        case 'GET':
            // جلب معلومات التواصل
            $sectionKey = $_GET['section'] ?? null;
            
            if ($sectionKey) {
                // جلب قسم محدد
                $contactInfo = $database->fetch(
                    "SELECT * FROM contact_info WHERE section_key = ? AND is_active = 1",
                    [$sectionKey]
                );
                
                if ($contactInfo) {
                    if ($contactInfo['data']) {
                        $contactInfo['data_parsed'] = json_decode($contactInfo['data'], true);
                    }
                    $response['success'] = true;
                    $response['data'] = $contactInfo;
                } else {
                    $response['message'] = 'القسم المطلوب غير موجود';
                }
            } else {
                // جلب جميع معلومات التواصل
                $contactInfoList = $database->fetchAll(
                    "SELECT * FROM contact_info WHERE is_active = 1 ORDER BY section_key"
                );
                
                $formattedData = [];
                foreach ($contactInfoList as $item) {
                    if ($item['data']) {
                        $item['data_parsed'] = json_decode($item['data'], true);
                    }
                    $formattedData[$item['section_key']] = $item;
                }
                
                $response['success'] = true;
                $response['data'] = $formattedData;
            }
            break;
            
        case 'POST':
            // إنشاء أو تحديث معلومات التواصل
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input || !isset($input['section_key'])) {
                $response['message'] = 'بيانات غير صحيحة';
                break;
            }
            
            $sectionKey = $input['section_key'];
            $title = $input['title'] ?? '';
            $content = $input['content'] ?? '';
            $data = $input['data'] ?? [];
            $isActive = $input['is_active'] ?? true;
            
            $dataJson = json_encode($data, JSON_UNESCAPED_UNICODE);
            
            // التحقق من وجود السجل
            $existing = $database->fetch("SELECT id FROM contact_info WHERE section_key = ?", [$sectionKey]);
            
            if ($existing) {
                // تحديث السجل الموجود
                $database->query(
                    "UPDATE contact_info SET title = ?, content = ?, data = ?, is_active = ?, updated_at = NOW() WHERE section_key = ?",
                    [$title, $content, $dataJson, $isActive, $sectionKey]
                );
                $response['message'] = 'تم تحديث معلومات التواصل بنجاح';
            } else {
                // إنشاء سجل جديد
                $database->query(
                    "INSERT INTO contact_info (section_key, title, content, data, is_active, created_at, updated_at) VALUES (?, ?, ?, ?, ?, NOW(), NOW())",
                    [$sectionKey, $title, $content, $dataJson, $isActive]
                );
                $response['message'] = 'تم إنشاء معلومات التواصل بنجاح';
            }
            
            $response['success'] = true;
            break;
            
        case 'PUT':
            // تحديث معلومات التواصل
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input || !isset($input['section_key'])) {
                $response['message'] = 'بيانات غير صحيحة';
                break;
            }
            
            $sectionKey = $input['section_key'];
            $title = $input['title'] ?? '';
            $content = $input['content'] ?? '';
            $data = $input['data'] ?? [];
            $isActive = $input['is_active'] ?? true;
            
            $dataJson = json_encode($data, JSON_UNESCAPED_UNICODE);
            
            $result = $database->query(
                "UPDATE contact_info SET title = ?, content = ?, data = ?, is_active = ?, updated_at = NOW() WHERE section_key = ?",
                [$title, $content, $dataJson, $isActive, $sectionKey]
            );
            
            if ($result) {
                $response['success'] = true;
                $response['message'] = 'تم تحديث معلومات التواصل بنجاح';
            } else {
                $response['message'] = 'فشل في تحديث معلومات التواصل';
            }
            break;
            
        case 'DELETE':
            // حذف معلومات التواصل (تعطيل)
            $sectionKey = $_GET['section'] ?? null;
            
            if (!$sectionKey) {
                $response['message'] = 'معرف القسم مطلوب';
                break;
            }
            
            $result = $database->query(
                "UPDATE contact_info SET is_active = 0, updated_at = NOW() WHERE section_key = ?",
                [$sectionKey]
            );
            
            if ($result) {
                $response['success'] = true;
                $response['message'] = 'تم تعطيل معلومات التواصل بنجاح';
            } else {
                $response['message'] = 'فشل في تعطيل معلومات التواصل';
            }
            break;
            
        default:
            $response['message'] = 'طريقة غير مدعومة';
            break;
    }
    
} catch (Exception $e) {
    $response['success'] = false;
    $response['message'] = 'حدث خطأ: ' . $e->getMessage();
}

echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
?>