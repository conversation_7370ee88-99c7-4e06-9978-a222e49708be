<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار زر إرسال التقييم</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .hidden { display: none !important; }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 class="text-2xl font-bold mb-6 text-center">اختبار زر إرسال التقييم</h1>
        
        <!-- نتيجة الاختبار -->
        <div id="reviewResult" class="mb-4 p-4 rounded-lg hidden">
            <div id="reviewMessage"></div>
        </div>
        
        <form id="reviewForm">
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">اسم المراجع</label>
                <input type="text" name="customer_name" value="مستخدم تجريبي" required 
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg">
            </div>
            
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">التقييم</label>
                <input type="hidden" name="rating" value="5">
                <div class="flex">
                    <span class="text-yellow-400 text-2xl">★★★★★</span>
                </div>
            </div>
            
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">التعليق</label>
                <textarea name="review_text" rows="3" required 
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg">تقييم تجريبي ممتاز</textarea>
            </div>
            
            <input type="hidden" name="product_id" value="1">
            
            <button type="submit" id="submitReviewBtn"
                    class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-300">
                <span class="btn-text">إرسال التقييم</span>
                <span class="btn-loading hidden">
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    جاري الإرسال...
                </span>
            </button>
        </form>
        
        <!-- معلومات التشخيص -->
        <div class="mt-6 p-4 bg-gray-50 rounded-lg">
            <h3 class="font-semibold mb-2">معلومات التشخيص:</h3>
            <div id="diagnostics" class="text-sm text-gray-600">
                <div>حالة الزر: <span id="buttonStatus">جاهز</span></div>
                <div>آخر استجابة: <span id="lastResponse">لا توجد</span></div>
                <div>وقت الاستجابة: <span id="responseTime">-</span></div>
                <div>حالة العناصر: <span id="elementsStatus">فحص...</span></div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing...');
            
            // فحص العناصر
            const reviewForm = document.getElementById('reviewForm');
            const submitBtn = document.getElementById('submitReviewBtn');
            const btnText = submitBtn.querySelector('.btn-text');
            const btnLoading = submitBtn.querySelector('.btn-loading');
            const resultDiv = document.getElementById('reviewResult');
            const messageDiv = document.getElementById('reviewMessage');
            
            // تحديث حالة العناصر
            document.getElementById('elementsStatus').textContent = 
                `Form: ${!!reviewForm}, Button: ${!!submitBtn}, Text: ${!!btnText}, Loading: ${!!btnLoading}, Result: ${!!resultDiv}, Message: ${!!messageDiv}`;
            
            if (!reviewForm || !submitBtn) {
                console.error('Required elements not found!');
                return;
            }
            
            reviewForm.addEventListener('submit', function(e) {
                e.preventDefault();
                console.log('Form submitted, processing...');
                
                const startTime = Date.now();
                document.getElementById('buttonStatus').textContent = 'معالجة...';
                
                // Store original button content
                const originalHTML = submitBtn.innerHTML;
                console.log('Original HTML:', originalHTML);
                
                // Function to reset button state
                function resetButtonState() {
                    console.log('Resetting button state...');
                    document.getElementById('buttonStatus').textContent = 'جاهز';
                    
                    if (btnText && btnLoading) {
                        console.log('Using span method');
                        btnText.classList.remove('hidden');
                        btnLoading.classList.add('hidden');
                    } else {
                        console.log('Using innerHTML fallback');
                        submitBtn.innerHTML = originalHTML;
                    }
                    submitBtn.disabled = false;
                }
                
                // Show loading state
                if (btnText && btnLoading) {
                    console.log('Showing loading with spans');
                    btnText.classList.add('hidden');
                    btnLoading.classList.remove('hidden');
                } else {
                    console.log('Showing loading with innerHTML');
                    submitBtn.innerHTML = 'جاري الإرسال...';
                }
                submitBtn.disabled = true;
                
                // Prepare form data
                const formData = new FormData(reviewForm);
                
                // Simulate API call
                fetch('http://localhost:8000/api/review.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    const endTime = Date.now();
                    document.getElementById('responseTime').textContent = `${endTime - startTime}ms`;
                    
                    console.log('Response received:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Data received:', data);
                    document.getElementById('lastResponse').textContent = JSON.stringify(data);
                    
                    // Reset button state
                    resetButtonState();
                    
                    // Show result
                    if (resultDiv && messageDiv) {
                        resultDiv.classList.remove('hidden');
                        
                        if (data.success) {
                            resultDiv.className = 'mb-4 p-4 rounded-lg bg-green-100 border border-green-400 text-green-700';
                            messageDiv.innerHTML = data.message || 'تم إرسال التقييم بنجاح!';
                            
                            // Reset form after 2 seconds
                            setTimeout(() => {
                                reviewForm.reset();
                                resultDiv.classList.add('hidden');
                            }, 3000);
                        } else {
                            resultDiv.className = 'mb-4 p-4 rounded-lg bg-red-100 border border-red-400 text-red-700';
                            messageDiv.innerHTML = data.message || 'حدث خطأ أثناء إرسال التقييم';
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('lastResponse').textContent = 'خطأ: ' + error.message;
                    
                    // Reset button state
                    resetButtonState();
                    
                    // Show error
                    if (resultDiv && messageDiv) {
                        resultDiv.classList.remove('hidden');
                        resultDiv.className = 'mb-4 p-4 rounded-lg bg-red-100 border border-red-400 text-red-700';
                        messageDiv.innerHTML = 'حدث خطأ في الاتصال: ' + error.message;
                    }
                });
            });
        });
    </script>
</body>
</html>