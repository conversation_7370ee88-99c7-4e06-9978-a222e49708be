<?php
require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<h1>🔧 إصلاح مشكلة user_id نهائياً</h1>";

try {
    global $database;
    $pdo = $database->getConnection();
    
    // 1. فحص وإصلاح بنية جدول reviews
    echo "<h2>1. فحص وإصلاح بنية جدول reviews:</h2>";
    
    // التحقق من وجود عمود user_id
    $columns = $database->fetchAll("DESCRIBE reviews");
    $hasUserId = false;
    
    foreach ($columns as $column) {
        if ($column['Field'] == 'user_id') {
            $hasUserId = true;
            break;
        }
    }
    
    if (!$hasUserId) {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; color: #856404;'>";
        echo "⚠️ عمود user_id غير موجود - جاري إضافته...";
        echo "</div>";
        
        $database->query("ALTER TABLE reviews ADD COLUMN user_id INT NULL AFTER id");
        
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
        echo "✅ تم إضافة عمود user_id بنجاح";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
        echo "✅ عمود user_id موجود";
        echo "</div>";
    }
    
    // 2. تنظيف البيانات التجريبية
    echo "<h2>2. تنظيف البيانات التجريبية:</h2>";
    $deleted = $database->query("DELETE FROM reviews WHERE comment LIKE '%تجريبية%' OR comment LIKE '%اختبار%'")->rowCount();
    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; color: #856404;'>";
    echo "تم حذف $deleted مراجعة تجريبية";
    echo "</div>";
    
    // 3. اختبار إدراج مباشر
    echo "<h2>3. اختبار إدراج مباشر:</h2>";
    
    $testData = [
        'product_id' => 1,
        'user_id' => 3,
        'name' => 'اختبار إصلاح user_id',
        'email' => '<EMAIL>',
        'rating' => 5,
        'title' => 'اختبار إصلاح',
        'comment' => 'اختبار إصلاح مشكلة user_id',
        'is_approved' => 1,
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    // حذف أي مراجعة سابقة
    $database->query("DELETE FROM reviews WHERE product_id = 1 AND user_id = 3");
    
    // إدراج جديد
    $insertId = $database->insert('reviews', $testData);
    
    if ($insertId) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
        echo "✅ تم الإدراج بنجاح - معرف المراجعة: $insertId";
        echo "</div>";
        
        // التحقق من البيانات المحفوظة
        $saved = $database->fetch("SELECT * FROM reviews WHERE id = ?", [$insertId]);
        
        if ($saved && $saved['user_id'] == 3) {
            echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin-top: 10px; color: #0c5460; border: 2px solid #17a2b8;'>";
            echo "<h3>🎉 نجح الإصلاح! user_id تم حفظه بشكل صحيح</h3>";
            echo "<strong>البيانات المحفوظة:</strong><br>";
            echo "• معرف المراجعة: " . $saved['id'] . "<br>";
            echo "• معرف المنتج: " . $saved['product_id'] . "<br>";
            echo "• <span style='background: yellow; padding: 2px; font-weight: bold;'>معرف المستخدم: " . $saved['user_id'] . "</span><br>";
            echo "• الاسم: " . $saved['name'] . "<br>";
            echo "• التقييم: " . $saved['rating'] . "<br>";
            echo "• تاريخ الحفظ: " . $saved['created_at'];
            echo "</div>";
            
            // 4. اختبار دالة saveUserReview
            echo "<h2>4. اختبار دالة saveUserReview:</h2>";
            
            // حذف المراجعة السابقة
            $database->query("DELETE FROM reviews WHERE id = ?", [$insertId]);
            
            // اختبار الدالة
            $functionResult = saveUserReview(1, 3, 'اختبار دالة', '<EMAIL>', 4, 'اختبار دالة saveUserReview', 'اختبار دالة');
            
            if ($functionResult && is_numeric($functionResult)) {
                $functionSaved = $database->fetch("SELECT * FROM reviews WHERE id = ?", [$functionResult]);
                
                if ($functionSaved && $functionSaved['user_id'] == 3) {
                    echo "<div style='background: #28a745; color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
                    echo "<h2>🎊 تم حل المشكلة بنجاح! 🎊</h2>";
                    echo "<p>دالة saveUserReview تعمل بشكل صحيح وتحفظ user_id</p>";
                    echo "<p><strong>معرف المستخدم المحفوظ: " . $functionSaved['user_id'] . "</strong></p>";
                    echo "</div>";
                } else {
                    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
                    echo "❌ دالة saveUserReview لا تحفظ user_id بشكل صحيح";
                    echo "</div>";
                }
            } else {
                echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
                echo "❌ دالة saveUserReview فشلت: " . $functionResult;
                echo "</div>";
            }
            
        } else {
            echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
            echo "❌ فشل في حفظ user_id حتى مع الإدراج المباشر";
            echo "</div>";
        }
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
        echo "❌ فشل في الإدراج المباشر";
        echo "</div>";
    }
    
    // 5. عرض جميع المراجعات
    echo "<h2>5. جميع المراجعات الحالية:</h2>";
    $allReviews = $database->fetchAll("SELECT * FROM reviews ORDER BY created_at DESC LIMIT 5");
    
    if (!empty($allReviews)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; background: white;'>";
        echo "<tr style='background: #007bff; color: white;'>";
        echo "<th>ID</th><th>Product ID</th><th style='background: #ffc107; color: black;'>User ID</th><th>Name</th><th>Rating</th><th>Comment</th><th>Created</th>";
        echo "</tr>";
        
        foreach ($allReviews as $review) {
            $userIdStyle = $review['user_id'] ? 'background: #d4edda; color: #155724;' : 'background: #f8d7da; color: #721c24;';
            echo "<tr>";
            echo "<td>" . $review['id'] . "</td>";
            echo "<td>" . $review['product_id'] . "</td>";
            echo "<td style='$userIdStyle'><strong>" . ($review['user_id'] ?: 'NULL') . "</strong></td>";
            echo "<td>" . $review['name'] . "</td>";
            echo "<td>" . $review['rating'] . "</td>";
            echo "<td>" . substr($review['comment'], 0, 30) . "...</td>";
            echo "<td>" . $review['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
    echo "<h3>❌ خطأ في الإصلاح:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
    echo "</div>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
}

h1, h2 {
    color: white;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

table {
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    border-radius: 8px;
    overflow: hidden;
}

th, td {
    padding: 10px;
    text-align: center;
}

div {
    margin: 10px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>