<?php
session_start();
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

echo "<h1>🧪 اختبار API النهائي لحفظ user_id</h1>";

// محاكاة تسجيل دخول المستخدم
$_SESSION['user_id'] = 3;
$_SESSION['username'] = 'المدير العام';
$_SESSION['email'] = '<EMAIL>';
$_SESSION['role'] = 'admin';

echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724; margin: 10px 0;'>";
echo "✅ تم تسجيل دخول المستخدم: " . $_SESSION['username'] . " (ID: " . $_SESSION['user_id'] . ")";
echo "</div>";

// اختبار API مباشرة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_api'])) {
    echo "<h2>📡 نتيجة اختبار API:</h2>";
    
    // إعداد بيانات POST للاختبار
    $_POST['product_id'] = 3;
    $_POST['rating'] = 5;
    $_POST['review_text'] = 'اختبار API نهائي مع user_id';
    $_POST['review_title'] = 'اختبار نهائي';
    
    // حذف المراجعات التجريبية السابقة
    global $database;
    $database->query("DELETE FROM reviews WHERE comment LIKE '%اختبار API نهائي%'");
    
    echo "<div style='background: #e2e3e5; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>بيانات POST المرسلة:</strong><br>";
    echo "• product_id: " . $_POST['product_id'] . "<br>";
    echo "• rating: " . $_POST['rating'] . "<br>";
    echo "• review_text: " . $_POST['review_text'] . "<br>";
    echo "• review_title: " . $_POST['review_title'] . "<br>";
    echo "<span style='background: yellow; padding: 2px;'>• user_id: سيتم الحصول عليه من الجلسة (ID: " . $_SESSION['user_id'] . ")</span>";
    echo "</div>";
    
    // تشغيل نفس منطق API
    try {
        $isLoggedIn = isLoggedIn();
        $currentUser = $isLoggedIn ? getCurrentUser() : null;
        
        echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; color: #0c5460;'>";
        echo "<strong>حالة المستخدم:</strong><br>";
        echo "• مسجل دخول: " . ($isLoggedIn ? 'نعم' : 'لا') . "<br>";
        if ($currentUser) {
            echo "• user_id: " . $currentUser['id'] . "<br>";
            echo "• الاسم: " . $currentUser['name'] . "<br>";
            echo "• البريد: " . $currentUser['email'] . "<br>";
        }
        echo "</div>";
        
        if ($isLoggedIn) {
            $user_id = $currentUser['id'];
            $customer_name = $currentUser['name'];
            $customer_email = $currentUser['email'];
            
            $product_id = (int)$_POST['product_id'];
            $rating = (int)$_POST['rating'];
            $review_text = $_POST['review_text'];
            $review_title = $_POST['review_title'];
            
            echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; color: #856404;'>";
            echo "<strong>استدعاء saveUserReview مع:</strong><br>";
            echo "• product_id: $product_id<br>";
            echo "<span style='background: yellow; padding: 2px; font-weight: bold;'>• user_id: $user_id</span><br>";
            echo "• customer_name: $customer_name<br>";
            echo "• customer_email: $customer_email<br>";
            echo "• rating: $rating<br>";
            echo "• review_text: $review_text<br>";
            echo "• review_title: $review_title<br>";
            echo "</div>";
            
            $review_id = saveUserReview($product_id, $user_id, $customer_name, $customer_email, $rating, $review_text, $review_title);
            
            if (is_numeric($review_id)) {
                echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724; margin: 10px 0;'>";
                echo "<h3>🎉 نجح الاختبار!</h3>";
                echo "• تم حفظ المراجعة بنجاح<br>";
                echo "• معرف المراجعة: $review_id<br>";
                echo "</div>";
                
                // التحقق من البيانات المحفوظة
                $saved = $database->fetch("SELECT * FROM reviews WHERE id = ?", [$review_id]);
                
                echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0; border-left: 5px solid #007bff;'>";
                echo "<h3>📋 البيانات المحفوظة في قاعدة البيانات:</h3>";
                echo "<table border='1' style='border-collapse: collapse; width: 100%; background: white;'>";
                echo "<tr style='background: #007bff; color: white;'>";
                echo "<th>الحقل</th><th>القيمة</th><th>الحالة</th>";
                echo "</tr>";
                
                $fields = [
                    'id' => 'معرف المراجعة',
                    'product_id' => 'معرف المنتج',
                    'user_id' => 'معرف المستخدم',
                    'name' => 'اسم العميل',
                    'email' => 'البريد الإلكتروني',
                    'rating' => 'التقييم',
                    'title' => 'عنوان المراجعة',
                    'comment' => 'نص المراجعة',
                    'is_approved' => 'حالة الموافقة',
                    'created_at' => 'تاريخ الإنشاء'
                ];
                
                foreach ($fields as $field => $label) {
                    $value = $saved[$field] ?? 'NULL';
                    $status = '';
                    $style = '';
                    
                    if ($field == 'user_id') {
                        if ($value && $value == $user_id) {
                            $status = '✅ صحيح';
                            $style = 'background: #d4edda; color: #155724; font-weight: bold;';
                        } else {
                            $status = '❌ خطأ';
                            $style = 'background: #f8d7da; color: #721c24; font-weight: bold;';
                        }
                    } elseif ($field == 'product_id') {
                        if ($value && $value == $product_id) {
                            $status = '✅ صحيح';
                            $style = 'background: #d4edda; color: #155724;';
                        } else {
                            $status = '❌ خطأ';
                            $style = 'background: #f8d7da; color: #721c24;';
                        }
                    } else {
                        $status = $value ? '✅ موجود' : '⚠️ فارغ';
                    }
                    
                    echo "<tr style='$style'>";
                    echo "<td><strong>$label</strong></td>";
                    echo "<td>" . ($value ?: 'NULL') . "</td>";
                    echo "<td>$status</td>";
                    echo "</tr>";
                }
                echo "</table>";
                echo "</div>";
                
            } else {
                echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24; margin: 10px 0;'>";
                echo "<h3>❌ فشل الاختبار!</h3>";
                echo "• خطأ في saveUserReview: $review_id<br>";
                echo "</div>";
            }
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24; margin: 10px 0;'>";
        echo "<h3>❌ خطأ في النظام:</h3>";
        echo "• " . $e->getMessage() . "<br>";
        echo "</div>";
    }
}

// عرض آخر المراجعات
echo "<h2>📊 آخر المراجعات:</h2>";
try {
    global $database;
    $recent = $database->fetchAll("
        SELECT r.*, u.username, p.name as product_name 
        FROM reviews r 
        LEFT JOIN users u ON r.user_id = u.id 
        LEFT JOIN products p ON r.product_id = p.id 
        ORDER BY r.created_at DESC 
        LIMIT 10
    ");
    
    if (!empty($recent)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; background: white; margin: 10px 0;'>";
        echo "<tr style='background: #007bff; color: white;'>";
        echo "<th>ID</th><th>المنتج</th><th style='background: #28a745;'>المستخدم</th><th>الاسم</th><th>التقييم</th><th>التعليق</th><th>التاريخ</th>";
        echo "</tr>";
        
        foreach ($recent as $review) {
            $userStyle = $review['user_id'] ? 'background: #d4edda; font-weight: bold;' : 'background: #f8d7da; color: #721c24;';
            echo "<tr>";
            echo "<td>" . $review['id'] . "</td>";
            echo "<td>" . ($review['product_name'] ?: 'منتج محذوف') . " (ID: " . $review['product_id'] . ")</td>";
            echo "<td style='$userStyle'>";
            if ($review['user_id']) {
                echo ($review['username'] ?: 'مستخدم محذوف') . " (ID: " . $review['user_id'] . ")";
            } else {
                echo "زائر (بدون user_id)";
            }
            echo "</td>";
            echo "<td>" . $review['name'] . "</td>";
            echo "<td>" . $review['rating'] . "/5</td>";
            echo "<td>" . substr($review['comment'], 0, 30) . "...</td>";
            echo "<td>" . $review['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; color: #856404;'>لا توجد مراجعات</div>";
    }
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>خطأ في عرض المراجعات: " . $e->getMessage() . "</div>";
}
?>

<h2>🧪 نموذج اختبار API:</h2>
<form method="POST" style="background: #f8f9fa; padding: 20px; border-radius: 10px; border: 2px solid #007bff;">
    <div style="background: #e7f3ff; padding: 15px; border-radius: 5px; margin-bottom: 15px;">
        <h3>📝 اختبار API مع user_id من الجلسة</h3>
        <p>هذا الاختبار سيحاكي إرسال مراجعة عبر API مع استخدام user_id من الجلسة</p>
    </div>
    
    <button type="submit" name="test_api" style="background: #007bff; color: white; padding: 15px 30px; border: none; border-radius: 5px; font-size: 16px; cursor: pointer;">
        🚀 اختبار API الآن
    </button>
</form>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background: #f8f9fa;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

table {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 5px;
    overflow: hidden;
}

th, td {
    padding: 8px;
    text-align: right;
    font-size: 12px;
}

button:hover {
    background: #0056b3 !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

div {
    margin: 10px 0;
}
</style>