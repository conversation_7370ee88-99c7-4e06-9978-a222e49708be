<?php
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../includes/functions.php';

// Get current user info
$currentUser = $_SESSION['user_name'] ?? 'المدير';
$currentUserEmail = $_SESSION['user_email'] ?? '';
$currentUserRole = $_SESSION['user_role'] ?? 'admin';

// Get notifications
try {
    // Recent messages
    $newMessages = $database->fetch("SELECT COUNT(*) as count FROM contact_messages WHERE status = 'unread' AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)")['count'] ?? 0;
    
    // Recent reviews
    $newReviews = $database->fetch("SELECT COUNT(*) as count FROM reviews WHERE status = 'pending' AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)")['count'] ?? 0;
    
    // Recent service requests
    $newServiceRequests = $database->fetch("SELECT COUNT(*) as count FROM service_requests WHERE status = 'pending' AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)")['count'] ?? 0;
    
    // Get recent notifications
    $notifications = $database->fetchAll("
        SELECT 'message' as type, id, name as title, 'رسالة جديدة' as description, created_at 
        FROM contact_messages 
        WHERE status = 'unread' AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        UNION ALL
        SELECT 'review' as type, id, CONCAT('تقييم من ', customer_name) as title, 'تقييم جديد في انتظار الموافقة' as description, created_at 
        FROM reviews 
        WHERE status = 'pending' AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        UNION ALL
        SELECT 'service' as type, id, CONCAT('طلب خدمة من ', customer_name) as title, 'طلب خدمة جديد' as description, created_at 
        FROM service_requests 
        WHERE status = 'pending' AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        ORDER BY created_at DESC 
        LIMIT 10
    ") ?? [];
    
} catch (Exception $e) {
    $newMessages = $newReviews = $newServiceRequests = 0;
    $notifications = [];
}

$totalNotifications = $newMessages + $newReviews + $newServiceRequests;
?>

<header class="bg-white shadow-sm border-b border-gray-200">
    <div class="flex items-center justify-between px-6 py-4">
        <!-- Left side - Page title and breadcrumb -->
        <div class="flex items-center">
            <button id="sidebar-toggle" class="lg:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
            </button>
            
            <div class="mr-4">
                <h1 class="text-xl font-semibold text-gray-900"><?php echo $pageTitle ?? 'لوحة التحكم'; ?></h1>
                <?php if (isset($breadcrumbs) && !empty($breadcrumbs)): ?>
                <nav class="flex mt-1" aria-label="Breadcrumb">
                    <ol class="flex items-center space-x-2 space-x-reverse text-sm text-gray-500">
                        <li>
                            <a href="/admin/" class="hover:text-gray-700">لوحة التحكم</a>
                        </li>
                        <?php foreach ($breadcrumbs as $item): ?>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 mx-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                            <?php if (isset($item['url'])): ?>
                                <a href="<?php echo $item['url']; ?>" class="hover:text-gray-700"><?php echo $item['title']; ?></a>
                            <?php else: ?>
                                <span class="text-gray-900"><?php echo $item['title']; ?></span>
                            <?php endif; ?>
                        </li>
                        <?php endforeach; ?>
                    </ol>
                </nav>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Right side - Search, notifications, and user menu -->
        <div class="flex items-center space-x-4 space-x-reverse">
            <!-- Search -->
            <div class="relative hidden md:block">
                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
                <input type="text" id="admin-search" 
                       class="block w-full pr-10 pl-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm" 
                       placeholder="البحث...">
            </div>
            
            <!-- Quick Actions -->
            <div class="flex items-center space-x-2 space-x-reverse">
                <!-- View Site -->
                <a href="/" target="_blank" 
                   class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors duration-200" 
                   title="عرض الموقع">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                    </svg>
                </a>
                
                <!-- Add New -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" 
                            class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors duration-200" 
                            title="إضافة جديد">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                    </button>
                    
                    <div x-show="open" @click.away="open = false" 
                         class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200">
                        <a href="/admin/products.php?action=add" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">منتج جديد</a>
                        <a href="/admin/users.php?action=add" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">مستخدم جديد</a>
                        <a href="/admin/faqs.php?action=add" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">سؤال شائع</a>
                        <a href="/admin/distributors.php?action=add" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">موزع جديد</a>
                    </div>
                </div>
            </div>
            
            <!-- Notifications -->
            <div class="relative" x-data="{ open: false }">
                <button @click="open = !open" 
                        class="relative p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors duration-200">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"></path>
                    </svg>
                    <?php if ($totalNotifications > 0): ?>
                    <span class="absolute top-0 left-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full">
                        <?php echo $totalNotifications > 99 ? '99+' : $totalNotifications; ?>
                    </span>
                    <?php endif; ?>
                </button>
                
                <div x-show="open" @click.away="open = false" 
                     class="absolute left-0 mt-2 w-80 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200">
                    <div class="px-4 py-2 border-b border-gray-200">
                        <h3 class="text-sm font-medium text-gray-900">الإشعارات</h3>
                    </div>
                    
                    <div class="max-h-64 overflow-y-auto">
                        <?php if (!empty($notifications)): ?>
                            <?php foreach ($notifications as $notification): ?>
                            <div class="px-4 py-3 hover:bg-gray-50 border-b border-gray-100">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <?php if ($notification['type'] === 'message'): ?>
                                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                                </svg>
                                            </div>
                                        <?php elseif ($notification['type'] === 'review'): ?>
                                            <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                                <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                                </svg>
                                            </div>
                                        <?php else: ?>
                                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="mr-3 flex-1">
                                        <p class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($notification['title']); ?></p>
                                        <p class="text-xs text-gray-500"><?php echo htmlspecialchars($notification['description']); ?></p>
                                        <p class="text-xs text-gray-400 mt-1"><?php echo formatDate($notification['created_at']); ?></p>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="px-4 py-8 text-center text-gray-500">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"></path>
                                </svg>
                                <p class="mt-2 text-sm">لا توجد إشعارات جديدة</p>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <?php if (!empty($notifications)): ?>
                    <div class="px-4 py-2 border-t border-gray-200">
                        <a href="/admin/notifications.php" class="text-sm text-blue-600 hover:text-blue-800">عرض جميع الإشعارات</a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- User Menu -->
            <div class="relative" x-data="{ open: false }">
                <button @click="open = !open" 
                        class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                        <span class="text-sm font-medium text-white">
                            <?php echo strtoupper(substr($currentUser, 0, 1)); ?>
                        </span>
                    </div>
                    <span class="mr-2 text-sm font-medium text-gray-700 hidden md:block"><?php echo htmlspecialchars($currentUser); ?></span>
                    <svg class="mr-1 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                
                <div x-show="open" @click.away="open = false" 
                     class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200">
                    <div class="px-4 py-2 border-b border-gray-200">
                        <p class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($currentUser); ?></p>
                        <p class="text-xs text-gray-500"><?php echo htmlspecialchars($currentUserEmail); ?></p>
                    </div>
                    
                    <a href="/admin/" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 9h6v6H9z"></path>
                            </svg>
                            لوحة التحكم
                        </div>
                    </a>
                    
                    <a href="/admin/profile.php" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            الملف الشخصي
                        </div>
                    </a>
                    
                    <a href="/admin/settings.php" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            الإعدادات
                        </div>
                    </a>
                    
                    <div class="border-t border-gray-200 my-1"></div>
                    
                    <a href="/admin/logout.php" class="block px-4 py-2 text-sm text-red-700 hover:bg-red-50">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                            </svg>
                            تسجيل الخروج
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</header>

<!-- Alpine.js for dropdowns -->
<script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>

<script>
// Search functionality
document.getElementById('admin-search')?.addEventListener('input', function(e) {
    const query = e.target.value.trim();
    if (query.length >= 2) {
        // Implement search functionality here
        console.log('Searching for:', query);
    }
});

// Sidebar toggle for mobile
document.getElementById('sidebar-toggle')?.addEventListener('click', function() {
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) {
        sidebar.classList.toggle('hidden');
    }
});

// Auto-refresh notifications every 30 seconds
setInterval(() => {
    fetch('/admin/api/notifications.php')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.count > 0) {
                // Update notification badge
                const badge = document.querySelector('.notification-badge');
                if (badge) {
                    badge.textContent = data.count > 99 ? '99+' : data.count;
                    badge.classList.remove('hidden');
                }
            }
        })
        .catch(error => console.error('Error fetching notifications:', error));
}, 30000);
</script>