<?php
$product_id = $_GET['id'] ?? 0;
$product = getProduct($product_id);

if (!$product) {
    header('HTTP/1.0 404 Not Found');
    include '404.php';
    exit;
}

$pageTitle = $product['name'];
$page = 'products';
$additionalCSS = '
<link rel="stylesheet" href="' . SITE_URL . '/assets/css/products.css">
<style>
/* Tab Styles */
.product-tab-button {
    transition: all 0.3s ease;
    position: relative;
    outline: none;
}

.product-tab-button:hover {
    color: #059669 !important;
    border-color: #d1d5db !important;
}

.product-tab-button.active {
    color: #059669 !important;
    border-color: #059669 !important;
}

/* Tab Panes */
.product-tab-pane {
    display: none !important;
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
}

.product-tab-pane.active {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Force display for active tabs */
.product-tab-pane[style*="display: block"] {
    opacity: 1 !important;
    visibility: visible !important;
}

/* Specifications styling */
.specifications-list {
    max-height: none;
    overflow: visible;
}

.specification-item {
    padding: 8px 0;
    border-bottom: 1px solid #f3f4f6;
}

.specification-item:last-child {
    border-bottom: none;
}

/* Debug styles (remove in production) */
.debug-info {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
    margin-top: 10px;
    font-size: 12px;
    color: #6c757d;
}

/* Ensure proper spacing */
.product-tab-content {
    min-height: 300px;
}

/* Animation for tab switching */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.product-tab-pane.active {
    animation: fadeIn 0.3s ease-in-out;
}
</style>';

// تضمين المساعد
require_once __DIR__ . '/../includes/ProductHelper.php';

// جلب البيانات المرتبطة
$product_images = getProductImages($product_id);
$product_reviews = getProductReviews($product_id);
$related_products = getProducts([
    'category' => $product['category_id'],
    'exclude' => $product_id,
    'limit' => 4
]);

// معالجة إرسال التقييم
if ($_POST && isset($_POST['submit_review'])) {
    // التحقق من تسجيل الدخول
    if (!isLoggedIn()) {
        $_SESSION['review_error'] = 'يجب عليك تسجيل الدخول أولاً لإضافة تقييم.';
    } else {
        // التحقق من صحة البيانات
        $errors = [];
        
        if (empty($_POST['rating']) || $_POST['rating'] < 1 || $_POST['rating'] > 5) {
            $errors[] = 'التقييم مطلوب ويجب أن يكون بين 1 و 5';
        }
        
        if (empty($_POST['comment'])) {
            $errors[] = 'التعليق مطلوب';
        }
        
        if (empty($errors)) {
            $review_data = [
                'product_id' => $product_id,
                'rating' => intval($_POST['rating']),
                'title' => sanitizeInput($_POST['title'] ?? ''),
                'comment' => sanitizeInput($_POST['comment'])
            ];
            
            $result = saveProductReview(
                $review_data['product_id'],
                $review_data['rating'],
                $review_data['title'],
                $review_data['comment']
            );
            if ($result === true) {
                $_SESSION['review_success'] = 'تم إرسال تقييمك بنجاح! سيتم مراجعته من قبل الإدارة وعرضه قريباً.';
                redirect(SITE_URL . '/products/' . $product_id);
            } else {
                $_SESSION['review_error'] = is_string($result) ? $result : 'حدث خطأ أثناء حفظ التقييم';
            }
        } else {
            $_SESSION['review_errors'] = $errors;
        }
    }
}

include 'header.php';
?>

<!-- Breadcrumb -->
<nav class="bg-gray-100 py-4">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <ol class="flex items-center space-x-2 space-x-reverse text-sm">
            <li><a href="<?php echo SITE_URL; ?>" class="text-gray-500 hover:text-primary">الرئيسية</a></li>
            <li class="text-gray-400">/</li>
            <li><a href="<?php echo SITE_URL; ?>/products" class="text-gray-500 hover:text-primary">المنتجات</a></li>
            <?php if ($product['category_name']): ?>
                <li class="text-gray-400">/</li>
                <li><a href="<?php echo SITE_URL; ?>/products?category=<?php echo $product['category_id']; ?>" class="text-gray-500 hover:text-primary"><?php echo htmlspecialchars($product['category_name']); ?></a></li>
            <?php endif; ?>
            <li class="text-gray-400">/</li>
            <li class="text-gray-900 font-medium"><?php echo htmlspecialchars($product['name']); ?></li>
        </ol>
    </div>
</nav>

<!-- Product Details -->
<section class="py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            
            <!-- Product Images -->
            <div class="space-y-4">
                <!-- Main Image -->
                <div class="aspect-w-1 aspect-h-1 bg-gray-200 rounded-lg overflow-hidden">
                    <img id="main-image" 
                         src="<?php echo $product['image'] ?: '/assets/images/default-product.jpg'; ?>" 
                         alt="<?php echo htmlspecialchars($product['name']); ?>"
                         class="w-full h-96 object-cover cursor-zoom-in"
                         onclick="openImageModal(this.src)">
                </div>
                
                <!-- Thumbnail Images -->
                <?php if (!empty($product_images)): ?>
                    <div class="grid grid-cols-4 gap-4">
                        <div class="aspect-w-1 aspect-h-1 bg-gray-200 rounded-lg overflow-hidden cursor-pointer border-2 border-primary" 
                             onclick="changeMainImage('<?php echo $product['image']; ?>', this)">
                            <img src="<?php echo $product['image']; ?>" 
                                 alt="<?php echo htmlspecialchars($product['name']); ?>" 
                                 class="w-full h-20 object-cover">
                        </div>
                        <?php foreach ($product_images as $image): ?>
                            <div class="aspect-w-1 aspect-h-1 bg-gray-200 rounded-lg overflow-hidden cursor-pointer border-2 border-transparent hover:border-primary transition-colors" 
                                 onclick="changeMainImage('<?php echo $image['image_url']; ?>', this)">
                                <img src="<?php echo $image['image_url']; ?>" 
                                     alt="<?php echo htmlspecialchars($product['name']); ?>" 
                                     class="w-full h-20 object-cover">
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- Product Info -->
            <div class="space-y-6">
                <!-- Product Title and Category -->
                <div>
                    <?php if ($product['category_name']): ?>
                        <span class="inline-block bg-primary text-white px-3 py-1 rounded-full text-sm font-medium mb-3">
                            <?php echo htmlspecialchars($product['category_name']); ?>
                        </span>
                    <?php endif; ?>
                    
                    <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                        <?php echo htmlspecialchars($product['name']); ?>
                    </h1>
                    
                    <!-- Badges -->
                    <div class="flex items-center space-x-2 space-x-reverse mb-4">
                        <?php if (!empty($product['badge'])): ?>
                            <span class="bg-primary text-white px-3 py-1 rounded-full text-sm font-semibold">
                                <?php echo htmlspecialchars($product['badge']); ?>
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Rating -->
                <?php if (isset($product['rating']) && $product['rating'] > 0): ?>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <div class="flex items-center">
                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                <svg class="w-5 h-5 <?php echo $i <= $product['rating'] ? 'text-yellow-400' : 'text-gray-300'; ?>" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                </svg>
                            <?php endfor; ?>
                        </div>
                        <span class="text-gray-600"><?php echo $product['rating']; ?> من 5</span>
                        <span class="text-gray-400">•</span>
                        <span class="text-gray-600"><?php echo count($product_reviews); ?> تقييم</span>
                    </div>
                <?php endif; ?>
                
                <!-- Description -->
                <?php if (!empty($product['description'])): ?>
                    <div class="prose prose-lg max-w-none">
                        <p class="text-gray-700 leading-relaxed">
                            <?php echo nl2br(htmlspecialchars($product['description'])); ?>
                        </p>
                    </div>
                <?php endif; ?>
                
                <!-- Specifications -->
                <?php if (!empty($product['specifications'])): ?>
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">المواصفات الفنية</h3>
                        <?php echo ProductHelper::renderFeatures($product['specifications'], 'w-5 h-5 text-blue-500 ml-2 flex-shrink-0', 'text-gray-700'); ?>
                    </div>
                <?php endif; ?>
                
                <!-- Features -->
                <?php if (!empty($product['features'])): ?>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">المميزات</h3>
                        <?php echo ProductHelper::renderFeatures($product['features']); ?>
                    </div>
                <?php endif; ?>
                
                <!-- Action Buttons -->
                <div class="space-y-4">
                    <div class="flex flex-col sm:flex-row gap-4">
                        <button onclick="requestQuote(<?php echo $product['id']; ?>)" 
                                class="flex-1 bg-primary hover:bg-secondary text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105">
                            طلب عرض سعر
                        </button>
                        
                        <button onclick="contactUs()" 
                                class="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300">
                            تواصل معنا
                        </button>
                    </div>
                    
                    <div class="flex items-center justify-center space-x-4 space-x-reverse text-sm text-gray-600">
                        <button onclick="toggleWishlist(<?php echo $product['id']; ?>)" class="flex items-center hover:text-primary transition-colors">
                            <svg class="w-5 h-5 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            </svg>
                            إضافة للمفضلة
                        </button>
                        
                        <button onclick="shareProduct()" class="flex items-center hover:text-primary transition-colors">
                            <svg class="w-5 h-5 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                            </svg>
                            مشاركة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Product Tabs -->
<section class="py-12 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Tab Navigation -->
        <div class="border-b border-gray-200 mb-8">
            <nav class="product-tabs-nav flex space-x-8 space-x-reverse">
                <button id="description-btn" class="product-tab-button active px-4 py-2 text-sm font-medium border-b-2 border-primary text-primary transition-all duration-300" data-tab="description">
                    الوصف التفصيلي
                </button>
                <button id="specifications-btn" class="product-tab-button px-4 py-2 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 transition-all duration-300" data-tab="specifications">
                    المواصفات
                </button>
                <button id="reviews-btn" class="product-tab-button px-4 py-2 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 transition-all duration-300" data-tab="reviews">
                    التقييمات (<?php echo count($product_reviews); ?>)
                </button>
            </nav>
        </div>
        
        <!-- Tab Content -->
        <div class="product-tab-content">
            <!-- Description Tab -->
            <div id="description-tab" class="product-tab-pane active">
                <div class="prose prose-lg max-w-none bg-white rounded-lg p-6">
                    <?php if (!empty($product['detailed_description'])): ?>
                        <?php echo nl2br(htmlspecialchars($product['detailed_description'])); ?>
                    <?php else: ?>
                        <?php echo nl2br(htmlspecialchars($product['description'])); ?>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Specifications Tab -->
            <div id="specifications-tab" class="product-tab-pane">
                <div class="bg-white rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">المواصفات الفنية</h3>
                    
                    <?php 
                    // تشخيص البيانات
                    $has_specifications = false;
                    $specifications_to_show = [];
                    
                    if (!empty($product['specifications'])) {
                        if (is_array($product['specifications'])) {
                            $specifications_to_show = $product['specifications'];
                            $has_specifications = true;
                        } elseif (is_string($product['specifications'])) {
                            // محاولة تحويل من JSON
                            $decoded = json_decode($product['specifications'], true);
                            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                                $specifications_to_show = $decoded;
                                $has_specifications = true;
                            } else {
                                // تقسيم النص بناءً على الأسطر
                                $lines = explode("\n", trim($product['specifications']));
                                $specifications_to_show = array_filter(array_map('trim', $lines));
                                $has_specifications = !empty($specifications_to_show);
                            }
                        }
                    }
                    ?>
                    
                    <?php if ($has_specifications): ?>
                        <div class="space-y-3">
                            <?php foreach ($specifications_to_show as $spec): ?>
                                <?php if (!empty(trim($spec))): ?>
                                    <div class="flex items-start">
                                        <svg class="w-5 h-5 text-blue-500 ml-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        <span class="text-gray-700"><?php echo htmlspecialchars(trim($spec)); ?></span>
                                    </div>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-8">
                            <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <p class="text-gray-500">لا توجد مواصفات فنية متاحة لهذا المنتج.</p>
                        </div>
                    <?php endif; ?>
                    

                </div>
            </div>
            
            <!-- Reviews Tab -->
            <div id="reviews-tab" class="product-tab-pane">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    
                    <!-- Reviews List -->
                    <div class="lg:col-span-2">
                        <?php if (!empty($product_reviews)): ?>
                            <div class="space-y-6">
                                <?php foreach ($product_reviews as $review): ?>
                                    <div class="bg-white rounded-lg p-6 shadow-sm">
                                        <div class="flex items-center justify-between mb-4">
                                            <div class="flex items-center">
                                                <div class="w-10 h-10 bg-primary bg-opacity-10 rounded-full flex items-center justify-center ml-3">
                                                    <span class="text-primary font-semibold">
                                                        <?php echo strtoupper(substr($review['name'], 0, 1)); ?>
                                                    </span>
                                                </div>
                                                <div>
                                                    <div class="font-medium text-gray-900"><?php echo htmlspecialchars($review['name']); ?></div>
                                                    <div class="text-sm text-gray-500"><?php echo formatDate($review['created_at']); ?></div>
                                                </div>
                                            </div>
                                            
                                            <div class="flex items-center">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <svg class="w-4 h-4 <?php echo $i <= $review['rating'] ? 'text-yellow-400' : 'text-gray-300'; ?>" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                                    </svg>
                                                <?php endfor; ?>
                                            </div>
                                        </div>
                                        
                                        <p class="text-gray-700"><?php echo nl2br(htmlspecialchars($review['comment'])); ?></p>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-8">
                                <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                </svg>
                                <p class="text-gray-500">لا توجد تقييمات لهذا المنتج بعد. كن أول من يقيم!</p>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Add Review Form -->
                    <div>
                        <div class="bg-white rounded-lg p-6 shadow-sm">
                            <!-- Review Notifications -->
                            <?php if (isset($_SESSION['review_success'])): ?>
                                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4" role="alert">
                                    <div class="flex">
                                        <div class="py-1">
                                            <svg class="fill-current h-6 w-6 text-green-500 mr-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                                                <path d="M2.93 17.07A10 10 0 1 1 17.07 2.93 10 10 0 0 1 2.93 17.07zm12.73-1.41A8 8 0 1 0 4.34 4.34a8 8 0 0 0 11.32 11.32zM9 11V9h2v6H9v-4zm0-6h2v2H9V5z"/>
                                            </svg>
                                        </div>
                                        <div>
                                            <?php echo $_SESSION['review_success']; unset($_SESSION['review_success']); ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (isset($_SESSION['review_error'])): ?>
                                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4" role="alert">
                                    <div class="flex">
                                        <div class="py-1">
                                            <svg class="fill-current h-6 w-6 text-red-500 mr-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                                                <path d="M2.93 17.07A10 10 0 1 1 17.07 2.93 10 10 0 0 1 2.93 17.07zm1.41-1.41A8 8 0 1 0 15.66 4.34 8 8 0 0 0 4.34 15.66zm9.9-8.49L11.41 10l2.83 2.83-1.41 1.41L10 11.41l-2.83 2.83-1.41-1.41L8.59 10 5.76 7.17l1.41-1.41L10 8.59l2.83-2.83 1.41 1.41z"/>
                                            </svg>
                                        </div>
                                        <div>
                                            <?php echo $_SESSION['review_error']; unset($_SESSION['review_error']); ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (isset($_SESSION['review_errors'])): ?>
                                <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4" role="alert">
                                    <div class="flex">
                                        <div class="py-1">
                                            <svg class="fill-current h-6 w-6 text-yellow-500 mr-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                                                <path d="M2.93 17.07A10 10 0 1 1 17.07 2.93 10 10 0 0 1 2.93 17.07zm1.41-1.41A8 8 0 1 0 15.66 4.34 8 8 0 0 0 4.34 15.66zm9.9-8.49L11.41 10l2.83 2.83-1.41 1.41L10 11.41l-2.83 2.83-1.41-1.41L8.59 10 5.76 7.17l1.41-1.41L10 8.59l2.83-2.83 1.41 1.41z"/>
                                            </svg>
                                        </div>
                                        <div>
                                            <strong>يرجى تصحيح الأخطاء التالية:</strong>
                                            <ul class="mt-2 list-disc list-inside">
                                                <?php foreach ($_SESSION['review_errors'] as $error): ?>
                                                    <li><?php echo $error; ?></li>
                                                <?php endforeach; ?>
                                            </ul>
                                            <?php unset($_SESSION['review_errors']); ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">أضف تقييمك</h3>
                            
                            <?php if (isLoggedIn()): ?>
                                <form method="POST" class="space-y-4" id="reviewForm">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">التقييم</label>
                                        <div class="flex items-center space-x-1 space-x-reverse">
                                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                                <button type="button" class="rating-star w-8 h-8 text-gray-300 hover:text-yellow-400 transition-colors" data-rating="<?php echo $i; ?>">
                                                    <svg class="w-full h-full" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                                    </svg>
                                                </button>
                                            <?php endfor; ?>
                                        </div>
                                        <input type="hidden" name="rating" id="rating-input" required>
                                    </div>
                                    
                                    <div>
                                        <label for="title" class="block text-sm font-medium text-gray-700 mb-2">عنوان التقييم (اختياري)</label>
                                        <input type="text" id="title" name="title" 
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                               placeholder="عنوان مختصر لتقييمك..."
                                               value="<?php echo isset($_POST['title']) ? htmlspecialchars($_POST['title']) : ''; ?>">
                                    </div>
                                    
                                    <div>
                                        <label for="comment" class="block text-sm font-medium text-gray-700 mb-2">التعليق</label>
                                        <textarea id="comment" name="comment" rows="4" required 
                                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                                  placeholder="شاركنا رأيك في هذا المنتج..."><?php echo isset($_POST['comment']) ? htmlspecialchars($_POST['comment']) : ''; ?></textarea>
                                    </div>
                                    
                                    <button type="submit" name="submit_review" id="submitReviewBtn"
                                            class="w-full bg-primary hover:bg-secondary text-white font-medium py-2 px-4 rounded-lg transition-colors duration-300">
                                        <span class="btn-text">إرسال التقييم</span>
                                        <span class="btn-loading hidden">
                                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                            جاري الإرسال...
                                        </span>
                                    </button>
                                </form>
                            <?php else: ?>
                                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 text-yellow-400 ml-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                        </svg>
                                        <p class="text-yellow-800">
                                            يجب عليك <a href="<?php echo SITE_URL; ?>/login" class="text-primary hover:underline font-medium">تسجيل الدخول</a> أولاً لإضافة تقييم.
                                        </p>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Products -->
<?php if (!empty($related_products['products'])): ?>
<section class="py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-2xl md:text-3xl font-bold text-gray-900 mb-8 text-center">منتجات ذات صلة</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <?php foreach ($related_products['products'] as $related_product): ?>
                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="relative">
                        <a href="<?php echo SITE_URL; ?>/products/<?php echo $related_product['id']; ?>">
                            <img src="<?php echo $related_product['image'] ?: '/assets/images/default-product.jpg'; ?>" 
                                 alt="<?php echo htmlspecialchars($related_product['name']); ?>" 
                                 class="w-full h-48 object-cover">
                        </a>
                    </div>
                    
                    <div class="p-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                            <a href="<?php echo SITE_URL; ?>/products/<?php echo $related_product['id']; ?>" class="hover:text-primary transition-colors">
                                <?php echo htmlspecialchars($related_product['name']); ?>
                            </a>
                        </h3>
                        
                        <div class="flex items-center justify-between">
                            <a href="<?php echo SITE_URL; ?>/products/<?php echo $related_product['id']; ?>" 
                               class="bg-primary hover:bg-secondary text-white px-4 py-2 rounded-lg transition-colors duration-300 text-sm">
                                عرض التفاصيل
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Image Modal -->
<div id="image-modal" class="fixed inset-0 bg-black bg-opacity-75 z-50 hidden flex items-center justify-center p-4">
    <div class="relative max-w-4xl max-h-full">
        <button onclick="closeImageModal()" class="absolute top-4 right-4 text-white hover:text-gray-300 z-10">
            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
        <img id="modal-image" src="" alt="" class="max-w-full max-h-full object-contain">
    </div>
</div>

<script>
// Tab functionality - Fixed version
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing tabs...');
    
    const tabButtons = document.querySelectorAll('.product-tab-button');
    const tabPanes = document.querySelectorAll('.product-tab-pane');
    
    console.log('Tab buttons found:', tabButtons.length);
    console.log('Tab panes found:', tabPanes.length);
    
    if (tabButtons.length === 0 || tabPanes.length === 0) {
        console.error('Tab elements not found!');
        return;
    }
    
    // Function to switch tabs
    function switchTab(targetTabId) {
        console.log('Switching to tab:', targetTabId);
        
        // Remove active class from all buttons
        tabButtons.forEach(btn => {
            btn.classList.remove('active', 'border-primary', 'text-primary');
            btn.classList.add('border-transparent', 'text-gray-500');
        });
        
        // Hide all tab panes
        tabPanes.forEach(pane => {
            pane.classList.remove('active');
        });
        
        // Find and activate the target button
        const targetButton = document.querySelector(`[data-tab="${targetTabId}"]`);
        if (targetButton) {
            targetButton.classList.add('active', 'border-primary', 'text-primary');
            targetButton.classList.remove('border-transparent', 'text-gray-500');
        }
        
        // Find and show the target pane
        const targetPane = document.getElementById(targetTabId + '-tab');
        if (targetPane) {
            targetPane.classList.add('active');
            console.log('Tab switched successfully to:', targetTabId);
        } else {
            console.error('Target pane not found:', targetTabId + '-tab');
        }
    }
    
    // Add click event to each tab button
    tabButtons.forEach((button) => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const tabId = this.dataset.tab;
            
            if (!tabId) {
                console.error('No data-tab attribute found on button');
                return;
            }
            
            switchTab(tabId);
        });
    });
    
    // Initialize first tab as active
    if (tabButtons.length > 0) {
        const firstTabId = tabButtons[0].dataset.tab;
        if (firstTabId) {
            switchTab(firstTabId);
            console.log('First tab initialized:', firstTabId);
        }
    }
    
    // Rating stars functionality
    const ratingStars = document.querySelectorAll('.rating-star');
    const ratingInput = document.getElementById('rating-input');
    
    if (ratingStars.length > 0 && ratingInput) {
        ratingStars.forEach(star => {
            star.addEventListener('click', function() {
                const rating = parseInt(this.dataset.rating);
                ratingInput.value = rating;
                
                ratingStars.forEach((s, index) => {
                    if (index < rating) {
                        s.classList.remove('text-gray-300');
                        s.classList.add('text-yellow-400');
                    } else {
                        s.classList.remove('text-yellow-400');
                        s.classList.add('text-gray-300');
                    }
                });
            });
            
            star.addEventListener('mouseenter', function() {
                const rating = parseInt(this.dataset.rating);
                
                ratingStars.forEach((s, index) => {
                    if (index < rating) {
                        s.classList.add('text-yellow-400');
                    } else {
                        s.classList.remove('text-yellow-400');
                    }
                });
            });
        });
    }
    
    // Review form submission with loading state
    const reviewForm = document.getElementById('reviewForm');
    const submitBtn = document.getElementById('submitReviewBtn');
    
    if (reviewForm && submitBtn) {
        reviewForm.addEventListener('submit', function(e) {
            // Show loading state
            const btnText = submitBtn.querySelector('.btn-text');
            const btnLoading = submitBtn.querySelector('.btn-loading');
            
            if (btnText && btnLoading) {
                btnText.classList.add('hidden');
                btnLoading.classList.remove('hidden');
                submitBtn.disabled = true;
            }
        });
    }
    
    // Auto-hide alerts after 5 seconds
    document.querySelectorAll('.alert, [role="alert"]').forEach(alert => {
        setTimeout(() => {
            alert.style.transition = 'opacity 0.5s ease-out';
            alert.style.opacity = '0';
            setTimeout(() => {
                alert.remove();
            }, 500);
        }, 5000);
    });
    
    console.log('Tab initialization complete');
});

// Image functionality
function changeMainImage(imageSrc, thumbnail) {
    const mainImage = document.getElementById('main-image');
    if (mainImage) {
        mainImage.src = imageSrc;
    }
    
    // Update thumbnail borders
    document.querySelectorAll('.aspect-w-1.aspect-h-1').forEach(thumb => {
        thumb.classList.remove('border-primary');
        thumb.classList.add('border-transparent');
    });
    
    if (thumbnail) {
        thumbnail.classList.remove('border-transparent');
        thumbnail.classList.add('border-primary');
    }
}

function openImageModal(imageSrc) {
    document.getElementById('modal-image').src = imageSrc;
    document.getElementById('image-modal').classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function closeImageModal() {
    document.getElementById('image-modal').classList.add('hidden');
    document.body.style.overflow = 'auto';
}

// Action functions
function requestQuote(productId) {
    window.location.href = `<?php echo SITE_URL; ?>/contact?product=${productId}`;
}

function contactUs() {
    window.location.href = '<?php echo SITE_URL; ?>/contact';
}

function toggleWishlist(productId) {
    console.log('Toggle wishlist for product:', productId);
    // Implement wishlist functionality
}

function shareProduct() {
    if (navigator.share) {
        navigator.share({
            title: '<?php echo htmlspecialchars($product['name']); ?>',
            text: '<?php echo htmlspecialchars(substr($product['description'], 0, 100)); ?>',
            url: window.location.href
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(window.location.href).then(() => {
            alert('تم نسخ رابط المنتج');
        });
    }
}

// Close modal on outside click
document.getElementById('image-modal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeImageModal();
    }
});
</script>

<?php include 'footer.php'; ?>