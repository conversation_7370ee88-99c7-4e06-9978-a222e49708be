<?php
/**
 * صفحة إدارة صلاحيات الدور
 * Role Permissions Management Page
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/middleware.php';

// التحقق من تسجيل الدخول وصلاحية الإدارة
requirePermission('manage-roles');

$roleId = $_GET['role_id'] ?? null;
if (!$roleId) {
    header('Location: manage_roles.php');
    exit;
}

// معالجة طلبات POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        switch ($action) {
            case 'update_permissions':
                $permissions = $_POST['permissions'] ?? [];
                
                // بدء المعاملة
                $database->beginTransaction();
                
                // حذف جميع صلاحيات الدور الحالية
                $stmt = $database->prepare("DELETE FROM role_permissions WHERE role_id = ?");
                $stmt->execute([$roleId]);
                
                // إضافة الصلاحيات الجديدة
                if (!empty($permissions)) {
                    $stmt = $database->prepare("INSERT INTO role_permissions (role_id, permission_id) VALUES (?, ?)");
                    foreach ($permissions as $permissionId) {
                        $stmt->execute([$roleId, $permissionId]);
                    }
                }
                
                // تأكيد المعاملة
                $database->commit();
                
                $_SESSION['success_message'] = 'تم تحديث صلاحيات الدور بنجاح';
                break;
                
            default:
                throw new Exception('عملية غير صحيحة');
        }
        
    } catch (Exception $e) {
        $database->rollBack();
        $_SESSION['error_message'] = $e->getMessage();
    }
    
    header('Location: role_permissions.php?role_id=' . $roleId);
    exit;
}

// جلب بيانات الدور
$stmt = $database->prepare("SELECT * FROM roles WHERE id = ?");
$stmt->execute([$roleId]);
$role = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$role) {
    header('Location: manage_roles.php');
    exit;
}

// جلب جميع الصلاحيات مجمعة حسب الفئة
$stmt = $database->prepare("
    SELECT p.*, 
           CASE WHEN rp.role_id IS NOT NULL THEN 1 ELSE 0 END as is_assigned
    FROM permissions p
    LEFT JOIN role_permissions rp ON p.id = rp.permission_id AND rp.role_id = ?
    ORDER BY p.category, p.name
");
$stmt->execute([$roleId]);
$allPermissions = $stmt->fetchAll(PDO::FETCH_ASSOC);

// تجميع الصلاحيات حسب الفئة
$permissionsByCategory = [];
foreach ($allPermissions as $permission) {
    $category = $permission['category'] ?? 'عام';
    $permissionsByCategory[$category][] = $permission;
}

// إحصائيات
$totalPermissions = count($allPermissions);
$assignedPermissions = count(array_filter($allPermissions, function($p) { return $p['is_assigned']; }));

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-left">
                    <a href="manage_roles.php" class="btn btn-light">
                        <i class="mdi mdi-arrow-left"></i> العودة للأدوار
                    </a>
                </div>
                <h4 class="page-title">صلاحيات الدور: <?php echo htmlspecialchars($role['name']); ?></h4>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row">
        <div class="col-md-4">
            <div class="card widget-flat">
                <div class="card-body">
                    <div class="float-end">
                        <i class="mdi mdi-shield-check widget-icon"></i>
                    </div>
                    <h5 class="text-muted fw-normal mt-0">إجمالي الصلاحيات</h5>
                    <h3 class="mt-3 mb-3"><?php echo $totalPermissions; ?></h3>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card widget-flat">
                <div class="card-body">
                    <div class="float-end">
                        <i class="mdi mdi-check-circle widget-icon bg-success-lighten text-success"></i>
                    </div>
                    <h5 class="text-muted fw-normal mt-0">الصلاحيات المعينة</h5>
                    <h3 class="mt-3 mb-3"><?php echo $assignedPermissions; ?></h3>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card widget-flat">
                <div class="card-body">
                    <div class="float-end">
                        <i class="mdi mdi-percent widget-icon bg-warning-lighten text-warning"></i>
                    </div>
                    <h5 class="text-muted fw-normal mt-0">نسبة التغطية</h5>
                    <h3 class="mt-3 mb-3"><?php echo $totalPermissions > 0 ? round(($assignedPermissions / $totalPermissions) * 100) : 0; ?>%</h3>
                </div>
            </div>
        </div>
    </div>

    <!-- رسائل النجاح والخطأ -->
    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- نموذج إدارة الصلاحيات -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="POST" id="permissionsForm">
                        <input type="hidden" name="action" value="update_permissions">
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <button type="button" class="btn btn-outline-success" onclick="selectAll()">تحديد الكل</button>
                                <button type="button" class="btn btn-outline-warning" onclick="deselectAll()">إلغاء تحديد الكل</button>
                            </div>
                            <div class="col-md-6 text-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="mdi mdi-content-save"></i> حفظ التغييرات
                                </button>
                            </div>
                        </div>

                        <?php foreach ($permissionsByCategory as $category => $permissions): ?>
                        <div class="card mb-3">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <input type="checkbox" class="form-check-input me-2" 
                                           onchange="toggleCategory('<?php echo $category; ?>')" 
                                           id="category_<?php echo md5($category); ?>">
                                    <?php echo htmlspecialchars($category); ?>
                                    <span class="badge bg-secondary ms-2"><?php echo count($permissions); ?></span>
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php foreach ($permissions as $permission): ?>
                                    <div class="col-md-6 col-lg-4 mb-2">
                                        <div class="form-check">
                                            <input type="checkbox" 
                                                   class="form-check-input category-<?php echo md5($category); ?>" 
                                                   name="permissions[]" 
                                                   value="<?php echo $permission['id']; ?>"
                                                   id="permission_<?php echo $permission['id']; ?>"
                                                   <?php echo $permission['is_assigned'] ? 'checked' : ''; ?>
                                                   onchange="updateCategoryCheckbox('<?php echo $category; ?>')">
                                            <label class="form-check-label" for="permission_<?php echo $permission['id']; ?>">
                                                <strong><?php echo htmlspecialchars($permission['name']); ?></strong>
                                                <?php if ($permission['description']): ?>
                                                <br><small class="text-muted"><?php echo htmlspecialchars($permission['description']); ?></small>
                                                <?php endif; ?>
                                            </label>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                        
                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="mdi mdi-content-save"></i> حفظ جميع التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function selectAll() {
    const checkboxes = document.querySelectorAll('input[name="permissions[]"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updateAllCategoryCheckboxes();
}

function deselectAll() {
    const checkboxes = document.querySelectorAll('input[name="permissions[]"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updateAllCategoryCheckboxes();
}

function toggleCategory(category) {
    const categoryHash = btoa(category).replace(/[^a-zA-Z0-9]/g, '');
    const categoryCheckbox = document.getElementById(`category_${categoryHash}`);
    const permissionCheckboxes = document.querySelectorAll(`.category-${categoryHash}`);
    
    permissionCheckboxes.forEach(checkbox => {
        checkbox.checked = categoryCheckbox.checked;
    });
}

function updateCategoryCheckbox(category) {
    const categoryHash = btoa(category).replace(/[^a-zA-Z0-9]/g, '');
    const categoryCheckbox = document.getElementById(`category_${categoryHash}`);
    const permissionCheckboxes = document.querySelectorAll(`.category-${categoryHash}`);
    
    const checkedCount = Array.from(permissionCheckboxes).filter(cb => cb.checked).length;
    const totalCount = permissionCheckboxes.length;
    
    if (checkedCount === 0) {
        categoryCheckbox.checked = false;
        categoryCheckbox.indeterminate = false;
    } else if (checkedCount === totalCount) {
        categoryCheckbox.checked = true;
        categoryCheckbox.indeterminate = false;
    } else {
        categoryCheckbox.checked = false;
        categoryCheckbox.indeterminate = true;
    }
}

function updateAllCategoryCheckboxes() {
    <?php foreach ($permissionsByCategory as $category => $permissions): ?>
    updateCategoryCheckbox('<?php echo $category; ?>');
    <?php endforeach; ?>
}

// تحديث حالة checkboxes الفئات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateAllCategoryCheckboxes();
});

// تأكيد قبل الحفظ
document.getElementById('permissionsForm').addEventListener('submit', function(e) {
    const checkedPermissions = document.querySelectorAll('input[name="permissions[]"]:checked').length;
    if (checkedPermissions === 0) {
        if (!confirm('لم تقم بتحديد أي صلاحيات. هل تريد المتابعة؟')) {
            e.preventDefault();
        }
    }
});
</script>

<?php include '../includes/footer.php'; ?>