<?php
$pageTitle = 'اتصل بنا';
$page = 'contact';

// معالجة إرسال النموذج
if ($_POST && isset($_POST['submit_contact'])) {
    $contact_data = [
        'name' => sanitizeInput($_POST['name']),
        'email' => sanitizeInput($_POST['email']),
        'phone' => sanitizeInput($_POST['phone']),
        'subject' => sanitizeInput($_POST['subject']),
        'message' => sanitizeInput($_POST['message']),
        'product_id' => isset($_POST['product_id']) ? intval($_POST['product_id']) : null
    ];
    
    if (saveContactMessage($contact_data)) {
        $_SESSION['success'] = 'تم إرسال رسالتك بنجاح. سنتواصل معك قريباً';
        redirect(SITE_URL . '/contact');
    } else {
        $_SESSION['error'] = 'حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى';
    }
}

// جلب معلومات الاتصال من الإعدادات
$contact_info = getContactInfo();
$product_id = $_GET['product'] ?? null;
$type = $_GET['type'] ?? null;
$product = null;

if ($product_id) {
    $product = getProduct($product_id);
}

include 'header.php';
?>

<!-- Hero Section -->
<section class="bg-green-600 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">
            اتصل بنا
        </h1>
        <p class="text-xl text-white opacity-90 max-w-2xl mx-auto">
            نحن هنا لمساعدتك. تواصل معنا وسنكون سعداء للإجابة على استفساراتك
        </p>
    </div>
</section>

<!-- Contact Section -->
<section class="py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            
            <!-- Contact Form -->
            <div class="bg-white rounded-lg shadow-lg p-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">أرسل لنا رسالة</h2>
                
                <?php if ($product): ?>
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-blue-500 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-blue-700 font-medium">استفسار حول: <?php echo htmlspecialchars($product['name']); ?></span>
                        </div>
                    </div>
                <?php endif; ?>
                
                <form method="POST" class="space-y-6">
                    <?php if ($product): ?>
                        <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                    <?php endif; ?>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل *</label>
                            <input type="text" id="name" name="name" required 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300"
                                   placeholder="أدخل اسمك الكامل">
                        </div>
                        
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني *</label>
                            <input type="email" id="email" name="email" required 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300"
                                   placeholder="أدخل بريدك الإلكتروني">
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                            <input type="tel" id="phone" name="phone" 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300"
                                   placeholder="أدخل رقم هاتفك">
                        </div>
                        
                        <div>
                            <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">الموضوع *</label>
                            <select id="subject" name="subject" required 
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300">
                                <option value="">اختر الموضوع</option>
                                <option value="استفسار عام" <?php echo ($product || $type) ? '' : 'selected'; ?>>استفسار عام</option>
                                <option value="طلب عرض سعر" <?php echo $product ? 'selected' : ''; ?>>طلب عرض سعر</option>
                                <option value="الدعم الفني">الدعم الفني</option>
                                <option value="شراكة" <?php echo $type === 'partnership' ? 'selected' : ''; ?>>شراكة</option>
                                <option value="شكوى">شكوى</option>
                                <option value="اقتراح">اقتراح</option>
                            </select>
                        </div>
                    </div>
                    
                    <div>
                        <label for="message" class="block text-sm font-medium text-gray-700 mb-2">الرسالة *</label>
                        <textarea id="message" name="message" rows="6" required 
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300"
                                  placeholder="اكتب رسالتك هنا..."></textarea>
                    </div>
                    
                    <button type="submit" name="submit_contact" 
                            class="w-full bg-primary hover:bg-secondary text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105">
                        إرسال الرسالة
                    </button>
                </form>
            </div>
            
            <!-- Contact Information -->
            <div class="space-y-8">
                
                <!-- Contact Details -->
                <div class="bg-gray-50 rounded-lg p-8">
                    <h3 class="text-xl font-bold text-gray-900 mb-6">معلومات الاتصال</h3>
                    
                    <div class="space-y-6">
                        <?php if (!empty($contact_info['address'])): ?>
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center hover:bg-secondary transition-colors duration-300">
                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="mr-4">
                                    <h4 class="text-lg font-medium text-gray-900">العنوان</h4>
                                    <p class="text-gray-600 mt-1"><?php echo nl2br(htmlspecialchars($contact_info['address'])); ?></p>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($contact_info['phone'])): ?>
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center hover:bg-secondary transition-colors duration-300">
                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="mr-4">
                                    <h4 class="text-lg font-medium text-gray-900">الهاتف</h4>
                                    <p class="text-gray-600 mt-1">
                                        <a href="tel:<?php echo $contact_info['phone']; ?>" class="hover:text-primary transition-colors">
                                            <?php echo htmlspecialchars($contact_info['phone']); ?>
                                        </a>
                                    </p>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($contact_info['email'])): ?>
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center hover:bg-secondary transition-colors duration-300">
                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="mr-4">
                                    <h4 class="text-lg font-medium text-gray-900">البريد الإلكتروني</h4>
                                    <p class="text-gray-600 mt-1">
                                        <a href="mailto:<?php echo $contact_info['email']; ?>" class="hover:text-primary transition-colors">
                                            <?php echo htmlspecialchars($contact_info['email']); ?>
                                        </a>
                                    </p>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($contact_info['working_hours'])): ?>
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center hover:bg-secondary transition-colors duration-300">
                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="mr-4">
                                    <h4 class="text-lg font-medium text-gray-900">ساعات العمل</h4>
                                    <p class="text-gray-600 mt-1"><?php echo nl2br(htmlspecialchars($contact_info['working_hours'])); ?></p>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Social Media -->
                <div class="bg-white rounded-lg shadow-lg p-8">
                    <h3 class="text-xl font-bold text-gray-900 mb-6">تابعنا على</h3>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <?php 
                        // جلب بيانات وسائل التواصل الاجتماعي من قاعدة البيانات
                        $social_media_data = isset($contact_info['social_media_data']) ? $contact_info['social_media_data'] : [];
                        
                        $social_links = [
                            'facebook' => ['name' => 'فيسبوك', 'icon' => 'M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z'],
                            'twitter' => ['name' => 'تويتر', 'icon' => 'M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z'],
                            'instagram' => ['name' => 'إنستغرام', 'icon' => 'M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z'],
                            'linkedin' => ['name' => 'لينكد إن', 'icon' => 'M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z'],
                            'youtube' => ['name' => 'يوتيوب', 'icon' => 'M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z']
                        ];
                        
                        foreach ($social_links as $platform => $data):
                            // استخدام البيانات من جدول contact_info
                            $url = isset($social_media_data[$platform]) ? $social_media_data[$platform] : '';
                            if (!empty($url)):
                        ?>
                            <a href="<?php echo htmlspecialchars($url); ?>" target="_blank" 
                               class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-primary hover:text-white transition-all duration-300 group">
                                <svg class="w-5 h-5 ml-2" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="<?php echo $data['icon']; ?>"/>
                                </svg>
                                <span class="font-medium"><?php echo $data['name']; ?></span>
                            </a>
                        <?php 
                            endif;
                        endforeach;
                        ?>
                    </div>
                </div>
                
                <!-- Quick Contact -->
                <div class="bg-primary rounded-lg p-8 text-white">
                    <h3 class="text-xl font-bold mb-4">تحتاج مساعدة فورية؟</h3>
                    <p class="mb-6 opacity-90">تواصل معنا مباشرة عبر الواتساب للحصول على رد سريع</p>
                    
                    <?php if (!empty($contact_info['whatsapp'])): ?>
                        <a href="https://wa.me/<?php echo preg_replace('/[^0-9]/', '', $contact_info['whatsapp']); ?>" 
                           target="_blank" 
                           class="inline-flex items-center bg-white text-primary px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-300">
                            <svg class="w-5 h-5 ml-2" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.465 3.488"/>
                            </svg>
                            تواصل عبر الواتساب
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Map Section -->
<?php if (!empty($contact_info['map_embed'])): ?>
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">موقعنا</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">
                يمكنك زيارتنا في مقرنا الرئيسي أو التواصل معنا عبر الوسائل المتاحة
            </p>
        </div>
        
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="aspect-w-16 aspect-h-9">
                <?php echo $contact_info['map_embed']; ?>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- FAQ Section -->
<section class="py-16">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">الأسئلة الشائعة</h2>
            <p class="text-gray-600">
                إجابات على أكثر الأسئلة شيوعاً
            </p>
        </div>
        
        <div class="space-y-4">
            <?php 
            $faqs = getFAQs(5); // جلب 5 أسئلة شائعة
            foreach ($faqs as $index => $faq):
            ?>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <button class="faq-toggle w-full px-6 py-4 text-right focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50" 
                            data-target="faq-<?php echo $index; ?>">
                        <div class="flex items-center justify-between">
                            <span class="font-medium text-gray-900"><?php echo htmlspecialchars($faq['question']); ?></span>
                            <svg class="faq-icon w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                    </button>
                    
                    <div id="faq-<?php echo $index; ?>" class="faq-content hidden px-6 pb-4">
                        <p class="text-gray-600"><?php echo nl2br(htmlspecialchars($faq['answer'])); ?></p>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <div class="text-center mt-8">
            <a href="<?php echo SITE_URL; ?>/faq" class="inline-flex items-center text-primary hover:text-secondary font-medium">
                عرض جميع الأسئلة الشائعة
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </a>
        </div>
    </div>
</section>

<script>
// FAQ Toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    const faqToggles = document.querySelectorAll('.faq-toggle');
    
    faqToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            const targetId = this.dataset.target;
            const content = document.getElementById(targetId);
            const icon = this.querySelector('.faq-icon');
            
            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                icon.style.transform = 'rotate(180deg)';
            } else {
                content.classList.add('hidden');
                icon.style.transform = 'rotate(0deg)';
            }
        });
    });
    
    // Form validation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const name = document.getElementById('name').value.trim();
            const email = document.getElementById('email').value.trim();
            const subject = document.getElementById('subject').value;
            const message = document.getElementById('message').value.trim();
            
            if (!name || !email || !subject || !message) {
                e.preventDefault();
                alert('يرجى ملء جميع الحقول المطلوبة');
                return false;
            }
            
            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                e.preventDefault();
                alert('يرجى إدخال بريد إلكتروني صحيح');
                return false;
            }
        });
    }
});
</script>

<?php include 'footer.php'; ?>