<?php
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// تجنب تكرار session_start
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// محاكاة تسجيل دخول المستخدم
if (!isLoggedIn()) {
    $_SESSION['user_id'] = 3;
    $_SESSION['user_name'] = 'المدير العام';
    $_SESSION['user_email'] = '<EMAIL>';
    $_SESSION['user_role'] = 'super-admin';
}

echo "<h1>تشخيص API المراجعات</h1>";

// 1. عرض معلومات المستخدم الحالي
echo "<h2>1. معلومات المستخدم الحالي:</h2>";
$isLoggedIn = isLoggedIn();
$currentUser = $isLoggedIn ? getCurrentUser() : null;

echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<strong>حالة تسجيل الدخول:</strong> " . ($isLoggedIn ? 'مسجل دخول' : 'غير مسجل') . "<br>";
if ($currentUser) {
    echo "<strong>معرف المستخدم:</strong> " . $currentUser['id'] . "<br>";
    echo "<strong>اسم المستخدم:</strong> " . $currentUser['name'] . "<br>";
    echo "<strong>البريد الإلكتروني:</strong> " . $currentUser['email'] . "<br>";
    echo "<strong>الدور:</strong> " . $currentUser['role'] . "<br>";
}
echo "</div>";

// 2. اختبار اتصال قاعدة البيانات
echo "<h2>2. اختبار اتصال قاعدة البيانات:</h2>";
try {
    global $database;
    $pdo = $database->getConnection();
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
    echo "✅ اتصال قاعدة البيانات ناجح";
    echo "</div>";
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
    echo "❌ خطأ في اتصال قاعدة البيانات: " . $e->getMessage();
    echo "</div>";
}

// 3. فحص جدول reviews
echo "<h2>3. فحص جدول المراجعات:</h2>";
try {
    $columns = $database->fetchAll("DESCRIBE reviews");
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
    echo "✅ جدول reviews موجود<br>";
    echo "<strong>الأعمدة:</strong><br>";
    foreach ($columns as $column) {
        echo "- " . $column['Field'] . " (" . $column['Type'] . ")<br>";
    }
    echo "</div>";
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
    echo "❌ خطأ في فحص جدول reviews: " . $e->getMessage();
    echo "</div>";
}

// 4. فحص المنتجات المتاحة
echo "<h2>4. فحص المنتجات المتاحة:</h2>";
try {
    $products = $database->fetchAll("SELECT id, name FROM products WHERE is_active = 1 LIMIT 5");
    if (!empty($products)) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
        echo "✅ المنتجات المتاحة:<br>";
        foreach ($products as $product) {
            echo "- معرف: " . $product['id'] . " - الاسم: " . $product['name'] . "<br>";
        }
        echo "</div>";
        $test_product_id = $products[0]['id']; // استخدام أول منتج متاح
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
        echo "❌ لا توجد منتجات متاحة في قاعدة البيانات";
        echo "</div>";
        $test_product_id = 1; // استخدام 1 كقيمة افتراضية
    }
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
    echo "❌ خطأ في فحص المنتجات: " . $e->getMessage();
    echo "</div>";
    $test_product_id = 1;
}

// 5. اختبار دالة saveUserReview مباشرة
echo "<h2>5. اختبار دالة saveUserReview:</h2>";

// حذف أي مراجعة سابقة للاختبار
try {
    $stmt = $database->query("DELETE FROM reviews WHERE product_id = ? AND user_id = ?", [$test_product_id, 3]);
    $deleted = $stmt->rowCount();
    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; color: #856404;'>";
    echo "تم حذف " . $deleted . " مراجعة سابقة للاختبار";
    echo "</div>";
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
    echo "خطأ في حذف المراجعات السابقة: " . $e->getMessage();
    echo "</div>";
}

// اختبار حفظ مراجعة جديدة
try {
    $result = saveUserReview(
        $test_product_id, // product_id
        3, // user_id
        'المدير العام', // customer_name
        '<EMAIL>', // customer_email
        5, // rating
        'هذه مراجعة تجريبية لاختبار النظام', // review_text
        'مراجعة تجريبية' // review_title
    );
    
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
    echo "✅ نتيجة saveUserReview: " . print_r($result, true);
    echo "</div>";
    
    // التحقق من حفظ البيانات
    if ($result && is_numeric($result)) {
        $savedReview = $database->fetch("SELECT * FROM reviews WHERE id = ?", [$result]);
        if ($savedReview) {
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
            echo "✅ تم حفظ المراجعة بنجاح:<br>";
            echo "<strong>معرف المراجعة:</strong> " . $savedReview['id'] . "<br>";
            echo "<strong>معرف المنتج:</strong> " . $savedReview['product_id'] . "<br>";
            echo "<strong>معرف المستخدم:</strong> " . $savedReview['user_id'] . "<br>";
            echo "<strong>الاسم:</strong> " . $savedReview['name'] . "<br>";
            echo "<strong>التقييم:</strong> " . $savedReview['rating'] . "<br>";
            echo "<strong>المراجعة:</strong> " . $savedReview['comment'] . "<br>";
            echo "<strong>معتمدة:</strong> " . ($savedReview['is_approved'] ? 'نعم' : 'لا') . "<br>";
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
    echo "❌ خطأ في saveUserReview: " . $e->getMessage();
    echo "<br><strong>Stack trace:</strong><br>" . $e->getTraceAsString();
    echo "</div>";
}

// 6. اختبار API مباشرة
echo "<h2>6. اختبار API مباشرة:</h2>";

// محاكاة POST request
$_POST = [
    'product_id' => $test_product_id,
    'rating' => 4,
    'review_text' => 'مراجعة تجريبية من API',
    'review_title' => 'اختبار API'
];

echo "<div style='background: #e2e3e5; padding: 10px; border-radius: 5px;'>";
echo "<strong>بيانات POST المرسلة:</strong><br>";
echo "<pre>" . print_r($_POST, true) . "</pre>";
echo "<div style='background: #d1ecf1; padding: 8px; border-radius: 3px; margin-top: 10px; color: #0c5460;'>";
echo "<strong>ملاحظة:</strong> user_id لا يتم إرساله في POST لأسباب أمنية - يتم الحصول عليه من الجلسة";
echo "</div>";
echo "</div>";

// تشغيل كود API
ob_start();
$_SERVER['REQUEST_METHOD'] = 'POST';

try {
    // نسخ كود API هنا للاختبار
    $isLoggedIn = isLoggedIn();
    $currentUser = $isLoggedIn ? getCurrentUser() : null;
    
    echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin-bottom: 10px; color: #0c5460;'>";
    echo "<strong>معالجة user_id في API:</strong><br>";
    echo "• المستخدم مسجل دخول: " . ($isLoggedIn ? 'نعم' : 'لا') . "<br>";
    if ($isLoggedIn && $currentUser) {
        echo "• user_id من الجلسة: " . $currentUser['id'] . "<br>";
        echo "• اسم المستخدم: " . $currentUser['name'] . "<br>";
        echo "• بريد المستخدم: " . $currentUser['email'] . "<br>";
    }
    echo "</div>";
    
    // Validate required fields
    $required_fields = ['product_id', 'rating', 'review_text'];
    
    if (!$isLoggedIn) {
        $required_fields[] = 'customer_name';
        $required_fields[] = 'customer_email';
    }
    
    $missing_fields = [];
    
    foreach ($required_fields as $field) {
        if (empty($_POST[$field])) {
            $missing_fields[] = $field;
        }
    }
    
    if (!empty($missing_fields)) {
        throw new Exception('الحقول التالية مطلوبة: ' . implode(', ', $missing_fields));
    }
    
    // Sanitize input
    $product_id = (int)$_POST['product_id'];
    $rating = (int)$_POST['rating'];
    $review_text = sanitizeInput($_POST['review_text']);
    $review_title = sanitizeInput($_POST['review_title'] ?? '');
    
    // تحديد بيانات المستخدم
    if ($isLoggedIn) {
        $user_id = $currentUser['id'];
        $customer_name = $currentUser['name'];
        $customer_email = $currentUser['email'];
    } else {
        $user_id = null;
        $customer_name = sanitizeInput($_POST['customer_name']);
        $customer_email = sanitizeInput($_POST['customer_email'] ?? '');
    }
    
    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin-bottom: 10px; color: #856404;'>";
    echo "<strong>البيانات الكاملة للحفظ:</strong><br>";
    echo "• product_id: " . $product_id . "<br>";
    echo "• user_id: " . ($user_id ?: 'NULL (زائر)') . "<br>";
    echo "• customer_name: " . $customer_name . "<br>";
    echo "• customer_email: " . $customer_email . "<br>";
    echo "• rating: " . $rating . "<br>";
    echo "• review_text: " . $review_text . "<br>";
    echo "• review_title: " . $review_title . "<br>";
    echo "</div>";
    
    // Validate rating
    if ($rating < 1 || $rating > 5) {
        throw new Exception('التقييم يجب أن يكون بين 1 و 5 نجوم');
    }
    
    // Check if product exists
    $product = getProduct($product_id);
    if (!$product) {
        throw new Exception('المنتج غير موجود');
    }
    
    // حذف أي مراجعة سابقة للاختبار
    if ($isLoggedIn) {
        $stmt = $database->query("DELETE FROM reviews WHERE product_id = ? AND user_id = ?", [$product_id, $user_id]);
    }
    
    // Save review
    if ($isLoggedIn) {
        $review_id = saveUserReview($product_id, $user_id, $customer_name, $customer_email, $rating, $review_text, $review_title);
    } else {
        $review_id = saveGuestReview($product_id, $customer_name, $customer_email, $rating, $review_text, $review_title);
    }
    
    if ($review_id) {
        updateProductRating($product_id);
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
        echo "✅ API نجح في حفظ المراجعة - معرف المراجعة: " . $review_id;
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
        echo "❌ API فشل في حفظ المراجعة";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
    echo "❌ خطأ في API: " . $e->getMessage();
    echo "</div>";
}

$api_output = ob_get_clean();
echo $api_output;

// 7. عرض آخر 5 مراجعات
echo "<h2>7. آخر 5 مراجعات في قاعدة البيانات:</h2>";
try {
    $reviews = $database->fetchAll("SELECT * FROM reviews ORDER BY created_at DESC LIMIT 5");
    if (!empty($reviews)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Product ID</th><th>User ID</th><th>Name</th><th>Rating</th><th>Comment</th><th>Approved</th><th>Created</th></tr>";
        foreach ($reviews as $review) {
            echo "<tr>";
            echo "<td>" . $review['id'] . "</td>";
            echo "<td>" . $review['product_id'] . "</td>";
            echo "<td>" . ($review['user_id'] ?: 'NULL') . "</td>";
            echo "<td>" . $review['name'] . "</td>";
            echo "<td>" . $review['rating'] . "</td>";
            echo "<td>" . substr($review['comment'], 0, 50) . "...</td>";
            echo "<td>" . ($review['is_approved'] ? 'نعم' : 'لا') . "</td>";
            echo "<td>" . $review['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; color: #856404;'>";
        echo "لا توجد مراجعات في قاعدة البيانات";
        echo "</div>";
    }
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
    echo "❌ خطأ في جلب المراجعات: " . $e->getMessage();
    echo "</div>";
}

// 8. نموذج اختبار يدوي
echo "<h2>8. نموذج اختبار يدوي:</h2>";
?>

<form method="POST" style="background: #f8f9fa; padding: 20px; border-radius: 5px;">
    <input type="hidden" name="manual_test" value="1">
    
    <div style="margin-bottom: 15px;">
        <label>معرف المنتج:</label><br>
        <input type="number" name="product_id" value="1" required style="width: 100%; padding: 8px;">
    </div>
    
    <div style="margin-bottom: 15px;">
        <label>التقييم (1-5):</label><br>
        <select name="rating" required style="width: 100%; padding: 8px;">
            <option value="1">1 نجمة</option>
            <option value="2">2 نجمة</option>
            <option value="3">3 نجوم</option>
            <option value="4">4 نجوم</option>
            <option value="5" selected>5 نجوم</option>
        </select>
    </div>
    
    <div style="margin-bottom: 15px;">
        <label>عنوان المراجعة:</label><br>
        <input type="text" name="review_title" value="مراجعة تجريبية يدوية" style="width: 100%; padding: 8px;">
    </div>
    
    <div style="margin-bottom: 15px;">
        <label>نص المراجعة:</label><br>
        <textarea name="review_text" required style="width: 100%; padding: 8px; height: 100px;">هذه مراجعة تجريبية يدوية لاختبار النظام</textarea>
    </div>
    
    <button type="submit" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
        إرسال المراجعة
    </button>
</form>

<?php
// معالجة النموذج اليدوي
if (isset($_POST['manual_test'])) {
    echo "<h3>نتيجة الاختبار اليدوي:</h3>";
    
    try {
        // حذف أي مراجعة سابقة
        $database->delete('reviews', ['product_id' => $_POST['product_id'], 'user_id' => 3]);
        
        $result = saveUserReview(
            (int)$_POST['product_id'],
            3,
            'المدير العام',
            '<EMAIL>',
            (int)$_POST['rating'],
            $_POST['review_text'],
            $_POST['review_title']
        );
        
        if ($result && is_numeric($result)) {
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
            echo "✅ تم حفظ المراجعة بنجاح - معرف المراجعة: " . $result;
            echo "</div>";
            
            // عرض المراجعة المحفوظة
            $savedReview = $database->fetch("SELECT * FROM reviews WHERE id = ?", [$result]);
            if ($savedReview) {
                echo "<div style='background: #e2e3e5; padding: 10px; border-radius: 5px; margin-top: 10px;'>";
                echo "<strong>تفاصيل المراجعة المحفوظة:</strong><br>";
                echo "معرف المراجعة: " . $savedReview['id'] . "<br>";
                echo "معرف المنتج: " . $savedReview['product_id'] . "<br>";
                echo "معرف المستخدم: " . $savedReview['user_id'] . "<br>";
                echo "الاسم: " . $savedReview['name'] . "<br>";
                echo "التقييم: " . $savedReview['rating'] . "<br>";
                echo "العنوان: " . $savedReview['title'] . "<br>";
                echo "المراجعة: " . $savedReview['comment'] . "<br>";
                echo "معتمدة: " . ($savedReview['is_approved'] ? 'نعم' : 'لا') . "<br>";
                echo "تاريخ الإنشاء: " . $savedReview['created_at'] . "<br>";
                echo "</div>";
            }
        } else {
            echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
            echo "❌ فشل في حفظ المراجعة: " . $result;
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
        echo "❌ خطأ في الاختبار اليدوي: " . $e->getMessage();
        echo "</div>";
    }
}
?>