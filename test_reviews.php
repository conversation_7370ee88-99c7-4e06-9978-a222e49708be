<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// جلب معرف المنتج من الرابط
$product_id = isset($_GET['product_id']) ? (int)$_GET['product_id'] : 1;

// جلب بيانات المنتج
$product = getProduct($product_id);
if (!$product) {
    die('المنتج غير موجود');
}

// جلب جميع التقييمات (معتمدة وغير معتمدة)
$all_reviews = getAllProductReviews($product_id);

// جلب التقييمات المعتمدة فقط
$approved_reviews = getProductReviews($product_id);

// جلب إحصائيات التقييمات
$rating_stats = getProductRatingStats($product_id);

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التقييمات - <?php echo htmlspecialchars($product['name']); ?></title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .product-info {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #28a745;
        }
        .reviews-section {
            margin-top: 30px;
        }
        .review-item {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
            background: #fff;
        }
        .review-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        .reviewer-info {
            font-weight: bold;
        }
        .review-date {
            color: #666;
            font-size: 0.9em;
        }
        .rating-stars {
            color: #ffc107;
            font-size: 18px;
        }
        .review-status {
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .status-approved { background: #d4edda; color: #155724; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-rejected { background: #f8d7da; color: #721c24; }
        .review-content {
            margin-top: 10px;
        }
        .review-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .review-comment {
            color: #555;
            line-height: 1.5;
        }
        .section-title {
            background: #007bff;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            margin: 20px 0 15px 0;
        }
        .no-reviews {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار التقييمات من جدول Reviews</h1>
        
        <div class="product-info">
            <h2>معلومات المنتج</h2>
            <p><strong>اسم المنتج:</strong> <?php echo htmlspecialchars($product['name']); ?></p>
            <p><strong>معرف المنتج:</strong> <?php echo $product_id; ?></p>
            <p><strong>التقييم المحفوظ في جدول المنتجات:</strong> <?php echo $product['rating'] ?? 'غير محدد'; ?></p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value"><?php echo $rating_stats['total_reviews']; ?></div>
                <div>إجمالي التقييمات المعتمدة</div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo number_format($rating_stats['average_rating'], 1); ?></div>
                <div>متوسط التقييم المحسوب</div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo count($all_reviews); ?></div>
                <div>إجمالي جميع التقييمات</div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo count($approved_reviews); ?></div>
                <div>التقييمات المعتمدة</div>
            </div>
        </div>

        <h2 class="section-title">جميع التقييمات (معتمدة وغير معتمدة)</h2>
        <?php if (empty($all_reviews)): ?>
            <div class="no-reviews">لا توجد تقييمات لهذا المنتج</div>
        <?php else: ?>
            <?php foreach ($all_reviews as $review): ?>
                <div class="review-item">
                    <div class="review-header">
                        <div class="reviewer-info">
                            <?php echo htmlspecialchars($review['name']); ?>
                            <?php if ($review['email']): ?>
                                <small>(<?php echo htmlspecialchars($review['email']); ?>)</small>
                            <?php endif; ?>
                        </div>
                        <div class="review-date">
                            <?php echo date('Y-m-d H:i', strtotime($review['created_at'])); ?>
                        </div>
                    </div>
                    
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                        <div class="rating-stars">
                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                <?php if ($i <= $review['rating']): ?>
                                    ★
                                <?php else: ?>
                                    ☆
                                <?php endif; ?>
                            <?php endfor; ?>
                            (<?php echo $review['rating']; ?>/5)
                        </div>
                        
                        <div class="review-status status-<?php echo $review['is_approved'] ? 'approved' : ($review['is_rejected'] ? 'rejected' : 'pending'); ?>">
                            <?php echo $review['status_text']; ?>
                        </div>
                    </div>
                    
                    <div class="review-content">
                        <?php if ($review['title']): ?>
                            <div class="review-title"><?php echo htmlspecialchars($review['title']); ?></div>
                        <?php endif; ?>
                        <?php if ($review['comment']): ?>
                            <div class="review-comment"><?php echo nl2br(htmlspecialchars($review['comment'])); ?></div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>

        <h2 class="section-title">التقييمات المعتمدة فقط</h2>
        <?php if (empty($approved_reviews)): ?>
            <div class="no-reviews">لا توجد تقييمات معتمدة لهذا المنتج</div>
        <?php else: ?>
            <?php foreach ($approved_reviews as $review): ?>
                <div class="review-item">
                    <div class="review-header">
                        <div class="reviewer-info">
                            <?php echo htmlspecialchars($review['name']); ?>
                            <?php if ($review['email']): ?>
                                <small>(<?php echo htmlspecialchars($review['email']); ?>)</small>
                            <?php endif; ?>
                        </div>
                        <div class="review-date">
                            <?php echo date('Y-m-d H:i', strtotime($review['created_at'])); ?>
                        </div>
                    </div>
                    
                    <div class="rating-stars">
                        <?php for ($i = 1; $i <= 5; $i++): ?>
                            <?php if ($i <= $review['rating']): ?>
                                ★
                            <?php else: ?>
                                ☆
                            <?php endif; ?>
                        <?php endfor; ?>
                        (<?php echo $review['rating']; ?>/5)
                    </div>
                    
                    <div class="review-content">
                        <?php if ($review['title']): ?>
                            <div class="review-title"><?php echo htmlspecialchars($review['title']); ?></div>
                        <?php endif; ?>
                        <?php if ($review['comment']): ?>
                            <div class="review-comment"><?php echo nl2br(htmlspecialchars($review['comment'])); ?></div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>

        <div style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
            <h3>تغيير المنتج للاختبار:</h3>
            <p>أضف <code>?product_id=X</code> إلى الرابط حيث X هو معرف المنتج</p>
            <p>مثال: <code>test_reviews.php?product_id=2</code></p>
        </div>
    </div>
</body>
</html>