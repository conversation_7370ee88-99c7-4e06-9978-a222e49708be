<?php
/**
 * ملف إعداد المستخدم الإداري
 * Admin User Setup Script
 */

require_once 'config/config.php';
require_once 'config/database.php';

$database = new Database();

// إنشاء جدول المستخدمين إذا لم يكن موجوداً
$database->query("
    CREATE TABLE IF NOT EXISTS users (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VA<PERSON>HA<PERSON>(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        role ENUM('user', 'admin', 'moderator') DEFAULT 'user',
        is_active BOOLEAN DEFAULT TRUE,
        email_verified BOOLEAN DEFAULT FALSE,
        remember_token VARCHAR(255) NULL,
        reset_token VARCHAR(255) NULL,
        reset_token_expires DATETIME NULL,
        last_login DATETIME NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )
");

// إنشاء جدول سجل الأنشطة إذا لم يكن موجوداً
$database->query("
    CREATE TABLE IF NOT EXISTS activity_logs (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NULL,
        action VARCHAR(255) NOT NULL,
        description TEXT,
        item_type VARCHAR(100) NULL,
        item_id INT NULL,
        metadata JSON NULL,
        ip_address VARCHAR(45) NULL,
        user_agent TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
    )
");

// التحقق من وجود مستخدم أدمن
$adminExists = $database->fetch("SELECT id FROM users WHERE role = 'admin' LIMIT 1");

if (!$adminExists) {
    // إنشاء مستخدم أدمن افتراضي
    $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
    
    $database->query("
        INSERT INTO users (name, email, password, role, is_active) 
        VALUES (:name, :email, :password, 'admin', 1)
    ", [
        'name' => 'مدير النظام',
        'email' => '<EMAIL>',
        'password' => $adminPassword
    ]);
    
    echo "تم إنشاء مستخدم الأدمن بنجاح!\n";
    echo "البريد الإلكتروني: <EMAIL>\n";
    echo "كلمة المرور: admin123\n";
    echo "يرجى تغيير كلمة المرور بعد تسجيل الدخول\n";
} else {
    echo "مستخدم الأدمن موجود بالفعل\n";
}

echo "تم إعداد قاعدة البيانات بنجاح!\n";
?>