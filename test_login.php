<?php
require_once 'config/config.php';

if ($_POST) {
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    
    if ($email && $password) {
        $result = login($email, $password);
        if ($result) {
            echo "<div style='color: green; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0;'>تم تسجيل الدخول بنجاح!</div>";
            echo "<script>setTimeout(function(){ window.location.href = 'product_detail.php?id=11&tab=reviews'; }, 2000);</script>";
        } else {
            echo "<div style='color: red; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0;'>خطأ في البريد الإلكتروني أو كلمة المرور</div>";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - اختبار</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen flex items-center justify-center">
        <div class="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
            <h2 class="text-2xl font-bold text-center mb-6">تسجيل الدخول</h2>
            
            <?php if (isLoggedIn()): ?>
                <div class="text-center">
                    <p class="text-green-600 mb-4">أنت مسجل دخول بالفعل!</p>
                    <p class="mb-4">مرحباً <?php echo htmlspecialchars($_SESSION['user_name'] ?? $_SESSION['user_email']); ?></p>
                    <a href="product_detail.php?id=11&tab=reviews" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                        الذهاب إلى صفحة المنتج
                    </a>
                    <br><br>
                    <a href="logout.php" class="text-red-500 hover:text-red-700">تسجيل الخروج</a>
                </div>
            <?php else: ?>
                <form method="POST" class="space-y-4">
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                        <input type="email" id="email" name="email" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="أدخل بريدك الإلكتروني">
                    </div>
                    
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور</label>
                        <input type="password" id="password" name="password" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="أدخل كلمة المرور">
                    </div>
                    
                    <button type="submit" 
                            class="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                        تسجيل الدخول
                    </button>
                </form>
                
                <div class="mt-6 text-center">
                    <p class="text-sm text-gray-600">للاختبار، يمكنك استخدام:</p>
                    <p class="text-xs text-gray-500">البريد: <EMAIL></p>
                    <p class="text-xs text-gray-500">كلمة المرور: admin123</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>