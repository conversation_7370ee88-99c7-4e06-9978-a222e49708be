<?php
/**
 * اختبار سريع لقاعدة البيانات بعد الإصلاح
 */

// تضمين الملفات المطلوبة
require_once 'config/config.php';

echo "<h1>اختبار قاعدة البيانات بعد الإصلاح</h1>";

try {
    // اختبار الاتصال
    echo "<h2>✅ اختبار الاتصال بقاعدة البيانات</h2>";
    $testQuery = $database->fetch("SELECT 1 as test");
    if ($testQuery && $testQuery['test'] == 1) {
        echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات يعمل بشكل صحيح</p>";
    } else {
        echo "<p style='color: red;'>❌ فشل في الاتصال بقاعدة البيانات</p>";
    }
    
    // اختبار دالة update
    echo "<h2>✅ اختبار دالة UPDATE</h2>";
    
    // إنشاء جدول اختبار مؤقت
    $database->query("CREATE TEMPORARY TABLE test_update (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100),
        email VARCHAR(100),
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");
    
    // إدراج بيانات اختبار
    $testId = $database->insert('test_update', [
        'name' => 'اختبار',
        'email' => '<EMAIL>'
    ]);
    
    echo "<p>تم إنشاء سجل اختبار برقم: {$testId}</p>";
    
    // اختبار دالة update
    $updateResult = $database->update('test_update', 
        ['name' => 'اختبار محدث'], 
        'id = :id', 
        ['id' => $testId]
    );
    
    if ($updateResult) {
        echo "<p style='color: green;'>✅ دالة UPDATE تعمل بشكل صحيح</p>";
        
        // التحقق من التحديث
        $updatedRecord = $database->fetch("SELECT * FROM test_update WHERE id = :id", ['id' => $testId]);
        if ($updatedRecord && $updatedRecord['name'] === 'اختبار محدث') {
            echo "<p style='color: green;'>✅ تم التحديث بنجاح: {$updatedRecord['name']}</p>";
        } else {
            echo "<p style='color: red;'>❌ فشل في التحديث</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ فشل في تنفيذ دالة UPDATE</p>";
    }
    
    // اختبار دالة delete
    echo "<h2>✅ اختبار دالة DELETE</h2>";
    $deleteResult = $database->delete('test_update', 'id = :id', ['id' => $testId]);
    if ($deleteResult) {
        echo "<p style='color: green;'>✅ دالة DELETE تعمل بشكل صحيح</p>";
    } else {
        echo "<p style='color: red;'>❌ فشل في تنفيذ دالة DELETE</p>";
    }
    
    echo "<h2>📋 ملخص الاختبار</h2>";
    echo "<ul>";
    echo "<li style='color: green;'>✅ الاتصال بقاعدة البيانات</li>";
    echo "<li style='color: green;'>✅ دالة INSERT</li>";
    echo "<li style='color: green;'>✅ دالة UPDATE</li>";
    echo "<li style='color: green;'>✅ دالة DELETE</li>";
    echo "<li style='color: green;'>✅ دالة SELECT</li>";
    echo "</ul>";
    
    echo "<p style='color: green; font-weight: bold; font-size: 18px;'>🎉 جميع الاختبارات نجحت! قاعدة البيانات تعمل بشكل مثالي</p>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ خطأ في الاختبار</h2>";
    echo "<p style='color: red;'>الخطأ: " . $e->getMessage() . "</p>";
    echo "<p style='color: red;'>الملف: " . $e->getFile() . "</p>";
    echo "<p style='color: red;'>السطر: " . $e->getLine() . "</p>";
}

echo "<hr>";
echo "<p><a href='test_final_contact.php'>اختبار نموذج التواصل</a></p>";
echo "<p><a href='contact'>صفحة التواصل الأصلية</a></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f5f5f5;
}

h1, h2 {
    color: #333;
    border-bottom: 2px solid #4CAF50;
    padding-bottom: 10px;
}

p {
    background: white;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
}

ul {
    background: white;
    padding: 20px;
    border-radius: 5px;
    list-style-type: none;
}

li {
    padding: 5px 0;
    font-weight: bold;
}

a {
    display: inline-block;
    background: #4CAF50;
    color: white;
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 5px;
    margin: 5px;
}

a:hover {
    background: #45a049;
}
</style>