<?php
/**
 * إدارة الصلاحيات
 * Manage Permissions
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/middleware.php';

// التحقق من تسجيل الدخول وصلاحية الإدارة
requirePermission('manage-permissions');

$message = '';
$error = '';

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'create_permission':
            $name = $_POST['name'] ?? '';
            $displayName = $_POST['display_name'] ?? '';
            $description = $_POST['description'] ?? '';
            $category = $_POST['category'] ?? 'عام';
            
            if ($name && $displayName) {
                try {
                    // التحقق من عدم وجود صلاحية بنفس الاسم
                    $stmt = $database->prepare("SELECT COUNT(*) FROM permissions WHERE name = ?");
                    $stmt->execute([$name]);
                    
                    if ($stmt->fetchColumn() > 0) {
                        $error = 'صلاحية بهذا الاسم موجودة بالفعل';
                    } else {
                        $stmt = $database->prepare("
                            INSERT INTO permissions (name, display_name, description, category, created_at) 
                            VALUES (?, ?, ?, ?, NOW())
                        ");
                        $stmt->execute([$name, $displayName, $description, $category]);
                        
                        $message = 'تم إنشاء الصلاحية بنجاح';
                    }
                } catch (Exception $e) {
                    $error = 'خطأ في إنشاء الصلاحية: ' . $e->getMessage();
                }
            } else {
                $error = 'يرجى ملء جميع الحقول المطلوبة';
            }
            break;
            
        case 'update_permission':
            $id = $_POST['id'] ?? null;
            $name = $_POST['name'] ?? '';
            $displayName = $_POST['display_name'] ?? '';
            $description = $_POST['description'] ?? '';
            $category = $_POST['category'] ?? 'عام';
            
            if ($id && $name && $displayName) {
                try {
                    // التحقق من عدم وجود صلاحية أخرى بنفس الاسم
                    $stmt = $database->prepare("SELECT COUNT(*) FROM permissions WHERE name = ? AND id != ?");
                    $stmt->execute([$name, $id]);
                    
                    if ($stmt->fetchColumn() > 0) {
                        $error = 'صلاحية بهذا الاسم موجودة بالفعل';
                    } else {
                        $stmt = $database->prepare("
                            UPDATE permissions 
                            SET name = ?, display_name = ?, description = ?, category = ?, updated_at = NOW() 
                            WHERE id = ?
                        ");
                        $stmt->execute([$name, $displayName, $description, $category, $id]);
                        
                        $message = 'تم تحديث الصلاحية بنجاح';
                    }
                } catch (Exception $e) {
                    $error = 'خطأ في تحديث الصلاحية: ' . $e->getMessage();
                }
            } else {
                $error = 'يرجى ملء جميع الحقول المطلوبة';
            }
            break;
            
        case 'delete_permission':
            $id = $_POST['id'] ?? null;
            
            if ($id) {
                try {
                    // التحقق من عدم استخدام الصلاحية في أي دور
                    $stmt = $database->prepare("SELECT COUNT(*) FROM role_permissions WHERE permission_id = ?");
                    $stmt->execute([$id]);
                    $usageCount = $stmt->fetchColumn();
                    
                    if ($usageCount > 0) {
                        $error = 'لا يمكن حذف الصلاحية لأنها مستخدمة في ' . $usageCount . ' دور';
                    } else {
                        $stmt = $database->prepare("DELETE FROM permissions WHERE id = ?");
                        $stmt->execute([$id]);
                        
                        $message = 'تم حذف الصلاحية بنجاح';
                    }
                } catch (Exception $e) {
                    $error = 'خطأ في حذف الصلاحية: ' . $e->getMessage();
                }
            }
            break;
    }
}

// جلب جميع الصلاحيات مجمعة حسب الفئة
$permissions = $database->query("
    SELECT p.*, 
           COUNT(rp.role_id) as roles_count
    FROM permissions p
    LEFT JOIN role_permissions rp ON p.id = rp.permission_id
    GROUP BY p.id
    ORDER BY p.category, p.name
")->fetchAll();

// تجميع الصلاحيات حسب الفئة
$permissionsByCategory = [];
foreach ($permissions as $permission) {
    $category = $permission['category'] ?? 'عام';
    $permissionsByCategory[$category][] = $permission;
}

// جلب الفئات المتاحة
$categories = $database->query("
    SELECT DISTINCT category 
    FROM permissions 
    WHERE category IS NOT NULL AND category != ''
    ORDER BY category
")->fetchAll(PDO::FETCH_COLUMN);

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الصلاحيات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
        }
        .permission-category {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .permission-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .permission-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .btn-success {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border: none;
        }
        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            border: none;
        }
        .btn-warning {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            border: none;
            color: #2d3436;
        }
        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .badge {
            font-size: 0.8em;
        }
        .category-header {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <!-- رسائل النجاح والخطأ -->
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <!-- العنوان والأزرار -->
        <div class="row mb-4">
            <div class="col-md-8">
                <h2>
                    <i class="fas fa-shield-alt me-2"></i>
                    إدارة الصلاحيات
                </h2>
                <p class="text-muted">إنشاء وتعديل وحذف صلاحيات النظام</p>
            </div>
            <div class="col-md-4 text-end">
                <button type="button" class="btn btn-success btn-lg" data-bs-toggle="modal" data-bs-target="#createPermissionModal">
                    <i class="fas fa-plus me-2"></i>
                    إنشاء صلاحية جديدة
                </button>
            </div>
        </div>
        
        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-shield-alt fa-2x text-primary mb-2"></i>
                        <h4><?php echo count($permissions); ?></h4>
                        <p class="text-muted mb-0">إجمالي الصلاحيات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-layer-group fa-2x text-info mb-2"></i>
                        <h4><?php echo count($permissionsByCategory); ?></h4>
                        <p class="text-muted mb-0">الفئات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-users-cog fa-2x text-success mb-2"></i>
                        <h4>
                            <?php 
                            $totalRoles = $database->query("SELECT COUNT(*) FROM roles")->fetchColumn();
                            echo $totalRoles;
                            ?>
                        </h4>
                        <p class="text-muted mb-0">الأدوار</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-link fa-2x text-warning mb-2"></i>
                        <h4>
                            <?php 
                            $totalAssignments = $database->query("SELECT COUNT(*) FROM role_permissions")->fetchColumn();
                            echo $totalAssignments;
                            ?>
                        </h4>
                        <p class="text-muted mb-0">الربطات</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- قائمة الصلاحيات مجمعة حسب الفئة -->
        <div class="row">
            <div class="col-12">
                <?php foreach ($permissionsByCategory as $category => $categoryPermissions): ?>
                    <div class="permission-category">
                        <div class="category-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h4 class="mb-0">
                                    <i class="fas fa-folder me-2"></i>
                                    <?php echo htmlspecialchars($category); ?>
                                </h4>
                                <span class="badge bg-light text-dark">
                                    <?php echo count($categoryPermissions); ?> صلاحية
                                </span>
                            </div>
                        </div>
                        
                        <div class="row">
                            <?php foreach ($categoryPermissions as $permission): ?>
                                <div class="col-md-6 col-lg-4">
                                    <div class="permission-item">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="mb-0">
                                                <?php echo htmlspecialchars($permission['display_name']); ?>
                                            </h6>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" href="#" onclick="editPermission(<?php echo $permission['id']; ?>)">
                                                            <i class="fas fa-edit me-2"></i>
                                                            تعديل
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item text-danger" href="#" onclick="deletePermission(<?php echo $permission['id']; ?>)">
                                                            <i class="fas fa-trash me-2"></i>
                                                            حذف
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                        
                                        <p class="text-muted small mb-2">
                                            <code><?php echo htmlspecialchars($permission['name']); ?></code>
                                        </p>
                                        
                                        <?php if ($permission['description']): ?>
                                            <p class="text-muted small mb-2">
                                                <?php echo htmlspecialchars($permission['description']); ?>
                                            </p>
                                        <?php endif; ?>
                                        
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="badge bg-primary">
                                                <?php echo $permission['roles_count']; ?> دور
                                            </span>
                                            <small class="text-muted">
                                                <?php echo date('Y-m-d', strtotime($permission['created_at'])); ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    
    <!-- نافذة إنشاء صلاحية جديدة -->
    <div class="modal fade" id="createPermissionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i>
                        إنشاء صلاحية جديدة
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="create_permission">
                        
                        <div class="mb-3">
                            <label for="permission_name" class="form-label">اسم الصلاحية (بالإنجليزية)</label>
                            <input type="text" class="form-control" id="permission_name" name="name" required>
                            <div class="form-text">مثال: view-users, edit-products</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="permission_display_name" class="form-label">الاسم المعروض</label>
                            <input type="text" class="form-control" id="permission_display_name" name="display_name" required>
                            <div class="form-text">مثال: عرض المستخدمين، تعديل المنتجات</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="permission_category" class="form-label">الفئة</label>
                            <select class="form-select" id="permission_category" name="category">
                                <option value="عام">عام</option>
                                <?php foreach ($categories as $cat): ?>
                                    <option value="<?php echo htmlspecialchars($cat); ?>">
                                        <?php echo htmlspecialchars($cat); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">أو اكتب فئة جديدة</div>
                            <input type="text" class="form-control mt-2" placeholder="فئة جديدة" onchange="updateCategorySelect(this)">
                        </div>
                        
                        <div class="mb-3">
                            <label for="permission_description" class="form-label">الوصف</label>
                            <textarea class="form-control" id="permission_description" name="description" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-2"></i>
                            إنشاء الصلاحية
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- نافذة تعديل الصلاحية -->
    <div class="modal fade" id="editPermissionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>
                        تعديل الصلاحية
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="editPermissionForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="update_permission">
                        <input type="hidden" name="id" id="edit_permission_id">
                        
                        <div class="mb-3">
                            <label for="edit_permission_name" class="form-label">اسم الصلاحية (بالإنجليزية)</label>
                            <input type="text" class="form-control" id="edit_permission_name" name="name" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="edit_permission_display_name" class="form-label">الاسم المعروض</label>
                            <input type="text" class="form-control" id="edit_permission_display_name" name="display_name" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="edit_permission_category" class="form-label">الفئة</label>
                            <select class="form-select" id="edit_permission_category" name="category">
                                <option value="عام">عام</option>
                                <?php foreach ($categories as $cat): ?>
                                    <option value="<?php echo htmlspecialchars($cat); ?>">
                                        <?php echo htmlspecialchars($cat); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="edit_permission_description" class="form-label">الوصف</label>
                            <textarea class="form-control" id="edit_permission_description" name="description" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function updateCategorySelect(input) {
            if (input.value.trim()) {
                const select = document.getElementById('permission_category');
                select.value = input.value.trim();
                
                // إضافة الخيار الجديد إذا لم يكن موجوداً
                let optionExists = false;
                for (let option of select.options) {
                    if (option.value === input.value.trim()) {
                        optionExists = true;
                        break;
                    }
                }
                
                if (!optionExists) {
                    const newOption = new Option(input.value.trim(), input.value.trim());
                    select.add(newOption);
                    select.value = input.value.trim();
                }
            }
        }
        
        function editPermission(permissionId) {
            // جلب بيانات الصلاحية
            fetch('get_permission.php?id=' + permissionId)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const permission = data.permission;
                        
                        document.getElementById('edit_permission_id').value = permission.id;
                        document.getElementById('edit_permission_name').value = permission.name;
                        document.getElementById('edit_permission_display_name').value = permission.display_name;
                        document.getElementById('edit_permission_category').value = permission.category || 'عام';
                        document.getElementById('edit_permission_description').value = permission.description || '';
                        
                        new bootstrap.Modal(document.getElementById('editPermissionModal')).show();
                    } else {
                        alert('خطأ في جلب بيانات الصلاحية: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('خطأ في الاتصال بالخادم');
                });
        }
        
        function deletePermission(permissionId) {
            if (confirm('هل أنت متأكد من حذف هذه الصلاحية؟\n\nتحذير: سيتم حذف الصلاحية من جميع الأدوار المرتبطة بها.')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_permission">
                    <input type="hidden" name="id" value="${permissionId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>