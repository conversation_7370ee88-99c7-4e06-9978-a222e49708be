<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// محاكاة تسجيل دخول مستخدم
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'admin';

echo "<h1>اختبار خيار التفعيل التلقائي للتقييمات</h1>";

// عرض الإعداد الحالي
$autoApproval = getSetting('reviews_auto_approval', false);
echo "<h2>الإعداد الحالي: " . ($autoApproval ? 'مفعل' : 'غير مفعل') . "</h2>";

// اختبار حفظ تقييم جديد
if ($_POST['test_review'] ?? false) {
    $result = saveProductReview(8, 5, 'تقييم تجريبي', 'هذا تقييم تجريبي لاختبار التفعيل التلقائي');
    
    if ($result === true) {
        echo "<div style='color: green; padding: 10px; background: #e8f5e8; border: 1px solid #4caf50; margin: 10px 0;'>";
        echo "✅ تم حفظ التقييم بنجاح!";
        echo "</div>";
        
        // التحقق من حالة التقييم
        global $database;
        $review = $database->fetch(
            "SELECT * FROM reviews WHERE product_id = 8 ORDER BY id DESC LIMIT 1"
        );
        
        if ($review) {
            echo "<h3>تفاصيل التقييم المحفوظ:</h3>";
            echo "<ul>";
            echo "<li><strong>المعرف:</strong> " . $review['id'] . "</li>";
            echo "<li><strong>التقييم:</strong> " . $review['rating'] . " نجوم</li>";
            echo "<li><strong>العنوان:</strong> " . $review['title'] . "</li>";
            echo "<li><strong>التعليق:</strong> " . $review['comment'] . "</li>";
            echo "<li><strong>معتمد:</strong> " . ($review['is_approved'] ? 'نعم ✅' : 'لا ❌') . "</li>";
            echo "<li><strong>تاريخ الإنشاء:</strong> " . $review['created_at'] . "</li>";
            echo "</ul>";
        }
    } else {
        echo "<div style='color: red; padding: 10px; background: #ffe8e8; border: 1px solid #f44336; margin: 10px 0;'>";
        echo "❌ خطأ: " . $result;
        echo "</div>";
    }
}

// اختبار تقييم زائر
if ($_POST['test_guest_review'] ?? false) {
    $result = saveGuestReview(8, 'زائر تجريبي', '<EMAIL>', 4, 'تقييم زائر تجريبي');
    
    if ($result === true) {
        echo "<div style='color: green; padding: 10px; background: #e8f5e8; border: 1px solid #4caf50; margin: 10px 0;'>";
        echo "✅ تم حفظ تقييم الزائر بنجاح!";
        echo "</div>";
        
        // التحقق من حالة التقييم
        global $database;
        $review = $database->fetch(
            "SELECT * FROM reviews WHERE email = '<EMAIL>' ORDER BY id DESC LIMIT 1"
        );
        
        if ($review) {
            echo "<h3>تفاصيل تقييم الزائر المحفوظ:</h3>";
            echo "<ul>";
            echo "<li><strong>المعرف:</strong> " . $review['id'] . "</li>";
            echo "<li><strong>الاسم:</strong> " . $review['name'] . "</li>";
            echo "<li><strong>البريد الإلكتروني:</strong> " . $review['email'] . "</li>";
            echo "<li><strong>التقييم:</strong> " . $review['rating'] . " نجوم</li>";
            echo "<li><strong>التعليق:</strong> " . $review['comment'] . "</li>";
            echo "<li><strong>معتمد:</strong> " . ($review['is_approved'] ? 'نعم ✅' : 'لا ❌') . "</li>";
            echo "<li><strong>تاريخ الإنشاء:</strong> " . $review['created_at'] . "</li>";
            echo "</ul>";
        }
    } else {
        echo "<div style='color: red; padding: 10px; background: #ffe8e8; border: 1px solid #f44336; margin: 10px 0;'>";
        echo "❌ خطأ: " . $result;
        echo "</div>";
    }
}
?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
    .test-section { 
        background: #f5f5f5; 
        padding: 20px; 
        margin: 20px 0; 
        border-radius: 5px; 
        border: 1px solid #ddd; 
    }
    button { 
        background: #007cba; 
        color: white; 
        padding: 10px 20px; 
        border: none; 
        border-radius: 3px; 
        cursor: pointer; 
        margin: 5px; 
    }
    button:hover { background: #005a87; }
    .links { margin: 20px 0; }
    .links a { 
        display: inline-block; 
        margin: 5px 10px; 
        padding: 8px 15px; 
        background: #28a745; 
        color: white; 
        text-decoration: none; 
        border-radius: 3px; 
    }
    .links a:hover { background: #1e7e34; }
</style>

<div class="test-section">
    <h3>اختبار تقييم مستخدم مسجل</h3>
    <p>سيتم حفظ التقييم حسب إعداد التفعيل التلقائي الحالي</p>
    <form method="post">
        <input type="hidden" name="test_review" value="1">
        <button type="submit">إضافة تقييم تجريبي</button>
    </form>
</div>

<div class="test-section">
    <h3>اختبار تقييم زائر</h3>
    <p>سيتم حفظ التقييم حسب إعداد التفعيل التلقائي الحالي</p>
    <form method="post">
        <input type="hidden" name="test_guest_review" value="1">
        <button type="submit">إضافة تقييم زائر تجريبي</button>
    </form>
</div>

<div class="links">
    <h3>روابط مفيدة:</h3>
    <a href="admin/reviews.php">إدارة التقييمات</a>
    <a href="check_reviews.php">فحص التقييمات</a>
    <a href="products/8">صفحة المنتج</a>
    <a href="api/test_review.php">اختبار API</a>
</div>

<script>
// تحديث الصفحة كل 30 ثانية لعرض آخر الإعدادات
setTimeout(() => {
    location.reload();
}, 30000);
</script>