<?php
require_once '../config/config.php';
require_once 'includes/inc_Pagination.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn()) {
    redirect('/admin/login.php');
}

if (!hasPermission('view_faqs')) {
    redirect('/admin/dashboard.php?error=' . urlencode('ليس لديك صلاحية للوصول إلى هذه الصفحة'));
}

// إعداد متغيرات الصفحة
$pageTitle = 'إدارة الأسئلة الشائعة';
$pageDescription = 'إدارة الأسئلة والأجوبة الشائعة للموقع';
$currentPage = 'content';
$breadcrumbs = [
    ['title' => 'المحتوى الإضافي', 'url' => '#'],
    ['title' => 'الأسئلة الشائعة']
];

// إضافة JavaScript إضافي
$additionalJS = [];

// تضمين التخطيط
require_once 'includes/layout.php';

// بدء التخطيط
startLayout();

$database = new Database();

// معالجة العمليات
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'add_faq':
                    $question = trim($_POST['question']);
                    $answer = trim($_POST['answer']);
                    $category = trim($_POST['category']);
                    $new_category = trim($_POST['new_category'] ?? '');
                    $sort_order = (int)$_POST['sort_order'];
                    $is_active = isset($_POST['is_active']) ? 1 : 0;
                    
                    // إذا تم اختيار "إضافة تصنيف جديد"، استخدم التصنيف الجديد
                    if ($category === '__new__' && !empty($new_category)) {
                        $category = $new_category;
                    }
                    
                    if (empty($question) || empty($answer) || empty($category)) {
                        throw new Exception('جميع الحقول مطلوبة');
                    }
                    
                    $database->query(
                        "INSERT INTO faqs (question, answer, category, sort_order, is_active, created_at, updated_at) 
                         VALUES (?, ?, ?, ?, ?, NOW(), NOW())",
                        [$question, $answer, $category, $sort_order, $is_active]
                    );
                    
                    $message = 'تم إضافة السؤال بنجاح';
                    logActivity('faq_add', "إضافة سؤال شائع: $question");
                    break;
                    
                case 'edit_faq':
                    $id = (int)$_POST['id'];
                    $question = trim($_POST['question']);
                    $answer = trim($_POST['answer']);
                    $category = trim($_POST['category']);
                    $new_category = trim($_POST['new_category'] ?? '');
                    $sort_order = (int)$_POST['sort_order'];
                    $is_active = isset($_POST['is_active']) ? 1 : 0;
                    
                    // إذا تم اختيار "إضافة تصنيف جديد"، استخدم التصنيف الجديد
                    if ($category === '__new__' && !empty($new_category)) {
                        $category = $new_category;
                    }
                    
                    if (empty($question) || empty($answer) || empty($category)) {
                        throw new Exception('جميع الحقول مطلوبة');
                    }
                    
                    $database->query(
                        "UPDATE faqs SET question = ?, answer = ?, category = ?, sort_order = ?, is_active = ?, updated_at = NOW() 
                         WHERE id = ?",
                        [$question, $answer, $category, $sort_order, $is_active, $id]
                    );
                    
                    $message = 'تم تحديث السؤال بنجاح';
                    logActivity('faq_edit', "تعديل سؤال شائع: $question");
                    break;
                    
                case 'delete_faq':
                    $id = (int)$_POST['id'];
                    
                    // جلب معلومات السؤال قبل الحذف
                    $faq = $database->fetch("SELECT question FROM faqs WHERE id = ?", [$id]);
                    
                    $database->query("DELETE FROM faqs WHERE id = ?", [$id]);
                    
                    $message = 'تم حذف السؤال بنجاح';
                    logActivity('faq_delete', "حذف سؤال شائع: " . ($faq['question'] ?? "ID: $id"));
                    break;
                    
                case 'toggle_status':
                    $id = (int)$_POST['id'];
                    $current_status = (int)$_POST['current_status'];
                    $new_status = $current_status ? 0 : 1;
                    
                    $database->query("UPDATE faqs SET is_active = ?, updated_at = NOW() WHERE id = ?", [$new_status, $id]);
                    
                    $status_text = $new_status ? 'تفعيل' : 'إلغاء تفعيل';
                    $message = "تم $status_text السؤال بنجاح";
                    logActivity('faq_toggle', "تغيير حالة سؤال شائع ID: $id إلى " . ($new_status ? 'مفعل' : 'غير مفعل'));
                    break;
            }
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// جلب البيانات
$search = $_GET['search'] ?? '';
$category_filter = $_GET['category'] ?? '';
$status_filter = $_GET['status'] ?? '';

// إعدادات الترقيم
$page = max(1, intval($_GET['page'] ?? 1));
$perPage = 10;
$offset = ($page - 1) * $perPage;

// بناء استعلام البحث
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(question LIKE ? OR answer LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if (!empty($category_filter)) {
    $where_conditions[] = "category = ?";
    $params[] = $category_filter;
}

if ($status_filter !== '') {
    $where_conditions[] = "is_active = ?";
    $params[] = (int)$status_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// حساب إجمالي السجلات
$countQuery = "SELECT COUNT(*) as total FROM faqs $where_clause";
$totalResult = $database->fetch($countQuery, $params);
$totalRecords = $totalResult['total'] ?? 0;

// جلب الأسئلة الشائعة مع الترقيم وبيانات التقييمات من كلا الجدولين
$faqs = $database->fetchAll(
    "SELECT f.*, 
            COALESCE(f.total_ratings, 0) as total_ratings_db,
            COALESCE(f.helpful_count, 0) as helpful_count_db,
            COALESCE(fr_counts.yes_count, 0) as yes_count_ratings,
            COALESCE(fr_counts.no_count, 0) as no_count_ratings,
            COALESCE(fr_counts.total_count, 0) as total_count_ratings,
            -- استخدام أكبر قيمة من الجدولين للحصول على البيانات الأكثر دقة
            GREATEST(COALESCE(f.helpful_count, 0), COALESCE(fr_counts.yes_count, 0)) as helpful_count,
            GREATEST(COALESCE(f.total_ratings, 0), COALESCE(fr_counts.total_count, 0)) as total_ratings,
            GREATEST(COALESCE(f.total_ratings, 0) - COALESCE(f.helpful_count, 0), COALESCE(fr_counts.no_count, 0)) as not_helpful_count
     FROM faqs f 
     LEFT JOIN (
         SELECT faq_id,
                COUNT(CASE WHEN rating = 'yes' THEN 1 END) as yes_count,
                COUNT(CASE WHEN rating = 'no' THEN 1 END) as no_count,
                COUNT(*) as total_count
         FROM faq_ratings 
         GROUP BY faq_id
     ) fr_counts ON f.id = fr_counts.faq_id
     $where_clause 
     ORDER BY f.sort_order, f.created_at DESC 
     LIMIT $perPage OFFSET $offset",
    $params
);

// جلب التصنيفات المتاحة
$categories = $database->fetchAll(
    "SELECT DISTINCT category FROM faqs WHERE category IS NOT NULL AND category != '' ORDER BY category"
);

// حساب معلومات الترقيم
$totalPages = ceil($totalRecords / $perPage);
$pagination = [
    'current_page' => $page,
    'total_pages' => $totalPages,
    'total_records' => $totalRecords,
    'per_page' => $perPage,
    'has_prev' => $page > 1,
    'has_next' => $page < $totalPages
];

// إحصائيات
$stats = [
    'total' => $database->fetch("SELECT COUNT(*) as count FROM faqs")['count'],
    'active' => $database->fetch("SELECT COUNT(*) as count FROM faqs WHERE is_active = 1")['count'],
    'inactive' => $database->fetch("SELECT COUNT(*) as count FROM faqs WHERE is_active = 0")['count'],
    'categories' => count($categories)
];

// عرض رأس الصفحة
showPageHeader();

// عرض الرسائل
showMessages();
?>

<!-- زر إضافة سؤال جديد -->
<div class="mb-6 flex justify-end">
    <button onclick="openAddModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200">
        <svg class="w-5 h-5 inline-block ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        إضافة سؤال جديد
    </button>
</div>

<!-- لوحة الإحصائيات -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="mr-4">
                        <p class="text-sm font-medium text-gray-600">إجمالي الأسئلة</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $stats['total']; ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-indigo-100 text-green-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="mr-4">
                        <p class="text-sm font-medium text-gray-600">الأسئلة المفعلة</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $stats['active']; ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-pink-100 text-red-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="mr-4">
                        <p class="text-sm font-medium text-gray-600">الأسئلة المعطلة</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $stats['inactive']; ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                        </svg>
                    </div>
                    <div class="mr-4">
                        <p class="text-sm font-medium text-gray-600">التصنيفات</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $stats['categories']; ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="p-6">
                <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                        <input type="text" name="search" value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="البحث في الأسئلة والأجوبة..." 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">التصنيف</label>
                        <select name="category" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">جميع التصنيفات</option>
                            <?php foreach ($categories as $cat): ?>
                                <option value="<?php echo htmlspecialchars($cat['category']); ?>" 
                                        <?php echo $category_filter === $cat['category'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($cat['category']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                        <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">جميع الحالات</option>
                            <option value="1" <?php echo $status_filter === '1' ? 'selected' : ''; ?>>مفعل</option>
                            <option value="0" <?php echo $status_filter === '0' ? 'selected' : ''; ?>>معطل</option>
                        </select>
                    </div>

                    <div class="flex items-end">
                        <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors duration-200">
                            تطبيق الفلاتر
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Results Info -->
        <?php if (!empty($faqs)): ?>
            <div class="bg-white rounded-lg shadow mb-4 p-4">
                <div class="flex justify-between items-center text-sm text-gray-600">
                    <div>
                        عرض <?php echo ($offset + 1); ?> - <?php echo min($offset + $perPage, $totalRecords); ?> من أصل <?php echo $totalRecords; ?> نتيجة
                    </div>
                    <div>
                        الصفحة <?php echo $pagination['current_page']; ?> من <?php echo $pagination['total_pages']; ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- FAQs Table -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">السؤال</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التصنيف</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الترتيب</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التقييمات</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الإنشاء</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php if (empty($faqs)): ?>
                            <tr>
                                <td colspan="7" class="px-6 py-12 text-center text-gray-500">
                                    <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    لا توجد أسئلة شائعة
                                </td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($faqs as $faq): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4">
                                        <div class="text-sm font-medium text-gray-900">
                                            <?php echo htmlspecialchars(substr($faq['question'], 0, 80)); ?>
                                            <?php if (strlen($faq['question']) > 80): ?>...<?php endif; ?>
                                        </div>
                                        <div class="text-sm text-gray-500 mt-1">
                                            <?php echo htmlspecialchars(substr($faq['answer'], 0, 100)); ?>
                                            <?php if (strlen($faq['answer']) > 100): ?>...<?php endif; ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            <?php echo htmlspecialchars($faq['category']); ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo $faq['sort_order']; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <div class="flex flex-col space-y-1">
                                            <div class="flex items-center justify-between">
                                                <span class="text-xs text-gray-500">نعم:</span>
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-indigo-100 text-green-800">
                                                    <?php echo $faq['helpful_count']; ?>
                                                </span>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <span class="text-xs text-gray-500">لا:</span>
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-pink-100 text-red-800">
                                                    <?php echo $faq['not_helpful_count']; ?>
                                                </span>
                                            </div>
                                            <div class="flex items-center justify-between border-t pt-1">
                                                <span class="text-xs text-gray-500">المجموع:</span>
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    <?php echo $faq['total_ratings']; ?>
                                                </span>
                                            </div>
                                            <?php if ($faq['total_count_ratings'] > 0 || $faq['total_ratings_db'] > 0): ?>
                                            <div class="text-xs text-gray-400 border-t pt-1">
                                                <div class="flex justify-between">
                                                    <span>من جدول التقييمات:</span>
                                                    <span><?php echo $faq['total_count_ratings']; ?></span>
                                                </div>
                                                <div class="flex justify-between">
                                                    <span>من جدول الأسئلة:</span>
                                                    <span><?php echo $faq['total_ratings_db']; ?></span>
                                                </div>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <form method="POST" class="inline">
                                            <input type="hidden" name="action" value="toggle_status">
                                            <input type="hidden" name="id" value="<?php echo $faq['id']; ?>">
                                            <input type="hidden" name="current_status" value="<?php echo $faq['is_active']; ?>">
                                            <button type="submit" class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-blue-800 <?php echo $faq['is_active'] ? 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800' : 'bg-purple-100 text-red-800 hover:bg-pink-200'; ?>">
                                                <?php echo $faq['is_active'] ? 'مفعل' : 'معطل'; ?>
                                            </button>
                                        </form>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php echo date('d/m/Y', strtotime($faq['created_at'])); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2 space-x-reverse">
                                            <button onclick="openEditModal(<?php echo htmlspecialchars(json_encode($faq)); ?>)" 
                                                    class="text-blue-600 hover:text-blue-900 transition-colors duration-200">
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                            </button>
                                            <button onclick="deleteFAQ(<?php echo $faq['id']; ?>, '<?php echo htmlspecialchars(addslashes($faq['question'])); ?>')" 
                                                    class="text-red-600 hover:text-red-900 transition-colors duration-200">
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- نظام ترقيم الصفحات -->
            <?php if ($totalRecords > $perPage): ?>
                <div class="px-6 py-4 border-t border-gray-200">
                    <?php 
                    // بناء معاملات الاستعلام لروابط الترقيم
                    $queryParams = [];
                    if (!empty($search)) $queryParams['search'] = $search;
                    if (!empty($category_filter)) $queryParams['category'] = $category_filter;
                    if ($status_filter !== '') $queryParams['status'] = $status_filter;
                    
                    echo renderPagination(
                        $pagination['current_page'],
                        $pagination['total_pages'],
                        'faqs.php',
                        $queryParams
                    );
                    ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add/Edit Modal -->
<div id="faqModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900" id="modalTitle">إضافة سؤال جديد</h3>
                <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <form id="faqForm" method="POST" class="space-y-4">
                <input type="hidden" name="action" id="formAction" value="add_faq">
                <input type="hidden" name="id" id="faqId">
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">السؤال *</label>
                    <input type="text" name="question" id="question" required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="اكتب السؤال هنا...">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">الإجابة *</label>
                    <textarea name="answer" id="answer" required rows="6"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              placeholder="اكتب الإجابة هنا..."></textarea>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">التصنيف *</label>
                        <select name="category" id="category" required 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                onchange="handleCategoryChange(this)">
                            <option value="">اختر التصنيف...</option>
                            <?php foreach ($categories as $cat): ?>
                                <option value="<?php echo htmlspecialchars($cat['category']); ?>">
                                    <?php echo htmlspecialchars($cat['category']); ?>
                                </option>
                            <?php endforeach; ?>
                            <option value="__new__">+ إضافة تصنيف جديد</option>
                        </select>
                        <input type="text" id="newCategory" name="new_category" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mt-2 hidden"
                               placeholder="اكتب التصنيف الجديد...">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">ترتيب العرض</label>
                        <input type="number" name="sort_order" id="sort_order" value="0" min="0"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="is_active" id="is_active" checked 
                               class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        <span class="mr-2 text-sm text-gray-700">مفعل</span>
                    </label>
                </div>
                
                <div class="flex justify-end space-x-3 space-x-reverse pt-4">
                    <button type="button" onclick="closeModal()" 
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors duration-200">
                        إلغاء
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200">
                        حفظ
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function handleCategoryChange(select) {
    const newCategoryInput = document.getElementById('newCategory');
    if (select.value === '__new__') {
        newCategoryInput.classList.remove('hidden');
        newCategoryInput.required = true;
        newCategoryInput.focus();
    } else {
        newCategoryInput.classList.add('hidden');
        newCategoryInput.required = false;
        newCategoryInput.value = '';
    }
}

function openAddModal() {
    document.getElementById('modalTitle').textContent = 'إضافة سؤال جديد';
    document.getElementById('formAction').value = 'add_faq';
    document.getElementById('faqId').value = '';
    document.getElementById('faqForm').reset();
    document.getElementById('is_active').checked = true;
    document.getElementById('newCategory').classList.add('hidden');
    document.getElementById('newCategory').required = false;
    document.getElementById('faqModal').classList.remove('hidden');
}

function openEditModal(faq) {
    document.getElementById('modalTitle').textContent = 'تعديل السؤال';
    document.getElementById('formAction').value = 'edit_faq';
    document.getElementById('faqId').value = faq.id;
    document.getElementById('question').value = faq.question;
    document.getElementById('answer').value = faq.answer;
    
    // تعيين التصنيف
    const categorySelect = document.getElementById('category');
    const newCategoryInput = document.getElementById('newCategory');
    
    // البحث عن التصنيف في القائمة
    let categoryFound = false;
    for (let option of categorySelect.options) {
        if (option.value === faq.category) {
            categorySelect.value = faq.category;
            categoryFound = true;
            break;
        }
    }
    
    // إذا لم يوجد التصنيف في القائمة، اعتبره تصنيف جديد
    if (!categoryFound) {
        categorySelect.value = '__new__';
        newCategoryInput.classList.remove('hidden');
        newCategoryInput.required = true;
        newCategoryInput.value = faq.category;
    } else {
        newCategoryInput.classList.add('hidden');
        newCategoryInput.required = false;
        newCategoryInput.value = '';
    }
    
    document.getElementById('sort_order').value = faq.sort_order;
    document.getElementById('is_active').checked = faq.is_active == 1;
    document.getElementById('faqModal').classList.remove('hidden');
}

function closeModal() {
    document.getElementById('faqModal').classList.add('hidden');
}

function deleteFAQ(id, question) {
    if (confirmDelete(`هل أنت متأكد من حذف السؤال: "${question}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_faq">
            <input type="hidden" name="id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Close modal when clicking outside
document.getElementById('faqModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeModal();
    }
});

// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات hover
    const cards = document.querySelectorAll('.bg-white');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 10px 25px rgba(0,0,0,0.1)';
            this.style.transition = 'all 0.3s ease';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 1px 3px rgba(0,0,0,0.1)';
        });
    });
});
</script>

<?php
// إنهاء التخطيط
endLayout();
?>