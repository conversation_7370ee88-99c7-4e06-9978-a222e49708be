<?php
/**
 * Simple Query Builder
 * Provides a fluent interface for building database queries safely
 */

class QueryBuilder {
    private $pdo;
    private $table;
    private $select = ['*'];
    private $where = [];
    private $orderBy = [];
    private $limit;
    private $offset;
    private $joins = [];
    private $params = [];
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    public function table($table) {
        $this->table = $table;
        return $this;
    }
    
    public function select($columns = ['*']) {
        $this->select = is_array($columns) ? $columns : func_get_args();
        return $this;
    }
    
    public function where($column, $operator = '=', $value = null) {
        if (func_num_args() === 2) {
            $value = $operator;
            $operator = '=';
        }
        
        $placeholder = ':where_' . count($this->params);
        $this->where[] = "$column $operator $placeholder";
        $this->params[$placeholder] = $value;
        return $this;
    }
    
    public function whereIn($column, $values) {
        $placeholders = [];
        foreach ($values as $i => $value) {
            $placeholder = ':wherein_' . count($this->params);
            $placeholders[] = $placeholder;
            $this->params[$placeholder] = $value;
        }
        
        $this->where[] = "$column IN (" . implode(', ', $placeholders) . ")";
        return $this;
    }
    
    public function orderBy($column, $direction = 'ASC') {
        $this->orderBy[] = "$column $direction";
        return $this;
    }
    
    public function limit($limit, $offset = null) {
        $this->limit = $limit;
        if ($offset !== null) {
            $this->offset = $offset;
        }
        return $this;
    }
    
    public function join($table, $first, $operator = '=', $second = null) {
        if (func_num_args() === 3) {
            $second = $operator;
            $operator = '=';
        }
        
        $this->joins[] = "JOIN $table ON $first $operator $second";
        return $this;
    }
    
    public function leftJoin($table, $first, $operator = '=', $second = null) {
        if (func_num_args() === 3) {
            $second = $operator;
            $operator = '=';
        }
        
        $this->joins[] = "LEFT JOIN $table ON $first $operator $second";
        return $this;
    }
    
    public function get() {
        $sql = $this->buildSelectQuery();
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($this->params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function first() {
        $this->limit(1);
        $results = $this->get();
        return $results ? $results[0] : null;
    }
    
    public function count() {
        $originalSelect = $this->select;
        $this->select = ['COUNT(*) as count'];
        $result = $this->first();
        $this->select = $originalSelect;
        return $result ? (int)$result['count'] : 0;
    }
    
    public function insert($data) {
        $columns = array_keys($data);
        $placeholders = array_map(function($col) { return ":$col"; }, $columns);
        
        $sql = "INSERT INTO {$this->table} (" . implode(', ', $columns) . ") VALUES (" . implode(', ', $placeholders) . ")";
        
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute($data);
    }
    
    public function update($data) {
        $setParts = [];
        foreach ($data as $column => $value) {
            $placeholder = ":set_$column";
            $setParts[] = "$column = $placeholder";
            $this->params[$placeholder] = $value;
        }
        
        $sql = "UPDATE {$this->table} SET " . implode(', ', $setParts);
        
        if (!empty($this->where)) {
            $sql .= " WHERE " . implode(' AND ', $this->where);
        }
        
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute($this->params);
    }
    
    public function delete() {
        $sql = "DELETE FROM {$this->table}";
        
        if (!empty($this->where)) {
            $sql .= " WHERE " . implode(' AND ', $this->where);
        }
        
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute($this->params);
    }
    
    private function buildSelectQuery() {
        $sql = "SELECT " . implode(', ', $this->select) . " FROM {$this->table}";
        
        if (!empty($this->joins)) {
            $sql .= " " . implode(' ', $this->joins);
        }
        
        if (!empty($this->where)) {
            $sql .= " WHERE " . implode(' AND ', $this->where);
        }
        
        if (!empty($this->orderBy)) {
            $sql .= " ORDER BY " . implode(', ', $this->orderBy);
        }
        
        if ($this->limit) {
            $sql .= " LIMIT {$this->limit}";
            if ($this->offset) {
                $sql .= " OFFSET {$this->offset}";
            }
        }
        
        return $sql;
    }
    
    public function toSql() {
        return $this->buildSelectQuery();
    }
    
    public function getParams() {
        return $this->params;
    }
}

// Helper function to create a new query builder instance
function query($pdo) {
    return new QueryBuilder($pdo);
}

// Usage examples:
/*
// Select all support files ordered by upload_date
$files = query($db)->table('support_files')->orderBy('upload_date', 'DESC')->get();

// Get a specific contact info by section
$contact = query($db)->table('contact_info')->where('section_key', 'header')->first();

// Count active testimonials
$count = query($db)->table('testimonials')->where('status', 'active')->count();

// Insert new FAQ
query($db)->table('faqs')->insert([
    'question' => 'How do I...?',
    'answer' => 'You can...',
    'status' => 'active'
]);

// Update warranty plan
query($db)->table('warranty_plans')->where('id', 1)->update([
    'title' => 'Updated Title',
    'description' => 'Updated Description'
]);
*/
?>