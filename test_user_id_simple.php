<?php
require_once 'config/config.php';

echo "<h1>🔍 اختبار مبسط لمشكلة user_id</h1>";

try {
    global $database;
    
    // 1. فحص الاتصال
    echo "<h2>1. فحص الاتصال:</h2>";
    $pdo = $database->getConnection();
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>✅ الاتصال ناجح</div>";
    
    // 2. فحص جدول reviews
    echo "<h2>2. فحص جدول reviews:</h2>";
    $columns = $database->fetchAll("DESCRIBE reviews");
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; background: white;'>";
    echo "<tr style='background: #007bff; color: white;'><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    $hasUserId = false;
    foreach ($columns as $col) {
        if ($col['Field'] == 'user_id') $hasUserId = true;
        $highlight = ($col['Field'] == 'user_id') ? 'background: yellow;' : '';
        echo "<tr style='$highlight'>";
        echo "<td><strong>" . $col['Field'] . "</strong></td>";
        echo "<td>" . $col['Type'] . "</td>";
        echo "<td>" . $col['Null'] . "</td>";
        echo "<td>" . $col['Key'] . "</td>";
        echo "<td>" . $col['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    if (!$hasUserId) {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24; margin-top: 10px;'>";
        echo "❌ عمود user_id غير موجود! جاري إضافته...";
        echo "</div>";
        
        $database->query("ALTER TABLE reviews ADD COLUMN user_id INT NULL AFTER id");
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>✅ تم إضافة عمود user_id</div>";
    }
    
    // 3. حذف البيانات التجريبية
    echo "<h2>3. تنظيف البيانات:</h2>";
    $deleted = $database->query("DELETE FROM reviews WHERE comment LIKE '%اختبار%' OR comment LIKE '%تجريبية%'")->rowCount();
    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; color: #856404;'>تم حذف $deleted مراجعة تجريبية</div>";
    
    // 4. اختبار إدراج بسيط
    echo "<h2>4. اختبار إدراج بسيط:</h2>";
    
    $testData = [
        'product_id' => 1,
        'user_id' => 999, // رقم واضح للاختبار
        'name' => 'اختبار بسيط',
        'email' => '<EMAIL>',
        'rating' => 5,
        'title' => 'اختبار',
        'comment' => 'اختبار بسيط لحفظ user_id',
        'is_approved' => 1,
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    echo "<div style='background: #e2e3e5; padding: 10px; border-radius: 5px;'>";
    echo "<strong>البيانات المرسلة:</strong><br>";
    foreach ($testData as $key => $value) {
        $highlight = ($key == 'user_id') ? 'background: yellow; padding: 2px;' : '';
        echo "<span style='$highlight'>• $key: $value</span><br>";
    }
    echo "</div>";
    
    // الإدراج
    $insertId = $database->insert('reviews', $testData);
    
    if ($insertId) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
        echo "✅ تم الإدراج بنجاح - معرف المراجعة: $insertId";
        echo "</div>";
        
        // التحقق من البيانات المحفوظة
        $saved = $database->fetch("SELECT * FROM reviews WHERE id = ?", [$insertId]);
        
        echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin-top: 10px; color: #0c5460;'>";
        echo "<strong>البيانات المحفوظة:</strong><br>";
        foreach ($saved as $key => $value) {
            $highlight = ($key == 'user_id') ? 'background: yellow; padding: 2px; font-weight: bold;' : '';
            echo "<span style='$highlight'>• $key: " . ($value ?: 'NULL') . "</span><br>";
        }
        echo "</div>";
        
        // التحقق النهائي
        if ($saved['user_id'] == 999) {
            echo "<div style='background: #28a745; color: white; padding: 20px; border-radius: 10px; text-align: center; margin-top: 20px;'>";
            echo "<h2>🎉 نجح الاختبار!</h2>";
            echo "<p>user_id تم حفظه بشكل صحيح: <strong>" . $saved['user_id'] . "</strong></p>";
            echo "</div>";
        } else {
            echo "<div style='background: #dc3545; color: white; padding: 20px; border-radius: 10px; text-align: center; margin-top: 20px;'>";
            echo "<h2>❌ فشل الاختبار!</h2>";
            echo "<p>user_id لم يتم حفظه بشكل صحيح</p>";
            echo "<p>المتوقع: 999، المحفوظ: " . ($saved['user_id'] ?: 'NULL') . "</p>";
            echo "</div>";
        }
        
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
        echo "❌ فشل في الإدراج";
        echo "</div>";
    }
    
    // 5. عرض آخر المراجعات
    echo "<h2>5. آخر المراجعات:</h2>";
    $recent = $database->fetchAll("SELECT * FROM reviews ORDER BY created_at DESC LIMIT 3");
    
    if (!empty($recent)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; background: white; margin-top: 10px;'>";
        echo "<tr style='background: #007bff; color: white;'>";
        echo "<th>ID</th><th>Product ID</th><th style='background: #ffc107; color: black;'>User ID</th><th>Name</th><th>Rating</th><th>Comment</th><th>Created</th>";
        echo "</tr>";
        
        foreach ($recent as $review) {
            $userIdStyle = $review['user_id'] ? 'background: #d4edda; font-weight: bold;' : 'background: #f8d7da; color: #721c24;';
            echo "<tr>";
            echo "<td>" . $review['id'] . "</td>";
            echo "<td>" . $review['product_id'] . "</td>";
            echo "<td style='$userIdStyle'>" . ($review['user_id'] ?: 'NULL') . "</td>";
            echo "<td>" . $review['name'] . "</td>";
            echo "<td>" . $review['rating'] . "</td>";
            echo "<td>" . substr($review['comment'], 0, 20) . "...</td>";
            echo "<td>" . $review['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
    echo "<h3>❌ خطأ:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background: #f8f9fa;
}

h1, h2 {
    color: #333;
}

table {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 5px;
    overflow: hidden;
}

th, td {
    padding: 8px;
    text-align: left;
}

div {
    margin: 10px 0;
}
</style>