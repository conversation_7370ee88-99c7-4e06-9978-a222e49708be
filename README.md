# مشروع الخط الأخضر - نظام إدارة المنتجات

## نظرة عامة

مشروع الخط الأخضر هو نظام شامل لإدارة وعرض المنتجات مبني بـ PHP مع تركيز على الأداء وقابلية الصيانة وتجربة المستخدم المتميزة.

## الميزات الرئيسية

### 🚀 الأداء والتحسين
- **نظام تخزين مؤقت متقدم**: تخزين مؤقت ذكي للمنتجات والبيانات
- **تحميل كسول للصور**: تحسين أوقات التحميل
- **ضغط البيانات**: تقليل استخدام التخزين
- **تنظيف تلقائي**: إزالة البيانات المنتهية الصلاحية

### 🛡️ الأمان والتحقق
- **التحقق الشامل من البيانات**: فحص جميع المدخلات
- **حماية من CSRF**: منع الهجمات العابرة للمواقع
- **تنظيف البيانات**: إزالة المحتوى الضار
- **التحقق من الصور**: فحص صحة الملفات المرفوعة

### 🎨 واجهة المستخدم
- **تصميم متجاوب**: يعمل على جميع الأجهزة
- **تجربة مستخدم محسنة**: تفاعل سلس وسريع
- **دعم اللغة العربية**: واجهة مصممة للعربية
- **إمكانية الوصول**: متوافق مع معايير الوصول

### 📊 إدارة المحتوى
- **إدارة المنتجات**: إضافة وتعديل وحذف المنتجات
- **معرض الصور**: رفع وإدارة صور متعددة
- **المميزات والمواصفات**: إدارة تفاصيل المنتجات
- **نظام التقييمات**: تقييمات المستخدمين

## هيكل المشروع

```
greenline_php/
├── assets/
│   ├── css/
│   │   ├── products.css          # أنماط المنتجات
│   │   └── style.css             # الأنماط العامة
│   ├── js/
│   │   ├── products.js           # وظائف المنتجات
│   │   └── main.js               # الوظائف العامة
│   └── images/                   # الصور والأيقونات
├── config/
│   └── products_config.php       # إعدادات المنتجات
├── includes/
│   ├── ProductHelper.php         # مساعد المنتجات
│   ├── ProductCache.php          # نظام التخزين المؤقت
│   ├── ProductValidator.php      # التحقق من البيانات
│   └── db.php                    # اتصال قاعدة البيانات
├── templates/
│   ├── product_detail.php        # صفحة تفاصيل المنتج
│   ├── header.php               # رأس الصفحة
│   └── footer.php               # تذييل الصفحة
├── cache/                        # ملفات التخزين المؤقت
├── uploads/                      # الملفات المرفوعة
└── README.md                     # هذا الملف
```

## التثبيت والإعداد

### المتطلبات
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- خادم ويب (Apache/Nginx)
- مكتبة GD لمعالجة الصور

### خطوات التثبيت

1. **استنساخ المشروع**
   ```bash
   git clone [repository-url]
   cd greenline_php
   ```

2. **إعداد قاعدة البيانات**
   - إنشاء قاعدة بيانات جديدة
   - استيراد ملف SQL المرفق
   - تحديث إعدادات الاتصال في `includes/db.php`

3. **إعداد الصلاحيات**
   ```bash
   chmod 755 cache/
   chmod 755 uploads/
   ```

4. **تكوين الإعدادات**
   - تحديث `config/products_config.php` حسب احتياجاتك
   - تعديل مسارات الملفات والصور

## الاستخدام

### إدارة المنتجات

#### إضافة منتج جديد
```php
// استخدام ProductHelper للتحقق من البيانات
$productData = [
    'name' => 'اسم المنتج',
    'description' => 'وصف المنتج',
    'price' => 100.00,
    'category_id' => 1,
    'features' => ['ميزة 1', 'ميزة 2'],
    'specifications' => ['المواصفة' => 'القيمة']
];

if (ProductHelper::validateProductData($productData)) {
    // حفظ المنتج
} else {
    $errors = ProductHelper::getValidationErrors();
}
```

#### عرض المنتجات مع التخزين المؤقت
```php
// الحصول على منتج مع التخزين المؤقت
$product = ProductHelper::getProductWithCache($productId);

// عرض المميزات
echo ProductHelper::renderFeatures($product['features']);

// عرض المواصفات
echo ProductHelper::renderSpecifications($product['specifications']);
```

### إدارة التخزين المؤقت

#### تنظيف التخزين المؤقت
```php
// تنظيف منتج محدد
ProductHelper::clearProductCache($productId);

// تنظيف جميع البيانات
ProductHelper::clearProductCache();

// تنظيف البيانات المنتهية الصلاحية
ProductHelper::cleanupCache();
```

#### إحصائيات التخزين المؤقت
```php
$stats = ProductHelper::getCacheStats();
echo "عدد الملفات: " . $stats['total_files'];
echo "الحجم الإجمالي: " . $stats['total_size_mb'] . " MB";
```

## التخصيص

### إضافة مميزات جديدة

1. **تحديث ملف التكوين**
   ```php
   // في config/products_config.php
   'custom_features' => [
       'enable_reviews' => true,
       'enable_wishlist' => true,
   ]
   ```

2. **إضافة دوال جديدة**
   ```php
   // في ProductHelper.php
   public static function customFunction($data) {
       // الكود المخصص
   }
   ```

### تخصيص التصميم

1. **تعديل ملف CSS**
   ```css
   /* في assets/css/products.css */
   .custom-product-card {
       /* الأنماط المخصصة */
   }
   ```

2. **إضافة JavaScript مخصص**
   ```javascript
   // في assets/js/products.js
   class CustomProductFeature {
       // الوظائف المخصصة
   }
   ```

## الأمان

### أفضل الممارسات المطبقة

- **تنظيف المدخلات**: جميع البيانات يتم تنظيفها قبل المعالجة
- **التحقق من الملفات**: فحص نوع وحجم الملفات المرفوعة
- **حماية SQL**: استخدام prepared statements
- **التشفير**: تشفير البيانات الحساسة
- **التحقق من الصلاحيات**: فحص صلاحيات المستخدم

### إعدادات الأمان
```php
// في config/products_config.php
'security' => [
    'enable_csrf' => true,
    'max_file_size' => 5 * 1024 * 1024, // 5MB
    'allowed_file_types' => ['jpg', 'png', 'webp'],
    'sanitize_input' => true,
]
```

## الأداء

### تحسينات مطبقة

- **التخزين المؤقت الذكي**: تخزين البيانات المستخدمة بكثرة
- **ضغط البيانات**: تقليل حجم الملفات
- **تحميل كسول**: تحميل المحتوى عند الحاجة
- **تحسين الصور**: ضغط وتحسين الصور تلقائياً

### مراقبة الأداء
```php
// الحصول على إحصائيات الأداء
$stats = ProductHelper::getCacheStats();
$performance = [
    'cache_hit_ratio' => $stats['valid_files'] / $stats['total_files'],
    'memory_usage' => $stats['total_size_mb'],
    'cleanup_needed' => $stats['expired_files'] > 0
];
```

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### مشكلة: لا تظهر الصور
```php
// التحقق من مسار الصور
if (!file_exists($imagePath)) {
    echo "الصورة غير موجودة: " . $imagePath;
}

// التحقق من الصلاحيات
if (!is_readable($imagePath)) {
    echo "لا يمكن قراءة الصورة";
}
```

#### مشكلة: بطء في التحميل
```php
// تفعيل التخزين المؤقت
ProductHelper::init();

// تنظيف البيانات المنتهية الصلاحية
ProductHelper::cleanupCache();
```

#### مشكلة: أخطاء في التحقق
```php
// عرض تفاصيل الأخطاء
$errors = ProductHelper::getValidationErrors();
foreach ($errors as $field => $fieldErrors) {
    echo "خطأ في {$field}: " . implode(', ', $fieldErrors);
}
```

## المساهمة

### إرشادات المساهمة

1. **Fork المشروع**
2. **إنشاء فرع جديد** (`git checkout -b feature/new-feature`)
3. **Commit التغييرات** (`git commit -am 'Add new feature'`)
4. **Push للفرع** (`git push origin feature/new-feature`)
5. **إنشاء Pull Request**

### معايير الكود

- استخدام PSR-4 للتحميل التلقائي
- توثيق جميع الدوال والكلاسات
- اتباع معايير PHP الحديثة
- كتابة اختبارات للميزات الجديدة

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:

- **البريد الإلكتروني**: <EMAIL>
- **GitHub Issues**: [رابط المشاكل]
- **الوثائق**: [رابط الوثائق]

## التحديثات

### الإصدار 2.0.0
- إضافة نظام التخزين المؤقت المتقدم
- تحسين نظام التحقق من البيانات
- إضافة دعم للصور المتعددة
- تحسين الأداء والأمان

### الإصدار 1.5.0
- إضافة نظام التقييمات
- تحسين واجهة المستخدم
- إضافة دعم للغة العربية
- تحسين التوافق مع الأجهزة المحمولة

---

**ملاحظة**: هذا المشروع في تطوير مستمر. نرحب بمساهماتكم واقتراحاتكم لتحسينه.




====================

قمت بالتراجع  فظهرت الصفحات ...

الان اعيد البرومب من جديد ..

الان اريد منك إنشاء صفحة "خدمة ما بعد البيع" مراعيا التخطيط العام لصفحات الأدمن Tailwind CSS ويكون فيها تابات لكل كارد : http://greenline_php.test/admin/after-sales.php في صفحة الأدمن  بحيث تقوم بقراءة محتوى الصفحة العامة : http://greenline_php.test/support بحيث اقوم بتحرير البيانات والتعديل عليها في صفحة الأدمن والاضافة  ويكون اسم التابات على عناوين الكاردات مع وجود ايقونات عند اسم التاب معبرة الموجودة فمثلا:

كارد "طرق التواصل معنا" = موجودة سابقا وتجلب بياناتها من جدول "contact_info"

كارد"الأسئلة الشائعة" = موجود في جدول "faqs" بحيث تظهر افضل 5 اسئلة تم التقييم عليها ..

كارد "ساعات العمل" = موجود في جدول "contact_info"

كارد "مركز التحميل" = يكون فيه امكانية لرقع ملفات من نوع "pdf" والتعليق عليها مثل ماهو موجود في الكارد الحالي

كارد "أرسل طلب دعم" = نعمل عليه لاحقاًً ...

بحيث عند التعديل على صفحة الادمن يظهر التأثير او التعديا او الاضافة في الصفحة العامة ...

هل فهمت المطلوب