<?php
/**
 * الدوال المساعدة العامة
 * General Helper Functions
 */

// تضمين ملف نظام الصلاحيات
require_once __DIR__ . '/permissions.php';

/**
 * دالة التحقق من تسجيل الدخول مع إعادة التوجيه
 */
function requireLogin($redirectTo = 'login.php') {
    if (!isLoggedIn()) {
        header('Location: ' . $redirectTo);
        exit();
    }
}

/**
 * دالة التحقق من صلاحية معينة
 */
function requirePermission($permission, $message = null) {
    requireLogin();
    
    global $permissionManager;
    $userId = getCurrentUserId();
    
    if (!$userId || !$permissionManager->hasPermission($userId, $permission)) {
        $defaultMessage = 'ليس لديك صلاحية للوصول إلى هذه الصفحة';
        die($message ?: $defaultMessage);
    }
}

/**
 * دالة التحقق من دور معين
 */
function requireRole($role, $message = null) {
    requireLogin();
    
    $user = getCurrentUser();
    if (!$user || $user['role'] !== $role) {
        $defaultMessage = 'ليس لديك الدور المطلوب للوصول إلى هذه الصفحة';
        die($message ?: $defaultMessage);
    }
}

/**
 * دالة التحقق من صلاحية الإدارة
 */
function requireAdmin($message = null) {
    requireLogin();
    
    $user = getCurrentUser();
    if (!$user || !in_array($user['role'], ['admin', 'super-admin'])) {
        $defaultMessage = 'ليس لديك صلاحية إدارية للوصول إلى هذه الصفحة';
        die($message ?: $defaultMessage);
    }
}

/**
 * دالة تسجيل الأنشطة
 */
function logActivity($action, $description = '', $user_id = null, $item_type = null, $item_id = null, $metadata = []) {
    global $database;
    
    if ($user_id === null && isset($_SESSION['user_id'])) {
        $user_id = $_SESSION['user_id'];
    }
    
    try {
        $database->insert('activity_logs', [
            'user_id' => $user_id,
            'action' => $action,
            'description' => $description,
            'item_type' => $item_type,
            'item_id' => $item_id,
            'metadata' => json_encode($metadata, JSON_UNESCAPED_UNICODE),
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    } catch (Exception $e) {
        // تجاهل أخطاء تسجيل الأنشطة
        error_log('Activity log error: ' . $e->getMessage());
    }
}

/**
 * دالة لتحميل البيانات من قاعدة البيانات
 */
function getSettings() {
    global $database;
    static $settings = null;
    
    if ($settings === null) {
        $rows = $database->fetchAll("SELECT setting_key, setting_value, setting_type FROM settings");
        $settings = [];
        
        foreach ($rows as $row) {
            $value = $row['setting_value'];
            
            // تحويل القيمة حسب النوع
            switch ($row['setting_type']) {
                case 'json':
                    $value = json_decode($value, true);
                    break;
                case 'boolean':
                    $value = (bool) $value;
                    break;
                case 'number':
                    $value = is_numeric($value) ? (float) $value : $value;
                    break;
            }
            
            $settings[$row['setting_key']] = $value;
        }
    }
    
    return $settings;
}

/**
 * دالة للحصول على إعداد معين
 */
function getSetting($key, $default = null) {
    $settings = getSettings();
    return isset($settings[$key]) ? $settings[$key] : $default;
}

/**
 * دالة لحفظ إعداد
 */
function setSetting($key, $value, $type = 'string') {
    global $database;
    
    if ($type === 'json') {
        $value = json_encode($value, JSON_UNESCAPED_UNICODE);
    } elseif ($type === 'boolean') {
        $value = $value ? '1' : '0';
    }
    
    $existing = $database->fetch("SELECT id FROM settings WHERE setting_key = :key", ['key' => $key]);
    
    if ($existing) {
        $database->update('settings', 
            ['setting_value' => $value, 'setting_type' => $type],
            'setting_key = :key',
            ['key' => $key]
        );
    } else {
        $database->insert('settings', [
            'setting_key' => $key,
            'setting_value' => $value,
            'setting_type' => $type
        ]);
    }
}

/**
 * دالة للحصول على جميع المنتجات
 */
function getProducts($filters = []) {
    global $database;
    
    $sql = "SELECT p.*, pc.name as category_name FROM products p 
            LEFT JOIN product_categories pc ON p.category_id = pc.id 
            WHERE p.is_active = 1 AND (p.is_visible IS NULL OR p.is_visible = 1)";
    $params = [];
    
    // تطبيق المرشحات
    if (!empty($filters['category']) && $filters['category'] !== 'all') {
        $sql .= " AND p.category_id = :category";
        $params['category'] = $filters['category'];
    }
    
    if (!empty($filters['search'])) {
        $sql .= " AND (p.name LIKE :search OR p.description LIKE :search)";
        $params['search'] = '%' . $filters['search'] . '%';
    }
    
    if (!empty($filters['min_price'])) {
        $sql .= " AND p.price >= :min_price";
        $params['min_price'] = $filters['min_price'];
    }
    
    if (!empty($filters['max_price'])) {
        $sql .= " AND p.price <= :max_price";
        $params['max_price'] = $filters['max_price'];
    }
    
    if (!empty($filters['featured'])) {
        $sql .= " AND p.is_featured = 1";
    }
    
    // حساب العدد الإجمالي للمنتجات
    $countSql = str_replace('SELECT p.*, pc.name as category_name', 'SELECT COUNT(*)', $sql);
    $totalResult = $database->fetch($countSql, $params);
    $total = $totalResult['COUNT(*)'];
    
    // ترتيب النتائج
    $orderBy = 'p.sort_order, p.created_at DESC';
    if (!empty($filters['sort'])) {
        switch ($filters['sort']) {
            case 'name':
                $orderBy = 'p.name ASC';
                break;
            case 'name_desc':
                $orderBy = 'p.name DESC';
                break;
            case 'price_low':
                $orderBy = 'p.price ASC';
                break;
            case 'price_high':
                $orderBy = 'p.price DESC';
                break;
            case 'rating':
                $orderBy = 'p.rating DESC';
                break;
            case 'newest':
                $orderBy = 'p.created_at DESC';
                break;
            case 'oldest':
                $orderBy = 'p.created_at ASC';
                break;
        }
    }
    
    $sql .= " ORDER BY $orderBy";
    
    // تطبيق التصفح
    if (!empty($filters['per_page'])) {
        $per_page = (int) $filters['per_page'];
        $page = max(1, (int) ($filters['page'] ?? 1));
        $offset = ($page - 1) * $per_page;
        
        $sql .= " LIMIT :limit OFFSET :offset";
        $params['limit'] = $per_page;
        $params['offset'] = $offset;
    } elseif (!empty($filters['limit'])) {
        $sql .= " LIMIT :limit";
        $params['limit'] = (int) $filters['limit'];
        
        if (!empty($filters['offset'])) {
            $sql .= " OFFSET :offset";
            $params['offset'] = (int) $filters['offset'];
        }
    }
    
    $products = $database->fetchAll($sql, $params);
    
    // تحويل JSON إلى مصفوفات للمنتجات
    foreach ($products as &$product) {
        $product['features'] = $product['features'] ? json_decode($product['features'], true) ?: [] : [];
        $product['specifications'] = $product['specifications'] ? json_decode($product['specifications'], true) ?: [] : [];
        $product['gallery'] = $product['gallery'] ? json_decode($product['gallery'], true) ?: [] : [];
    }
    
    return [
        'products' => $products,
        'total' => $total
    ];
}

/**
 * دالة للحصول على منتج واحد
 */
function getProduct($id) {
    global $database;
    
    $product = $database->fetch(
        "SELECT p.*, pc.name as category_name FROM products p 
         LEFT JOIN product_categories pc ON p.category_id = pc.id 
         WHERE p.id = :id AND p.is_active = 1 AND (p.is_visible IS NULL OR p.is_visible = 1)",
        ['id' => $id]
    );
    
    if ($product) {
        // تحويل JSON إلى مصفوفات
        $product['features'] = $product['features'] ? json_decode($product['features'], true) ?: [] : [];
        $product['specifications'] = $product['specifications'] ? json_decode($product['specifications'], true) ?: [] : [];
        $product['gallery'] = $product['gallery'] ? json_decode($product['gallery'], true) ?: [] : [];
    }
    
    return $product;
}

/**
 * دالة للحصول على المنتجات المميزة
 */
function getFeaturedProducts($limit = null) {
    global $database;
    
    $query = "SELECT p.*, pc.name as category_name FROM products p 
             LEFT JOIN product_categories pc ON p.category_id = pc.id 
             WHERE p.is_featured = 1 AND p.is_active = 1 AND (p.is_visible IS NULL OR p.is_visible = 1) 
             ORDER BY p.sort_order ASC, p.id ASC";
    
    if ($limit !== null && is_numeric($limit) && $limit > 0) {
        $query .= " LIMIT " . intval($limit);
    }
    
    $products = $database->fetchAll($query);
    
    // تحويل JSON إلى مصفوفات للمنتجات
    foreach ($products as &$product) {
        $product['features'] = $product['features'] ? json_decode($product['features'], true) ?: [] : [];
        $product['specifications'] = $product['specifications'] ? json_decode($product['specifications'], true) ?: [] : [];
        $product['gallery'] = $product['gallery'] ? json_decode($product['gallery'], true) ?: [] : [];
    }
    
    return $products;
}

/**
 * دالة للحصول على فئات المنتجات
 */
function getProductCategories($limit = null) {
    global $database;
    
    $query = "SELECT * FROM product_categories WHERE is_active = 1 AND (is_visible IS NULL OR is_visible = 1) ORDER BY sort_order";
    
    if ($limit !== null && is_numeric($limit) && $limit > 0) {
        $query .= " LIMIT " . intval($limit);
    }
    
    $categories = $database->fetchAll($query);
    
    // إضافة عدد المنتجات لكل فئة
    foreach ($categories as &$category) {
        $count = $database->fetch(
            "SELECT COUNT(*) as count FROM products WHERE category_id = :id AND is_active = 1 AND (is_visible IS NULL OR is_visible = 1)",
            ['id' => $category['id']]
        );
        $category['count'] = $count['count'];
    }
    
    return $categories;
}

/**
 * دالة للحصول على جميع فئات المنتجات للإدارة (بدون فلتر is_visible)
 */
function getAllProductCategories() {
    global $database;
    
    $categories = $database->fetchAll(
        "SELECT * FROM product_categories WHERE is_active = 1 ORDER BY sort_order"
    );
    
    // إضافة عدد المنتجات لكل فئة
    foreach ($categories as &$category) {
        $count = $database->fetch(
            "SELECT COUNT(*) as count FROM products WHERE category_id = :id AND is_active = 1",
            ['id' => $category['id']]
        );
        $category['count'] = $count['count'];
    }
    
    return $categories;
}

/**
 * دالة للحصول على جميع المنتجات للإدارة (بدون فلتر is_visible)
 */
function getAllProducts($filters = []) {
    global $database;
    
    $where = ['p.is_active = 1'];
    $params = [];
    
    // فلتر الفئة
    if (!empty($filters['category'])) {
        $where[] = 'p.category_id = :category';
        $params['category'] = $filters['category'];
    }
    
    // فلتر البحث
    if (!empty($filters['search'])) {
        $where[] = '(p.name LIKE :search OR p.description LIKE :search)';
        $params['search'] = '%' . $filters['search'] . '%';
    }
    
    // فلتر المميز
    if (isset($filters['featured'])) {
        $where[] = 'p.is_featured = :featured';
        $params['featured'] = $filters['featured'] ? 1 : 0;
    }
    
    // فلتر السعر
    if (!empty($filters['min_price'])) {
        $where[] = 'p.price >= :min_price';
        $params['min_price'] = $filters['min_price'];
    }
    
    if (!empty($filters['max_price'])) {
        $where[] = 'p.price <= :max_price';
        $params['max_price'] = $filters['max_price'];
    }
    
    $whereClause = implode(' AND ', $where);
    
    // ترتيب النتائج
    $orderBy = 'p.sort_order ASC, p.id DESC';
    if (!empty($filters['sort'])) {
        switch ($filters['sort']) {
            case 'name':
                $orderBy = 'p.name ASC';
                break;
            case 'price_low':
                $orderBy = 'p.price ASC';
                break;
            case 'price_high':
                $orderBy = 'p.price DESC';
                break;
            case 'newest':
                $orderBy = 'p.id DESC';
                break;
            case 'rating':
                $orderBy = 'p.rating DESC';
                break;
        }
    }
    
    // حساب العدد الإجمالي
    $totalQuery = "SELECT COUNT(*) as total FROM products p LEFT JOIN product_categories c ON p.category_id = c.id WHERE $whereClause";
    $totalResult = $database->fetch($totalQuery, $params);
    $total = $totalResult['total'] ?? 0;
    
    // تطبيق التصفح
    $page = max(1, intval($filters['page'] ?? 1));
    $limit = intval($filters['limit'] ?? 12);
    $offset = ($page - 1) * $limit;
    
    $query = "SELECT p.*, c.name as category_name FROM products p LEFT JOIN product_categories c ON p.category_id = c.id WHERE $whereClause ORDER BY $orderBy LIMIT $limit OFFSET $offset";
    $products = $database->fetchAll($query, $params);
    
    return [
        'products' => $products,
        'total' => $total,
        'page' => $page,
        'limit' => $limit,
        'pages' => ceil($total / $limit)
    ];
}

/**
 * دالة للحصول على جميع الشهادات للإدارة (بدون فلتر is_visible)
 */
function getAllTestimonials() {
    global $database;
    
    // استخدام جدول reviews بدلاً من testimonials
    $testimonials = $database->fetchAll(
        "SELECT 
            r.id,
            r.name,
            r.email,
            r.comment as content,
            r.rating,
            r.created_at,
            p.name as product_name,
            'عميل' as position,
            '' as image,
            1 as is_active,
            r.id as sort_order
        FROM reviews r 
        LEFT JOIN products p ON r.product_id = p.id 
        WHERE r.status = 'approved' 
        ORDER BY r.rating DESC, r.created_at DESC"
    );
    
    // تحويل البيانات لتتوافق مع التنسيق المطلوب
    $formattedTestimonials = [];
    foreach ($testimonials as $testimonial) {
        $formattedTestimonials[] = [
            'id' => $testimonial['id'],
            'name' => $testimonial['name'],
            'position' => !empty($testimonial['product_name']) ? 'عميل - ' . $testimonial['product_name'] : 'عميل',
            'content' => $testimonial['content'],
            'rating' => $testimonial['rating'],
            'image' => $testimonial['image'],
            'is_active' => $testimonial['is_active'],
            'sort_order' => $testimonial['sort_order']
        ];
    }
    
    // إذا لم توجد تقييمات في قاعدة البيانات، إرجاع بيانات افتراضية
    if (empty($formattedTestimonials)) {
        return [
            [
                'id' => 1,
                'name' => 'أحمد محمد',
                'position' => 'عميل',
                'content' => 'خدمة ممتازة وجودة عالية في المنتجات. أنصح بالتعامل معهم.',
                'rating' => 5,
                'image' => null,
                'is_active' => 1,
                'sort_order' => 1
            ],
            [
                'id' => 2,
                'name' => 'فاطمة أحمد',
                'position' => 'عميل',
                'content' => 'تم التوصيل بسرعة ومهنية عالية. شكراً لكم.',
                'rating' => 5,
                'image' => null,
                'is_active' => 1,
                'sort_order' => 2
            ],
            [
                'id' => 3,
                'name' => 'محمد علي',
                'position' => 'عميل',
                'content' => 'أفضل شركة تعاملت معها. خدمة ما بعد البيع ممتازة.',
                'rating' => 5,
                'image' => null,
                'is_active' => 1,
                'sort_order' => 3
            ]
        ];
    }
    
    return $formattedTestimonials;
}

/**
 * دالة للحصول على التقييمات
 */
function getReviews($productId = null, $limit = null) {
    global $database;
    
    $sql = "SELECT r.*, u.name as user_name FROM reviews r 
            LEFT JOIN users u ON r.user_id = u.id 
            WHERE r.status = 'approved'";
    $params = [];
    
    if ($productId) {
        $sql .= " AND r.product_id = :product_id";
        $params['product_id'] = $productId;
    }
    
    $sql .= " ORDER BY r.created_at DESC";
    
    if ($limit) {
        $sql .= " LIMIT :limit";
        $params['limit'] = (int) $limit;
    }
    
    return $database->fetchAll($sql, $params);
}



/**
 * دالة للحصول على فئات الأسئلة الشائعة
 */
function getFAQCategories() {
    global $database;
    
    // جلب الفئات المميزة من جدول الأسئلة الشائعة
    $categories = $database->fetchAll(
        "SELECT DISTINCT category as name, category as slug 
         FROM faqs 
         WHERE is_active = 1 AND category IS NOT NULL AND category != '' 
         ORDER BY category"
    );
    
    // إضافة عدد الأسئلة لكل فئة
    foreach ($categories as &$category) {
        $count = $database->fetch(
            "SELECT COUNT(*) as count FROM faqs WHERE category = :category AND is_active = 1",
            ['category' => $category['name']]
        );
        $category['count'] = $count['count'];
    }
    
    return $categories;
}

/**
 * دالة للحصول على الأسئلة الشائعة
 */
function getFAQs($limit = null, $category = 'all', $search = '', $order = 'default') {
    global $database;
    
    $sql = "SELECT * FROM faqs WHERE is_active = 1";
    $params = [];
    
    // تصفية حسب التصنيف
    if ($category && $category !== 'all') {
        $sql .= " AND category = :category";
        $params['category'] = $category;
    }
    
    // البحث في الأسئلة والأجوبة
    if ($search && !empty(trim($search))) {
        $searchTerm = '%' . trim($search) . '%';
        $sql .= " AND (question LIKE :search1 OR answer LIKE :search2)";
        $params['search1'] = $searchTerm;
        $params['search2'] = $searchTerm;
    }
    
    // ترتيب النتائج
    switch ($order) {
        case 'popular':
            // استخدام sort_order بدلاً من helpful_yes لأن العمود غير موجود
            $sql .= " ORDER BY sort_order ASC, created_at DESC";
            break;
        case 'recent':
            $sql .= " ORDER BY created_at DESC";
            break;
        default:
            $sql .= " ORDER BY sort_order, created_at";
            break;
    }
    
    // تحديد عدد النتائج
    if ($limit && is_numeric($limit)) {
        $sql .= " LIMIT :limit";
        $params['limit'] = (int) $limit;
    }
    
    return $database->fetchAll($sql, $params);
}





/**
 * دالة للحصول على الموزعين
 */
function getDistributors($region = null, $search = null) {
    global $database;
    
    try {
        $sql = "SELECT * FROM distributors WHERE is_active = 1";
        $params = [];
        
        // التحقق من صحة معامل المنطقة
        if ($region && $region !== 'all' && is_string($region)) {
            $region = trim($region);
            if (!empty($region)) {
                $sql .= " AND region = :region";
                $params['region'] = $region;
            }
        }
        
        // التحقق من صحة معامل البحث
        if ($search && is_string($search)) {
            $search = trim($search);
            if (!empty($search) && strlen($search) <= 100) {
                $searchParam = '%' . $search . '%';
                $sql .= " AND (name LIKE :search1 OR region LIKE :search2 OR address LIKE :search3 OR description LIKE :search4 OR manager LIKE :search5 OR services LIKE :search6)";
                $params['search1'] = $searchParam;
                $params['search2'] = $searchParam;
                $params['search3'] = $searchParam;
                $params['search4'] = $searchParam;
                $params['search5'] = $searchParam;
                $params['search6'] = $searchParam;
            }
        }
        
        $sql .= " ORDER BY sort_order, name";
        
        $distributors = $database->fetchAll($sql, $params);
    
        // تحويل JSON إلى مصفوفات
        foreach ($distributors as &$distributor) {
            $distributor['services'] = $distributor['services'] ? json_decode($distributor['services'], true) ?: [] : [];
            $distributor['working_hours'] = $distributor['working_hours'] ? json_decode($distributor['working_hours'], true) ?: [] : [];
            $distributor['location'] = $distributor['location'] ? json_decode($distributor['location'], true) ?: [] : [];
        }
        
        return $distributors;
        
    } catch (PDOException $e) {
        // تسجيل الخطأ
        error_log("Database error in getDistributors: " . $e->getMessage());
        return [];
    } catch (Exception $e) {
        // تسجيل الخطأ العام
        error_log("General error in getDistributors: " . $e->getMessage());
        return [];
    }
}

/**
 * دالة للحصول على مناطق الموزعين
 */
function getDistributorRegions() {
    global $database;
    
    $regions = $database->fetchAll(
        "SELECT region as name, 
                LOWER(REPLACE(region, ' ', '-')) as slug,
                COUNT(*) as count 
         FROM distributors 
         WHERE is_active = 1 AND region IS NOT NULL AND region != '' 
         GROUP BY region 
         ORDER BY region"
    );
    
    return $regions;
}

/**
 * دالة للحصول على معلومات الاتصال
 */
function getContactInfo() {
    global $database;
    
    try {
        // جلب معلومات الاتصال من جدول contact_info
        $contact_data = $database->fetchAll(
            "SELECT section_key, title, content, data FROM contact_info WHERE is_active = 1"
        );
        
        $contact_info = [];
        
        // تحويل البيانات إلى مصفوفة مفهرسة
        foreach ($contact_data as $item) {
            $section_key = $item['section_key'];
            $contact_info[$section_key] = $item['content'];
            
            // إضافة البيانات الإضافية من JSON
            if (!empty($item['data'])) {
                $json_data = json_decode($item['data'], true);
                if ($json_data) {
                    $contact_info[$section_key . '_data'] = $json_data;
                    
                    // إضافة بعض الحقول المهمة مباشرة للتوافق مع الكود الحالي
                    if ($section_key === 'social_media') {
                        $contact_info['facebook'] = $json_data['facebook'] ?? '';
                        $contact_info['twitter'] = $json_data['twitter'] ?? '';
                        $contact_info['instagram'] = $json_data['instagram'] ?? '';
                        $contact_info['linkedin'] = $json_data['linkedin'] ?? '';
                        $contact_info['youtube'] = $json_data['youtube'] ?? '';
                    } elseif ($section_key === 'map_embed') {
                        $contact_info['map_embed'] = $json_data['embed_code'] ?? '';
                    }
                }
            }
        }
        
        // إضافة القيم الافتراضية إذا لم تكن موجودة
        $defaults = [
            'address' => 'المملكة العربية السعودية، الرياض',
            'phone' => '+966 11 123 4567',
            'email' => '<EMAIL>',
            'working_hours' => 'الأحد - الخميس: 8:00 ص - 6:00 م\nالجمعة: 2:00 م - 6:00 م\nالسبت: مغلق',
            'whatsapp' => '+966501234567',
            'facebook' => '',
            'twitter' => '',
            'instagram' => '',
            'linkedin' => '',
            'youtube' => '',
            'map_embed' => ''
        ];
        
        foreach ($defaults as $key => $default_value) {
            if (!isset($contact_info[$key])) {
                $contact_info[$key] = $default_value;
            }
        }
        
        return $contact_info;
        
    } catch (Exception $e) {
        error_log('خطأ في جلب معلومات الاتصال: ' . $e->getMessage());
        
        // إرجاع القيم الافتراضية في حالة الخطأ
        return [
            'address' => 'المملكة العربية السعودية، الرياض',
            'phone' => '+966 11 123 4567',
            'email' => '<EMAIL>',
            'working_hours' => 'الأحد - الخميس: 8:00 ص - 6:00 م\nالجمعة: 2:00 م - 6:00 م\nالسبت: مغلق',
            'whatsapp' => '+966501234567',
            'facebook' => '',
            'twitter' => '',
            'instagram' => '',
            'linkedin' => '',
            'youtube' => '',
            'map_embed' => ''
        ];
    }
}

/**
 * دالة لحفظ رسالة تواصل
 */
function saveContactMessage($data) {
    global $database;
    
    // إذا كانت البيانات مرسلة من API contact.php
    if (is_string($data)) {
        // البيانات مرسلة كمعاملات منفصلة (name, email, phone, subject, message)
        $args = func_get_args();
        $contactData = [
            'name' => sanitizeInput($args[0]),
            'email' => sanitizeInput($args[1]),
            'phone' => sanitizeInput($args[2] ?? ''),
            'subject' => sanitizeInput($args[3] ?? ''),
            'message' => sanitizeInput($args[4]),
            'type' => sanitizeInput($args[5] ?? 'general'),
            'status' => 'new'
        ];
    } else {
        // البيانات مرسلة كمصفوفة
        $contactData = [
            'name' => sanitizeInput($data['name']),
            'email' => sanitizeInput($data['email']),
            'phone' => sanitizeInput($data['phone'] ?? ''),
            'subject' => sanitizeInput($data['subject'] ?? ''),
            'message' => sanitizeInput($data['message']),
            'type' => sanitizeInput($data['type'] ?? 'general'),
            'status' => 'new'
        ];
    }
    
    return $database->insert('contact_messages', $contactData);
}

/**
 * دالة للحصول على صور المنتج
 */
function getProductImages($productId) {
    global $database;
    
    return $database->fetchAll(
        "SELECT * FROM product_images WHERE product_id = :id ORDER BY sort_order",
        ['id' => $productId]
    );
}

/**
 * دالة للحصول على تقييمات المنتج
 */
function getProductReviews($productId, $limit = null, $offset = 0) {
    global $database;
    
    $sql = "SELECT r.*, 
                   COALESCE(u.name, r.name) as name,
                   u.name as user_name,
                   r.email,
                   r.title,
                   r.comment,
                   r.rating,
                   r.created_at,
                   r.is_approved,
                   r.is_rejected
            FROM reviews r 
            LEFT JOIN users u ON r.user_id = u.id 
            WHERE r.product_id = :product_id AND r.is_approved = 1
            ORDER BY r.created_at DESC";
    
    $params = ['product_id' => $productId];
    
    if ($limit) {
        $sql .= " LIMIT :limit OFFSET :offset";
        $params['limit'] = (int) $limit;
        $params['offset'] = (int) $offset;
    }
    
    return $database->fetchAll($sql, $params);
}

/**
 * دالة لحساب عدد التقييمات المعتمدة للمنتج
 */
function getProductReviewsCount($productId) {
    global $database;
    
    $result = $database->fetch(
        "SELECT COUNT(*) as total FROM reviews WHERE product_id = :product_id AND is_approved = 1",
        ['product_id' => $productId]
    );
    
    return $result ? (int) $result['total'] : 0;
}

/**
 * دالة لجلب جميع تقييمات المنتج (بما في ذلك غير المعتمدة) - للإدارة
 */
function getAllProductReviews($productId, $limit = null) {
    global $database;
    
    $sql = "SELECT r.*, 
                   COALESCE(u.name, r.name) as name,
                   u.name as user_name,
                   r.email,
                   r.title,
                   r.comment,
                   r.rating,
                   r.created_at,
                   r.is_approved,
                   r.is_rejected,
                   CASE 
                       WHEN r.is_approved = 1 THEN 'معتمد'
                       WHEN r.is_rejected = 1 THEN 'مرفوض'
                       ELSE 'في الانتظار'
                   END as status_text
            FROM reviews r 
            LEFT JOIN users u ON r.user_id = u.id 
            WHERE r.product_id = :product_id
            ORDER BY r.created_at DESC";
    
    $params = ['product_id' => $productId];
    
    if ($limit) {
        $sql .= " LIMIT :limit";
        $params['limit'] = (int) $limit;
    }
    
    return $database->fetchAll($sql, $params);
}

/**
 * دالة لحفظ تقييم المنتج
 */
function saveProductReview($productId, $rating, $title = '', $comment = '') {
    global $database;
    
    // التحقق من تسجيل الدخول
    if (!isLoggedIn()) {
        return 'يجب تسجيل الدخول أولاً لإضافة تقييم';
    }
    
    $user = getCurrentUser();
    
    // التحقق من عدم وجود تقييم سابق من نفس المستخدم
    $existing = $database->fetch(
        "SELECT id FROM reviews WHERE product_id = :product_id AND user_id = :user_id",
        ['product_id' => $productId, 'user_id' => $user['id']]
    );
    
    if ($existing) {
        return 'لقد قمت بتقييم هذا المنتج مسبقاً';
    }
    
    // التحقق من إعداد التفعيل التلقائي للتقييمات
    $autoApproval = getSetting('reviews_auto_approval', false);
    
    try {
        $result = $database->insert('reviews', [
            'product_id' => (int) $productId,
            'user_id' => $user['id'],
            'name' => $user['name'] ?? $user['username'] ?? 'مستخدم',
            'email' => $user['email'] ?? '',
            'rating' => (int) $rating,
            'title' => sanitizeInput($title),
            'comment' => sanitizeInput($comment),
            'is_approved' => $autoApproval ? 1 : 0,
            'is_rejected' => 0,
            'created_at' => date('Y-m-d H:i:s')
        ]);
        
        if ($result && $result > 0) {
            // تحديث تقييم المنتج إذا تم اعتماد التقييم
            if ($autoApproval) {
                updateProductRating($productId);
            }
            return true;
        } else {
            return 'حدث خطأ أثناء حفظ التقييم';
        }
    } catch (Exception $e) {
        error_log('خطأ في حفظ التقييم: ' . $e->getMessage());
        return 'حدث خطأ أثناء حفظ التقييم';
    }
}

/**
 * دالة لتحديث تقييم المنتج
 */
function updateProductRating($productId) {
    global $database;
    
    $stats = $database->fetch(
        "SELECT AVG(rating) as avg_rating, COUNT(*) as review_count 
         FROM reviews 
         WHERE product_id = :id AND is_approved = 1",
        ['id' => $productId]
    );
    
    if ($stats) {
        $database->update('products', [
            'average_rating' => round($stats['avg_rating'], 1),
            'reviews_count' => $stats['review_count']
        ], 'id = :id', ['id' => $productId]);
    }
}

/**
 * دالة لحفظ تقييم
 */
function saveReview($data) {
    global $database;
    
    if (!isLoggedIn()) {
        return false;
    }
    
    $user = getCurrentUser();
    
    // التحقق من عدم وجود تقييم سابق
    $existing = $database->fetch(
        "SELECT id FROM reviews WHERE product_id = :product_id AND user_id = :user_id",
        ['product_id' => $data['product_id'], 'user_id' => $user['id']]
    );
    
    if ($existing) {
        return false; // المستخدم قيم المنتج مسبقاً
    }
    
    return $database->insert('reviews', [
        'product_id' => (int) $data['product_id'],
        'user_id' => $user['id'],
        'name' => $user['name'] ?? $user['username'] ?? 'مستخدم',
        'email' => $user['email'] ?? '',
        'rating' => (int) $data['rating'],
        'title' => sanitizeInput($data['title'] ?? ''),
        'comment' => sanitizeInput($data['comment'] ?? ''),
        'is_approved' => 0,
        'is_rejected' => 0,
        'created_at' => date('Y-m-d H:i:s')
    ]);
}

/**
 * دالة لحفظ تقييم من زائر غير مسجل
 */
function saveGuestReview($product_id, $customer_name, $customer_email, $rating, $review_text, $review_title = '') {
    global $database;
    
    // التحقق من وجود تقييم سابق من نفس البريد الإلكتروني للمنتج
    $existing = $database->fetch(
        "SELECT id FROM reviews WHERE product_id = :product_id AND email = :email",
        ['product_id' => $product_id, 'email' => $customer_email]
    );
    
    if ($existing) {
        return 'لقد قمت بتقييم هذا المنتج مسبقاً';
    }
    
    // التحقق من إعداد التفعيل التلقائي للتقييمات
    $autoApproval = getSetting('reviews_auto_approval', false);
    
    try {
        $result = $database->insert('reviews', [
            'product_id' => (int) $product_id,
            'user_id' => null, // زائر غير مسجل
            'name' => sanitizeInput($customer_name),
            'email' => sanitizeInput($customer_email),
            'rating' => (int) $rating,
            'title' => sanitizeInput($review_title),
            'comment' => sanitizeInput($review_text),
            'is_approved' => $autoApproval ? 1 : 0,
            'is_rejected' => 0,
            'created_at' => date('Y-m-d H:i:s')
        ]);
        
        if ($result && $result > 0) {
            // تحديث تقييم المنتج إذا تم اعتماد التقييم
            if ($autoApproval) {
                updateProductRating($product_id);
            }
            return true;
        } else {
            return 'حدث خطأ أثناء حفظ التقييم';
        }
    } catch (Exception $e) {
        error_log('Error saving guest review: ' . $e->getMessage());
        return false;
    }
}

/**
 * دالة لحفظ تقييم المستخدم المسجل
 */
function saveUserReview($product_id, $user_id, $customer_name, $customer_email, $rating, $review_text, $review_title = '') {
    global $database;
    
    // التحقق من وجود تقييم سابق من نفس المستخدم للمنتج
    $existing = $database->fetch(
        "SELECT id FROM reviews WHERE product_id = :product_id AND user_id = :user_id",
        ['product_id' => $product_id, 'user_id' => $user_id]
    );
    
    if ($existing) {
        return 'لقد قمت بتقييم هذا المنتج مسبقاً';
    }
    
    // التحقق من إعداد التفعيل التلقائي للتقييمات
    $autoApproval = getSetting('reviews_auto_approval', false);
    
    try {
        $result = $database->insert('reviews', [
            'product_id' => (int) $product_id,
            'user_id' => (int) $user_id, // معرف المستخدم المسجل
            'name' => sanitizeInput($customer_name),
            'email' => sanitizeInput($customer_email),
            'rating' => (int) $rating,
            'title' => sanitizeInput($review_title),
            'comment' => sanitizeInput($review_text),
            'is_approved' => $autoApproval ? 1 : 0,
            'is_rejected' => 0,
            'created_at' => date('Y-m-d H:i:s')
        ]);
        
        if ($result && $result > 0) {
            // تحديث تقييم المنتج إذا تم اعتماد التقييم
            if ($autoApproval) {
                updateProductRating($product_id);
            }
            return $result; // إرجاع معرف التقييم
        } else {
            return 'حدث خطأ أثناء حفظ التقييم';
        }
    } catch (Exception $e) {
        error_log('Error saving user review: ' . $e->getMessage());
        return false;
    }
}





/**
 * دالة لتحميل ملف
 */
function uploadFile($file, $directory = 'general', $allowedTypes = null) {
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        return ['success' => false, 'error' => 'لم يتم تحديد ملف'];
    }
    
    // التحقق من حجم الملف
    if ($file['size'] > MAX_FILE_SIZE) {
        return ['success' => false, 'error' => 'حجم الملف كبير جداً'];
    }
    
    // التحقق من نوع الملف
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    $allowedTypes = $allowedTypes ?: ALLOWED_IMAGE_TYPES;
    
    if (!in_array($extension, $allowedTypes)) {
        return ['success' => false, 'error' => 'نوع الملف غير مسموح'];
    }
    
    // إنشاء اسم ملف فريد
    $filename = time() . '_' . uniqid() . '.' . $extension;
    $uploadDir = UPLOADS_PATH . '/' . $directory;
    
    // إنشاء المجلد إذا لم يكن موجوداً
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    $filePath = $uploadDir . '/' . $filename;
    
    if (move_uploaded_file($file['tmp_name'], $filePath)) {
        return [
            'success' => true,
            'filename' => $filename,
            'path' => $filePath,
            'url' => UPLOADS_URL . '/' . $directory . '/' . $filename
        ];
    }
    
    return ['success' => false, 'error' => 'فشل في تحميل الملف'];
}

/**
 * دالة لحذف ملف
 */
function deleteFile($filePath) {
    if (file_exists($filePath)) {
        return unlink($filePath);
    }
    return false;
}

/**
 * دالة لإرسال بريد إلكتروني
 */
function sendEmail($to, $subject, $message, $isHtml = true) {
    // يمكن استخدام PHPMailer أو أي مكتبة أخرى
    // هذا مثال بسيط باستخدام mail() function
    
    $headers = [];
    $headers[] = 'From: ' . SMTP_FROM_NAME . ' <' . SMTP_FROM_EMAIL . '>';
    $headers[] = 'Reply-To: ' . SMTP_FROM_EMAIL;
    $headers[] = 'X-Mailer: PHP/' . phpversion();
    
    if ($isHtml) {
        $headers[] = 'Content-Type: text/html; charset=UTF-8';
    } else {
        $headers[] = 'Content-Type: text/plain; charset=UTF-8';
    }
    
    return mail($to, $subject, $message, implode("\r\n", $headers));
}

/**
 * دالة للتصفح (Pagination)
 */
function paginate($totalItems, $itemsPerPage, $currentPage = 1) {
    $totalPages = ceil($totalItems / $itemsPerPage);
    $currentPage = max(1, min($totalPages, $currentPage));
    $offset = ($currentPage - 1) * $itemsPerPage;
    
    return [
        'total_items' => $totalItems,
        'items_per_page' => $itemsPerPage,
        'total_pages' => $totalPages,
        'current_page' => $currentPage,
        'offset' => $offset,
        'has_previous' => $currentPage > 1,
        'has_next' => $currentPage < $totalPages,
        'previous_page' => $currentPage > 1 ? $currentPage - 1 : null,
        'next_page' => $currentPage < $totalPages ? $currentPage + 1 : null
    ];
}

/**
 * دالة للحصول على شرائح البانر الرئيسي
 */
function getHeroSlides() {
    global $database;
    
    $slides = $database->fetchAll("SELECT * FROM hero_slides WHERE is_active = 1 ORDER BY sort_order, created_at DESC");
    
    // إذا لم توجد شرائح في قاعدة البيانات، إرجاع بيانات وهمية
    if (empty($slides)) {
        return [
            [
                'id' => 1,
                'title' => 'مرحباً بكم في Green Line',
                'subtitle' => 'أفضل حلول التكييف والتبريد',
                'image' => '/assets/images/hero-slide-1.jpg',
                'button_text' => 'تصفح منتجاتنا',
                'button_link' => '/products',
                'is_active' => true
            ],
            [
                'id' => 2,
                'title' => 'جودة عالية وخدمة ممتازة',
                'subtitle' => 'نقدم أحدث تقنيات التبريد والتكييف',
                'image' => '/assets/images/hero-slide-2.jpg',
                'button_text' => 'اتصل بنا',
                'button_link' => '/contact',
                'is_active' => true
            ]
        ];
    }
    
    return $slides;
}

/**
 * دالة للحصول على المميزات
 */
function getFeatures() {
    // استخدام البيانات الجديدة من قاعدة البيانات
    return getWhyChooseUsFeatures();
}

/**
 * دالة للحصول على الإحصائيات
 */
function getStatistics() {
    global $database;
    
    // جلب الإحصائيات من قاعدة البيانات
    $stats = $database->fetch("SELECT * FROM company_stats WHERE id = 1");
    
    // إذا لم توجد إحصائيات في قاعدة البيانات، إرجاع بيانات افتراضية
    if (!$stats) {
        return [
            [
                'title' => 'عملاء راضون',
                'value' => '500+',
                'icon' => 'fas fa-users'
            ],
            [
                'title' => 'مشاريع مكتملة',
                'value' => '250+',
                'icon' => 'fas fa-project-diagram'
            ],
            [
                'title' => 'سنوات خبرة',
                'value' => '10+',
                'icon' => 'fas fa-calendar-alt'
            ],
            [
                'title' => 'أعضاء الفريق',
                'value' => '25+',
                'icon' => 'fas fa-user-tie'
            ]
        ];
    }
    
    // تحويل البيانات من قاعدة البيانات إلى التنسيق المطلوب
    return [
        [
            'title' => 'عملاء راضون',
            'value' => $stats['happy_clients'] . '+',
            'icon' => 'fas fa-users'
        ],
        [
            'title' => 'مشاريع مكتملة',
            'value' => $stats['completed_projects'] . '+',
            'icon' => 'fas fa-project-diagram'
        ],
        [
            'title' => 'سنوات خبرة',
            'value' => $stats['years_experience'] . '+',
            'icon' => 'fas fa-calendar-alt'
        ],
        [
            'title' => 'أعضاء الفريق',
            'value' => $stats['team_members'] . '+',
            'icon' => 'fas fa-user-tie'
        ]
    ];
}

/**
 * تحديث دالة getTestimonials لتتوافق مع الاستدعاء في home.php
 */
function getTestimonials($limit = null, $filter = 'all') {
    global $database;
    
    // استخدام جدول reviews بدلاً من testimonials
    $sql = "SELECT 
                r.id,
                r.name,
                r.email,
                r.comment as content,
                r.rating,
                r.created_at,
                p.name as product_name,
                'عميل' as position,
                '' as image
            FROM reviews r 
            LEFT JOIN products p ON r.product_id = p.id 
            WHERE r.is_approved = 1 AND r.is_rejected = 0";
    $params = [];
    
    // Apply filter
    switch($filter) {
        case 'best':
            $sql .= " AND r.rating = 5";
            $sql .= " ORDER BY r.rating DESC, r.created_at DESC";
            break;
        case 'random':
            $sql .= " ORDER BY RAND()";
            if ($limit === null) {
                $limit = 3; // Default to 3 for random
            }
            break;
        default:
            $sql .= " ORDER BY r.rating DESC, r.created_at DESC";
            break;
    }
    
    if ($limit) {
        $sql .= " LIMIT ?";
        $params[] = (int) $limit;
    }
    
    $testimonials = $database->fetchAll($sql, $params);
    
    // تحويل البيانات لتتوافق مع التنسيق المطلوب
    $formattedTestimonials = [];
    foreach ($testimonials as $testimonial) {
        $formattedTestimonials[] = [
            'id' => $testimonial['id'],
            'name' => $testimonial['name'],
            'position' => !empty($testimonial['product_name']) ? 'عميل - ' . $testimonial['product_name'] : 'عميل',
            'content' => $testimonial['content'],
            'rating' => $testimonial['rating'],
            'image' => $testimonial['image'],
            'created_at' => $testimonial['created_at'],
            'is_featured' => $testimonial['rating'] >= 4
        ];
    }
    
    // إذا لم توجد تقييمات في قاعدة البيانات، إرجاع بيانات وهمية
    if (empty($formattedTestimonials)) {
        return [
            [
                'id' => 1,
                'name' => 'أحمد محمد',
                'position' => 'عميل',
                'content' => 'خدمة ممتازة وجودة عالية، أنصح بالتعامل معهم',
                'rating' => 5,
                'image' => '',
                'is_featured' => true
            ],
            [
                'id' => 2,
                'name' => 'فاطمة أحمد',
                'position' => 'عميل',
                'content' => 'تركيب سريع ومهني، والمنتج يعمل بكفاءة عالية',
                'rating' => 5,
                'image' => '',
                'is_featured' => true
            ]
        ];
    }
    
    return $formattedTestimonials;
}

/**
 * دالة للحصول على محتوى صفحة من نحن
 */
function getAboutContent() {
    global $database;
    
    $content = $database->fetch("SELECT * FROM about_content WHERE id = 1");
    
    // إذا لم يوجد محتوى في قاعدة البيانات، إرجاع بيانات افتراضية
    if (!$content) {
        return [
            'hero_subtitle' => 'نحن شركة رائدة في مجال الخطوط الخضراء، نقدم حلولاً مبتكرة وخدمات عالية الجودة',
            'story' => 'تأسست شركة الخطوط الخضراء بهدف تقديم حلول مبتكرة ومستدامة في مجال التكنولوجيا والخدمات. منذ انطلاقتنا، نسعى لتحقيق التميز والريادة في السوق من خلال فريق عمل متخصص وخبرات متراكمة.\n\nنؤمن بأن النجاح يأتي من خلال الالتزام بالجودة والابتكار المستمر، ولذلك نستثمر في أحدث التقنيات ونطور مهارات فريقنا باستمرار لنقدم أفضل الخدمات لعملائنا.\n\nرؤيتنا هي أن نكون الخيار الأول للعملاء الذين يبحثون عن الجودة والموثوقية، ونسعى لبناء شراكات طويلة الأمد تقوم على الثقة والتميز.',
            'story_image' => '/assets/images/about-story.jpg',
            'mission' => 'تقديم حلول مبتكرة وخدمات عالية الجودة تلبي احتياجات عملائنا وتساهم في نجاحهم وتطوير أعمالهم بأفضل الطرق الممكنة.',
            'vision' => 'أن نكون الشركة الرائدة والمفضلة في مجالنا، ونحقق التميز من خلال الابتكار المستمر والجودة العالية في جميع خدماتنا.',
            'values' => 'النزاهة والشفافية في التعامل، الالتزام بالجودة والتميز، الابتكار والتطوير المستمر، واحترام العملاء وتقدير ثقتهم.'
        ];
    }
    
    return $content;
}

/**
 * دالة للحصول على أعضاء الفريق
 */
function getTeamMembers() {
    global $database;
    
    $members = $database->fetchAll("SELECT * FROM team_members WHERE is_active = 1 ORDER BY sort_order");
    
    // إذا لم يوجد أعضاء في قاعدة البيانات، إرجاع مصفوفة فارغة
    if (empty($members)) {
        return [];
    }
    
    return $members;
}

/**
 * دالة للحصول على إحصائيات الشركة
 */
function getCompanyStats() {
    global $database;
    
    $stats = $database->fetch("SELECT * FROM company_stats WHERE id = 1");
    
    // إذا لم توجد إحصائيات في قاعدة البيانات، إرجاع بيانات افتراضية
    if (!$stats) {
        return [
            'happy_clients' => 500,
            'completed_projects' => 250,
            'years_experience' => 10,
            'team_members' => 25
        ];
    }
    
    return $stats;
}

/**
 * دالة للحصول على قيمنا ومبادئنا
 */
function getValuesPrinciples() {
    global $database;
    
    $values = $database->fetchAll("SELECT * FROM values_principles WHERE is_active = 1 ORDER BY sort_order");
    
    // إذا لم توجد قيم في قاعدة البيانات، إرجاع بيانات افتراضية
    if (empty($values)) {
        return [
            [
                'id' => 1,
                'title' => 'الجودة',
                'description' => 'نلتزم بأعلى معايير الجودة في جميع منتجاتنا وخدماتنا',
                'icon' => 'fas fa-star',
                'sort_order' => 1
            ],
            [
                'id' => 2,
                'title' => 'الابتكار',
                'description' => 'نستثمر في أحدث التقنيات لتقديم حلول مبتكرة ومتطورة',
                'icon' => 'fas fa-lightbulb',
                'sort_order' => 2
            ],
            [
                'id' => 3,
                'title' => 'خدمة العملاء',
                'description' => 'رضا عملائنا هو أولويتنا القصوى في كل ما نقوم به',
                'icon' => 'fas fa-users',
                'sort_order' => 3
            ],
            [
                'id' => 4,
                'title' => 'الاستدامة',
                'description' => 'نحرص على تقديم منتجات صديقة للبيئة ومستدامة',
                'icon' => 'fas fa-leaf',
                'sort_order' => 4
            ]
        ];
    }
    
    return $values;
}

/**
 * دالة للحصول على ميزات "لماذا تختارنا؟"
 */
function getWhyChooseUsFeatures() {
    global $database;
    
    $features = $database->fetchAll("SELECT * FROM why_choose_us WHERE is_active = 1 ORDER BY sort_order");
    
    // إذا لم توجد ميزات في قاعدة البيانات، إرجاع بيانات افتراضية
    if (empty($features)) {
        return [
            [
                'id' => 1,
                'title' => 'الخبرة والاحترافية',
                'description' => 'فريق عمل متخصص وذو خبرة عالية في مجال عمله',
                'icon' => 'fas fa-user-tie',
                'background_color' => '#047857',
                'sort_order' => 1
            ],
            [
                'id' => 2,
                'title' => 'الجودة العالية',
                'description' => 'نلتزم بأعلى معايير الجودة في جميع خدماتنا ومنتجاتنا',
                'icon' => 'fas fa-award',
                'background_color' => '#059669',
                'sort_order' => 2
            ],
            [
                'id' => 3,
                'title' => 'الدعم المستمر',
                'description' => 'نقدم دعماً فنياً متواصلاً على مدار الساعة',
                'icon' => 'fas fa-headset',
                'background_color' => '#10b981',
                'sort_order' => 3
            ]
        ];
    }
    
    return $features;
}

/**
 * دالة للحصول على إعدادات قسم الاستشارة المجانية
 */
function getConsultationSection() {
    global $database;
    
    $section = $database->fetch("SELECT * FROM consultation_section WHERE id = 1");
    
    // إذا لم توجد إعدادات في قاعدة البيانات، إرجاع بيانات افتراضية
    if (!$section) {
        return [
            'title' => 'هل تحتاج إلى استشارة مجانية؟',
            'subtitle' => '',
            'description' => 'نحن هنا لمساعدتك في اختيار أفضل الحلول لاحتياجاتك. احصل على استشارة مجانية من خبرائنا.',
            'button_text' => 'احصل على استشارة مجانية',
            'button_link' => '/contact',
            'background_color' => '#047857',
            'text_color' => '#FFFFFF',
            'is_active' => 1
        ];
    }
    
    return $section;
}

/**
 * دالة للحصول على إعدادات قسم "لماذا تختارنا؟" للصفحة الرئيسية
 */
function getHomeWhyChooseUsSettings() {
    global $database;
    
    $settings = $database->fetch("SELECT * FROM home_why_choose_us_settings WHERE id = 1");
    
    // إذا لم توجد إعدادات في قاعدة البيانات، إرجاع بيانات افتراضية
    if (!$settings) {
        return [
            'max_items' => 6,
            'is_visible' => 1,
            'title' => 'لماذا تختارنا؟',
            'subtitle' => 'نحن نقدم أفضل الحلول والخدمات لعملائنا بجودة عالية وأسعار تنافسية'
        ];
    }
    
    return $settings;
}

/**
 * دالة للحصول على ميزات "لماذا تختارنا؟" للصفحة الرئيسية مع تطبيق الحد الأقصى
 */
function getHomeWhyChooseUsFeatures() {
    global $database;
    
    // الحصول على إعدادات القسم
    $settings = getHomeWhyChooseUsSettings();
    
    // إذا كان القسم غير مرئي، إرجاع مصفوفة فارغة
    if (!$settings['is_visible']) {
        return [];
    }
    
    // الحصول على الميزات مع تطبيق الحد الأقصى
    $maxItems = $settings['max_items'] ?? 6;
    $features = $database->fetchAll(
        "SELECT * FROM why_choose_us WHERE is_active = 1 ORDER BY sort_order LIMIT :limit",
        ['limit' => (int) $maxItems]
    );
    
    return $features;
}

/**
 * دالة للحصول على عناصر مسيرتنا (التايم لاين) - للعرض العام
 */
function getOurJourney() {
    global $database;
    
    $journey = $database->fetchAll("SELECT * FROM our_journey WHERE is_active = 1 ORDER BY sort_order");
    
    // إذا لم توجد عناصر في قاعدة البيانات، إرجاع بيانات افتراضية
    if (empty($journey)) {
        return [
            [
                'id' => 1,
                'year' => '2010',
                'title' => 'تأسيس الشركة',
                'description' => 'بدأت رحلتنا برؤية واضحة لتقديم حلول تقنية مبتكرة',
                'icon' => 'fas fa-rocket',
                'background_color' => '#047857',
                'border_color' => '#10B981',
                'text_color' => '#FFFFFF',
                'is_future' => 0,
                'sort_order' => 1
            ],
            [
                'id' => 2,
                'year' => '2013',
                'title' => 'التوسع الأول',
                'description' => 'افتتحنا أول فرع لنا وضاعفنا حجم فريق العمل',
                'icon' => 'fas fa-building',
                'background_color' => '#047857',
                'border_color' => '#10B981',
                'text_color' => '#FFFFFF',
                'is_future' => 0,
                'sort_order' => 2
            ],
            [
                'id' => 3,
                'year' => '2016',
                'title' => 'الشراكات الاستراتيجية',
                'description' => 'أقمنا شراكات مع كبرى الشركات التقنية العالمية',
                'icon' => 'fas fa-handshake',
                'background_color' => '#047857',
                'border_color' => '#10B981',
                'text_color' => '#FFFFFF',
                'is_future' => 0,
                'sort_order' => 3
            ],
            [
                'id' => 4,
                'year' => '2019',
                'title' => 'الابتكار والتطوير',
                'description' => 'أطلقنا منصتنا التقنية المتطورة وحصلنا على جوائز الابتكار',
                'icon' => 'fas fa-lightbulb',
                'background_color' => '#047857',
                'border_color' => '#10B981',
                'text_color' => '#FFFFFF',
                'is_future' => 0,
                'sort_order' => 4
            ],
            [
                'id' => 5,
                'year' => '2022',
                'title' => 'التحول الرقمي',
                'description' => 'قدنا مشاريع التحول الرقمي لأكثر من 100 شركة',
                'icon' => 'fas fa-digital-tachograph',
                'background_color' => '#047857',
                'border_color' => '#10B981',
                'text_color' => '#FFFFFF',
                'is_future' => 0,
                'sort_order' => 5
            ],
            [
                'id' => 6,
                'year' => '2024',
                'title' => 'المستقبل',
                'description' => 'نتطلع لتوسيع خدماتنا عالمياً وتطوير تقنيات الذكاء الاصطناعي',
                'icon' => 'fas fa-star',
                'background_color' => '#6366f1',
                'border_color' => '#8b5cf6',
                'text_color' => '#FFFFFF',
                'is_future' => 1,
                'sort_order' => 6
            ]
        ];
    }
    
    return $journey;
}

/**
 * دالة للحصول على جميع عناصر مسيرتنا (للإدارة) - تشمل النشطة وغير النشطة
 */
function getAllOurJourney() {
    global $database;
    
    $journey = $database->fetchAll("SELECT * FROM our_journey ORDER BY sort_order");
    
    // إذا لم توجد عناصر في قاعدة البيانات، إرجاع بيانات افتراضية
    if (empty($journey)) {
        return [
            [
                'id' => 1,
                'year' => '2010',
                'title' => 'تأسيس الشركة',
                'description' => 'بدأت رحلتنا برؤية واضحة لتقديم حلول تقنية مبتكرة',
                'icon' => 'fas fa-rocket',
                'background_color' => '#047857',
                'border_color' => '#10B981',
                'text_color' => '#FFFFFF',
                'is_future' => 0,
                'sort_order' => 1
            ],
            [
                'id' => 2,
                'year' => '2013',
                'title' => 'التوسع الأول',
                'description' => 'افتتحنا أول فرع لنا وضاعفنا حجم فريق العمل',
                'icon' => 'fas fa-building',
                'background_color' => '#047857',
                'border_color' => '#10B981',
                'text_color' => '#FFFFFF',
                'is_future' => 0,
                'sort_order' => 2
            ],
            [
                'id' => 3,
                'year' => '2016',
                'title' => 'الشراكات الاستراتيجية',
                'description' => 'أقمنا شراكات مع كبرى الشركات التقنية العالمية',
                'icon' => 'fas fa-handshake',
                'background_color' => '#047857',
                'border_color' => '#10B981',
                'text_color' => '#FFFFFF',
                'is_future' => 0,
                'sort_order' => 3
            ],
            [
                'id' => 4,
                'year' => '2019',
                'title' => 'الابتكار والتطوير',
                'description' => 'أطلقنا منصتنا التقنية المتطورة وحصلنا على جوائز الابتكار',
                'icon' => 'fas fa-lightbulb',
                'background_color' => '#047857',
                'border_color' => '#10B981',
                'text_color' => '#FFFFFF',
                'is_future' => 0,
                'sort_order' => 4
            ],
            [
                'id' => 5,
                'year' => '2022',
                'title' => 'التحول الرقمي',
                'description' => 'قدنا مشاريع التحول الرقمي لأكثر من 100 شركة',
                'icon' => 'fas fa-digital-tachograph',
                'background_color' => '#047857',
                'border_color' => '#10B981',
                'text_color' => '#FFFFFF',
                'is_future' => 0,
                'sort_order' => 5
            ],
            [
                'id' => 6,
                'year' => '2024',
                'title' => 'المستقبل',
                'description' => 'نتطلع لتوسيع خدماتنا عالمياً وتطوير تقنيات الذكاء الاصطناعي',
                'icon' => 'fas fa-star',
                'background_color' => '#6366f1',
                'border_color' => '#8b5cf6',
                'text_color' => '#FFFFFF',
                'is_future' => 1,
                'sort_order' => 6
            ]
        ];
    }
    
    return $journey;
}

/**
 * دالة للحصول على عدد التقييمات لسؤال معين
 */
function getFAQRatingCounts($faq_id) {
    global $database;
    
    try {
        $result = $database->fetch("
            SELECT 
                COUNT(CASE WHEN rating = 'yes' THEN 1 END) as yes_count,
                COUNT(CASE WHEN rating = 'no' THEN 1 END) as no_count,
                COUNT(*) as total_count
            FROM faq_ratings 
            WHERE faq_id = :faq_id
        ", ['faq_id' => $faq_id]);
        
        return [
            'yes_count' => (int) $result['yes_count'],
            'no_count' => (int) $result['no_count'],
            'total_count' => (int) $result['total_count']
        ];
    } catch (Exception $e) {
        error_log('Error getting FAQ rating counts: ' . $e->getMessage());
        return [
            'yes_count' => 0,
            'no_count' => 0,
            'total_count' => 0
        ];
    }
}

/**
 * دالة للحصول على عدد التقييمات لجميع الأسئلة
 */
function getAllFAQRatingCounts() {
    global $database;
    
    try {
        $results = $database->fetchAll("
            SELECT 
                faq_id,
                COUNT(CASE WHEN rating = 'yes' THEN 1 END) as yes_count,
                COUNT(CASE WHEN rating = 'no' THEN 1 END) as no_count,
                COUNT(*) as total_count
            FROM faq_ratings 
            GROUP BY faq_id
        ");
        
        $counts = [];
        foreach ($results as $result) {
            $counts[$result['faq_id']] = [
                'yes_count' => (int) $result['yes_count'],
                'no_count' => (int) $result['no_count'],
                'total_count' => (int) $result['total_count']
            ];
        }
        
        return $counts;
    } catch (Exception $e) {
        error_log('Error getting all FAQ rating counts: ' . $e->getMessage());
        return [];
    }
}

/**
 * دالة محسنة للحصول على معلومات التواصل مع تفاصيل إضافية
 */
function getDetailedContactInfo() {
    global $database;
    
    try {
        // جلب معلومات الاتصال من جدول contact_info
        $contact_data = $database->fetchAll(
            "SELECT section_key, title, content, data FROM contact_info WHERE is_active = 1"
        );
        
        $contact_info = [];
        
        // تحويل البيانات إلى مصفوفة مفهرسة مع تفاصيل إضافية
        foreach ($contact_data as $item) {
            $section_key = $item['section_key'];
            $contact_info[$section_key] = [
                'title' => $item['title'],
                'content' => $item['content'],
                'data' => !empty($item['data']) ? json_decode($item['data'], true) : []
            ];
        }
        
        // إضافة دوال مساعدة للوصول السريع
        $result = [
            'raw_data' => $contact_info,
            
            // العنوان
            'address' => $contact_info['address']['content'] ?? 'المملكة العربية السعودية، الرياض',
            'address_details' => $contact_info['address']['data'] ?? [],
            
            // الهاتف
            'phone' => $contact_info['phone']['content'] ?? '+966 11 123 4567',
            'phone_details' => $contact_info['phone']['data'] ?? [],
            'phone_primary' => $contact_info['phone']['data']['primary'] ?? $contact_info['phone']['content'] ?? '+966 11 123 4567',
            'phone_secondary' => $contact_info['phone']['data']['secondary'] ?? '',
            'phone_mobile' => $contact_info['phone']['data']['mobile'] ?? '',
            'phone_fax' => $contact_info['phone']['data']['fax'] ?? '',
            
            // البريد الإلكتروني
            'email' => $contact_info['email']['content'] ?? '<EMAIL>',
            'email_details' => $contact_info['email']['data'] ?? [],
            'email_general' => $contact_info['email']['data']['general'] ?? $contact_info['email']['content'] ?? '<EMAIL>',
            'email_sales' => $contact_info['email']['data']['sales'] ?? '',
            'email_support' => $contact_info['email']['data']['support'] ?? '',
            'email_hr' => $contact_info['email']['data']['hr'] ?? '',
            
            // ساعات العمل
            'working_hours' => $contact_info['working_hours']['content'] ?? 'الأحد - الخميس: 8:00 ص - 6:00 م',
            'working_hours_details' => $contact_info['working_hours']['data'] ?? [],
            
            // واتساب
            'whatsapp' => $contact_info['whatsapp']['content'] ?? '+966501234567',
            'whatsapp_details' => $contact_info['whatsapp']['data'] ?? [],
            
            // وسائل التواصل الاجتماعي
            'social_media' => $contact_info['social_media']['data'] ?? [],
            'facebook' => $contact_info['social_media']['data']['facebook'] ?? '',
            'twitter' => $contact_info['social_media']['data']['twitter'] ?? '',
            'instagram' => $contact_info['social_media']['data']['instagram'] ?? '',
            'linkedin' => $contact_info['social_media']['data']['linkedin'] ?? '',
            'youtube' => $contact_info['social_media']['data']['youtube'] ?? '',
            
            // الخريطة
            'map_embed' => $contact_info['map_embed']['data']['embed_code'] ?? '',
            'map_coordinates' => $contact_info['map_embed']['data']['coordinates'] ?? [],
            
            // معلومات الشركة
            'company_info' => $contact_info['company_info']['data'] ?? [],
            'company_name' => $contact_info['company_info']['data']['company_name'] ?? 'شركة جرين لاين',
            'commercial_register' => $contact_info['company_info']['data']['commercial_register'] ?? '',
            'tax_number' => $contact_info['company_info']['data']['tax_number'] ?? '',
            'established_year' => $contact_info['company_info']['data']['established_year'] ?? '',
            'license_number' => $contact_info['company_info']['data']['license_number'] ?? ''
        ];
        
        return $result;
        
    } catch (Exception $e) {
        error_log('خطأ في جلب معلومات الاتصال المفصلة: ' . $e->getMessage());
        
        // إرجاع القيم الافتراضية في حالة الخطأ
        return [
            'address' => 'المملكة العربية السعودية، الرياض',
            'phone' => '+966 11 123 4567',
            'email' => '<EMAIL>',
            'working_hours' => 'الأحد - الخميس: 8:00 ص - 6:00 م',
            'whatsapp' => '+966501234567',
            'facebook' => '',
            'twitter' => '',
            'instagram' => '',
            'linkedin' => '',
            'youtube' => '',
            'map_embed' => '',
            'company_name' => 'شركة جرين لاين'
        ];
    }
}

/**
 * دالة للحصول على إحصائيات التقييمات للمنتج
 */
function getProductRatingStats($product_id) {
    global $database;
    
    try {
        // جلب إحصائيات التقييمات
        $stats = $database->fetch("
            SELECT 
                COUNT(*) as total_reviews,
                AVG(rating) as average_rating,
                COUNT(CASE WHEN rating = 5 THEN 1 END) as five_star,
                COUNT(CASE WHEN rating = 4 THEN 1 END) as four_star,
                COUNT(CASE WHEN rating = 3 THEN 1 END) as three_star,
                COUNT(CASE WHEN rating = 2 THEN 1 END) as two_star,
                COUNT(CASE WHEN rating = 1 THEN 1 END) as one_star
            FROM reviews 
            WHERE product_id = :product_id AND is_approved = 1
        ", ['product_id' => $product_id]);
        
        $total_reviews = (int) $stats['total_reviews'];
        $average_rating = $total_reviews > 0 ? round((float) $stats['average_rating'], 2) : 0;
        
        // حساب النسب المئوية
        $rating_percentages = [];
        if ($total_reviews > 0) {
            $rating_percentages = [
                5 => round(((int) $stats['five_star'] / $total_reviews) * 100),
                4 => round(((int) $stats['four_star'] / $total_reviews) * 100),
                3 => round(((int) $stats['three_star'] / $total_reviews) * 100),
                2 => round(((int) $stats['two_star'] / $total_reviews) * 100),
                1 => round(((int) $stats['one_star'] / $total_reviews) * 100)
            ];
        } else {
            $rating_percentages = [5 => 0, 4 => 0, 3 => 0, 2 => 0, 1 => 0];
        }
        
        return [
            'total_reviews' => $total_reviews,
            'average_rating' => $average_rating,
            'rating_counts' => [
                5 => (int) $stats['five_star'],
                4 => (int) $stats['four_star'],
                3 => (int) $stats['three_star'],
                2 => (int) $stats['two_star'],
                1 => (int) $stats['one_star']
            ],
            'rating_percentages' => $rating_percentages
        ];
        
    } catch (Exception $e) {
        error_log('Error getting product rating stats: ' . $e->getMessage());
        return [
            'total_reviews' => 0,
            'average_rating' => 0,
            'rating_counts' => [5 => 0, 4 => 0, 3 => 0, 2 => 0, 1 => 0],
            'rating_percentages' => [5 => 0, 4 => 0, 3 => 0, 2 => 0, 1 => 0]
        ];
    }
}

/**
 * دالة لإنشاء نجوم التقييم بصيغة HTML
 */
function generateStarRating($rating, $max_stars = 5) {
    $full_stars = floor($rating);
    $half_star = ($rating - $full_stars) >= 0.5;
    $empty_stars = $max_stars - $full_stars - ($half_star ? 1 : 0);
    
    $html = '';
    
    // النجوم المملوءة
    for ($i = 0; $i < $full_stars; $i++) {
        $html .= '<svg class="w-4 h-4 text-yellow-300 me-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 22 20">
            <path d="M20.924 7.625a1.523 1.523 0 0 0-1.238-1.044l-5.051-.734-2.259-4.577a1.534 1.534 0 0 0-2.752 0L7.365 5.847l-5.051.734A1.535 1.535 0 0 0 1.463 9.2l3.656 3.563-.863 5.031a1.532 1.532 0 0 0 2.226 1.616L11 17.033l4.518 2.375a1.534 1.534 0 0 0 2.226-1.617l-.863-5.03L20.537 9.2a1.523 1.523 0 0 0 .387-1.575Z"/>
        </svg>';
    }
    
    // النجمة النصفية (إذا كانت موجودة)
    if ($half_star) {
        $html .= '<svg class="w-4 h-4 text-yellow-300 me-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 22 20">
            <defs>
                <linearGradient id="half-star">
                    <stop offset="50%" stop-color="currentColor"/>
                    <stop offset="50%" stop-color="#d1d5db"/>
                </linearGradient>
            </defs>
            <path fill="url(#half-star)" d="M20.924 7.625a1.523 1.523 0 0 0-1.238-1.044l-5.051-.734-2.259-4.577a1.534 1.534 0 0 0-2.752 0L7.365 5.847l-5.051.734A1.535 1.535 0 0 0 1.463 9.2l3.656 3.563-.863 5.031a1.532 1.532 0 0 0 2.226 1.616L11 17.033l4.518 2.375a1.534 1.534 0 0 0 2.226-1.617l-.863-5.03L20.537 9.2a1.523 1.523 0 0 0 .387-1.575Z"/>
        </svg>';
    }
    
    // النجوم الفارغة
    for ($i = 0; $i < $empty_stars; $i++) {
        $html .= '<svg class="w-4 h-4 text-gray-300 me-1 dark:text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 22 20">
            <path d="M20.924 7.625a1.523 1.523 0 0 0-1.238-1.044l-5.051-.734-2.259-4.577a1.534 1.534 0 0 0-2.752 0L7.365 5.847l-5.051.734A1.535 1.535 0 0 0 1.463 9.2l3.656 3.563-.863 5.031a1.532 1.532 0 0 0 2.226 1.616L11 17.033l4.518 2.375a1.534 1.534 0 0 0 2.226-1.617l-.863-5.03L20.537 9.2a1.523 1.523 0 0 0 .387-1.575Z"/>
        </svg>';
    }
    
    return $html;
}

/**
 * دالة للتحقق من وجود تقييم سابق للمستخدم على منتج معين
 */
function hasUserReviewed($product_id, $user_id = null, $email = null) {
    global $database;
    
    try {
        if ($user_id) {
            // التحقق للمستخدمين المسجلين
            $existing = $database->fetch(
                "SELECT id FROM reviews WHERE product_id = :product_id AND user_id = :user_id",
                ['product_id' => $product_id, 'user_id' => $user_id]
            );
        } elseif ($email) {
            // التحقق للزوار غير المسجلين باستخدام البريد الإلكتروني
            $existing = $database->fetch(
                "SELECT id FROM reviews WHERE product_id = :product_id AND email = :email",
                ['product_id' => $product_id, 'email' => $email]
            );
        } else {
            return false;
        }
        
        return !empty($existing);
        
    } catch (Exception $e) {
        error_log('خطأ في التحقق من التقييم السابق: ' . $e->getMessage());
        return false;
    }
}

/**
 * دالة للحصول على تقييم المستخدم السابق
 */
function getUserReview($product_id, $user_id = null, $email = null) {
    global $database;
    
    try {
        if ($user_id) {
            // جلب التقييم للمستخدمين المسجلين
            $review = $database->fetch(
                "SELECT * FROM reviews WHERE product_id = :product_id AND user_id = :user_id",
                ['product_id' => $product_id, 'user_id' => $user_id]
            );
        } elseif ($email) {
            // جلب التقييم للزوار غير المسجلين باستخدام البريد الإلكتروني
            $review = $database->fetch(
                "SELECT * FROM reviews WHERE product_id = :product_id AND email = :email",
                ['product_id' => $product_id, 'email' => $email]
            );
        } else {
            return null;
        }
        
        return $review;
        
    } catch (Exception $e) {
        error_log('خطأ في جلب التقييم السابق: ' . $e->getMessage());
        return null;
    }
}

?>