<?php
require_once 'includes/layout.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !hasPermission('admin')) {
    redirect('/admin/login.php');
}

$pageTitle = 'إدارة صفحة من نحن';
$currentPage = 'about';
$pageDescription = 'إدارة محتوى صفحة من نحن وبيانات الشركة';
$breadcrumbs = [
    ['title' => 'من نحن']
];

require_once '../config/database.php';
$database = new Database();
$pdo = $database->getConnection();

$message = '';
$error = '';

// إنشاء الجداول إذا لم تكن موجودة
try {
    // جدول محتوى صفحة من نحن
    $pdo->exec("CREATE TABLE IF NOT EXISTS about_content (
        id INT PRIMARY KEY AUTO_INCREMENT,
        hero_subtitle TEXT,
        story TEXT,
        story_image VARCHAR(255),
        mission TEXT,
        vision TEXT,
        company_values TEXT,
        hero_bg_color1 VARCHAR(7) DEFAULT '#047857',
        hero_bg_color2 VARCHAR(7) DEFAULT '#059669',
        hero_bg_color3 VARCHAR(7) DEFAULT '#10b981',
        hero_bg_color4 VARCHAR(7) DEFAULT '#34d399',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");

    // جدول إحصائيات الشركة
    $pdo->exec("CREATE TABLE IF NOT EXISTS company_stats (
        id INT PRIMARY KEY AUTO_INCREMENT,
        happy_clients INT DEFAULT 0,
        completed_projects INT DEFAULT 0,
        years_experience INT DEFAULT 0,
        team_members INT DEFAULT 0,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");

    // جدول أعضاء الفريق
    $pdo->exec("CREATE TABLE IF NOT EXISTS team_members (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(255) NOT NULL,
        position VARCHAR(255),
        email VARCHAR(255),
        linkedin VARCHAR(255),
        twitter VARCHAR(255),
        bio TEXT,
        image VARCHAR(255),
        sort_order INT DEFAULT 0,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");

    // جدول القيم والمبادئ
    $pdo->exec("CREATE TABLE IF NOT EXISTS values_principles (
        id INT PRIMARY KEY AUTO_INCREMENT,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        icon VARCHAR(100) DEFAULT 'fas fa-star',
        sort_order INT DEFAULT 0,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");

    // جدول لماذا تختارنا
    $pdo->exec("CREATE TABLE IF NOT EXISTS why_choose_us (
        id INT PRIMARY KEY AUTO_INCREMENT,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        icon VARCHAR(100) DEFAULT 'fas fa-star',
        background_color VARCHAR(7) DEFAULT '#047857',
        icon_background_color VARCHAR(7) DEFAULT '#10B981',
        icon_color VARCHAR(7) DEFAULT '#FFFFFF',
        icon_shape ENUM('rounded', 'circle', 'square') DEFAULT 'rounded',
        sort_order INT DEFAULT 0,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");

    // جدول مسيرتنا (التايم لاين)
    $pdo->exec("CREATE TABLE IF NOT EXISTS our_journey (
        id INT PRIMARY KEY AUTO_INCREMENT,
        year VARCHAR(10) NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        icon VARCHAR(100) DEFAULT 'fas fa-calendar',
        background_color VARCHAR(7) DEFAULT '#047857',
        border_color VARCHAR(7) DEFAULT '#10B981',
        text_color VARCHAR(7) DEFAULT '#FFFFFF',
        is_future BOOLEAN DEFAULT FALSE,
        sort_order INT DEFAULT 0,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");

    // إدراج البيانات الافتراضية إذا لم تكن موجودة
    $stmt = $pdo->query("SELECT COUNT(*) FROM about_content");
    if ($stmt->fetchColumn() == 0) {
        $pdo->exec("INSERT INTO about_content (hero_subtitle, story, mission, vision, company_values) VALUES (
            'نحن شركة رائدة في مجال التكنولوجيا والابتكار',
            'تأسست شركتنا عام 2010 برؤية واضحة لتقديم حلول تقنية مبتكرة تساعد الشركات على النمو والازدهار في العصر الرقمي.',
            'نسعى لتقديم أفضل الحلول التقنية المبتكرة التي تساعد عملاءنا على تحقيق أهدافهم وتطوير أعمالهم.',
            'أن نكون الشركة الرائدة عالمياً في تقديم الحلول التقنية المتطورة والمبتكرة.',
            'الجودة، الابتكار، الشفافية، والالتزام بتقديم أفضل خدمة لعملائنا.'
        )");
    }

    $stmt = $pdo->query("SELECT COUNT(*) FROM company_stats");
    if ($stmt->fetchColumn() == 0) {
        $pdo->exec("INSERT INTO company_stats (happy_clients, completed_projects, years_experience, team_members) VALUES (150, 200, 10, 25)");
    }

    // إدراج البيانات الافتراضية لجدول مسيرتنا
    $stmt = $pdo->query("SELECT COUNT(*) FROM our_journey");
    if ($stmt->fetchColumn() == 0) {
        $pdo->exec("INSERT INTO our_journey (year, title, description, icon, background_color, border_color, text_color, is_future, sort_order) VALUES 
            ('2010', 'تأسيس الشركة', 'بدأت رحلتنا برؤية واضحة لتقديم حلول تقنية مبتكرة', 'fas fa-rocket', '#047857', '#10B981', '#FFFFFF', 0, 1),
            ('2013', 'التوسع الأول', 'افتتحنا أول فرع لنا وضاعفنا حجم فريق العمل', 'fas fa-building', '#047857', '#10B981', '#FFFFFF', 0, 2),
            ('2016', 'الشراكات الاستراتيجية', 'أقمنا شراكات مع كبرى الشركات التقنية العالمية', 'fas fa-handshake', '#047857', '#10B981', '#FFFFFF', 0, 3),
            ('2019', 'الابتكار والتطوير', 'أطلقنا منصتنا التقنية المتطورة وحصلنا على جوائز الابتكار', 'fas fa-lightbulb', '#047857', '#10B981', '#FFFFFF', 0, 4),
            ('2022', 'التحول الرقمي', 'قدنا مشاريع التحول الرقمي لأكثر من 100 شركة', 'fas fa-digital-tachograph', '#047857', '#10B981', '#FFFFFF', 0, 5),
            ('2024', 'المستقبل', 'نتطلع لتوسيع خدماتنا عالمياً وتطوير تقنيات الذكاء الاصطناعي', 'fas fa-star', '#6366f1', '#8b5cf6', '#FFFFFF', 1, 6)");
    }

} catch (PDOException $e) {
    $error = "خطأ في قاعدة البيانات: " . $e->getMessage();
}

// معالجة الطلبات
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'update_about':
            try {
                // معالجة رفع الصورة
                $story_image = $_POST['current_story_image'] ?? '';
                
                if (isset($_FILES['story_image_file']) && $_FILES['story_image_file']['error'] === UPLOAD_ERR_OK) {
                    $upload_dir = '../assets/images/';
                    
                    // إنشاء المجلد إذا لم يكن موجوداً
                    if (!is_dir($upload_dir)) {
                        mkdir($upload_dir, 0755, true);
                    }
                    
                    $file_info = pathinfo($_FILES['story_image_file']['name']);
                    $file_extension = strtolower($file_info['extension']);
                    
                    // التحقق من نوع الملف
                    $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
                    if (in_array($file_extension, $allowed_extensions)) {
                        $new_filename = 'story_' . time() . '.' . $file_extension;
                        $upload_path = $upload_dir . $new_filename;
                        
                        if (move_uploaded_file($_FILES['story_image_file']['tmp_name'], $upload_path)) {
                            $story_image = '/assets/images/' . $new_filename;
                            
                            // حذف الصورة القديمة إذا كانت موجودة
                            $old_image = $_POST['current_story_image'] ?? '';
                            if ($old_image && file_exists('..' . $old_image)) {
                                unlink('..' . $old_image);
                            }
                        } else {
                            throw new Exception('فشل في رفع الصورة');
                        }
                    } else {
                        throw new Exception('نوع الملف غير مدعوم. يرجى استخدام: JPG, PNG, GIF, WEBP');
                    }
                }
                
                $database->query("
                    UPDATE about_content SET 
                        hero_subtitle = :hero_subtitle,
                        story = :story,
                        story_image = :story_image,
                        mission = :mission,
                        vision = :vision,
                        `values` = :values,
                        hero_bg_color1 = :hero_bg_color1,
                        hero_bg_color2 = :hero_bg_color2,
                        hero_bg_color3 = :hero_bg_color3,
                        hero_bg_color4 = :hero_bg_color4
                    WHERE id = 1
                ", [
                    'hero_subtitle' => $_POST['hero_subtitle'] ?? '',
                    'story' => $_POST['story'] ?? '',
                    'story_image' => $story_image,
                    'mission' => $_POST['mission'] ?? '',
                    'vision' => $_POST['vision'] ?? '',
                    'values' => $_POST['values'] ?? '',
                    'hero_bg_color1' => $_POST['hero_bg_color1'] ?? '#047857',
                    'hero_bg_color2' => $_POST['hero_bg_color2'] ?? '#059669',
                    'hero_bg_color3' => $_POST['hero_bg_color3'] ?? '#10b981',
                    'hero_bg_color4' => $_POST['hero_bg_color4'] ?? '#34d399'
                ]);
                $message = 'تم تحديث محتوى صفحة من نحن بنجاح';
            } catch (Exception $e) {
                $error = 'حدث خطأ أثناء التحديث: ' . $e->getMessage();
            }
            break;
            
        case 'update_stats':
            try {
                $database->query("
                    UPDATE company_stats SET 
                        happy_clients = :happy_clients,
                        completed_projects = :completed_projects,
                        years_experience = :years_experience,
                        team_members = :team_members
                    WHERE id = 1
                ", [
                    'happy_clients' => (int)($_POST['happy_clients'] ?? 0),
                    'completed_projects' => (int)($_POST['completed_projects'] ?? 0),
                    'years_experience' => (int)($_POST['years_experience'] ?? 0),
                    'team_members' => (int)($_POST['team_members'] ?? 0)
                ]);
                $message = 'تم تحديث إحصائيات الشركة بنجاح';
            } catch (Exception $e) {
                $error = 'حدث خطأ أثناء التحديث: ' . $e->getMessage();
            }
            break;
            
        case 'add_team_member':
            try {
                // معالجة رفع الصورة
                $image_path = $_POST['image'] ?? '';
                
                if (isset($_FILES['image_file']) && $_FILES['image_file']['error'] === UPLOAD_ERR_OK) {
                    $upload_dir = '../assets/images/team/';
                    
                    // إنشاء المجلد إذا لم يكن موجوداً
                    if (!is_dir($upload_dir)) {
                        mkdir($upload_dir, 0755, true);
                    }
                    
                    $file_info = pathinfo($_FILES['image_file']['name']);
                    $file_extension = strtolower($file_info['extension']);
                    
                    // التحقق من نوع الملف
                    $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
                    if (in_array($file_extension, $allowed_extensions)) {
                        $new_filename = 'team_' . time() . '_' . uniqid() . '.' . $file_extension;
                        $upload_path = $upload_dir . $new_filename;
                        
                        if (move_uploaded_file($_FILES['image_file']['tmp_name'], $upload_path)) {
                            $image_path = '/assets/images/team/' . $new_filename;
                        } else {
                            throw new Exception('فشل في رفع الصورة');
                        }
                    } else {
                        throw new Exception('نوع الملف غير مدعوم. يرجى استخدام: JPG, PNG, GIF, WEBP');
                    }
                }
                
                $database->query("
                    INSERT INTO team_members (name, position, bio, image, linkedin, twitter, email, sort_order, is_active) 
                    VALUES (:name, :position, :bio, :image, :linkedin, :twitter, :email, :sort_order, 1)
                ", [
                    'name' => $_POST['name'] ?? '',
                    'position' => $_POST['position'] ?? '',
                    'bio' => $_POST['bio'] ?? '',
                    'image' => $image_path,
                    'linkedin' => $_POST['linkedin'] ?? '',
                    'twitter' => $_POST['twitter'] ?? '',
                    'email' => $_POST['email'] ?? '',
                    'sort_order' => (int)($_POST['sort_order'] ?? 0)
                ]);
                $message = 'تم إضافة عضو الفريق بنجاح';
            } catch (Exception $e) {
                $error = 'حدث خطأ أثناء الإضافة: ' . $e->getMessage();
            }
            break;
            
        case 'update_team_member':
            try {
                // معالجة رفع الصورة
                $image_path = $_POST['current_image'] ?? $_POST['image'] ?? '';
                
                if (isset($_FILES['image_file']) && $_FILES['image_file']['error'] === UPLOAD_ERR_OK) {
                    $upload_dir = '../assets/images/team/';
                    
                    // إنشاء المجلد إذا لم يكن موجوداً
                    if (!is_dir($upload_dir)) {
                        mkdir($upload_dir, 0755, true);
                    }
                    
                    $file_info = pathinfo($_FILES['image_file']['name']);
                    $file_extension = strtolower($file_info['extension']);
                    
                    // التحقق من نوع الملف
                    $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
                    if (in_array($file_extension, $allowed_extensions)) {
                        $new_filename = 'team_' . time() . '_' . uniqid() . '.' . $file_extension;
                        $upload_path = $upload_dir . $new_filename;
                        
                        if (move_uploaded_file($_FILES['image_file']['tmp_name'], $upload_path)) {
                            $image_path = '/assets/images/team/' . $new_filename;
                            
                            // حذف الصورة القديمة إذا كانت موجودة
                            $old_image = $_POST['current_image'] ?? '';
                            if ($old_image && file_exists('..' . $old_image)) {
                                unlink('..' . $old_image);
                            }
                        } else {
                            throw new Exception('فشل في رفع الصورة');
                        }
                    } else {
                        throw new Exception('نوع الملف غير مدعوم. يرجى استخدام: JPG, PNG, GIF, WEBP');
                    }
                }
                
                $database->query("
                    UPDATE team_members SET 
                        name = :name,
                        position = :position,
                        bio = :bio,
                        image = :image,
                        linkedin = :linkedin,
                        twitter = :twitter,
                        email = :email,
                        sort_order = :sort_order,
                        is_active = :is_active
                    WHERE id = :id
                ", [
                    'id' => (int)($_POST['member_id'] ?? 0),
                    'name' => $_POST['name'] ?? '',
                    'position' => $_POST['position'] ?? '',
                    'bio' => $_POST['bio'] ?? '',
                    'image' => $image_path,
                    'linkedin' => $_POST['linkedin'] ?? '',
                    'twitter' => $_POST['twitter'] ?? '',
                    'email' => $_POST['email'] ?? '',
                    'sort_order' => (int)($_POST['sort_order'] ?? 0),
                    'is_active' => isset($_POST['is_active']) ? 1 : 0
                ]);
                $message = 'تم تحديث عضو الفريق بنجاح';
            } catch (Exception $e) {
                $error = 'حدث خطأ أثناء التحديث: ' . $e->getMessage();
            }
            break;
            
        case 'delete_team_member':
            try {
                $database->query("DELETE FROM team_members WHERE id = :id", [
                    'id' => (int)($_POST['member_id'] ?? 0)
                ]);
                $message = 'تم حذف عضو الفريق بنجاح';
            } catch (Exception $e) {
                $error = 'حدث خطأ أثناء الحذف: ' . $e->getMessage();
            }
            break;
            
        case 'add_value_principle':
            try {
                // تحديد الأيقونة المناسبة
                $icon = $_POST['icon'] ?? 'fas fa-star';
                if (!empty($_POST['custom_icon'])) {
                    $icon = $_POST['custom_icon'];
                }
                
                $database->query("
                    INSERT INTO values_principles (title, description, icon, sort_order, is_active) 
                    VALUES (:title, :description, :icon, :sort_order, 1)
                ", [
                    'title' => $_POST['title'] ?? '',
                    'description' => $_POST['description'] ?? '',
                    'icon' => $icon,
                    'sort_order' => (int)($_POST['sort_order'] ?? 0)
                ]);
                $message = 'تم إضافة القيمة بنجاح';
            } catch (Exception $e) {
                $error = 'حدث خطأ أثناء الإضافة: ' . $e->getMessage();
            }
            break;
            
        case 'update_value_principle':
            try {
                // تحديد الأيقونة المناسبة
                $icon = $_POST['icon'] ?? 'fas fa-star';
                if (!empty($_POST['custom_icon'])) {
                    $icon = $_POST['custom_icon'];
                }
                
                $database->query("
                    UPDATE values_principles SET 
                        title = :title,
                        description = :description,
                        icon = :icon,
                        sort_order = :sort_order,
                        is_active = :is_active
                    WHERE id = :id
                ", [
                    'id' => (int)($_POST['value_id'] ?? 0),
                    'title' => $_POST['title'] ?? '',
                    'description' => $_POST['description'] ?? '',
                    'icon' => $icon,
                    'sort_order' => (int)($_POST['sort_order'] ?? 0),
                    'is_active' => isset($_POST['is_active']) ? 1 : 0
                ]);
                $message = 'تم تحديث القيمة بنجاح';
            } catch (Exception $e) {
                $error = 'حدث خطأ أثناء التحديث: ' . $e->getMessage();
            }
            break;
            
        case 'delete_value_principle':
            try {
                $database->query("DELETE FROM values_principles WHERE id = :id", [
                    'id' => (int)($_POST['value_id'] ?? 0)
                ]);
                $message = 'تم حذف القيمة بنجاح';
            } catch (Exception $e) {
                $error = 'حدث خطأ أثناء الحذف: ' . $e->getMessage();
            }
            break;
            
        case 'add_why_choose_us':
            try {
                // تحديد الأيقونة المناسبة
                $icon = $_POST['icon'] ?? 'fas fa-star';
                if (!empty($_POST['custom_icon'])) {
                    $icon = $_POST['custom_icon'];
                }
                
                $database->query("
                    INSERT INTO why_choose_us (title, description, icon, background_color, icon_background_color, icon_color, icon_shape, sort_order, is_active) 
                    VALUES (:title, :description, :icon, :background_color, :icon_background_color, :icon_color, :icon_shape, :sort_order, 1)
                ", [
                    'title' => $_POST['title'] ?? '',
                    'description' => $_POST['description'] ?? '',
                    'icon' => $icon,
                    'background_color' => $_POST['background_color'] ?? '#047857',
                    'icon_background_color' => $_POST['icon_background_color'] ?? '#10B981',
                    'icon_color' => $_POST['icon_color'] ?? '#FFFFFF',
                    'icon_shape' => $_POST['icon_shape'] ?? 'rounded',
                    'sort_order' => (int)($_POST['sort_order'] ?? 0)
                ]);
                $message = 'تم إضافة الميزة بنجاح';
            } catch (Exception $e) {
                $error = 'حدث خطأ أثناء الإضافة: ' . $e->getMessage();
            }
            break;
            
        case 'update_why_choose_us':
            try {
                // تحديد الأيقونة المناسبة
                $icon = $_POST['icon'] ?? 'fas fa-star';
                if (!empty($_POST['custom_icon'])) {
                    $icon = $_POST['custom_icon'];
                }
                
                $database->query("
                    UPDATE why_choose_us SET 
                        title = :title,
                        description = :description,
                        icon = :icon,
                        background_color = :background_color,
                        icon_background_color = :icon_background_color,
                        icon_color = :icon_color,
                        icon_shape = :icon_shape,
                        sort_order = :sort_order,
                        is_active = :is_active
                    WHERE id = :id
                ", [
                    'id' => (int)($_POST['why_choose_us_id'] ?? 0),
                    'title' => $_POST['title'] ?? '',
                    'description' => $_POST['description'] ?? '',
                    'icon' => $icon,
                    'background_color' => $_POST['background_color'] ?? '#047857',
                    'icon_background_color' => $_POST['icon_background_color'] ?? '#10B981',
                    'icon_color' => $_POST['icon_color'] ?? '#FFFFFF',
                    'icon_shape' => $_POST['icon_shape'] ?? 'rounded',
                    'sort_order' => (int)($_POST['sort_order'] ?? 0),
                    'is_active' => isset($_POST['is_active']) ? 1 : 0
                ]);
                $message = 'تم تحديث الميزة بنجاح';
            } catch (Exception $e) {
                $error = 'حدث خطأ أثناء التحديث: ' . $e->getMessage();
            }
            break;
            
        case 'delete_why_choose_us':
            try {
                $database->query("DELETE FROM why_choose_us WHERE id = :id", [
                    'id' => (int)($_POST['why_choose_us_id'] ?? 0)
                ]);
                $message = 'تم حذف الميزة بنجاح';
            } catch (Exception $e) {
                $error = 'حدث خطأ أثناء الحذف: ' . $e->getMessage();
            }
            break;
            
        case 'add_journey_item':
            try {
                // تحديد الأيقونة المناسبة
                $icon = $_POST['icon'] ?? 'fas fa-calendar';
                if (!empty($_POST['custom_icon'])) {
                    $icon = $_POST['custom_icon'];
                }
                
                $database->query("
                    INSERT INTO our_journey (year, title, description, icon, background_color, border_color, text_color, is_future, sort_order, is_active) 
                    VALUES (:year, :title, :description, :icon, :background_color, :border_color, :text_color, :is_future, :sort_order, 1)
                ", [
                    'year' => $_POST['year'] ?? '',
                    'title' => $_POST['title'] ?? '',
                    'description' => $_POST['description'] ?? '',
                    'icon' => $icon,
                    'background_color' => $_POST['background_color'] ?? '#047857',
                    'border_color' => $_POST['border_color'] ?? '#10B981',
                    'text_color' => $_POST['text_color'] ?? '#FFFFFF',
                    'is_future' => (int)($_POST['is_future'] ?? 0),
                    'sort_order' => (int)($_POST['sort_order'] ?? 0)
                ]);
                $message = 'تم إضافة عنصر المسيرة بنجاح';
            } catch (Exception $e) {
                $error = 'حدث خطأ أثناء الإضافة: ' . $e->getMessage();
            }
            break;
            
        case 'update_journey_item':
            try {
                // تحديد الأيقونة المناسبة
                $icon = $_POST['icon'] ?? 'fas fa-calendar';
                if (!empty($_POST['custom_icon'])) {
                    $icon = $_POST['custom_icon'];
                }
                
                $database->query("
                    UPDATE our_journey SET 
                        year = :year,
                        title = :title,
                        description = :description,
                        icon = :icon,
                        background_color = :background_color,
                        border_color = :border_color,
                        text_color = :text_color,
                        is_future = :is_future,
                        sort_order = :sort_order,
                        is_active = :is_active
                    WHERE id = :id
                ", [
                    'id' => (int)($_POST['journey_id'] ?? 0),
                    'year' => $_POST['year'] ?? '',
                    'title' => $_POST['title'] ?? '',
                    'description' => $_POST['description'] ?? '',
                    'icon' => $icon,
                    'background_color' => $_POST['background_color'] ?? '#047857',
                    'border_color' => $_POST['border_color'] ?? '#10B981',
                    'text_color' => $_POST['text_color'] ?? '#FFFFFF',
                    'is_future' => (int)($_POST['is_future'] ?? 0),
                    'sort_order' => (int)($_POST['sort_order'] ?? 0),
                    'is_active' => isset($_POST['is_active']) ? 1 : 0
                ]);
                $message = 'تم تحديث عنصر المسيرة بنجاح';
            } catch (Exception $e) {
                $error = 'حدث خطأ أثناء التحديث: ' . $e->getMessage();
            }
            break;
            
        case 'delete_journey_item':
            try {
                $database->query("DELETE FROM our_journey WHERE id = :id", [
                    'id' => (int)($_POST['journey_id'] ?? 0)
                ]);
                $message = 'تم حذف عنصر المسيرة بنجاح';
            } catch (Exception $e) {
                $error = 'حدث خطأ أثناء الحذف: ' . $e->getMessage();
            }
            break;
    }
}

// جلب البيانات الحالية
$aboutContent = getAboutContent();
$companyStats = getCompanyStats();
$teamMembers = getTeamMembers();
$valuesPrinciples = getValuesPrinciples();
$whyChooseUsFeatures = getWhyChooseUsFeatures();
$ourJourney = getAllOurJourney(); // استخدام دالة الإدارة لعرض جميع السجلات

$page = $_GET['page'] ?? 'home';

// معالجة طلبات POST للصفحة الرئيسية
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $page === 'home') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_slide':
                $slideId = $_POST['slide_id'] ?? null;
                $title = $_POST['title'] ?? '';
                $subtitle = $_POST['subtitle'] ?? '';
                $description = $_POST['description'] ?? '';
                $buttonText = $_POST['button_text'] ?? '';
                $buttonLink = $_POST['button_link'] ?? '';
                $backgroundColor = $_POST['background_color'] ?? '#047857';
                $textColor = $_POST['text_color'] ?? '#FFFFFF';
                $sortOrder = !empty($_POST['sort_order']) ? (int)$_POST['sort_order'] : 0;
                $isActive = isset($_POST['is_active']) ? 1 : 0;
                
                $imagePath = null;
                if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                    $uploadDir = '../assets/images/';
                    $fileName = 'slide_' . time() . '_' . basename($_FILES['image']['name']);
                    $imagePath = '/assets/images/' . $fileName;
                    move_uploaded_file($_FILES['image']['tmp_name'], $uploadDir . $fileName);
                }
                
                if ($slideId) {
                    $query = "UPDATE hero_slides SET title = :title, subtitle = :subtitle, description = :description, button_text = :button_text, button_link = :button_link, sort_order = :sort_order, is_active = :is_active";
                    $params = [
                        'title' => $title,
                        'subtitle' => $subtitle,
                        'description' => $description,
                        'button_text' => $buttonText,
                        'button_link' => $buttonLink,
                        'sort_order' => $sortOrder,
                        'is_active' => $isActive,
                        'id' => $slideId
                    ];
                    
                    if ($imagePath) {
                        $query .= ", image = :image";
                        $params['image'] = $imagePath;
                    }
                    
                    $query .= " WHERE id = :id";
                    $database->query($query, $params);
                } else {
                    $database->query("
                        INSERT INTO hero_slides (title, subtitle, description, image, button_text, button_link, sort_order, is_active) 
                        VALUES (:title, :subtitle, :description, :image, :button_text, :button_link, :sort_order, :is_active)
                    ", [
                        'title' => $title,
                        'subtitle' => $subtitle,
                        'description' => $description,
                        'image' => $imagePath,
                        'button_text' => $buttonText,
                        'button_link' => $buttonLink,
                        'sort_order' => $sortOrder,
                        'is_active' => $isActive
                    ]);
                }
                $message = 'تم تحديث السلايدر بنجاح';
                break;
                
            case 'delete_slide':
                $slideId = $_POST['slide_id'] ?? null;
                if ($slideId) {
                    $database->query("DELETE FROM hero_slides WHERE id = :id", ['id' => $slideId]);
                    $message = 'تم حذف السلايدر بنجاح';
                }
                break;
                
            case 'update_testimonial':
                $testimonialId = $_POST['testimonial_id'] ?? null;
                $name = $_POST['name'] ?? '';
                $position = $_POST['position'] ?? '';
                $company = $_POST['company'] ?? '';
                $content = $_POST['content'] ?? '';
                $rating = $_POST['rating'] ?? 5;
                $isFeatured = isset($_POST['is_featured']) ? 1 : 0;
                $sortOrder = $_POST['sort_order'] ?? 0;
                $isActive = isset($_POST['is_active']) ? 1 : 0;
                
                $imagePath = null;
                if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                    $uploadDir = '../assets/images/';
                    $fileName = 'testimonial_' . time() . '_' . basename($_FILES['image']['name']);
                    $imagePath = '/assets/images/' . $fileName;
                    move_uploaded_file($_FILES['image']['tmp_name'], $uploadDir . $fileName);
                }
                
                if ($testimonialId) {
                    $query = "UPDATE testimonials SET name = :name, position = :position, company = :company, content = :content, rating = :rating, is_featured = :is_featured, sort_order = :sort_order, is_active = :is_active";
                    $params = [
                        'name' => $name,
                        'position' => $position,
                        'company' => $company,
                        'content' => $content,
                        'rating' => $rating,
                        'is_featured' => $isFeatured,
                        'sort_order' => $sortOrder,
                        'is_active' => $isActive,
                        'id' => $testimonialId
                    ];
                    
                    if ($imagePath) {
                        $query .= ", image = :image";
                        $params['image'] = $imagePath;
                    }
                    
                    $query .= " WHERE id = :id";
                    $database->query($query, $params);
                } else {
                    $database->query("
                        INSERT INTO testimonials (name, position, company, content, rating, image, is_featured, sort_order, is_active) 
                        VALUES (:name, :position, :company, :content, :rating, :image, :is_featured, :sort_order, :is_active)
                    ", [
                        'name' => $name,
                        'position' => $position,
                        'company' => $company,
                        'content' => $content,
                        'rating' => $rating,
                        'image' => $imagePath,
                        'is_featured' => $isFeatured,
                        'sort_order' => $sortOrder,
                        'is_active' => $isActive
                    ]);
                }
                $message = 'تم تحديث المراجعة بنجاح';
                break;
                
            case 'delete_testimonial':
                $testimonialId = $_POST['testimonial_id'] ?? null;
                if ($testimonialId) {
                    $database->query("DELETE FROM testimonials WHERE id = :id", ['id' => $testimonialId]);
                    $message = 'تم حذف المراجعة بنجاح';
                }
                break;
                
            case 'update_consultation':
                $title = $_POST['title'] ?? '';
                $subtitle = $_POST['subtitle'] ?? '';
                $description = $_POST['description'] ?? '';
                $buttonText = $_POST['button_text'] ?? '';
                $buttonLink = $_POST['button_link'] ?? '';
                $backgroundColor = $_POST['background_color'] ?? '#047857';
                $textColor = $_POST['text_color'] ?? '#FFFFFF';
                $isActive = isset($_POST['is_active']) ? 1 : 0;
                
                $database->query("
                    UPDATE consultation_section SET 
                    title = :title, subtitle = :subtitle, description = :description, 
                    button_text = :button_text, button_link = :button_link, 
                    background_color = :background_color, text_color = :text_color, is_active = :is_active 
                    WHERE id = 1
                ", [
                    'title' => $title,
                    'subtitle' => $subtitle,
                    'description' => $description,
                    'button_text' => $buttonText,
                    'button_link' => $buttonLink,
                    'background_color' => $backgroundColor,
                    'text_color' => $textColor,
                    'is_active' => $isActive
                ]);
                $message = 'تم تحديث قسم الاستشارة بنجاح';
                break;
                
            case 'update_why_choose_us_settings':
                $maxItems = $_POST['max_items'] ?? 6;
                $isVisible = isset($_POST['is_visible']) ? 1 : 0;
                $title = $_POST['title'] ?? '';
                $subtitle = $_POST['subtitle'] ?? '';
                
                $database->query("
                    UPDATE home_why_choose_us_settings SET 
                    max_items = :max_items, is_visible = :is_visible, title = :title, subtitle = :subtitle 
                    WHERE id = 1
                ", [
                    'max_items' => $maxItems,
                    'is_visible' => $isVisible,
                    'title' => $title,
                    'subtitle' => $subtitle
                ]);
                $message = 'تم تحديث إعدادات قسم "لماذا تختارنا؟" بنجاح';
                break;
        }
    }
}

// إنشاء جداول إضافية للصفحة الرئيسية
if ($page === 'home') {
    // جدول السلايدرز
    $database->query("
        CREATE TABLE IF NOT EXISTS hero_slides (
            id INT PRIMARY KEY AUTO_INCREMENT,
            title VARCHAR(255) NOT NULL,
            subtitle TEXT,
            description TEXT,
            image VARCHAR(255),
            button_text VARCHAR(100),
            button_link VARCHAR(255),
            sort_order INT DEFAULT 0,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    
    // جدول المراجعات/الشهادات
    $database->query("
        CREATE TABLE IF NOT EXISTS testimonials (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            position VARCHAR(100),
            company VARCHAR(100),
            content TEXT NOT NULL,
            rating INT DEFAULT 5,
            image VARCHAR(255),
            is_featured BOOLEAN DEFAULT 0,
            sort_order INT DEFAULT 0,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    
    // جدول قسم الاستشارة المجانية
    $database->query("
        CREATE TABLE IF NOT EXISTS consultation_section (
            id INT PRIMARY KEY AUTO_INCREMENT,
            title VARCHAR(255) DEFAULT 'هل تحتاج إلى استشارة مجانية؟',
            subtitle TEXT,
            description TEXT,
            button_text VARCHAR(100) DEFAULT 'احصل على استشارة مجانية',
            button_link VARCHAR(255) DEFAULT '/contact',
            background_color VARCHAR(7) DEFAULT '#047857',
            text_color VARCHAR(7) DEFAULT '#FFFFFF',
            is_active BOOLEAN DEFAULT 1,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    
    // جدول إعدادات قسم "لماذا تختارنا؟" للصفحة الرئيسية
    $database->query("
        CREATE TABLE IF NOT EXISTS home_why_choose_us_settings (
            id INT PRIMARY KEY AUTO_INCREMENT,
            max_items INT DEFAULT 6,
            is_visible BOOLEAN DEFAULT 1,
            title VARCHAR(255) DEFAULT 'لماذا تختارنا؟',
            subtitle TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    
    // إدراج البيانات الافتراضية
    $slidesExists = $database->fetch("SELECT id FROM hero_slides LIMIT 1");
    if (!$slidesExists) {
        $defaultSlides = [
            ['مرحباً بكم في الخطوط الخضراء', 'أفضل حلول التكييف والتبريد', 'نقدم أحدث تقنيات التبريد والتكييف مع خدمة عملاء متميزة', '/assets/images/hero-slide-1.jpg', 'تصفح منتجاتنا', '/products', 1],
            ['جودة عالية وخدمة ممتازة', 'نقدم أحدث تقنيات التبريد والتكييف', 'فريق متخصص وخبرة واسعة في مجال التكييف والتبريد', '/assets/images/hero-slide-2.jpg', 'اتصل بنا', '/contact', 2]
        ];
        
        foreach ($defaultSlides as $slide) {
            $database->query("
                INSERT INTO hero_slides (title, subtitle, description, image, button_text, button_link, sort_order) 
                VALUES (:title, :subtitle, :description, :image, :button_text, :button_link, :sort_order)
            ", [
                'title' => $slide[0],
                'subtitle' => $slide[1],
                'description' => $slide[2],
                'image' => $slide[3],
                'button_text' => $slide[4],
                'button_link' => $slide[5],
                'sort_order' => $slide[6]
            ]);
        }
    }
    
    $testimonialsExists = $database->fetch("SELECT id FROM testimonials LIMIT 1");
    if (!$testimonialsExists) {
        $defaultTestimonials = [
            ['أحمد محمد', 'مدير شركة', 'شركة النجاح التجارية', 'خدمة ممتازة وجودة عالية، أنصح بالتعامل معهم بشدة. فريق محترف ومتعاون.', 5, '/assets/images/testimonial-1.jpg', 1, 1],
            ['فاطمة أحمد', 'ربة منزل', '', 'تركيب سريع ومهني، والمكيف يعمل بكفاءة عالية. خدمة ما بعد البيع ممتازة.', 5, '/assets/images/testimonial-2.jpg', 1, 2],
            ['محمد علي', 'مهندس', 'شركة البناء الحديث', 'أسعار مناسبة وجودة ممتازة. تعامل راقي ومهني من الفريق.', 5, '/assets/images/testimonial-3.jpg', 1, 3]
        ];
        
        foreach ($defaultTestimonials as $testimonial) {
            $database->query("
                INSERT INTO testimonials (name, position, company, content, rating, image, is_featured, sort_order) 
                VALUES (:name, :position, :company, :content, :rating, :image, :is_featured, :sort_order)
            ", [
                'name' => $testimonial[0],
                'position' => $testimonial[1],
                'company' => $testimonial[2],
                'content' => $testimonial[3],
                'rating' => $testimonial[4],
                'image' => $testimonial[5],
                'is_featured' => $testimonial[6],
                'sort_order' => $testimonial[7]
            ]);
        }
    }
    
    $consultationExists = $database->fetch("SELECT id FROM consultation_section WHERE id = 1");
    if (!$consultationExists) {
        $database->query("
            INSERT INTO consultation_section (id, title, subtitle, description, button_text, button_link, background_color, text_color) 
            VALUES (1, 'هل تحتاج إلى استشارة مجانية؟', 'فريقنا المتخصص جاهز لمساعدتك', 'احصل على استشارة مجانية من خبرائنا في مجال التكييف والتبريد. نحن هنا لمساعدتك في اختيار الحل الأمثل لاحتياجاتك.', 'احصل على استشارة مجانية', '/contact', '#047857', '#FFFFFF')
        ");
    }
    
    $whyChooseUsSettingsExists = $database->fetch("SELECT id FROM home_why_choose_us_settings WHERE id = 1");
    if (!$whyChooseUsSettingsExists) {
        $database->query("
            INSERT INTO home_why_choose_us_settings (id, max_items, is_visible, title, subtitle) 
            VALUES (1, 6, 1, 'لماذا تختارنا؟', 'نحن نقدم أفضل الحلول والخدمات في مجال التكييف والتبريد')
        ");
    }
}

startLayout();
showPageHeader('إدارة محتوى صفحة من نحن', 'يمكنك تحرير وتحديث محتوى صفحة من نحن من هنا');
showMessages($message, $error);
?>

                    <!-- التبويبات -->
                    <div class="bg-white rounded-lg shadow-md" x-data="{ activeTab: 'content' }">
                        <div class="border-b border-gray-200">
                            <nav class="-mb-px flex space-x-8 px-6">
                                <button @click="activeTab = 'content'" 
                                        :class="activeTab === 'content' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                                    <i class="fas fa-file-alt ml-2"></i>
                                    المحتوى الأساسي
                                </button>
                                <button @click="activeTab = 'stats'" 
                                        :class="activeTab === 'stats' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                                    <i class="fas fa-chart-bar ml-2"></i>
                                    الإحصائيات
                                </button>
                                <button @click="activeTab = 'team'" 
                                        :class="activeTab === 'team' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                                    <i class="fas fa-users ml-2"></i>
                                    فريق العمل
                                </button>
                                <button @click="activeTab = 'values'" 
                                        :class="activeTab === 'values' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                                    <i class="fas fa-heart ml-2"></i>
                                    قيمنا ومبادئنا
                                </button>
                                <button @click="activeTab = 'why-choose-us'" 
                                        :class="activeTab === 'why-choose-us' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                                    <i class="fas fa-star ml-2"></i>
                                    لماذا تختارنا؟
                                </button>
                                <button @click="activeTab = 'journey'" 
                                        :class="activeTab === 'journey' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                                    <i class="fas fa-road ml-2"></i>
                                    مسيرتنا
                                </button>
                            </nav>
                        </div>

                        <!-- محتوى المحتوى الأساسي -->
                        <div x-show="activeTab === 'content'" class="p-6">
                            <form method="POST" enctype="multipart/form-data" class="space-y-6">
                                <input type="hidden" name="action" value="update_about">
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">العنوان الفرعي للبطل</label>
                                    <textarea name="hero_subtitle" rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo htmlspecialchars($aboutContent['hero_subtitle'] ?? ''); ?></textarea>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">قصة الشركة</label>
                                    <textarea name="story" rows="8" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo htmlspecialchars($aboutContent['story'] ?? ''); ?></textarea>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">صورة القصة</label>
                                    <div class="space-y-4">
                                        <!-- معاينة الصورة الحالية -->
                                        <?php if (!empty($aboutContent['story_image'])): ?>
                                            <div class="relative inline-block">
                                                <img src="<?php echo htmlspecialchars($aboutContent['story_image']); ?>" alt="صورة القصة" class="w-32 h-32 object-cover rounded-lg border border-gray-300">
                                                <div class="absolute top-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs">
                                                    الصورة الحالية
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <!-- رفع صورة جديدة -->
                                        <div class="flex items-center space-x-4">
                                            <input type="file" name="story_image_file" accept="image/*" class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100" onchange="previewImage(this, 'story-preview')">
                                        </div>
                                        
                                        <!-- معاينة الصورة الجديدة -->
                                        <div id="story-preview" class="hidden">
                                            <img id="story-preview-img" class="w-32 h-32 object-cover rounded-lg border border-gray-300" alt="معاينة الصورة">
                                            <p class="text-sm text-gray-500 mt-2">معاينة الصورة الجديدة</p>
                                        </div>
                                        
                                        <!-- الاحتفاظ بالمسار الحالي -->
                                        <input type="hidden" name="current_story_image" value="<?php echo htmlspecialchars($aboutContent['story_image'] ?? ''); ?>">
                                        
                                        <p class="text-sm text-gray-500">يمكنك رفع صورة جديدة (JPG, PNG, GIF) أو ترك الحقل فارغاً للاحتفاظ بالصورة الحالية</p>
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">الرسالة</label>
                                    <textarea name="mission" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo htmlspecialchars($aboutContent['mission'] ?? ''); ?></textarea>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">الرؤية</label>
                                    <textarea name="vision" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo htmlspecialchars($aboutContent['vision'] ?? ''); ?></textarea>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">القيم</label>
                                    <textarea name="values" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo htmlspecialchars($aboutContent['values'] ?? ''); ?></textarea>
                                </div>

                                <!-- ألوان خلفية القسم الرئيسي -->
                                <div class="border-t pt-6">
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">ألوان خلفية القسم الرئيسي</h3>
                                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">اللون الأول (غامق)</label>
                                            <input type="color" name="hero_bg_color1" value="<?php echo htmlspecialchars($aboutContent['hero_bg_color1'] ?? '#047857'); ?>" class="w-full h-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">اللون الثاني</label>
                                            <input type="color" name="hero_bg_color2" value="<?php echo htmlspecialchars($aboutContent['hero_bg_color2'] ?? '#059669'); ?>" class="w-full h-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">اللون الثالث</label>
                                            <input type="color" name="hero_bg_color3" value="<?php echo htmlspecialchars($aboutContent['hero_bg_color3'] ?? '#10b981'); ?>" class="w-full h-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">اللون الرابع (فاتح)</label>
                                            <input type="color" name="hero_bg_color4" value="<?php echo htmlspecialchars($aboutContent['hero_bg_color4'] ?? '#34d399'); ?>" class="w-full h-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                    </div>
                                    <p class="text-sm text-gray-500 mt-2">يمكنك تخصيص ألوان التدرج في خلفية القسم الرئيسي لصفحة "من نحن"</p>
                                </div>

                                <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <i class="fas fa-save ml-2"></i>
                                    حفظ التغييرات
                                </button>
                            </form>
                        </div>

                        <!-- محتوى الإحصائيات -->
                        <div x-show="activeTab === 'stats'" class="p-6">
                            <form method="POST" class="space-y-6">
                                <input type="hidden" name="action" value="update_stats">
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">العملاء السعداء</label>
                                        <input type="number" name="happy_clients" value="<?php echo $companyStats['happy_clients'] ?? 0; ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">المشاريع المكتملة</label>
                                        <input type="number" name="completed_projects" value="<?php echo $companyStats['completed_projects'] ?? 0; ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">سنوات الخبرة</label>
                                        <input type="number" name="years_experience" value="<?php echo $companyStats['years_experience'] ?? 0; ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">أعضاء الفريق</label>
                                        <input type="number" name="team_members" value="<?php echo $companyStats['team_members'] ?? 0; ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    </div>
                                </div>

                                <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <i class="fas fa-save ml-2"></i>
                                    حفظ الإحصائيات
                                </button>
                            </form>
                        </div>

                        <!-- محتوى فريق العمل -->
                        <div x-show="activeTab === 'team'" class="p-6">
                            <!-- إضافة عضو جديد -->
                            <div class="mb-8 bg-gray-50 p-6 rounded-lg" x-data="{ showAddForm: false }">
                                <button @click="showAddForm = !showAddForm" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 mb-4">
                                    <i class="fas fa-plus ml-2"></i>
                                    إضافة عضو جديد
                                </button>
                                
                                <form x-show="showAddForm" method="POST" enctype="multipart/form-data" class="space-y-4">
                                    <input type="hidden" name="action" value="add_team_member">
                                    
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">الاسم</label>
                                            <input type="text" name="name" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                        
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">المنصب</label>
                                            <input type="text" name="position" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                        
                                        <div class="md:col-span-2">
                                            <label class="block text-sm font-medium text-gray-700 mb-2">الصورة</label>
                                            <div class="space-y-2">
                                                <input type="file" name="image_file" accept="image/*" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                <p class="text-xs text-gray-500">أو أدخل رابط الصورة:</p>
                                                <input type="text" name="image" placeholder="https://example.com/image.jpg" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            </div>
                                        </div>
                                        
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">ترتيب العرض</label>
                                            <input type="number" name="sort_order" value="0" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                        
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                                            <input type="email" name="email" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                        
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">LinkedIn</label>
                                            <input type="url" name="linkedin" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                        
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Twitter</label>
                                            <input type="url" name="twitter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">النبذة الشخصية</label>
                                        <textarea name="bio" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                                    </div>
                                    
                                    <button type="submit" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                                        <i class="fas fa-plus ml-2"></i>
                                        إضافة العضو
                                    </button>
                                </form>
                            </div>

                            <!-- قائمة أعضاء الفريق -->
                            <div class="space-y-4">
                                <?php if (empty($teamMembers)): ?>
                                    <p class="text-gray-500 text-center py-8">لا يوجد أعضاء فريق حالياً</p>
                                <?php else: ?>
                                    <?php foreach ($teamMembers as $member): ?>
                                        <div class="bg-white border border-gray-200 rounded-lg p-6" x-data="{ editing: false }">
                                            <div x-show="!editing" class="flex items-center justify-between">
                                                <div class="flex items-center space-x-4">
                                                    <?php if ($member['image']): ?>
                                                        <img src="<?php echo htmlspecialchars($member['image']); ?>" alt="<?php echo htmlspecialchars($member['name']); ?>" class="w-16 h-16 rounded-full object-cover">
                                                    <?php else: ?>
                                                        <div class="w-16 h-16 bg-gray-300 rounded-full flex items-center justify-center">
                                                            <i class="fas fa-user text-gray-600 text-xl"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                    <div>
                                                        <h3 class="text-lg font-semibold"><?php echo htmlspecialchars($member['name']); ?></h3>
                                                        <p class="text-gray-600"><?php echo htmlspecialchars($member['position'] ?? ''); ?></p>
                                                        <p class="text-sm text-gray-500">ترتيب: <?php echo $member['sort_order']; ?> | حالة: <?php echo $member['is_active'] ? 'نشط' : 'غير نشط'; ?></p>
                                                    </div>
                                                </div>
                                                <div class="flex space-x-2">
                                                    <button @click="editing = true" class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <form method="POST" class="inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا العضو؟')">
                                                        <input type="hidden" name="action" value="delete_team_member">
                                                        <input type="hidden" name="member_id" value="<?php echo $member['id']; ?>">
                                                        <button type="submit" class="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </div>
                                            
                                            <form x-show="editing" method="POST" enctype="multipart/form-data" class="space-y-4">
                                                <input type="hidden" name="action" value="update_team_member">
                                                <input type="hidden" name="member_id" value="<?php echo $member['id']; ?>">
                                                <input type="hidden" name="current_image" value="<?php echo htmlspecialchars($member['image'] ?? ''); ?>">
                                                
                                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700 mb-2">الاسم</label>
                                                        <input type="text" name="name" value="<?php echo htmlspecialchars($member['name']); ?>" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                    </div>
                                                    
                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700 mb-2">المنصب</label>
                                                        <input type="text" name="position" value="<?php echo htmlspecialchars($member['position'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                    </div>
                                                    
                                                    <div class="md:col-span-2">
                                                        <label class="block text-sm font-medium text-gray-700 mb-2">الصورة</label>
                                                        <div class="space-y-2">
                                                            <?php if ($member['image']): ?>
                                                                <div class="flex items-center space-x-2 mb-2">
                                                                    <img src="<?php echo htmlspecialchars($member['image']); ?>" alt="الصورة الحالية" class="w-16 h-16 rounded-full object-cover">
                                                                    <span class="text-sm text-gray-600">الصورة الحالية</span>
                                                                </div>
                                                            <?php endif; ?>
                                                            <input type="file" name="image_file" accept="image/*" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                            <p class="text-xs text-gray-500">أو أدخل رابط الصورة:</p>
                                                            <input type="text" name="image" value="<?php echo htmlspecialchars($member['image'] ?? ''); ?>" placeholder="https://example.com/image.jpg" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                        </div>
                                                    </div>
                                                    
                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700 mb-2">ترتيب العرض</label>
                                                        <input type="number" name="sort_order" value="<?php echo $member['sort_order']; ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                    </div>
                                                    
                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                                                        <input type="email" name="email" value="<?php echo htmlspecialchars($member['email'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                    </div>
                                                    
                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700 mb-2">LinkedIn</label>
                                                        <input type="url" name="linkedin" value="<?php echo htmlspecialchars($member['linkedin'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                    </div>
                                                    
                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700 mb-2">Twitter</label>
                                                        <input type="url" name="twitter" value="<?php echo htmlspecialchars($member['twitter'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                    </div>
                                                </div>
                                                
                                                <div>
                                                    <label class="block text-sm font-medium text-gray-700 mb-2">النبذة الشخصية</label>
                                                    <textarea name="bio" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo htmlspecialchars($member['bio'] ?? ''); ?></textarea>
                                                </div>
                                                
                                                <div class="flex items-center">
                                                    <input type="checkbox" name="is_active" id="is_active_<?php echo $member['id']; ?>" <?php echo $member['is_active'] ? 'checked' : ''; ?> class="mr-2">
                                                    <label for="is_active_<?php echo $member['id']; ?>" class="text-sm text-gray-700">نشط</label>
                                                </div>
                                                
                                                <div class="flex space-x-2">
                                                    <button type="submit" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                                                        <i class="fas fa-save ml-2"></i>
                                                        حفظ
                                                    </button>
                                                    <button type="button" @click="editing = false" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
                                                        إلغاء
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- محتوى القيم والمبادئ -->
                        <div x-show="activeTab === 'values'" class="p-6">
                            <!-- إضافة قيمة جديدة -->
                            <div class="mb-8 bg-gray-50 p-6 rounded-lg" x-data="{ showAddForm: false }">
                                <button @click="showAddForm = !showAddForm" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 mb-4">
                                    <i class="fas fa-plus ml-2"></i>
                                    إضافة قيمة جديدة
                                </button>
                                
                                <form x-show="showAddForm" method="POST" class="space-y-4">
                                    <input type="hidden" name="action" value="add_value_principle">
                                    
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">العنوان</label>
                                            <input type="text" name="title" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                        
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">الأيقونة (Font Awesome)</label>
                                            <select name="icon" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                <option value="fas fa-star">⭐ نجمة - fas fa-star</option>
                                                <option value="fas fa-heart">❤️ قلب - fas fa-heart</option>
                                                <option value="fas fa-lightbulb">💡 مصباح - fas fa-lightbulb</option>
                                                <option value="fas fa-award">🏆 جائزة - fas fa-award</option>
                                                <option value="fas fa-users">👥 مستخدمون - fas fa-users</option>
                                                <option value="fas fa-handshake">🤝 مصافحة - fas fa-handshake</option>
                                                <option value="fas fa-shield-alt">🛡️ درع - fas fa-shield-alt</option>
                                                <option value="fas fa-rocket">🚀 صاروخ - fas fa-rocket</option>
                                                <option value="fas fa-gem">💎 جوهرة - fas fa-gem</option>
                                                <option value="fas fa-thumbs-up">👍 إعجاب - fas fa-thumbs-up</option>
                                                <option value="fas fa-check-circle">✅ صح - fas fa-check-circle</option>
                                                <option value="fas fa-eye">👁️ عين - fas fa-eye</option>
                                                <option value="fas fa-target">🎯 هدف - fas fa-target</option>
                                                <option value="fas fa-cog">⚙️ إعدادات - fas fa-cog</option>
                                                <option value="fas fa-leaf">🍃 ورقة - fas fa-leaf</option>
                                                <option value="fas fa-globe">🌍 كرة أرضية - fas fa-globe</option>
                                                <option value="fas fa-clock">⏰ ساعة - fas fa-clock</option>
                                                <option value="fas fa-phone">📞 هاتف - fas fa-phone</option>
                                                <option value="fas fa-envelope">✉️ مظروف - fas fa-envelope</option>
                                                <option value="fas fa-map-marker-alt">📍 موقع - fas fa-map-marker-alt</option>
                                            </select>
                                            <p class="text-xs text-gray-500 mt-1">اختر أيقونة من القائمة أو أدخل كود Font Awesome مخصص</p>
                                            <input type="text" name="custom_icon" placeholder="أو أدخل كود مخصص (مثال: fas fa-custom)" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mt-2">
                                        </div>
                                        
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">ترتيب العرض</label>
                                            <input type="number" name="sort_order" value="0" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                                        <textarea name="description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                                    </div>
                                    
                                    <button type="submit" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                                        <i class="fas fa-plus ml-2"></i>
                                        إضافة القيمة
                                    </button>
                                </form>
                            </div>

                            <!-- قائمة القيم والمبادئ -->
                            <div class="space-y-4">
                                <?php if (empty($valuesPrinciples)): ?>
                                    <p class="text-gray-500 text-center py-8">لا توجد قيم ومبادئ حالياً</p>
                                <?php else: ?>
                                    <?php foreach ($valuesPrinciples as $value): ?>
                                        <div class="bg-white border border-gray-200 rounded-lg p-6" x-data="{ editing: false }">
                                            <div x-show="!editing" class="flex items-center justify-between">
                                                <div class="flex items-center space-x-4">
                                                    <div class="ml-4 w-16 h-16 bg-blue-100 rounded-full items-center justify-center">
                                                        <i class="mr-6 mt-4 <?php echo htmlspecialchars($value['icon']); ?> text-blue-600 text-xl"></i>
                                                    </div>
                                                    <div>
                                                        <h3 class="text-lg font-semibold"><?php echo htmlspecialchars($value['title']); ?></h3>
                                                        <p class="text-gray-600"><?php echo htmlspecialchars($value['description'] ?? ''); ?></p>
                                                        <p class="text-sm text-gray-500">ترتيب: <?php echo $value['sort_order']; ?> | حالة: <?php echo $value['is_active'] ? 'نشط' : 'غير نشط'; ?></p>
                                                    </div>
                                                </div>
                                                <div class="flex space-x-2">
                                                    <button @click="editing = true" class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <form method="POST" class="inline" onsubmit="return confirm('هل أنت متأكد من حذف هذه القيمة؟')">
                                                        <input type="hidden" name="action" value="delete_value_principle">
                                                        <input type="hidden" name="value_id" value="<?php echo $value['id']; ?>">
                                                        <button type="submit" class="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </div>
                                            
                                            <form x-show="editing" method="POST" class="space-y-4">
                                                <input type="hidden" name="action" value="update_value_principle">
                                                <input type="hidden" name="value_id" value="<?php echo $value['id']; ?>">
                                                
                                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700 mb-2">العنوان</label>
                                                        <input type="text" name="title" value="<?php echo htmlspecialchars($value['title']); ?>" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                    </div>
                                                    
                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700 mb-2">الأيقونة (Font Awesome)</label>
                                                        <select name="icon" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                            <option value="fas fa-star" <?php echo ($value['icon'] ?? 'fas fa-star') === 'fas fa-star' ? 'selected' : ''; ?>>⭐ نجمة - fas fa-star</option>
                                                            <option value="fas fa-heart" <?php echo ($value['icon'] ?? '') === 'fas fa-heart' ? 'selected' : ''; ?>>❤️ قلب - fas fa-heart</option>
                                                            <option value="fas fa-lightbulb" <?php echo ($value['icon'] ?? '') === 'fas fa-lightbulb' ? 'selected' : ''; ?>>💡 مصباح - fas fa-lightbulb</option>
                                                            <option value="fas fa-award" <?php echo ($value['icon'] ?? '') === 'fas fa-award' ? 'selected' : ''; ?>>🏆 جائزة - fas fa-award</option>
                                                            <option value="fas fa-users" <?php echo ($value['icon'] ?? '') === 'fas fa-users' ? 'selected' : ''; ?>>👥 مستخدمون - fas fa-users</option>
                                                            <option value="fas fa-handshake" <?php echo ($value['icon'] ?? '') === 'fas fa-handshake' ? 'selected' : ''; ?>>🤝 مصافحة - fas fa-handshake</option>
                                                            <option value="fas fa-shield-alt" <?php echo ($value['icon'] ?? '') === 'fas fa-shield-alt' ? 'selected' : ''; ?>>🛡️ درع - fas fa-shield-alt</option>
                                                            <option value="fas fa-rocket" <?php echo ($value['icon'] ?? '') === 'fas fa-rocket' ? 'selected' : ''; ?>>🚀 صاروخ - fas fa-rocket</option>
                                                            <option value="fas fa-gem" <?php echo ($value['icon'] ?? '') === 'fas fa-gem' ? 'selected' : ''; ?>>💎 جوهرة - fas fa-gem</option>
                                                            <option value="fas fa-thumbs-up" <?php echo ($value['icon'] ?? '') === 'fas fa-thumbs-up' ? 'selected' : ''; ?>>👍 إعجاب - fas fa-thumbs-up</option>
                                                            <option value="fas fa-check-circle" <?php echo ($value['icon'] ?? '') === 'fas fa-check-circle' ? 'selected' : ''; ?>>✅ صح - fas fa-check-circle</option>
                                                            <option value="fas fa-eye" <?php echo ($value['icon'] ?? '') === 'fas fa-eye' ? 'selected' : ''; ?>>👁️ عين - fas fa-eye</option>
                                                            <option value="fas fa-target" <?php echo ($value['icon'] ?? '') === 'fas fa-target' ? 'selected' : ''; ?>>🎯 هدف - fas fa-target</option>
                                                            <option value="fas fa-cog" <?php echo ($value['icon'] ?? '') === 'fas fa-cog' ? 'selected' : ''; ?>>⚙️ إعدادات - fas fa-cog</option>
                                                            <option value="fas fa-leaf" <?php echo ($value['icon'] ?? '') === 'fas fa-leaf' ? 'selected' : ''; ?>>🍃 ورقة - fas fa-leaf</option>
                                                            <option value="fas fa-globe" <?php echo ($value['icon'] ?? '') === 'fas fa-globe' ? 'selected' : ''; ?>>🌍 كرة أرضية - fas fa-globe</option>
                                                            <option value="fas fa-clock" <?php echo ($value['icon'] ?? '') === 'fas fa-clock' ? 'selected' : ''; ?>>⏰ ساعة - fas fa-clock</option>
                                                            <option value="fas fa-phone" <?php echo ($value['icon'] ?? '') === 'fas fa-phone' ? 'selected' : ''; ?>>📞 هاتف - fas fa-phone</option>
                                                            <option value="fas fa-envelope" <?php echo ($value['icon'] ?? '') === 'fas fa-envelope' ? 'selected' : ''; ?>>✉️ مظروف - fas fa-envelope</option>
                                                            <option value="fas fa-map-marker-alt" <?php echo ($value['icon'] ?? '') === 'fas fa-map-marker-alt' ? 'selected' : ''; ?>>📍 موقع - fas fa-map-marker-alt</option>
                                                        </select>
                                                        <p class="text-xs text-gray-500 mt-1">اختر أيقونة من القائمة أو أدخل كود Font Awesome مخصص</p>
                                                        <input type="text" name="custom_icon" value="<?php echo !in_array($value['icon'] ?? '', ['fas fa-star', 'fas fa-heart', 'fas fa-lightbulb', 'fas fa-award', 'fas fa-users', 'fas fa-handshake', 'fas fa-shield-alt', 'fas fa-rocket', 'fas fa-gem', 'fas fa-thumbs-up', 'fas fa-check-circle', 'fas fa-eye', 'fas fa-target', 'fas fa-cog', 'fas fa-leaf', 'fas fa-globe', 'fas fa-clock', 'fas fa-phone', 'fas fa-envelope', 'fas fa-map-marker-alt']) ? htmlspecialchars($value['icon'] ?? '') : ''; ?>" placeholder="أو أدخل كود مخصص (مثال: fas fa-custom)" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mt-2">
                                                    </div>
                                                    
                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700 mb-2">ترتيب العرض</label>
                                                        <input type="number" name="sort_order" value="<?php echo $value['sort_order']; ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                    </div>
                                                </div>
                                                
                                                <div>
                                                    <label class="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                                                    <textarea name="description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo htmlspecialchars($value['description'] ?? ''); ?></textarea>
                                                </div>
                                                
                                                <div class="flex items-center">
                                                    <input type="checkbox" name="is_active" id="is_active_value_<?php echo $value['id']; ?>" <?php echo $value['is_active'] ? 'checked' : ''; ?> class="mr-2">
                                                    <label for="is_active_value_<?php echo $value['id']; ?>" class="text-sm text-gray-700">نشط</label>
                                                </div>
                                                
                                                <div class="flex space-x-2">
                                                    <button type="submit" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                                                        <i class="fas fa-save ml-2"></i>
                                                        حفظ
                                                    </button>
                                                    <button type="button" @click="editing = false" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
                                                        إلغاء
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- محتوى لماذا تختارنا؟ -->
                        <div x-show="activeTab === 'why-choose-us'" class="p-6">
                            <!-- إضافة ميزة جديدة -->
                            <div class="mb-8 bg-gray-50 p-6 rounded-lg" x-data="{ showAddForm: false }">
                                <button @click="showAddForm = !showAddForm" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 mb-4">
                                    <i class="fas fa-plus ml-2"></i>
                                    إضافة ميزة جديدة
                                </button>
                                
                                <form x-show="showAddForm" method="POST" class="space-y-4">
                                    <input type="hidden" name="action" value="add_why_choose_us">
                                    
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">العنوان</label>
                                            <input type="text" name="title" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                        
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">الأيقونة (Font Awesome)</label>
                                            <select name="icon" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                <option value="fas fa-star">⭐ نجمة - fas fa-star</option>
                                                <option value="fas fa-heart">❤️ قلب - fas fa-heart</option>
                                                <option value="fas fa-lightbulb">💡 مصباح - fas fa-lightbulb</option>
                                                <option value="fas fa-award">🏆 جائزة - fas fa-award</option>
                                                <option value="fas fa-users">👥 مستخدمون - fas fa-users</option>
                                                <option value="fas fa-handshake">🤝 مصافحة - fas fa-handshake</option>
                                                <option value="fas fa-shield-alt">🛡️ درع - fas fa-shield-alt</option>
                                                <option value="fas fa-rocket">🚀 صاروخ - fas fa-rocket</option>
                                                <option value="fas fa-gem">💎 جوهرة - fas fa-gem</option>
                                                <option value="fas fa-thumbs-up">👍 إعجاب - fas fa-thumbs-up</option>
                                                <option value="fas fa-check-circle">✅ صح - fas fa-check-circle</option>
                                                <option value="fas fa-eye">👁️ عين - fas fa-eye</option>
                                                <option value="fas fa-target">🎯 هدف - fas fa-target</option>
                                                <option value="fas fa-cog">⚙️ إعدادات - fas fa-cog</option>
                                                <option value="fas fa-leaf">🍃 ورقة - fas fa-leaf</option>
                                                <option value="fas fa-globe">🌍 كرة أرضية - fas fa-globe</option>
                                                <option value="fas fa-clock">⏰ ساعة - fas fa-clock</option>
                                                <option value="fas fa-phone">📞 هاتف - fas fa-phone</option>
                                                <option value="fas fa-envelope">✉️ مظروف - fas fa-envelope</option>
                                                <option value="fas fa-map-marker-alt">📍 موقع - fas fa-map-marker-alt</option>
                                            </select>
                                            <p class="text-xs text-gray-500 mt-1">اختر أيقونة من القائمة أو أدخل كود Font Awesome مخصص</p>
                                            <input type="text" name="custom_icon" placeholder="أو أدخل كود مخصص (مثال: fas fa-custom)" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mt-2">
                                        </div>
                                        
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">لون خلفية الكارد</label>
                                            <input type="color" name="background_color" value="#10B981" class="w-full h-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                        
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">لون خلفية الأيقونة</label>
                                            <input type="color" name="icon_background_color" value="#10B981" class="w-full h-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                        
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">لون الأيقونة</label>
                                            <input type="color" name="icon_color" value="#FFFFFF" class="w-full h-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                        
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">شكل خلفية الأيقونة</label>
                                            <select name="icon_shape" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                <option value="rounded">🔲 مربع ناعم الحدود</option>
                                                <option value="circle">⭕ دائري</option>
                                                <option value="square">⬜ مربع</option>
                                                <option value="diamond">💎 معين</option>
                                                <option value="hexagon">⬡ سداسي</option>
                                            </select>
                                        </div>
                                        
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">ترتيب العرض</label>
                                            <input type="number" name="sort_order" value="0" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                                        <textarea name="description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                                    </div>
                                    
                                    <button type="submit" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                                        <i class="fas fa-plus ml-2"></i>
                                        إضافة الميزة
                                    </button>
                                </form>
                            </div>

                            <!-- قائمة الميزات -->
                            <div class="space-y-4">
                                <?php if (empty($whyChooseUsFeatures)): ?>
                                    <p class="text-gray-500 text-center py-8">لا توجد ميزات حالياً</p>
                                <?php else: ?>
                                    <?php foreach ($whyChooseUsFeatures as $feature): ?>
                                        <div class="bg-white border border-gray-200 rounded-lg p-6" x-data="{ editing: false }">
                                            <div x-show="!editing" class="flex items-center justify-between">
                                                <div class="flex items-center space-x-4">
                                                    <div class="icon-container icon-shape-<?php echo htmlspecialchars($feature['icon_shape'] ?? 'rounded'); ?> w-12 h-12 flex items-center justify-center ml-4 flex-shrink-0" style="--icon-bg-color: <?php echo htmlspecialchars($feature['icon_background_color'] ?? $feature['background_color'] ?? '#10B981'); ?>; background-color: <?php echo htmlspecialchars($feature['icon_background_color'] ?? $feature['background_color'] ?? '#10B981'); ?>">
                                                        <i class="<?php echo htmlspecialchars($feature['icon']); ?> text-lg" style="color: <?php echo htmlspecialchars($feature['icon_color'] ?? '#FFFFFF'); ?>"></i>
                                                    </div>
                                                    <div class="mr-4">
                                                        <h3 class="text-lg font-semibold"><?php echo htmlspecialchars($feature['title']); ?></h3>
                                                        <p class="text-gray-600"><?php echo htmlspecialchars($feature['description'] ?? ''); ?></p>
                                                        <p class="text-sm text-gray-500">ترتيب: <?php echo $feature['sort_order']; ?> | حالة: <?php echo $feature['is_active'] ? 'نشط' : 'غير نشط'; ?></p>
                                                    </div>
                                                </div>
                                                <div class="flex space-x-2">
                                                    <button @click="editing = true" class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <form method="POST" class="inline" onsubmit="return confirm('هل أنت متأكد من حذف هذه الميزة؟')">
                                                        <input type="hidden" name="action" value="delete_why_choose_us">
                                                        <input type="hidden" name="why_choose_us_id" value="<?php echo $feature['id']; ?>">
                                                        <button type="submit" class="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </div>
                                            
                                            <form x-show="editing" method="POST" class="space-y-4">
                                                <input type="hidden" name="action" value="update_why_choose_us">
                                                <input type="hidden" name="why_choose_us_id" value="<?php echo $feature['id']; ?>">
                                                
                                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700 mb-2">العنوان</label>
                                                        <input type="text" name="title" value="<?php echo htmlspecialchars($feature['title']); ?>" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                    </div>
                                                    
                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700 mb-2">الأيقونة (Font Awesome)</label>
                                                        <select name="icon" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                            <option value="fas fa-star" <?php echo ($feature['icon'] ?? 'fas fa-star') === 'fas fa-star' ? 'selected' : ''; ?>>⭐ نجمة - fas fa-star</option>
                                                            <option value="fas fa-heart" <?php echo ($feature['icon'] ?? '') === 'fas fa-heart' ? 'selected' : ''; ?>>❤️ قلب - fas fa-heart</option>
                                                            <option value="fas fa-lightbulb" <?php echo ($feature['icon'] ?? '') === 'fas fa-lightbulb' ? 'selected' : ''; ?>>💡 مصباح - fas fa-lightbulb</option>
                                                            <option value="fas fa-award" <?php echo ($feature['icon'] ?? '') === 'fas fa-award' ? 'selected' : ''; ?>>🏆 جائزة - fas fa-award</option>
                                                            <option value="fas fa-users" <?php echo ($feature['icon'] ?? '') === 'fas fa-users' ? 'selected' : ''; ?>>👥 مستخدمون - fas fa-users</option>
                                                            <option value="fas fa-handshake" <?php echo ($feature['icon'] ?? '') === 'fas fa-handshake' ? 'selected' : ''; ?>>🤝 مصافحة - fas fa-handshake</option>
                                                            <option value="fas fa-shield-alt" <?php echo ($feature['icon'] ?? '') === 'fas fa-shield-alt' ? 'selected' : ''; ?>>🛡️ درع - fas fa-shield-alt</option>
                                                            <option value="fas fa-rocket" <?php echo ($feature['icon'] ?? '') === 'fas fa-rocket' ? 'selected' : ''; ?>>🚀 صاروخ - fas fa-rocket</option>
                                                            <option value="fas fa-gem" <?php echo ($feature['icon'] ?? '') === 'fas fa-gem' ? 'selected' : ''; ?>>💎 جوهرة - fas fa-gem</option>
                                                            <option value="fas fa-thumbs-up" <?php echo ($feature['icon'] ?? '') === 'fas fa-thumbs-up' ? 'selected' : ''; ?>>👍 إعجاب - fas fa-thumbs-up</option>
                                                            <option value="fas fa-check-circle" <?php echo ($feature['icon'] ?? '') === 'fas fa-check-circle' ? 'selected' : ''; ?>>✅ صح - fas fa-check-circle</option>
                                                            <option value="fas fa-eye" <?php echo ($feature['icon'] ?? '') === 'fas fa-eye' ? 'selected' : ''; ?>>👁️ عين - fas fa-eye</option>
                                                            <option value="fas fa-target" <?php echo ($feature['icon'] ?? '') === 'fas fa-target' ? 'selected' : ''; ?>>🎯 هدف - fas fa-target</option>
                                                            <option value="fas fa-cog" <?php echo ($feature['icon'] ?? '') === 'fas fa-cog' ? 'selected' : ''; ?>>⚙️ إعدادات - fas fa-cog</option>
                                                            <option value="fas fa-leaf" <?php echo ($feature['icon'] ?? '') === 'fas fa-leaf' ? 'selected' : ''; ?>>🍃 ورقة - fas fa-leaf</option>
                                                            <option value="fas fa-globe" <?php echo ($feature['icon'] ?? '') === 'fas fa-globe' ? 'selected' : ''; ?>>🌍 كرة أرضية - fas fa-globe</option>
                                                            <option value="fas fa-clock" <?php echo ($feature['icon'] ?? '') === 'fas fa-clock' ? 'selected' : ''; ?>>⏰ ساعة - fas fa-clock</option>
                                                            <option value="fas fa-phone" <?php echo ($feature['icon'] ?? '') === 'fas fa-phone' ? 'selected' : ''; ?>>📞 هاتف - fas fa-phone</option>
                                                            <option value="fas fa-envelope" <?php echo ($feature['icon'] ?? '') === 'fas fa-envelope' ? 'selected' : ''; ?>>✉️ مظروف - fas fa-envelope</option>
                                                            <option value="fas fa-map-marker-alt" <?php echo ($feature['icon'] ?? '') === 'fas fa-map-marker-alt' ? 'selected' : ''; ?>>📍 موقع - fas fa-map-marker-alt</option>
                                                        </select>
                                                        <p class="text-xs text-gray-500 mt-1">اختر أيقونة من القائمة أو أدخل كود Font Awesome مخصص</p>
                                                        <input type="text" name="custom_icon" value="<?php echo !in_array($feature['icon'] ?? '', ['fas fa-star', 'fas fa-heart', 'fas fa-lightbulb', 'fas fa-award', 'fas fa-users', 'fas fa-handshake', 'fas fa-shield-alt', 'fas fa-rocket', 'fas fa-gem', 'fas fa-thumbs-up', 'fas fa-check-circle', 'fas fa-eye', 'fas fa-target', 'fas fa-cog', 'fas fa-leaf', 'fas fa-globe', 'fas fa-clock', 'fas fa-phone', 'fas fa-envelope', 'fas fa-map-marker-alt']) ? htmlspecialchars($feature['icon'] ?? '') : ''; ?>" placeholder="أو أدخل كود مخصص (مثال: fas fa-custom)" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mt-2">
                                                    </div>
                                                    
                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700 mb-2">لون خلفية الكارد</label>
                                                        <input type="color" name="background_color" value="<?php echo htmlspecialchars($feature['background_color'] ?? '#10B981'); ?>" class="w-full h-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                    </div>
                                                    
                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700 mb-2">لون خلفية الأيقونة</label>
                                                        <input type="color" name="icon_background_color" value="<?php echo htmlspecialchars($feature['icon_background_color'] ?? '#10B981'); ?>" class="w-full h-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                    </div>
                                                    
                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700 mb-2">لون الأيقونة</label>
                                                        <input type="color" name="icon_color" value="<?php echo htmlspecialchars($feature['icon_color'] ?? '#FFFFFF'); ?>" class="w-full h-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                    </div>
                                                    
                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700 mb-2">شكل خلفية الأيقونة</label>
                                                        <select name="icon_shape" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                            <option value="rounded" <?php echo ($feature['icon_shape'] ?? 'rounded') === 'rounded' ? 'selected' : ''; ?>>🔲 مربع ناعم الحدود</option>
                                                            <option value="circle" <?php echo ($feature['icon_shape'] ?? '') === 'circle' ? 'selected' : ''; ?>>⭕ دائري</option>
                                                            <option value="square" <?php echo ($feature['icon_shape'] ?? '') === 'square' ? 'selected' : ''; ?>>⬜ مربع</option>
                                                            <option value="diamond" <?php echo ($feature['icon_shape'] ?? '') === 'diamond' ? 'selected' : ''; ?>>💎 معين</option>
                                                            <option value="hexagon" <?php echo ($feature['icon_shape'] ?? '') === 'hexagon' ? 'selected' : ''; ?>>⬡ سداسي</option>
                                                        </select>
                                                    </div>
                                                    
                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700 mb-2">ترتيب العرض</label>
                                                        <input type="number" name="sort_order" value="<?php echo $feature['sort_order']; ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                    </div>
                                                </div>
                                                
                                                <div>
                                                    <label class="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                                                    <textarea name="description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo htmlspecialchars($feature['description'] ?? ''); ?></textarea>
                                                </div>
                                                
                                                <div class="flex items-center">
                                                    <input type="checkbox" name="is_active" id="is_active_feature_<?php echo $feature['id']; ?>" <?php echo $feature['is_active'] ? 'checked' : ''; ?> class="mr-2">
                                                    <label for="is_active_feature_<?php echo $feature['id']; ?>" class="text-sm text-gray-700">نشط</label>
                                                </div>
                                                
                                                <div class="flex space-x-2">
                                                    <button type="submit" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                                                        <i class="fas fa-save ml-2"></i>
                                                        حفظ
                                                    </button>
                                                    <button type="button" @click="editing = false" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
                                                        إلغاء
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    
                        <!-- محتوى مسيرتنا -->
                        <div x-show="activeTab === 'journey'" class="p-6">
                            <!-- إضافة عنصر جديد -->
                            <div class="mb-8 bg-gray-50 p-6 rounded-lg" x-data="{ showAddForm: false }">
                                <button @click="showAddForm = !showAddForm" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 mb-4">
                                    <i class="fas fa-plus ml-2"></i>
                                    إضافة عنصر جديد في المسيرة
                                </button>
                                
                                <form x-show="showAddForm" method="POST" class="space-y-4">
                                    <input type="hidden" name="action" value="add_journey_item">
                                    
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">السنة</label>
                                            <input type="number" name="year" min="1900" max="2100" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                        
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">العنوان</label>
                                            <input type="text" name="title" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                        
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">الأيقونة (Font Awesome)</label>
                                            <select name="icon" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                <option value="fas fa-rocket">🚀 صاروخ - fas fa-rocket</option>
                                                <option value="fas fa-lightbulb">💡 مصباح - fas fa-lightbulb</option>
                                                <option value="fas fa-star">⭐ نجمة - fas fa-star</option>
                                                <option value="fas fa-award">🏆 جائزة - fas fa-award</option>
                                                <option value="fas fa-users">👥 مستخدمون - fas fa-users</option>
                                                <option value="fas fa-handshake">🤝 مصافحة - fas fa-handshake</option>
                                                <option value="fas fa-shield-alt">🛡️ درع - fas fa-shield-alt</option>
                                                <option value="fas fa-gem">💎 جوهرة - fas fa-gem</option>
                                                <option value="fas fa-thumbs-up">👍 إعجاب - fas fa-thumbs-up</option>
                                                <option value="fas fa-check-circle">✅ صح - fas fa-check-circle</option>
                                                <option value="fas fa-eye">👁️ عين - fas fa-eye</option>
                                                <option value="fas fa-target">🎯 هدف - fas fa-target</option>
                                                <option value="fas fa-cog">⚙️ إعدادات - fas fa-cog</option>
                                                <option value="fas fa-leaf">🍃 ورقة - fas fa-leaf</option>
                                                <option value="fas fa-globe">🌍 كرة أرضية - fas fa-globe</option>
                                                <option value="fas fa-clock">⏰ ساعة - fas fa-clock</option>
                                                <option value="fas fa-building">🏢 مبنى - fas fa-building</option>
                                                <option value="fas fa-chart-line">📈 رسم بياني - fas fa-chart-line</option>
                                                <option value="fas fa-graduation-cap">🎓 تخرج - fas fa-graduation-cap</option>
                                            </select>
                                            <input type="text" name="custom_icon" placeholder="أو أدخل كود مخصص (مثال: fas fa-custom)" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mt-2">
                                        </div>
                                        
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">لون الخلفية</label>
                                            <input type="color" name="background_color" value="#10B981" class="w-full h-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                        
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">لون الأيقونة</label>
                                            <input type="color" name="icon_color" value="#FFFFFF" class="w-full h-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                        
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">شكل خلفية الأيقونة</label>
                                            <select name="icon_shape" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                <option value="rounded">🔲 مربع ناعم الحدود</option>
                                                <option value="circle">⭕ دائري</option>
                                                <option value="square">⬜ مربع</option>
                                                <option value="diamond">💎 معين</option>
                                                <option value="hexagon">⬡ سداسي</option>
                                            </select>
                                        </div>
                                        
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">ترتيب العرض</label>
                                            <input type="number" name="sort_order" value="0" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                        
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">نوع العنصر</label>
                                            <select name="is_future" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                <option value="0">حدث ماضي/حالي</option>
                                                <option value="1">هدف مستقبلي</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                                        <textarea name="description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                                    </div>
                                    
                                    <button type="submit" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                                        <i class="fas fa-plus ml-2"></i>
                                        إضافة عنصر المسيرة
                                    </button>
                                </form>
                            </div>

                            <!-- عرض عناصر المسيرة الحالية -->
                            <div class="space-y-4">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4">عناصر المسيرة الحالية</h3>
                                
                                <?php if (!empty($ourJourney)): ?>
                                    <div class="grid gap-4">
                                        <?php foreach ($ourJourney as $journey): ?>
                                            <div class="bg-white border border-gray-200 rounded-lg p-6 shadow-sm" x-data="{ editing: false }">
                                                <!-- عرض البيانات -->
                                                <div x-show="!editing" class="flex items-center justify-between">
                                                    <div class="flex items-center space-x-4 space-x-reverse">
                                                        <div class="flex-shrink-0">
                                                            <div class="w-12 h-12 rounded-lg flex items-center justify-center" style="background-color: <?php echo htmlspecialchars($journey['background_color'] ?? '#10B981'); ?>">
                                                                <i class="<?php echo htmlspecialchars($journey['icon'] ?? 'fas fa-star'); ?>" style="color: <?php echo htmlspecialchars($journey['icon_color'] ?? '#FFFFFF'); ?>"></i>
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <h4 class="text-lg font-semibold text-gray-900"><?php echo htmlspecialchars($journey['year']); ?> - <?php echo htmlspecialchars($journey['title']); ?></h4>
                                                            <p class="text-gray-600"><?php echo htmlspecialchars($journey['description']); ?></p>
                                                            <div class="flex items-center space-x-4 space-x-reverse mt-2">
                                                                <span class="text-sm text-gray-500">الترتيب: <?php echo $journey['sort_order']; ?></span>
                                                                <span class="text-sm <?php echo $journey['is_future'] ? 'text-blue-600' : 'text-green-600'; ?>">
                                                                    <?php echo $journey['is_future'] ? 'هدف مستقبلي' : 'حدث ماضي/حالي'; ?>
                                                                </span>
                                                                <span class="text-sm <?php echo $journey['is_active'] ? 'text-green-600' : 'text-red-600'; ?>">
                                                                    <?php echo $journey['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="flex space-x-2 space-x-reverse">
                                                        <button @click="editing = true" class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">
                                                            <i class="fas fa-edit ml-1"></i>
                                                            تعديل
                                                        </button>
                                                        <form method="POST" class="inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا العنصر؟')">
                                                            <input type="hidden" name="action" value="delete_journey_item">
                                                            <input type="hidden" name="journey_id" value="<?php echo $journey['id']; ?>">
                                                            <button type="submit" class="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700">
                                                                <i class="fas fa-trash ml-1"></i>
                                                                حذف
                                                            </button>
                                                        </form>
                                                    </div>
                                                </div>
                                                
                                                <!-- نموذج التعديل -->
                                                <div x-show="editing" class="space-y-4">
                                                    <form method="POST">
                                                        <input type="hidden" name="action" value="update_journey_item">
                                                        <input type="hidden" name="journey_id" value="<?php echo $journey['id']; ?>">
                                                        
                                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                            <div>
                                                                <label class="block text-sm font-medium text-gray-700 mb-2">السنة</label>
                                                                <input type="number" name="year" value="<?php echo $journey['year']; ?>" min="1900" max="2100" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                            </div>
                                                            
                                                            <div>
                                                                <label class="block text-sm font-medium text-gray-700 mb-2">العنوان</label>
                                                                <input type="text" name="title" value="<?php echo htmlspecialchars($journey['title']); ?>" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                            </div>
                                                            
                                                            <div>
                                                                <label class="block text-sm font-medium text-gray-700 mb-2">الأيقونة</label>
                                                                <select name="icon" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                                    <option value="fas fa-rocket" <?php echo ($journey['icon'] ?? '') === 'fas fa-rocket' ? 'selected' : ''; ?>>🚀 صاروخ</option>
                                                                    <option value="fas fa-lightbulb" <?php echo ($journey['icon'] ?? '') === 'fas fa-lightbulb' ? 'selected' : ''; ?>>💡 مصباح</option>
                                                                    <option value="fas fa-star" <?php echo ($journey['icon'] ?? '') === 'fas fa-star' ? 'selected' : ''; ?>>⭐ نجمة</option>
                                                                    <option value="fas fa-award" <?php echo ($journey['icon'] ?? '') === 'fas fa-award' ? 'selected' : ''; ?>>🏆 جائزة</option>
                                                                    <option value="fas fa-users" <?php echo ($journey['icon'] ?? '') === 'fas fa-users' ? 'selected' : ''; ?>>👥 مستخدمون</option>
                                                                    <option value="fas fa-handshake" <?php echo ($journey['icon'] ?? '') === 'fas fa-handshake' ? 'selected' : ''; ?>>🤝 مصافحة</option>
                                                                    <option value="fas fa-shield-alt" <?php echo ($journey['icon'] ?? '') === 'fas fa-shield-alt' ? 'selected' : ''; ?>>🛡️ درع</option>
                                                                    <option value="fas fa-gem" <?php echo ($journey['icon'] ?? '') === 'fas fa-gem' ? 'selected' : ''; ?>>💎 جوهرة</option>
                                                                    <option value="fas fa-building" <?php echo ($journey['icon'] ?? '') === 'fas fa-building' ? 'selected' : ''; ?>>🏢 مبنى</option>
                                                                    <option value="fas fa-chart-line" <?php echo ($journey['icon'] ?? '') === 'fas fa-chart-line' ? 'selected' : ''; ?>>📈 رسم بياني</option>
                                                                </select>
                                                                <input type="text" name="custom_icon" value="<?php echo !in_array($journey['icon'] ?? '', ['fas fa-rocket', 'fas fa-lightbulb', 'fas fa-star', 'fas fa-award', 'fas fa-users', 'fas fa-handshake', 'fas fa-shield-alt', 'fas fa-gem', 'fas fa-building', 'fas fa-chart-line']) ? htmlspecialchars($journey['icon'] ?? '') : ''; ?>" placeholder="أو أدخل كود مخصص" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mt-2">
                                                            </div>
                                                            
                                                            <div>
                                                                <label class="block text-sm font-medium text-gray-700 mb-2">لون الخلفية</label>
                                                                <input type="color" name="background_color" value="<?php echo htmlspecialchars($journey['background_color'] ?? '#10B981'); ?>" class="w-full h-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                            </div>
                                                            
                                                            <div>
                                                                <label class="block text-sm font-medium text-gray-700 mb-2">لون الأيقونة</label>
                                                                <input type="color" name="icon_color" value="<?php echo htmlspecialchars($journey['icon_color'] ?? '#FFFFFF'); ?>" class="w-full h-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                            </div>
                                                            
                                                            <div>
                                                                <label class="block text-sm font-medium text-gray-700 mb-2">ترتيب العرض</label>
                                                                <input type="number" name="sort_order" value="<?php echo $journey['sort_order']; ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                            </div>
                                                            
                                                            <div>
                                                                <label class="block text-sm font-medium text-gray-700 mb-2">نوع العنصر</label>
                                                                <select name="is_future" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                                    <option value="0" <?php echo !$journey['is_future'] ? 'selected' : ''; ?>>حدث ماضي/حالي</option>
                                                                    <option value="1" <?php echo $journey['is_future'] ? 'selected' : ''; ?>>هدف مستقبلي</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                        
                                                        <div>
                                                            <label class="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                                                            <textarea name="description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo htmlspecialchars($journey['description']); ?></textarea>
                                                        </div>
                                                        
                                                        <div class="flex items-center mb-4">
                                                            <input type="checkbox" name="is_active" id="is_active_journey_<?php echo $journey['id']; ?>" <?php echo $journey['is_active'] ? 'checked' : ''; ?> class="mr-2">
                                                            <label for="is_active_journey_<?php echo $journey['id']; ?>" class="text-sm text-gray-700">نشط</label>
                                                        </div>
                                                        
                                                        <div class="flex space-x-2 space-x-reverse">
                                                            <button type="submit" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                                                                <i class="fas fa-save ml-2"></i>
                                                                حفظ
                                                            </button>
                                                            <button type="button" @click="editing = false" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
                                                                إلغاء
                                                            </button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php else: ?>
                                    <div class="text-center py-8 text-gray-500">
                                        <i class="fas fa-timeline text-4xl mb-4"></i>
                                        <p>لا توجد عناصر في المسيرة حالياً</p>
                                        <p class="text-sm">استخدم النموذج أعلاه لإضافة عناصر جديدة</p>
                                    </div>
                                <?php endif; ?>
                            </div>

                        </div>
                    
                </div>
            </main>
        </div>
    </div>
<script>
function previewStoryImage(input) {
    const preview = document.getElementById('story-image-preview');
    const previewImg = document.getElementById('story-preview-img');
    
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            previewImg.src = e.target.result;
            preview.style.display = 'block';
        }
        
        reader.readAsDataURL(input.files[0]);
    }
}
</script>
<?php endLayout(); ?>