<?php
/**
 * ملف تشغيل الاختبارات الرئيسي
 * Main Test Runner File
 */

// تعيين مسار المشروع
define('PROJECT_ROOT', dirname(__DIR__));

// تضمين الملفات المطلوبة
require_once PROJECT_ROOT . '/includes/database.php';
require_once __DIR__ . '/TestRunner.php';

// إعداد البيئة للاختبار
ini_set('display_errors', 1);
error_reporting(E_ALL);

// بدء الجلسة للاختبارات التي تتطلب ذلك
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "\n";
echo "███████╗██████╗ ███████╗███████╗███╗   ██╗    ██╗     ██╗███╗   ██╗███████╗\n";
echo "██╔════╝██╔══██╗██╔════╝██╔════╝████╗  ██║    ██║     ██║████╗  ██║██╔════╝\n";
echo "██║  ███╗██████╔╝█████╗  █████╗  ██╔██╗ ██║    ██║     ██║██╔██╗ ██║█████╗  \n";
echo "██║   ██║██╔══██╗██╔══╝  ██╔══╝  ██║╚██╗██║    ██║     ██║██║╚██╗██║██╔══╝  \n";
echo "╚██████╔╝██║  ██║███████╗███████╗██║ ╚████║    ███████╗██║██║ ╚████║███████╗\n";
echo " ╚═════╝ ╚═╝  ╚═╝╚══════╝╚══════╝╚═╝  ╚═══╝    ╚══════╝╚═╝╚═╝  ╚═══╝╚══════╝\n";
echo "\n";
echo "                    🧪 نظام اختبار Green Line 🧪\n";
echo "\n";

/**
 * فئة إعداد الاختبارات
 */
class TestSetup {
    private $database;
    
    public function __construct() {
        global $database;
        $this->database = $database;
    }
    
    /**
     * إعداد بيانات الاختبار
     */
    public function setupTestData() {
        echo "⚙️  إعداد بيانات الاختبار...\n";
        
        // إنشاء مستخدم اختبار
        $this->createTestUser();
        
        // إنشاء منتج اختبار
        $this->createTestProduct();
        
        // إنشاء فئة اختبار
        $this->createTestCategory();
        
        echo "✅ تم إعداد بيانات الاختبار بنجاح\n\n";
    }
    
    /**
     * تنظيف بيانات الاختبار
     */
    public function cleanupTestData() {
        echo "\n🧹 تنظيف بيانات الاختبار...\n";
        
        // حذف بيانات الاختبار
        $this->database->delete('users', "email LIKE '<EMAIL>'");
        $this->database->delete('products', "name LIKE 'منتج اختبار%'");
        $this->database->delete('product_categories', "name LIKE 'فئة اختبار%'");
        $this->database->delete('rate_limits', "identifier LIKE 'test_%'");
        
        echo "✅ تم تنظيف بيانات الاختبار\n";
    }
    
    /**
     * إنشاء مستخدم اختبار
     */
    private function createTestUser() {
        $testUser = [
            'name' => 'مستخدم اختبار',
            'email' => '<EMAIL>',
            'password' => password_hash('test123', PASSWORD_DEFAULT),
            'phone' => '0501234567',
            'role' => 'user',
            'status' => 'active'
        ];
        
        // التحقق من عدم وجود المستخدم
        $existing = $this->database->fetch(
            "SELECT id FROM users WHERE email = :email",
            ['email' => $testUser['email']]
        );
        
        if (!$existing) {
            $this->database->insert('users', $testUser);
        }
    }
    
    /**
     * إنشاء منتج اختبار
     */
    private function createTestProduct() {
        $testProduct = [
            'name' => 'منتج اختبار',
            'description' => 'وصف منتج الاختبار',
            'price' => 99.99,
            'stock_quantity' => 10,
            'category_id' => 1,
            'status' => 'active'
        ];
        
        $existing = $this->database->fetch(
            "SELECT id FROM products WHERE name = :name",
            ['name' => $testProduct['name']]
        );
        
        if (!$existing) {
            $this->database->insert('products', $testProduct);
        }
    }
    
    /**
     * إنشاء فئة اختبار
     */
    private function createTestCategory() {
        $testCategory = [
            'name' => 'فئة اختبار',
            'description' => 'وصف فئة الاختبار',
            'status' => 'active'
        ];
        
        $existing = $this->database->fetch(
            "SELECT id FROM product_categories WHERE name = :name",
            ['name' => $testCategory['name']]
        );
        
        if (!$existing) {
            $this->database->insert('product_categories', $testCategory);
        }
    }
}

/**
 * فئة تقرير الاختبارات
 */
class TestReporter {
    
    /**
     * إنشاء تقرير HTML
     */
    public function generateHTMLReport($results) {
        $html = "
        <!DOCTYPE html>
        <html dir='rtl' lang='ar'>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>تقرير اختبارات Green Line</title>
            <style>
                body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; }
                .header { background: #2c5530; color: white; padding: 20px; text-align: center; }
                .summary { background: #f8f9fa; padding: 15px; margin: 20px 0; border-radius: 5px; }
                .test-result { margin: 10px 0; padding: 10px; border-radius: 3px; }
                .passed { background: #d4edda; border: 1px solid #c3e6cb; }
                .failed { background: #f8d7da; border: 1px solid #f5c6cb; }
                .timestamp { color: #666; font-size: 0.9em; }
            </style>
        </head>
        <body>
            <div class='header'>
                <h1>🧪 تقرير اختبارات Green Line</h1>
                <p class='timestamp'>تم إنشاؤه في: " . date('Y-m-d H:i:s') . "</p>
            </div>
            
            <div class='summary'>
                <h2>📊 ملخص النتائج</h2>
                <p><strong>إجمالي الاختبارات:</strong> {$results['total']}</p>
                <p><strong>نجح:</strong> {$results['passed']}</p>
                <p><strong>فشل:</strong> {$results['failed']}</p>
                <p><strong>معدل النجاح:</strong> {$results['success_rate']}%</p>
            </div>
            
            <div class='details'>
                <h2>📋 تفاصيل الاختبارات</h2>
        ";
        
        foreach ($results['tests'] as $test) {
            $class = $test['status'] === 'passed' ? 'passed' : 'failed';
            $icon = $test['status'] === 'passed' ? '✅' : '❌';
            
            $html .= "
                <div class='test-result {$class}'>
                    <strong>{$icon} {$test['name']}</strong>
            ";
            
            if ($test['status'] === 'failed') {
                $html .= "<br><small>خطأ: {$test['message']}</small>";
            }
            
            $html .= "</div>";
        }
        
        $html .= "
            </div>
        </body>
        </html>
        ";
        
        file_put_contents(__DIR__ . '/test_report.html', $html);
        echo "📄 تم إنشاء تقرير HTML: tests/test_report.html\n";
    }
}

// الدالة الرئيسية
function main() {
    // التحقق من الاتصال بقاعدة البيانات
    global $database;
    if (!$database || !$database->isConnected()) {
        echo "❌ خطأ: لا يمكن الاتصال بقاعدة البيانات\n";
        exit(1);
    }
    
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح\n\n";
    
    // إعداد الاختبارات
    $setup = new TestSetup();
    $setup->setupTestData();
    
    // إنشاء مشغل الاختبارات
    $runner = new TestRunner();
    
    // إضافة الاختبارات
    $runner->addTest(new AuthTest());
    $runner->addTest(new PermissionsTest());
    $runner->addTest(new CacheTest());
    $runner->addTest(new CSRFTest());
    $runner->addTest(new RateLimiterTest());
    $runner->addTest(new IntegrationTest());
    $runner->addTest(new PerformanceTest());
    
    // تشغيل الاختبارات
    $runner->runAll();
    
    // تنظيف البيانات
    $setup->cleanupTestData();
    
    echo "\n🎉 انتهت جميع الاختبارات!\n";
}

// معالجة المعاملات من سطر الأوامر
if (isset($argv[1])) {
    switch ($argv[1]) {
        case '--help':
        case '-h':
            echo "\nاستخدام: php run_tests.php [خيارات]\n";
            echo "\nالخيارات:\n";
            echo "  --help, -h     عرض هذه المساعدة\n";
            echo "  --setup        إعداد بيانات الاختبار فقط\n";
            echo "  --cleanup      تنظيف بيانات الاختبار فقط\n";
            echo "  --unit         تشغيل اختبارات الوحدة فقط\n";
            echo "  --integration  تشغيل اختبارات التكامل فقط\n";
            echo "\n";
            exit(0);
            
        case '--setup':
            $setup = new TestSetup();
            $setup->setupTestData();
            echo "✅ تم إعداد بيانات الاختبار\n";
            exit(0);
            
        case '--cleanup':
            $setup = new TestSetup();
            $setup->cleanupTestData();
            exit(0);
            
        case '--unit':
            echo "🧪 تشغيل اختبارات الوحدة فقط...\n\n";
            $runner = new TestRunner();
            $runner->addTest(new AuthTest());
            $runner->addTest(new PermissionsTest());
            $runner->addTest(new CacheTest());
            $runner->addTest(new CSRFTest());
            $runner->addTest(new RateLimiterTest());
            $runner->runAll();
            exit(0);
            
        case '--integration':
            echo "🔗 تشغيل اختبارات التكامل فقط...\n\n";
            $setup = new TestSetup();
            $setup->setupTestData();
            $runner = new TestRunner();
            $runner->addTest(new IntegrationTest());
            $runner->runAll();
            $setup->cleanupTestData();
            exit(0);
            
        default:
            echo "❌ خيار غير معروف: {$argv[1]}\n";
            echo "استخدم --help للمساعدة\n";
            exit(1);
    }
}

// تشغيل الاختبارات الرئيسية
try {
    main();
} catch (Exception $e) {
    echo "\n❌ خطأ في تشغيل الاختبارات: " . $e->getMessage() . "\n";
    exit(1);
}

?>