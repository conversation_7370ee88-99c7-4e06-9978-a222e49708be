<?php
$pageTitle = 'الأسئلة الشائعة';
$page = 'faq';

// جلب الأسئلة الشائعة
$category = $_GET['category'] ?? 'all';
$search = $_GET['search'] ?? '';

$faqs = getFAQs(null, $category, $search);
$faq_categories = getFAQCategories();

// جلب عدد التقييمات لجميع الأسئلة
$all_ratings = getAllFAQRatingCounts();

include 'header.php';
?>

<!-- Hero Section -->
<section class="bg-gradient-to-r from-primary to-secondary py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">
            الأسئلة الشائعة
        </h1>
        <p class="text-xl text-white opacity-90 max-w-2xl mx-auto">
            إجابات شاملة على أكثر الأسئلة شيوعاً حول خدماتنا ومنتجاتنا
        </p>
    </div>
</section>

<!-- Search and Filter Section -->
<section class="py-8 bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <!-- Search Bar -->
        <div class="mb-8">
            <form method="GET" class="relative">
                <input type="hidden" name="category" value="<?php echo htmlspecialchars($category); ?>">
                <div class="relative">
                    <input type="text" 
                           name="search" 
                           value="<?php echo htmlspecialchars($search); ?>"
                           placeholder="ابحث في الأسئلة الشائعة..."
                           class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    <button type="submit" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-primary">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </button>
                </div>
            </form>
        </div>
        
        <!-- Category Filter -->
        <div class="flex flex-wrap gap-2 justify-center">
            <a href="<?php echo SITE_URL; ?>/faq<?php echo $search ? '?search=' . urlencode($search) : ''; ?>" 
               class="px-4 py-2 rounded-full text-sm font-medium transition-colors duration-300 <?php echo $category === 'all' ? 'bg-primary text-white' : 'bg-white text-gray-700 hover:bg-primary hover:text-white'; ?>">
                جميع الأسئلة
            </a>
            
            <?php foreach ($faq_categories as $cat): ?>
                <a href="<?php echo SITE_URL; ?>/faq?category=<?php echo urlencode($cat['slug']); ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>" 
                   class="px-4 py-2 rounded-full text-sm font-medium transition-colors duration-300 <?php echo $category === $cat['slug'] ? 'bg-primary text-white' : 'bg-white text-gray-700 hover:bg-primary hover:text-white'; ?>">
                    <?php echo htmlspecialchars($cat['name']); ?>
                    <span class="mr-1 text-xs opacity-75">(<?php echo $cat['count']; ?>)</span>
                </a>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- FAQ Content -->
<section class="py-16">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <?php if (!empty($search)): ?>
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-2">
                    نتائج البحث عن: "<?php echo htmlspecialchars($search); ?>"
                </h2>
                <p class="text-gray-600">
                    تم العثور على <?php echo count($faqs); ?> نتيجة
                </p>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($faqs)): ?>
            <div class="space-y-4">
                <?php foreach ($faqs as $index => $faq): ?>
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-300">
                        <button class="faq-toggle w-full px-6 py-5 text-right focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 rounded-lg" 
                                data-target="faq-<?php echo $index; ?>">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <h3 class="font-semibold text-gray-900 text-lg mb-1">
                                        <?php echo htmlspecialchars($faq['question']); ?>
                                    </h3>
                                    <?php if (!empty($faq['category_name'])): ?>
                                        <span class="inline-block bg-primary bg-opacity-10 text-primary px-2 py-1 rounded-full text-xs font-medium">
                                            <?php echo htmlspecialchars($faq['category_name']); ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                                
                                <svg class="faq-icon w-6 h-6 text-gray-500 transform transition-transform duration-200 flex-shrink-0 mr-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </button>
                        
                        <div id="faq-<?php echo $index; ?>" class="faq-content hidden px-6 pb-5">
                            <div class="prose prose-lg max-w-none text-gray-700">
                                <?php echo nl2br(htmlspecialchars($faq['answer'])); ?>
                            </div>
                            
                            <!-- Helpful Rating -->
                            <div class="mt-6 pt-4 border-t border-gray-200">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-600">هل كانت هذه الإجابة مفيدة؟</span>
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <?php 
                                        $faq_ratings = $all_ratings[$faq['id']] ?? ['yes_count' => 0, 'no_count' => 0, 'total_count' => 0];
                                        ?>
                                        <button onclick="rateFAQ(<?php echo $faq['id']; ?>, 'yes')" 
                                                class="flex items-center px-3 py-1 text-sm text-green-600 hover:bg-green-50 rounded-lg transition-colors duration-300"
                                                id="yes-btn-<?php echo $faq['id']; ?>">
                                            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"></path>
                                            </svg>
                                            نعم <span class="mr-1 text-xs opacity-75" id="yes-count-<?php echo $faq['id']; ?>">(<?php echo $faq_ratings['yes_count']; ?>)</span>
                                        </button>
                                        <button onclick="rateFAQ(<?php echo $faq['id']; ?>, 'no')" 
                                                class="flex items-center px-3 py-1 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-300"
                                                id="no-btn-<?php echo $faq['id']; ?>">
                                            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14H5.236a2 2 0 01-1.789-2.894l3.5-7A2 2 0 018.736 3h4.018c.163 0 .326.02.485.60L17 4m-7 10v2a2 2 0 002 2h.095c.5 0 .905-.405.905-.905 0-.714.211-1.412.608-2.006L17 13V4m-7 10h2m5-10h2a2 2 0 012 2v6a2 2 0 01-2 2h-2.5"></path>
                                            </svg>
                                            لا <span class="mr-1 text-xs opacity-75" id="no-count-<?php echo $faq['id']; ?>">(<?php echo $faq_ratings['no_count']; ?>)</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <!-- No Results -->
            <div class="text-center py-12">
                <svg class="mx-auto h-16 w-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                
                <?php if (!empty($search)): ?>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">لم نجد نتائج لبحثك</h3>
                    <p class="text-gray-600 mb-6">جرب استخدام كلمات مختلفة أو تصفح الأسئلة حسب التصنيف</p>
                    <a href="<?php echo SITE_URL; ?>/faq" class="inline-flex items-center text-primary hover:text-secondary font-medium">
                        عرض جميع الأسئلة
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </a>
                <?php else: ?>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد أسئلة في هذا التصنيف</h3>
                    <p class="text-gray-600 mb-6">جرب تصنيفاً آخر أو تصفح جميع الأسئلة</p>
                    <a href="<?php echo SITE_URL; ?>/faq" class="inline-flex items-center text-primary hover:text-secondary font-medium">
                        عرض جميع الأسئلة
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Popular Questions -->
<?php if (empty($search) && $category === 'all'): ?>
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">
                الأسئلة الأكثر شيوعاً
            </h2>
            <p class="text-gray-600">
                الأسئلة التي يطرحها عملاؤنا بكثرة
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <?php 
            $popular_faqs = getFAQs(6, 'all', '', 'popular');
            foreach ($popular_faqs as $faq):
            ?>
                <div class="bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow duration-300">
                    <div class="flex items-start mb-4">
                        <div class="w-8 h-8 bg-primary bg-opacity-10 rounded-full flex items-center justify-center flex-shrink-0 ml-3">
                            <svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h3 class="font-semibold text-gray-900 text-sm leading-tight">
                            <?php echo htmlspecialchars($faq['question']); ?>
                        </h3>
                    </div>
                    
                    <p class="text-gray-600 text-sm line-clamp-3">
                        <?php echo htmlspecialchars(substr($faq['answer'], 0, 120)) . '...'; ?>
                    </p>
                    
                    <button onclick="scrollToFAQ(<?php echo $faq['id']; ?>)" 
                            class="mt-4 text-primary hover:text-secondary text-sm font-medium">
                        اقرأ الإجابة الكاملة
                    </button>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Contact Section -->
<section class="py-16">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="bg-gradient-to-r from-primary to-secondary rounded-lg p-8 text-white">
            <h2 class="text-2xl md:text-3xl font-bold mb-4">
                لم تجد إجابة لسؤالك؟
            </h2>
            <p class="text-lg opacity-90 mb-6">
                لا تتردد في التواصل معنا، فريقنا جاهز لمساعدتك
            </p>
            
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="<?php echo SITE_URL; ?>/contact" 
                   class="bg-white text-primary px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-300">
                    تواصل معنا
                </a>
                
                <?php 
                $whatsapp = getSetting('whatsapp_number');
                if ($whatsapp):
                ?>
                    <a href="https://wa.me/<?php echo preg_replace('/[^0-9]/', '', $whatsapp); ?>?text=مرحباً، لدي سؤال حول خدماتكم" 
                       target="_blank" 
                       class="border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary transition-colors duration-300">
                        واتساب
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Quick Links -->
<section class="py-12 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">
                روابط مفيدة
            </h2>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <a href="<?php echo SITE_URL; ?>/products" 
               class="bg-white rounded-lg p-6 text-center hover:shadow-md transition-shadow duration-300 group">
                <div class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:bg-secondary transition-colors duration-300">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                </div>
                <h3 class="font-semibold text-gray-900 mb-2">منتجاتنا</h3>
                <p class="text-gray-600 text-sm">تصفح مجموعة منتجاتنا المتنوعة</p>
            </a>
            
            <a href="<?php echo SITE_URL; ?>/about" 
               class="bg-white rounded-lg p-6 text-center hover:shadow-md transition-shadow duration-300 group">
                <div class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:bg-secondary transition-colors duration-300">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <h3 class="font-semibold text-gray-900 mb-2">من نحن</h3>
                <p class="text-gray-600 text-sm">تعرف على قصتنا ورؤيتنا</p>
            </a>
            
            <a href="<?php echo SITE_URL; ?>/contact" 
               class="bg-white rounded-lg p-6 text-center hover:shadow-md transition-shadow duration-300 group">
                <div class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:bg-secondary transition-colors duration-300">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                    </svg>
                </div>
                <h3 class="font-semibold text-gray-900 mb-2">اتصل بنا</h3>
                <p class="text-gray-600 text-sm">تواصل معنا للاستفسارات</p>
            </a>
            
            <a href="<?php echo SITE_URL; ?>/distributors" 
               class="bg-white rounded-lg p-6 text-center hover:shadow-md transition-shadow duration-300 group">
                <div class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:bg-secondary transition-colors duration-300">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </div>
                <h3 class="font-semibold text-gray-900 mb-2">الموزعون</h3>
                <p class="text-gray-600 text-sm">اعثر على أقرب موزع لك</p>
            </a>
        </div>
    </div>
</section>

<script>
// FAQ Toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    const faqToggles = document.querySelectorAll('.faq-toggle');
    
    faqToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            const targetId = this.dataset.target;
            const content = document.getElementById(targetId);
            const icon = this.querySelector('.faq-icon');
            
            // Close all other FAQs
            faqToggles.forEach(otherToggle => {
                if (otherToggle !== this) {
                    const otherTargetId = otherToggle.dataset.target;
                    const otherContent = document.getElementById(otherTargetId);
                    const otherIcon = otherToggle.querySelector('.faq-icon');
                    
                    if (otherContent && !otherContent.classList.contains('hidden')) {
                        otherContent.classList.add('hidden');
                        otherIcon.style.transform = 'rotate(0deg)';
                    }
                }
            });
            
            // Toggle current FAQ
            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                icon.style.transform = 'rotate(180deg)';
            } else {
                content.classList.add('hidden');
                icon.style.transform = 'rotate(0deg)';
            }
        });
    });
    
    // Auto-open FAQ if hash is present
    const hash = window.location.hash;
    if (hash) {
        const targetElement = document.querySelector(hash);
        if (targetElement) {
            const toggle = targetElement.previousElementSibling;
            if (toggle && toggle.classList.contains('faq-toggle')) {
                toggle.click();
                setTimeout(() => {
                    targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }, 300);
            }
        }
    }
});

// FAQ Rating
function rateFAQ(faqId, rating) {
    // العثور على أزرار التقييم للسؤال المحدد
    const ratingButtons = document.querySelectorAll(`[onclick*="rateFAQ(${faqId}"]`);
    const ratingContainer = ratingButtons[0]?.closest('.flex');
    
    // تعطيل الأزرار أثناء الإرسال
    ratingButtons.forEach(btn => {
        btn.disabled = true;
        btn.style.opacity = '0.5';
        btn.style.cursor = 'not-allowed';
    });
    
    // إضافة مؤشر التحميل
    if (ratingContainer) {
        const loadingSpinner = document.createElement('div');
        loadingSpinner.className = 'loading-spinner ml-2';
        loadingSpinner.innerHTML = '<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>';
        ratingContainer.appendChild(loadingSpinner);
    }
    
    fetch('/api/rate-faq.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            faq_id: faqId,
            rating: rating
        })
    })
    .then(response => response.json())
    .then(data => {
        // إزالة مؤشر التحميل
        const loadingSpinner = ratingContainer?.querySelector('.loading-spinner');
        if (loadingSpinner) {
            loadingSpinner.remove();
        }
        
        if (data.success) {
            // تحديث عدد التقييمات
            if (data.new_counts) {
                const yesCountElement = document.getElementById(`yes-count-${faqId}`);
                const noCountElement = document.getElementById(`no-count-${faqId}`);
                
                if (yesCountElement) {
                    yesCountElement.textContent = `(${data.new_counts.yes_count})`;
                }
                if (noCountElement) {
                    noCountElement.textContent = `(${data.new_counts.no_count})`;
                }
            }
            
            // إظهار رسالة نجاح
            showRatingMessage(ratingContainer, 'شكراً لك على تقييمك!', 'success');
            
            // تمييز الزر المختار
            ratingButtons.forEach(btn => {
                btn.style.opacity = '0.3';
                if (btn.onclick.toString().includes(`'${rating}'`)) {
                    btn.style.opacity = '1';
                    btn.classList.add('bg-green-100', 'text-green-700');
                }
            });
            
            // إخفاء الأزرار بعد 3 ثوان
            setTimeout(() => {
                if (ratingContainer) {
                    ratingContainer.style.transition = 'opacity 0.5s ease';
                    ratingContainer.style.opacity = '0.5';
                }
            }, 3000);
            
        } else {
            // إظهار رسالة خطأ
            showRatingMessage(ratingContainer, data.message || 'حدث خطأ أثناء حفظ التقييم', 'error');
            
            // إعادة تفعيل الأزرار
            ratingButtons.forEach(btn => {
                btn.disabled = false;
                btn.style.opacity = '1';
                btn.style.cursor = 'pointer';
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        
        // إزالة مؤشر التحميل
        const loadingSpinner = ratingContainer?.querySelector('.loading-spinner');
        if (loadingSpinner) {
            loadingSpinner.remove();
        }
        
        // إظهار رسالة خطأ
        showRatingMessage(ratingContainer, 'حدث خطأ في الاتصال، يرجى المحاولة مرة أخرى', 'error');
        
        // إعادة تفعيل الأزرار
        ratingButtons.forEach(btn => {
            btn.disabled = false;
            btn.style.opacity = '1';
            btn.style.cursor = 'pointer';
        });
    });
}

// دالة لإظهار رسائل التقييم
function showRatingMessage(container, message, type) {
    if (!container) return;
    
    // إزالة أي رسالة سابقة
    const existingMessage = container.querySelector('.rating-message');
    if (existingMessage) {
        existingMessage.remove();
    }
    
    // إنشاء رسالة جديدة
    const messageDiv = document.createElement('div');
    messageDiv.className = `rating-message text-sm mt-2 p-2 rounded-lg ${
        type === 'success' 
            ? 'bg-green-100 text-green-700 border border-green-200' 
            : 'bg-red-100 text-red-700 border border-red-200'
    }`;
    messageDiv.textContent = message;
    
    // إضافة الرسالة
    container.appendChild(messageDiv);
    
    // إزالة الرسالة بعد 5 ثوان
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.style.transition = 'opacity 0.5s ease';
            messageDiv.style.opacity = '0';
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.remove();
                }
            }, 500);
        }
    }, 5000);
}

// Scroll to specific FAQ
function scrollToFAQ(faqId) {
    // Find the FAQ element and open it
    const faqElements = document.querySelectorAll('[data-target^="faq-"]');
    faqElements.forEach((element, index) => {
        const content = document.getElementById(element.dataset.target);
        if (content && content.querySelector(`[onclick*="${faqId}"]`)) {
            element.click();
            setTimeout(() => {
                element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }, 300);
        }
    });
}

// Search functionality enhancement
const searchInput = document.querySelector('input[name="search"]');
if (searchInput) {
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            this.form.submit();
        }
    });
}
</script>

<?php include 'footer.php'; ?>