<?php
/**
 * Fix support_files table structure
 */

require_once __DIR__ . '/config/database.php';

try {
    $database = new Database();
    $pdo = $database->getConnection();
    
    echo "Checking support_files table structure...\n";
    
    // Check if table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'support_files'");
    if ($stmt->rowCount() == 0) {
        echo "Creating support_files table...\n";
        $sql = "CREATE TABLE support_files (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            file_path VARCHAR(500) NOT NULL,
            file_size INT,
            upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $pdo->exec($sql);
        echo "✅ support_files table created successfully!\n";
    } else {
        echo "support_files table exists. Checking columns...\n";
        
        // Check columns
        $stmt = $pdo->query("DESCRIBE support_files");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $hasUploadDate = false;
        $hasCreatedAt = false;
        
        foreach ($columns as $column) {
            echo "- " . $column['Field'] . " (" . $column['Type'] . ")\n";
            if ($column['Field'] === 'upload_date') {
                $hasUploadDate = true;
            }
            if ($column['Field'] === 'created_at') {
                $hasCreatedAt = true;
            }
        }
        
        // Fix column issues
        if (!$hasUploadDate && $hasCreatedAt) {
            echo "Renaming created_at to upload_date...\n";
            $pdo->exec("ALTER TABLE support_files CHANGE created_at upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP");
            echo "✅ Column renamed successfully!\n";
        } elseif (!$hasUploadDate && !$hasCreatedAt) {
            echo "Adding upload_date column...\n";
            $pdo->exec("ALTER TABLE support_files ADD COLUMN upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP");
            echo "✅ upload_date column added successfully!\n";
        } else {
            echo "✅ upload_date column exists!\n";
        }
    }
    
    echo "\nFinal table structure:\n";
    $stmt = $pdo->query("DESCRIBE support_files");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($columns as $column) {
        echo "- " . $column['Field'] . " (" . $column['Type'] . ")\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>