# 🧪 نظام اختبارات Green Line

نظام اختبارات شامل لمشروع Green Line يتضمن اختبارات الوحدة واختبارات التكامل واختبارات الأداء.

## 📋 المحتويات

- [التثبيت والإعداد](#التثبيت-والإعداد)
- [تشغيل الاختبارات](#تشغيل-الاختبارات)
- [أنواع الاختبارات](#أنواع-الاختبارات)
- [إضافة اختبارات جديدة](#إضافة-اختبارات-جديدة)
- [التقارير](#التقارير)

## 🚀 التثبيت والإعداد

### المتطلبات
- PHP 7.4 أو أحدث
- MySQL/MariaDB
- Redis (اختياري للتخزين المؤقت)
- إعد<PERSON> قاعدة البيانات

### الإعداد الأولي

1. **تأكد من إعداد قاعدة البيانات:**
   ```bash
   # تشغيل سكريبت إنشاء الجداول
   mysql -u username -p database_name < ../sql/permissions_schema.sql
   ```

2. **إعداد بيانات الاختبار:**
   ```bash
   php run_tests.php --setup
   ```

## 🏃‍♂️ تشغيل الاختبارات

### تشغيل جميع الاختبارات
```bash
php run_tests.php
```

### تشغيل اختبارات محددة
```bash
# اختبارات الوحدة فقط
php run_tests.php --unit

# اختبارات التكامل فقط
php run_tests.php --integration
```

### خيارات أخرى
```bash
# عرض المساعدة
php run_tests.php --help

# إعداد بيانات الاختبار
php run_tests.php --setup

# تنظيف بيانات الاختبار
php run_tests.php --cleanup
```

## 🧪 أنواع الاختبارات

### 1. اختبارات الوحدة (Unit Tests)

#### AuthTest
- اختبار تسجيل الدخول
- اختبار تسجيل المستخدمين الجدد
- اختبار التحقق من كلمات المرور
- اختبار إنشاء الرموز المميزة

#### PermissionsTest
- اختبار إنشاء الأدوار
- اختبار إسناد الصلاحيات
- اختبار التحقق من الصلاحيات
- اختبار إزالة الأدوار

#### CacheTest
- اختبار تخزين واسترجاع البيانات
- اختبار انتهاء صلاحية الكاش
- اختبار حذف البيانات المؤقتة
- اختبار عمليات الذاكرة

#### CSRFTest
- اختبار إنشاء رموز CSRF
- اختبار التحقق من الرموز
- اختبار انتهاء صلاحية الرموز
- اختبار الحماية من الهجمات

#### RateLimiterTest
- اختبار تحديد معدل الطلبات
- اختبار تجاوز الحدود المسموحة
- اختبار إعادة تعيين العدادات
- اختبار الحظر المؤقت

### 2. اختبارات التكامل (Integration Tests)

#### IntegrationTest
- اختبار الاتصال بقاعدة البيانات
- اختبار تسجيل المستخدمين الكامل
- اختبار عرض المنتجات
- اختبار Meta Tags للـ SEO
- اختبار تكامل النظم المختلفة

### 3. اختبارات الأداء (Performance Tests)

#### PerformanceTest
- اختبار سرعة استعلامات قاعدة البيانات
- اختبار أداء عمليات الكاش
- اختبار استهلاك الذاكرة
- اختبار أوقات الاستجابة

## ➕ إضافة اختبارات جديدة

### إنشاء فئة اختبار جديدة

```php
class MyNewTest extends TestCase {
    
    public function testSomething() {
        // ترتيب البيانات
        $expected = 'القيمة المتوقعة';
        
        // تنفيذ العملية
        $actual = someFunction();
        
        // التحقق من النتيجة
        $this->assertEquals($expected, $actual, 'رسالة الخطأ');
    }
    
    public function testAnotherThing() {
        $result = anotherFunction();
        $this->assertTrue($result, 'يجب أن تكون النتيجة true');
    }
}
```

### إضافة الاختبار إلى المشغل

في ملف `run_tests.php`:

```php
// إضافة الاختبار الجديد
$runner->addTest(new MyNewTest());
```

### دوال التحقق المتاحة

```php
// التحقق من التساوي
$this->assertEquals($expected, $actual, $message);

// التحقق من الصحة
$this->assertTrue($condition, $message);
$this->assertFalse($condition, $message);

// التحقق من القيم الفارغة
$this->assertNull($value, $message);
$this->assertNotNull($value, $message);

// التحقق من الاستثناءات
$this->expectException(Exception::class);
```

## 📊 التقارير

### تقرير وقت التشغيل
يتم عرض النتائج مباشرة في وقت التشغيل مع:
- عدد الاختبارات المنجزة
- عدد الاختبارات الناجحة والفاشلة
- تفاصيل الأخطاء
- الوقت المستغرق

### تقرير HTML
يتم إنشاء تقرير HTML تلقائياً في:
```
tests/test_report.html
```

يحتوي التقرير على:
- ملخص شامل للنتائج
- تفاصيل كل اختبار
- رسائل الأخطاء
- الطوابع الزمنية

## 🔧 استكشاف الأخطاء

### مشاكل شائعة

#### خطأ في الاتصال بقاعدة البيانات
```bash
❌ خطأ: لا يمكن الاتصال بقاعدة البيانات
```
**الحل:** تحقق من إعدادات قاعدة البيانات في `includes/database.php`

#### فشل في إعداد بيانات الاختبار
```bash
❌ خطأ في إنشاء بيانات الاختبار
```
**الحل:** تأكد من وجود الجداول المطلوبة وتشغيل:
```bash
php run_tests.php --cleanup
php run_tests.php --setup
```

#### خطأ في اختبارات Redis
```bash
❌ فشل اختبار الكاش
```
**الحل:** تأكد من تشغيل خادم Redis أو تعطيل اختبارات Redis

### تسجيل الأخطاء
يتم تسجيل جميع الأخطاء في:
- الإخراج المباشر
- تقرير HTML
- سجلات النظام (إذا كانت مفعلة)

## 📝 أفضل الممارسات

### كتابة الاختبارات
1. **اجعل الاختبارات مستقلة:** كل اختبار يجب أن يعمل بمفرده
2. **استخدم أسماء وصفية:** `testUserCanLoginWithValidCredentials`
3. **اختبر حالة واحدة فقط:** اختبار واحد = وظيفة واحدة
4. **نظف البيانات:** احرص على تنظيف البيانات بعد كل اختبار

### تنظيم الاختبارات
1. **جمع الاختبارات المترابطة:** ضع اختبارات النظام الواحد في فئة واحدة
2. **استخدم التعليقات:** وضح الغرض من كل اختبار
3. **اتبع التسلسل المنطقي:** رتب الاختبارات من البسيط إلى المعقد

### الأداء
1. **تجنب الاختبارات البطيئة:** استخدم بيانات وهمية بدلاً من العمليات الحقيقية
2. **استخدم الكاش بحكمة:** لا تختبر الكاش في كل اختبار
3. **راقب استهلاك الذاكرة:** خاصة في اختبارات الأداء

## 🤝 المساهمة

لإضافة اختبارات جديدة أو تحسين الموجودة:

1. أنشئ فئة اختبار جديدة
2. اتبع نمط التسمية المعتمد
3. أضف التوثيق المناسب
4. اختبر الكود قبل الإرسال

## 📞 الدعم

للحصول على المساعدة:
- راجع هذا الدليل
- تحقق من رسائل الأخطاء
- استخدم `--help` للخيارات المتاحة

---

**ملاحظة:** هذا النظام مصمم لبيئة التطوير. لا تستخدمه في بيئة الإنتاج مباشرة.