<?php
require_once 'config/database.php';

try {
    echo "<h2>إصلاح جدول contact_info</h2>";
    
    // التحقق من وجود العمود section_key
    $stmt = $pdo->query("SHOW COLUMNS FROM contact_info LIKE 'section_key'");
    
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>❌ العمود section_key مفقود! سيتم إصلاح الجدول...</p>";
        
        // حفظ البيانات الموجودة إذا كانت هناك أي
        $existingData = [];
        try {
            $stmt = $pdo->query("SELECT * FROM contact_info");
            $existingData = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<p>تم حفظ " . count($existingData) . " سجل من البيانات الموجودة</p>";
        } catch (Exception $e) {
            echo "<p>لا توجد بيانات موجودة أو الجدول غير صحيح.</p>";
        }
        
        // حذف الجدول الحالي
        $pdo->exec("DROP TABLE IF EXISTS contact_info");
        echo "<p>✅ تم حذف الجدول القديم</p>";
        
        // إنشاء الجدول بالهيكل الصحيح
        $sql = "CREATE TABLE contact_info (
            id INT AUTO_INCREMENT PRIMARY KEY,
            section_key VARCHAR(100) NOT NULL UNIQUE,
            title VARCHAR(255) NOT NULL,
            content TEXT,
            data JSON,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        $pdo->exec($sql);
        echo "<p>✅ تم إنشاء الجدول بالهيكل الصحيح</p>";
        
        // إدراج البيانات الافتراضية
        $defaultData = [
            ['address', 'العنوان', 'الرياض، المملكة العربية السعودية', '{"street": "", "city": "الرياض", "country": "المملكة العربية السعودية"}'],
            ['phone', 'الهاتف', '+966 11 123 4567', '{"primary": "+966 11 123 4567", "secondary": ""}'],
            ['email', 'البريد الإلكتروني', '<EMAIL>', '{"primary": "<EMAIL>", "support": "<EMAIL>"}'],
            ['working_hours', 'ساعات العمل', 'الأحد - الخميس: 8:00 ص - 5:00 م', '{"sunday_thursday_open": "08:00", "sunday_thursday_close": "17:00", "friday_open": "08:00", "friday_close": "12:00", "saturday": "مغلق", "timezone": "Asia/Riyadh"}'],
            ['whatsapp', 'واتساب', '+966 50 123 4567', '{"number": "+966 50 123 4567", "message": "مرحباً، أحتاج مساعدة"}'],
            ['map_embed', 'خريطة الموقع', '', '{"embed_code": "", "latitude": "", "longitude": ""}'],
            ['social_media', 'وسائل التواصل الاجتماعي', '', '{"facebook": "", "twitter": "", "instagram": "", "linkedin": ""}'],
            ['company_info', 'معلومات الشركة', 'شركة الخط الأخضر للتكييف والتبريد', '{"description": "شركة رائدة في مجال التكييف والتبريد", "established": "", "license": ""}']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO contact_info (section_key, title, content, data, is_active, created_at, updated_at) VALUES (?, ?, ?, ?, 1, NOW(), NOW())");
        
        foreach ($defaultData as $data) {
            $stmt->execute($data);
        }
        
        echo "<p>✅ تم إدراج البيانات الافتراضية (" . count($defaultData) . " سجل)</p>";
        
    } else {
        echo "<p style='color: green;'>✅ العمود section_key موجود بالفعل!</p>";
    }
    
    // عرض الهيكل النهائي
    echo "<h3>الهيكل النهائي للجدول:</h3>";
    $stmt = $pdo->query("DESCRIBE contact_info");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th style='padding: 8px;'>اسم العمود</th>";
    echo "<th style='padding: 8px;'>النوع</th>";
    echo "<th style='padding: 8px;'>Null</th>";
    echo "<th style='padding: 8px;'>Key</th>";
    echo "<th style='padding: 8px;'>Default</th>";
    echo "</tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($column['Default']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // اختبار الاستعلام
    echo "<h3>اختبار استعلام section_key:</h3>";
    try {
        $stmt = $pdo->prepare("SELECT section_key, title FROM contact_info WHERE section_key = ?");
        $stmt->execute(['address']);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result) {
            echo "<p style='color: green;'>✅ استعلام section_key يعمل بشكل طبيعي</p>";
            echo "<p>النتيجة: " . htmlspecialchars($result['section_key']) . " - " . htmlspecialchars($result['title']) . "</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد بيانات للـ section_key = 'address'</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في استعلام section_key: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "<p style='color: green; font-weight: bold; margin-top: 20px;'>🎉 تم إصلاح الجدول بنجاح!</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>