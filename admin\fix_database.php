<?php
/**
 * Database Fix - Add replied_at column
 */

require_once __DIR__ . '/../config/database.php';

try {
    echo "Starting database fix...\n";
    
    // Check if table exists
    echo "Checking if contact_messages table exists...\n";
    
    $tableExists = $database->fetch("
        SELECT COUNT(*) as count 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() 
        AND table_name = 'contact_messages'
    ");
    
    if ($tableExists['count'] == 0) {
        echo "Creating contact_messages table...\n";
        $database->query("
            CREATE TABLE contact_messages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                email VARCHAR(255) NOT NULL,
                phone VARCHAR(20),
                subject VARCHAR(255),
                message TEXT NOT NULL,
                status ENUM('unread', 'read', 'replied') DEFAULT 'unread',
                reply TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ");
        echo "contact_messages table created successfully.\n";
    } else {
        echo "contact_messages table exists.\n";
    }
    
    // Check if replied_at column exists
    echo "Checking if replied_at column exists...\n";
    
    $columnExists = $database->fetch("
        SELECT COUNT(*) as count 
        FROM information_schema.columns 
        WHERE table_schema = DATABASE() 
        AND table_name = 'contact_messages' 
        AND column_name = 'replied_at'
    ");
    
    if ($columnExists['count'] == 0) {
        echo "Adding replied_at column...\n";
        $database->query("
            ALTER TABLE contact_messages 
            ADD COLUMN replied_at TIMESTAMP NULL AFTER reply
        ");
        echo "replied_at column added successfully.\n";
        
        // Update existing data
        echo "Updating existing data...\n";
        $updated = $database->query("
            UPDATE contact_messages 
            SET replied_at = updated_at 
            WHERE status = 'replied' AND reply IS NOT NULL AND reply != ''
        ");
        echo "Existing data updated.\n";
        
    } else {
        echo "replied_at column already exists.\n";
    }
    
    // Check final structure
    echo "Checking final table structure...\n";
    $columns = $database->fetchAll("
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_schema = DATABASE() 
        AND table_name = 'contact_messages'
        ORDER BY ordinal_position
    ");
    
    echo "contact_messages table columns:\n";
    foreach ($columns as $column) {
        echo "- {$column['column_name']} ({$column['data_type']}) - " . 
             ($column['is_nullable'] === 'YES' ? 'NULL' : 'NOT NULL') . "\n";
    }
    
    echo "\nDatabase fix completed successfully!\n";
    echo "You can now access the contact messages page without errors.\n";
    
} catch (Exception $e) {
    echo "Error fixing database: " . $e->getMessage() . "\n";
    echo "Error details: " . $e->getTraceAsString() . "\n";
}
?>