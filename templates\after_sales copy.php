<?php
$pageTitle = 'خدمات ما بعد البيع';
$page = 'after_sales';

// جلب خدمات ما بعد البيع
$services = getAfterSalesServices();

include 'header.php';
?>

<!-- Hero Section -->
<section class="bg-gradient-to-r from-primary to-secondary py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">
            خدمات ما بعد البيع
        </h1>
        <p class="text-xl text-white opacity-90 max-w-2xl mx-auto">
            نحن ملتزمون بتقديم أفضل خدمات الدعم والصيانة لضمان رضاكم التام
        </p>
    </div>
</section>

<!-- Services Overview -->
<section class="py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">
                خدماتنا المتميزة
            </h2>
            <p class="text-gray-600 max-w-2xl mx-auto">
                نقدم مجموعة شاملة من خدمات ما بعد البيع لضمان استمرارية عمل منتجاتنا بأعلى كفاءة
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php foreach ($services as $service): ?>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-300 overflow-hidden">
                    <?php if (!empty($service['icon'])): ?>
                        <div class="p-6 pb-4">
                            <div class="w-16 h-16 bg-primary bg-opacity-10 rounded-lg flex items-center justify-center mb-4">
                                <img src="<?php echo htmlspecialchars($service['icon']); ?>" 
                                     alt="<?php echo htmlspecialchars($service['title']); ?>"
                                     class="w-8 h-8">
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <div class="px-6 pb-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-3">
                            <?php echo htmlspecialchars($service['title']); ?>
                        </h3>
                        
                        <p class="text-gray-600 mb-4">
                            <?php echo htmlspecialchars($service['description']); ?>
                        </p>
                        
                        <?php if (!empty($service['features'])): ?>
                            <ul class="space-y-2 mb-6">
                                <?php 
                                $features = explode(',', $service['features']);
                                foreach ($features as $feature):
                                ?>
                                    <li class="flex items-center text-sm text-gray-600">
                                        <svg class="w-4 h-4 text-green-500 flex-shrink-0 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        <?php echo htmlspecialchars(trim($feature)); ?>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        <?php endif; ?>
                        
                        <?php if (!empty($service['response_time'])): ?>
                            <div class="flex items-center text-sm text-primary mb-4">
                                <svg class="w-4 h-4 flex-shrink-0 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                وقت الاستجابة: <?php echo htmlspecialchars($service['response_time']); ?>
                            </div>
                        <?php endif; ?>
                        
                        <button onclick="requestService('<?php echo htmlspecialchars($service['id']); ?>', '<?php echo htmlspecialchars($service['title']); ?>')" 
                                class="w-full bg-primary text-white px-4 py-2 rounded-lg font-medium hover:bg-primary-dark transition-colors duration-300">
                            طلب الخدمة
                        </button>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Service Request Form -->
<section class="py-16 bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white rounded-lg shadow-lg p-8">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">
                    طلب خدمة ما بعد البيع
                </h2>
                <p class="text-gray-600">
                    املأ النموذج أدناه وسيتواصل معك فريقنا في أقرب وقت ممكن
                </p>
            </div>
            
            <form id="serviceRequestForm" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="customer_name" class="block text-sm font-medium text-gray-700 mb-2">
                            الاسم الكامل *
                        </label>
                        <input type="text" 
                               id="customer_name" 
                               name="customer_name" 
                               required 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                    
                    <div>
                        <label for="customer_phone" class="block text-sm font-medium text-gray-700 mb-2">
                            رقم الهاتف *
                        </label>
                        <input type="tel" 
                               id="customer_phone" 
                               name="customer_phone" 
                               required 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="customer_email" class="block text-sm font-medium text-gray-700 mb-2">
                            البريد الإلكتروني
                        </label>
                        <input type="email" 
                               id="customer_email" 
                               name="customer_email" 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                    
                    <div>
                        <label for="service_type" class="block text-sm font-medium text-gray-700 mb-2">
                            نوع الخدمة المطلوبة *
                        </label>
                        <select id="service_type" 
                                name="service_type" 
                                required 
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            <option value="">اختر نوع الخدمة</option>
                            <?php foreach ($services as $service): ?>
                                <option value="<?php echo htmlspecialchars($service['id']); ?>">
                                    <?php echo htmlspecialchars($service['title']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="product_model" class="block text-sm font-medium text-gray-700 mb-2">
                            موديل المنتج
                        </label>
                        <input type="text" 
                               id="product_model" 
                               name="product_model" 
                               placeholder="مثال: GreenLine Pro 2024"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                    
                    <div>
                        <label for="serial_number" class="block text-sm font-medium text-gray-700 mb-2">
                            الرقم التسلسلي
                        </label>
                        <input type="text" 
                               id="serial_number" 
                               name="serial_number" 
                               placeholder="الرقم التسلسلي للمنتج"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="purchase_date" class="block text-sm font-medium text-gray-700 mb-2">
                            تاريخ الشراء
                        </label>
                        <input type="date" 
                               id="purchase_date" 
                               name="purchase_date" 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                    
                    <div>
                        <label for="priority" class="block text-sm font-medium text-gray-700 mb-2">
                            أولوية الطلب
                        </label>
                        <select id="priority" 
                                name="priority" 
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            <option value="normal">عادي</option>
                            <option value="high">عالي</option>
                            <option value="urgent">عاجل</option>
                        </select>
                    </div>
                </div>
                
                <div>
                    <label for="customer_address" class="block text-sm font-medium text-gray-700 mb-2">
                        العنوان *
                    </label>
                    <textarea id="customer_address" 
                              name="customer_address" 
                              rows="3" 
                              required 
                              placeholder="العنوان الكامل مع تفاصيل الوصول"
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"></textarea>
                </div>
                
                <div>
                    <label for="issue_description" class="block text-sm font-medium text-gray-700 mb-2">
                        وصف المشكلة أو الخدمة المطلوبة *
                    </label>
                    <textarea id="issue_description" 
                              name="issue_description" 
                              rows="4" 
                              required 
                              placeholder="اشرح المشكلة بالتفصيل أو نوع الخدمة المطلوبة"
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"></textarea>
                </div>
                
                <div>
                    <label for="preferred_time" class="block text-sm font-medium text-gray-700 mb-2">
                        الوقت المفضل للزيارة
                    </label>
                    <select id="preferred_time" 
                            name="preferred_time" 
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="">اختر الوقت المفضل</option>
                        <option value="morning">صباحاً (8:00 - 12:00)</option>
                        <option value="afternoon">بعد الظهر (12:00 - 17:00)</option>
                        <option value="evening">مساءً (17:00 - 20:00)</option>
                        <option value="anytime">أي وقت</option>
                    </select>
                </div>
                
                <div class="flex items-center">
                    <input type="checkbox" 
                           id="terms_agreement" 
                           name="terms_agreement" 
                           required 
                           class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                    <label for="terms_agreement" class="mr-2 block text-sm text-gray-700">
                        أوافق على 
                        <a href="#" class="text-primary hover:text-secondary">شروط وأحكام الخدمة</a>
                        و
                        <a href="#" class="text-primary hover:text-secondary">سياسة الخصوصية</a>
                    </label>
                </div>
                
                <div class="text-center">
                    <button type="submit" 
                            class="bg-primary text-white px-8 py-3 rounded-lg font-semibold hover:bg-primary-dark transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed">
                        <span class="submit-text">إرسال طلب الخدمة</span>
                        <span class="loading-text hidden">جاري الإرسال...</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</section>

<!-- Track Service Request -->
<section class="py-16">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white rounded-lg shadow-lg p-8">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">
                    تتبع طلب الخدمة
                </h2>
                <p class="text-gray-600">
                    أدخل رقم طلب الخدمة لمتابعة حالة طلبك
                </p>
            </div>
            
            <form id="trackServiceForm" class="max-w-md mx-auto">
                <div class="flex gap-4">
                    <input type="text" 
                           id="tracking_number" 
                           name="tracking_number" 
                           placeholder="رقم طلب الخدمة"
                           required 
                           class="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    <button type="submit" 
                            class="bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-primary-dark transition-colors duration-300">
                        تتبع
                    </button>
                </div>
            </form>
            
            <!-- Tracking Results -->
            <div id="trackingResults" class="hidden mt-8">
                <div class="border-t border-gray-200 pt-8">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">حالة طلب الخدمة</h3>
                    
                    <div class="space-y-4">
                        <!-- Status Timeline -->
                        <div class="flex items-center">
                            <div class="flex-shrink-0 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <div class="mr-4 flex-1">
                                <h4 class="text-sm font-medium text-gray-900">تم استلام الطلب</h4>
                                <p class="text-sm text-gray-500">تم استلام طلبك وهو قيد المراجعة</p>
                            </div>
                            <span class="text-sm text-gray-500">منذ ساعتين</span>
                        </div>
                        
                        <div class="flex items-center">
                            <div class="flex-shrink-0 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="mr-4 flex-1">
                                <h4 class="text-sm font-medium text-gray-900">جاري التحضير</h4>
                                <p class="text-sm text-gray-500">يتم تحضير الفريق والأدوات اللازمة</p>
                            </div>
                            <span class="text-sm text-gray-500">الآن</span>
                        </div>
                        
                        <div class="flex items-center opacity-50">
                            <div class="flex-shrink-0 w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                            </div>
                            <div class="mr-4 flex-1">
                                <h4 class="text-sm font-medium text-gray-900">في الطريق</h4>
                                <p class="text-sm text-gray-500">الفريق في طريقه إليك</p>
                            </div>
                            <span class="text-sm text-gray-500">قريباً</span>
                        </div>
                        
                        <div class="flex items-center opacity-50">
                            <div class="flex-shrink-0 w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <div class="mr-4 flex-1">
                                <h4 class="text-sm font-medium text-gray-900">تم إنجاز الخدمة</h4>
                                <p class="text-sm text-gray-500">تم إنجاز الخدمة بنجاح</p>
                            </div>
                            <span class="text-sm text-gray-500">قريباً</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Support -->
<section class="py-16 bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="bg-gradient-to-r from-primary to-secondary rounded-lg p-8 text-white">
            <h2 class="text-2xl md:text-3xl font-bold mb-4">
                هل تحتاج مساعدة فورية؟
            </h2>
            <p class="text-lg opacity-90 mb-6">
                فريق الدعم الفني متاح على مدار الساعة لمساعدتك
            </p>
            
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="tel:<?php echo getSetting('support_phone'); ?>" 
                   class="bg-white text-primary px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-300">
                    اتصل بالدعم الفني
                </a>
                
                <?php 
                $whatsapp = getSetting('whatsapp_number');
                if ($whatsapp):
                ?>
                    <a href="https://wa.me/<?php echo preg_replace('/[^0-9]/', '', $whatsapp); ?>?text=مرحباً، أحتاج مساعدة في خدمات ما بعد البيع" 
                       target="_blank" 
                       class="border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary transition-colors duration-300">
                        واتساب
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-16">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">
                الأسئلة الشائعة
            </h2>
            <p class="text-gray-600">
                إجابات على أكثر الأسئلة شيوعاً حول خدمات ما بعد البيع
            </p>
        </div>
        
        <div class="space-y-4">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <button class="faq-toggle w-full px-6 py-4 text-right focus:outline-none" data-target="faq-1">
                    <div class="flex items-center justify-between">
                        <h3 class="font-semibold text-gray-900">ما هي مدة الضمان على المنتجات؟</h3>
                        <svg class="faq-icon w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </div>
                </button>
                <div id="faq-1" class="faq-content hidden px-6 pb-4">
                    <p class="text-gray-600">نقدم ضماناً شاملاً لمدة سنتين على جميع منتجاتنا، يشمل قطع الغيار والصيانة المجانية.</p>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <button class="faq-toggle w-full px-6 py-4 text-right focus:outline-none" data-target="faq-2">
                    <div class="flex items-center justify-between">
                        <h3 class="font-semibold text-gray-900">كم يستغرق وقت الاستجابة لطلبات الخدمة؟</h3>
                        <svg class="faq-icon w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </div>
                </button>
                <div id="faq-2" class="faq-content hidden px-6 pb-4">
                    <p class="text-gray-600">نستجيب لطلبات الخدمة العادية خلال 24 ساعة، والطلبات العاجلة خلال 4 ساعات.</p>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <button class="faq-toggle w-full px-6 py-4 text-right focus:outline-none" data-target="faq-3">
                    <div class="flex items-center justify-between">
                        <h3 class="font-semibold text-gray-900">هل تتوفر قطع الغيار الأصلية؟</h3>
                        <svg class="faq-icon w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </div>
                </button>
                <div id="faq-3" class="faq-content hidden px-6 pb-4">
                    <p class="text-gray-600">نعم، نحتفظ بمخزون كامل من قطع الغيار الأصلية لجميع منتجاتنا لضمان أفضل أداء.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
// Service request functionality
function requestService(serviceId, serviceName) {
    document.getElementById('service_type').value = serviceId;
    document.getElementById('serviceRequestForm').scrollIntoView({ behavior: 'smooth' });
}

// Form submission
document.getElementById('serviceRequestForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const submitBtn = this.querySelector('button[type="submit"]');
    const submitText = submitBtn.querySelector('.submit-text');
    const loadingText = submitBtn.querySelector('.loading-text');
    
    // Show loading state
    submitBtn.disabled = true;
    submitText.classList.add('hidden');
    loadingText.classList.remove('hidden');
    
    // Collect form data
    const formData = new FormData(this);
    
    // Submit to API
    fetch('<?php echo SITE_URL; ?>/api/service-request', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`تم إرسال طلبك بنجاح!\nرقم الطلب: ${data.request_id}\nسيتم التواصل معك قريباً.`);
            this.reset();
        } else {
            alert('حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى.');
    })
    .finally(() => {
        // Reset button state
        submitBtn.disabled = false;
        submitText.classList.remove('hidden');
        loadingText.classList.add('hidden');
    });
});

// Tracking form submission
document.getElementById('trackServiceForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const trackingNumber = document.getElementById('tracking_number').value;
    const resultsDiv = document.getElementById('trackingResults');
    
    // Show tracking results (this would normally fetch from API)
    resultsDiv.classList.remove('hidden');
    resultsDiv.scrollIntoView({ behavior: 'smooth' });
});

// FAQ Toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    const faqToggles = document.querySelectorAll('.faq-toggle');
    
    faqToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            const targetId = this.dataset.target;
            const content = document.getElementById(targetId);
            const icon = this.querySelector('.faq-icon');
            
            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                icon.style.transform = 'rotate(180deg)';
            } else {
                content.classList.add('hidden');
                icon.style.transform = 'rotate(0deg)';
            }
        });
    });
});
</script>

<?php include 'footer.php'; ?>