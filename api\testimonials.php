<?php
require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    $action = $_GET['action'] ?? '';
    
    switch ($action) {
        case 'filter':
            $filter = $_GET['filter'] ?? 'all';
            $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : null;
            
            $testimonials = getTestimonials($limit, $filter);
            
            echo json_encode([
                'success' => true,
                'data' => $testimonials,
                'count' => count($testimonials)
            ]);
            break;
            
        case 'random':
            $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 3;
            $testimonials = getTestimonials($limit, 'random');
            
            echo json_encode([
                'success' => true,
                'data' => $testimonials,
                'count' => count($testimonials)
            ]);
            break;
            
        case 'best':
            $testimonials = getTestimonials(null, 'best');
            
            echo json_encode([
                'success' => true,
                'data' => $testimonials,
                'count' => count($testimonials)
            ]);
            break;
            
        default:
            echo json_encode([
                'success' => false,
                'message' => 'Invalid action'
            ]);
            break;
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}
?>