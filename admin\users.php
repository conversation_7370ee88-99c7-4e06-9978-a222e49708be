<?php
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !hasPermission('admin')) {
    redirect('/admin/login.php');
}

require_once '../config/database.php';

$database = new Database();
$message = '';
$error = '';
$pageTitle = 'إدارة المستخدمين';
$currentPage = 'users';
$pageDescription = 'إدارة وتنظيم جميع المستخدمين في النظام';
$breadcrumbs = [
    ['title' => 'المستخدمين']
];

// تضمين التخطيط الموحد
require_once 'includes/layout.php';

// Create users table if not exists (compatible with setup_admin.php)
$database->query("
    CREATE TABLE IF NOT EXISTS users (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHA<PERSON>(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        role ENUM('super-admin', 'admin', 'company', 'client', 'guest', 'user', 'moderator') DEFAULT 'user',
        phone VARCHAR(20),
        avatar VARCHAR(255) NULL,
        status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
        is_active BOOLEAN DEFAULT TRUE,
        email_verified BOOLEAN DEFAULT FALSE,
        remember_token VARCHAR(255) NULL,
        reset_token VARCHAR(255) NULL,
        reset_token_expires DATETIME NULL,
        last_login DATETIME NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_email (email),
        INDEX idx_role (role)
    )
");

// Add additional columns for user management if they don't exist
try {
    $database->query("ALTER TABLE users ADD COLUMN phone VARCHAR(20) NULL");
} catch (Exception $e) {
    // Column already exists
}

try {
    $database->query("ALTER TABLE users ADD COLUMN avatar VARCHAR(255) NULL");
} catch (Exception $e) {
    // Column already exists
}

try {
    $database->query("ALTER TABLE users ADD COLUMN status ENUM('active', 'inactive', 'suspended') DEFAULT 'active'");
} catch (Exception $e) {
    // Column already exists
}

// معالجة طلبات POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_user':
                $name = $_POST['name'] ?? '';
                $email = $_POST['email'] ?? '';
                $password = $_POST['password'] ?? '';
                $phone = $_POST['phone'] ?? '';
                $roleId = $_POST['role_id'] ?? null;
                $status = $_POST['status'] ?? 'active';
                
                if ($name && $email && $password) {
                    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                    
                    $avatarPath = null;
                    if (isset($_FILES['avatar']) && $_FILES['avatar']['error'] === UPLOAD_ERR_OK) {
                        $uploadDir = '../assets/images/';
                        $fileName = 'user_' . time() . '_' . basename($_FILES['avatar']['name']);
                        $avatarPath = '/assets/images/' . $fileName;
                        move_uploaded_file($_FILES['avatar']['tmp_name'], $uploadDir . $fileName);
                    }
                    
                    try {
                        // بدء المعاملة
                        $database->query("START TRANSACTION");
                        
                        // إضافة المستخدم الجديد
                        $database->query("
                            INSERT INTO users (name, email, password, phone, role_id, avatar, is_active) 
                            VALUES (:name, :email, :password, :phone, :role_id, :avatar, :is_active)
                        ", [
                            'name' => $name,
                            'email' => $email,
                            'password' => $hashedPassword,
                            'phone' => $phone,
                            'role_id' => $roleId,
                            'avatar' => $avatarPath,
                            'is_active' => ($status === 'active') ? 1 : 0
                        ]);
                        
                        // الحصول على ID المستخدم الجديد
                        $newUserId = $database->lastInsertId();
                        
                        // إضافة الدور إلى جدول user_roles إذا كان موجوداً
                        if ($roleId && $newUserId) {
                            try {
                                // إضافة الدور للمستخدم الجديد
                                $database->query(
                                    "INSERT INTO user_roles (user_id, role_id, assigned_by, assigned_at) VALUES (:user_id, :role_id, :assigned_by, NOW())",
                                    [
                                        'user_id' => $newUserId,
                                        'role_id' => $roleId,
                                        'assigned_by' => getCurrentUser()['id'] ?? null
                                    ]
                                );
                            } catch (Exception $roleException) {
                                // إذا لم يوجد جدول user_roles، تجاهل الخطأ
                                // لأن النظام يمكن أن يعمل بدونه
                            }
                        }
                        
                        // تأكيد المعاملة
                        $database->query("COMMIT");
                        $message = 'تم إضافة المستخدم بنجاح';
                        
                    } catch (Exception $e) {
                        // إلغاء المعاملة في حالة الخطأ
                        $database->query("ROLLBACK");
                        $error = 'خطأ في إضافة المستخدم: ' . $e->getMessage();
                    }
                } else {
                    $error = 'يرجى ملء جميع الحقول المطلوبة';
                }
                break;
                
            case 'edit_user':
                $userId = $_POST['user_id'] ?? null;
                $name = $_POST['name'] ?? '';
                $email = $_POST['email'] ?? '';
                $phone = $_POST['phone'] ?? '';
                $roleId = $_POST['role_id'] ?? null;
                $status = $_POST['status'] ?? 'active';
                
                if ($userId && $name && $email) {
                    try {
                        // بدء المعاملة
                        $database->query("START TRANSACTION");
                        
                        // تحديث بيانات المستخدم الأساسية
                        $query = "UPDATE users SET name = :name, email = :email, phone = :phone, role_id = :role_id, is_active = :is_active";
                        $params = [
                            'name' => $name,
                            'email' => $email,
                            'phone' => $phone,
                            'role_id' => $roleId,
                            'is_active' => ($status === 'active') ? 1 : 0,
                            'id' => $userId
                        ];
                        
                        // Update password if provided
                        if (!empty($_POST['password'])) {
                            $query .= ", password = :password";
                            $params['password'] = password_hash($_POST['password'], PASSWORD_DEFAULT);
                        }
                        
                        // Update avatar if provided
                        if (isset($_FILES['avatar']) && $_FILES['avatar']['error'] === UPLOAD_ERR_OK) {
                            $uploadDir = '../assets/images/';
                            $fileName = 'user_' . time() . '_' . basename($_FILES['avatar']['name']);
                            $avatarPath = '/assets/images/' . $fileName;
                            move_uploaded_file($_FILES['avatar']['tmp_name'], $uploadDir . $fileName);
                            $query .= ", avatar = :avatar";
                            $params['avatar'] = $avatarPath;
                        }
                        
                        $query .= " WHERE id = :id";
                        $database->query($query, $params);
                        
                        // تحديث role_id في جدول user_roles إذا كان موجوداً
                        if ($roleId) {
                            try {
                                // حذف الأدوار السابقة للمستخدم
                                $database->query("DELETE FROM user_roles WHERE user_id = :user_id", ['user_id' => $userId]);
                                
                                // إضافة الدور الجديد
                                $database->query(
                                    "INSERT INTO user_roles (user_id, role_id, assigned_by, assigned_at) VALUES (:user_id, :role_id, :assigned_by, NOW())",
                                    [
                                        'user_id' => $userId,
                                        'role_id' => $roleId,
                                        'assigned_by' => getCurrentUser()['id'] ?? null
                                    ]
                                );
                            } catch (Exception $roleException) {
                                // إذا لم يوجد جدول user_roles، تجاهل الخطأ
                                // لأن النظام يمكن أن يعمل بدونه
                            }
                        }
                        
                        // تأكيد المعاملة
                        $database->query("COMMIT");
                        $message = 'تم تحديث المستخدم بنجاح';
                        
                    } catch (Exception $e) {
                        // إلغاء المعاملة في حالة الخطأ
                        $database->query("ROLLBACK");
                        $error = 'خطأ في تحديث المستخدم: ' . $e->getMessage();
                    }
                } else {
                    $error = 'يرجى ملء جميع الحقول المطلوبة';
                }
                break;
                
            case 'delete_user':
                $userId = $_POST['user_id'] ?? null;
                if ($userId) {
                    try {
                        // بدء المعاملة
                        $database->query("START TRANSACTION");
                        
                        // حذف الأدوار المرتبطة بالمستخدم من جدول user_roles
                        try {
                            $database->query("DELETE FROM user_roles WHERE user_id = :user_id", ['user_id' => $userId]);
                        } catch (Exception $roleException) {
                            // إذا لم يوجد جدول user_roles، تجاهل الخطأ
                        }
                        
                        // حذف المستخدم من جدول users
                        $database->query("DELETE FROM users WHERE id = :id", ['id' => $userId]);
                        
                        // تأكيد المعاملة
                        $database->query("COMMIT");
                        $message = 'تم حذف المستخدم بنجاح';
                        
                    } catch (Exception $e) {
                        // إلغاء المعاملة في حالة الخطأ
                        $database->query("ROLLBACK");
                        $error = 'خطأ في حذف المستخدم: ' . $e->getMessage();
                    }
                }
                break;
        }
    }
}

// جلب جميع المستخدمين مع أدوارهم من جدول roles
$users = $database->query("
    SELECT u.*, r.display_name as role_display_name, r.name as role_name 
    FROM users u 
    LEFT JOIN roles r ON u.role_id = r.id 
    ORDER BY u.created_at DESC
")->fetchAll();

// جلب جميع الأدوار من قاعدة البيانات
$roles = [];
try {
    $roles = $database->fetchAll("SELECT * FROM roles ORDER BY name");
} catch (Exception $e) {
    // إذا لم يوجد جدول الأدوار، استخدم الأدوار الافتراضية
    $roles = [
        ['id' => 1, 'name' => 'super-admin', 'display_name' => 'مدير عام', 'description' => 'صلاحيات كاملة لإدارة النظام'],
        ['id' => 2, 'name' => 'admin', 'display_name' => 'مدير', 'description' => 'صلاحيات إدارية محدودة'],
        ['id' => 3, 'name' => 'company', 'display_name' => 'شركة', 'description' => 'إدارة العاملات والحجوزات'],
        ['id' => 4, 'name' => 'client', 'display_name' => 'عميل', 'description' => 'تصفح وحجز العاملات'],
        ['id' => 5, 'name' => 'guest', 'display_name' => 'زائر', 'description' => 'تصفح محدود للموقع'],
        ['id' => 6, 'name' => 'user', 'display_name' => 'مستخدم عادي', 'description' => 'مستخدم عادي'],
        ['id' => 7, 'name' => 'moderator', 'display_name' => 'مشرف', 'description' => 'مشرف']
    ];
}

// دالة للحصول على اسم الدور للعرض
function getRoleDisplayName($roleName, $roles) {
    foreach ($roles as $role) {
        if ($role['name'] === $roleName) {
            return $role['display_name'] ?? $role['description'] ?? $role['name'];
        }
    }
    // إذا لم يوجد الدور، ارجع الاسم كما هو
    return $roleName;
}

// إضافة بيانات تجريبية إذا كان الجدول فارغاً
if (empty($users)) {
    $sampleUsers = [
        [
            'name' => 'مدير النظام',
            'email' => '<EMAIL>',
            'password' => password_hash('admin123', PASSWORD_DEFAULT),
            'phone' => '+966501234567',
            'role' => 'admin',
            'is_active' => 1
        ],
        [
            'name' => 'أحمد محمد',
            'email' => '<EMAIL>',
            'password' => password_hash('manager123', PASSWORD_DEFAULT),
            'phone' => '+966507654321',
            'role' => 'moderator',
            'is_active' => 1
        ]
    ];
    
    foreach ($sampleUsers as $user) {
        try {
            $database->query("
                INSERT INTO users (name, email, password, phone, role, is_active) 
                VALUES (:name, :email, :password, :phone, :role, :is_active)
            ", $user);
        } catch (Exception $e) {
            // تجاهل الأخطاء إذا كانت البيانات موجودة بالفعل
        }
    }
    
    // إعادة جلب المستخدمين بعد إضافة البيانات التجريبية
    $users = $database->query("SELECT * FROM users ORDER BY created_at DESC")->fetchAll();
}

// بدء التخطيط
startLayout();
showPageHeader();
showMessages();
?>

<div class="space-y-6">
    <!-- إحصائيات المستخدمين -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <?php
        $totalUsers = count($users);
        $activeUsers = count(array_filter($users, fn($u) => isset($u['is_active']) && $u['is_active'] == 1));
        // حساب المديرين بناءً على role_name أو role_id
        $adminUsers = count(array_filter($users, function($u) {
            return (isset($u['role_name']) && $u['role_name'] === 'admin') || 
                   (isset($u['role']) && $u['role'] === 'admin');
        }));
        $inactiveUsers = count(array_filter($users, fn($u) => isset($u['is_active']) && $u['is_active'] == 0));
        ?>
        
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">إجمالي المستخدمين</p>
                    <p class="text-2xl font-semibold text-gray-900"><?= $totalUsers ?></p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-indigo-100 rounded-lg">
                    <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">المستخدمين النشطين</p>
                    <p class="text-2xl font-semibold text-gray-900"><?= $activeUsers ?></p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 rounded-lg">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">المديرين</p>
                    <p class="text-2xl font-semibold text-gray-900"><?= $adminUsers ?></p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-red-100 rounded-lg">
                    <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">المستخدمين غير النشطين</p>
                    <p class="text-2xl font-semibold text-gray-900"><?= $inactiveUsers ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة المستخدمين -->
    <?php echo createCard('قائمة المستخدمين', '', '
        <div class="mb-4">
            <button onclick="showAddUserModal()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                <i class="fas fa-plus ml-2"></i>
                إضافة مستخدم جديد
            </button>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المستخدم</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">البريد الإلكتروني</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الهاتف</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الدور</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ التسجيل</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    ' . (empty($users) ? '
                        <tr>
                            <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                لا توجد مستخدمين مسجلين حتى الآن
                            </td>
                        </tr>
                    ' : implode('', array_map(function($user) use ($roles) {
                        $isActive = $user['is_active'] ?? 0;
                        // استخدام role_name من جدول roles أو role_display_name للعرض
                        $role = $user['role_name'] ?? $user['role'] ?? 'user';
                        $roleDisplayName = $user['role_display_name'] ?? getRoleDisplayName($role, $roles);
                        $avatar = $user['avatar'] ?? null;
                        $name = $user['name'] ?? 'غير محدد';
                        $email = $user['email'] ?? 'غير محدد';
                        $phone = $user['phone'] ?? '-';
                        $createdAt = $user['created_at'] ?? date('Y-m-d H:i:s');
                        
                        $statusClass = $isActive ? 'bg-pink-100 text-pink-800' : 'bg-red-100 text-red-800';
                        $roleClass = $role === 'admin' ? 'bg-purple-100 text-purple-800' : 
                                    ($role === 'moderator' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800');
                        
                        return '
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            ' . ($avatar ? 
                                                '<img class="h-10 w-10 rounded-full object-cover" src="' . htmlspecialchars($avatar) . '" alt="' . htmlspecialchars($name) . '">' : 
                                                '<div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                                    <span class="text-sm font-medium text-gray-700">' . strtoupper(substr($name, 0, 1)) . '</span>
                                                </div>'
                                            ) . '
                                        </div>
                                        <div class="mr-4">
                                            <div class="text-sm font-medium text-gray-900">' . htmlspecialchars($name) . '</div>
                                            <div class="text-sm text-gray-500">' . htmlspecialchars($email) . '</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">' . htmlspecialchars($email) . '</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">' . htmlspecialchars($phone) . '</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ' . $roleClass . '">
                                        ' . $roleDisplayName . '
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ' . $statusClass . '">
                                        ' . ($isActive ? 'نشط' : 'غير نشط') . '
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">' . date('Y-m-d', strtotime($createdAt)) . '</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button onclick="editUser(' . $user['id'] . ')" class="text-blue-600 hover:text-blue-900 ml-3">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button onclick="deleteUser(' . $user['id'] . ')" class="text-red-600 hover:text-red-900">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        ';
                    }, $users))) . '
                </tbody>
            </table>
        </div>
    '); ?>
</div>

<!-- Modal إضافة مستخدم -->
<div id="addUserModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">إضافة مستخدم جديد</h3>
                <button onclick="hideAddUserModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form method="POST" enctype="multipart/form-data" class="space-y-4">
                <input type="hidden" name="action" value="add_user">
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل *</label>
                        <input type="text" name="name" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني *</label>
                        <input type="email" name="email" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور *</label>
                        <input type="password" name="password" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                        <input type="tel" name="phone" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">الصورة الشخصية</label>
                    <input type="file" name="avatar" accept="image/*" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الدور</label>
                        <select name="role_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                            <option value="">اختر الدور</option>
                            <?php foreach ($roles as $role): ?>
                                <option value="<?= htmlspecialchars($role['id']) ?>">
                                    <?= htmlspecialchars($role['display_name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                        <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                        </select>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="hideAddUserModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                        إلغاء
                    </button>
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700">
                        إضافة المستخدم
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal تعديل مستخدم -->
<div id="editUserModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">تعديل المستخدم</h3>
                <button onclick="hideEditUserModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form method="POST" enctype="multipart/form-data" class="space-y-4">
                <input type="hidden" name="action" value="edit_user">
                <input type="hidden" name="user_id" id="edit_user_id">
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل *</label>
                        <input type="text" name="name" id="edit_name" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني *</label>
                        <input type="email" name="email" id="edit_email" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور الجديدة (اتركها فارغة للاحتفاظ بالحالية)</label>
                        <input type="password" name="password" id="edit_password" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                        <input type="tel" name="phone" id="edit_phone" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">الصورة الشخصية الجديدة</label>
                    <input type="file" name="avatar" accept="image/*" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الدور</label>
                        <select name="role_id" id="edit_role_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                            <option value="">اختر الدور</option>
                            <?php foreach ($roles as $role): ?>
                                <option value="<?= htmlspecialchars($role['id']) ?>">
                                    <?= htmlspecialchars($role['display_name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                        <select name="status" id="edit_status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                        </select>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="hideEditUserModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                        إلغاء
                    </button>
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">
                        تحديث المستخدم
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// بيانات المستخدمين لـ JavaScript
const users = <?= json_encode($users) ?>;

// إظهار modal إضافة مستخدم
function showAddUserModal() {
    document.getElementById('addUserModal').classList.remove('hidden');
}

// إخفاء modal إضافة مستخدم
function hideAddUserModal() {
    document.getElementById('addUserModal').classList.add('hidden');
}

// إظهار modal تعديل مستخدم
function editUser(userId) {
    const user = users.find(u => u.id == userId);
    if (user) {
        document.getElementById('edit_user_id').value = user.id;
        document.getElementById('edit_name').value = user.name;
        document.getElementById('edit_email').value = user.email;
        document.getElementById('edit_phone').value = user.phone || '';
        document.getElementById('edit_role_id').value = user.role_id || '';
        document.getElementById('edit_status').value = user.is_active ? 'active' : 'inactive';
        document.getElementById('edit_password').value = '';
        
        document.getElementById('editUserModal').classList.remove('hidden');
    }
}

// إخفاء modal تعديل مستخدم
function hideEditUserModal() {
    document.getElementById('editUserModal').classList.add('hidden');
}

// حذف مستخدم
function deleteUser(userId) {
    const user = users.find(u => u.id == userId);
    if (user && confirm(`هل أنت متأكد من حذف المستخدم "${user.name}"؟`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_user">
            <input type="hidden" name="user_id" value="${userId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// إغلاق المودال عند النقر خارجه
window.onclick = function(event) {
    const addModal = document.getElementById('addUserModal');
    const editModal = document.getElementById('editUserModal');
    
    if (event.target === addModal) {
        hideAddUserModal();
    }
    if (event.target === editModal) {
        hideEditUserModal();
    }
}
</script>

<?php
endLayout();
?>