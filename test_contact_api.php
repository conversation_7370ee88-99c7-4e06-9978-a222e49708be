<?php
require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<h2>اختبار API contact.php</h2>";

// محاكاة إرسال بيانات POST
$_POST = [
    'name' => 'اختبار API',
    'email' => '<EMAIL>',
    'phone' => '0501112233',
    'subject' => 'اختبار API مباشر',
    'message' => 'هذا اختبار مباشر لـ API contact.php',
    'type' => 'general'
];

$_SERVER['REQUEST_METHOD'] = 'POST';

echo "<h3>البيانات المرسلة:</h3>";
echo "<pre>";
print_r($_POST);
echo "</pre>";

echo "<h3>نتيجة معالجة API:</h3>";

// تشغيل نفس منطق contact.php
try {
    // Validate required fields
    $required_fields = ['name', 'email', 'subject', 'message'];
    $missing_fields = [];
    
    foreach ($required_fields as $field) {
        if (empty($_POST[$field])) {
            $missing_fields[] = $field;
        }
    }
    
    if (!empty($missing_fields)) {
        echo "❌ الحقول التالية مطلوبة: " . implode(', ', $missing_fields) . "<br>";
    } else {
        echo "✅ جميع الحقول المطلوبة موجودة<br>";
        
        // Sanitize input
        $name = sanitizeInput($_POST['name']);
        $email = sanitizeInput($_POST['email']);
        $phone = sanitizeInput($_POST['phone'] ?? '');
        $subject = sanitizeInput($_POST['subject']);
        $message = sanitizeInput($_POST['message']);
        $type = sanitizeInput($_POST['type'] ?? 'general');
        
        echo "✅ تم تنظيف البيانات<br>";
        
        // Validate email
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            echo "❌ البريد الإلكتروني غير صحيح<br>";
        } else {
            echo "✅ البريد الإلكتروني صحيح<br>";
            
            // Save contact message
            $contact_data = [
                'name' => $name,
                'email' => $email,
                'phone' => $phone,
                'subject' => $subject,
                'message' => $message,
                'type' => $type
            ];
            
            echo "<h4>البيانات المعدة للحفظ:</h4>";
            echo "<pre>";
            print_r($contact_data);
            echo "</pre>";
            
            $message_id = saveContactMessage($contact_data);
            
            if ($message_id) {
                echo "✅ تم حفظ الرسالة بنجاح - ID: {$message_id}<br>";
                
                // التحقق من الحفظ
                global $database;
                $saved_message = $database->fetch("SELECT * FROM contact_messages WHERE id = ?", [$message_id]);
                if ($saved_message) {
                    echo "✅ تم التحقق من حفظ الرسالة في قاعدة البيانات<br>";
                    echo "<h4>البيانات المحفوظة:</h4>";
                    echo "<pre>";
                    print_r($saved_message);
                    echo "</pre>";
                    
                    // حذف الرسالة التجريبية
                    $database->delete('contact_messages', 'id = ?', [$message_id]);
                    echo "✅ تم حذف الرسالة التجريبية<br>";
                } else {
                    echo "❌ لم يتم العثور على الرسالة في قاعدة البيانات<br>";
                }
            } else {
                echo "❌ فشل في حفظ الرسالة<br>";
            }
        }
    }
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "<br>";
    echo "تفاصيل الخطأ: " . $e->getTraceAsString() . "<br>";
}

echo "<br><h3>اختبار مباشر لملف contact.php:</h3>";
echo "<form method='POST' action='/api/contact.php' target='_blank'>";
echo "<input type='hidden' name='name' value='اختبار مباشر'>";
echo "<input type='hidden' name='email' value='<EMAIL>'>";
echo "<input type='hidden' name='phone' value='0509876543'>";
echo "<input type='hidden' name='subject' value='اختبار مباشر للـ API'>";
echo "<input type='hidden' name='message' value='هذا اختبار مباشر لملف contact.php'>";
echo "<input type='hidden' name='type' value='general'>";
echo "<button type='submit'>اختبار API مباشرة</button>";
echo "</form>";
?>