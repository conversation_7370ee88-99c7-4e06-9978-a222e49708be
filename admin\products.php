<?php
require_once 'includes/layout.php';
require_once 'includes/inc_Pagination.php';


// Check if user is logged in and has admin privileges
if (!isLoggedIn() || !hasPermission('manage_products')) {
    header('Location: login.php');
    exit;
}

$pageTitle = 'إدارة المنتجات';
$currentPage = 'products';
$pageDescription = 'إدارة وتنظيم جميع المنتجات في النظام';
$breadcrumbs = [
    ['title' => 'المنتجات']
];

$action = $_GET['action'] ?? 'list';
$productId = $_GET['id'] ?? null;
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if ($action === 'add') {
            $name = trim($_POST['name'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $category_id = trim($_POST['category_id'] ?? '');
            $price = floatval($_POST['price'] ?? 0);
            $original_price = !empty($_POST['original_price']) ? floatval($_POST['original_price']) : null;
            $rating = floatval($_POST['rating'] ?? 0);
            $reviews_count = intval($_POST['reviews_count'] ?? 0);
            $features = !empty($_POST['features']) ? json_encode(explode("\n", trim($_POST['features'])), JSON_UNESCAPED_UNICODE) : null;
            $specifications = !empty($_POST['specifications']) ? json_encode(explode("\n", trim($_POST['specifications'])), JSON_UNESCAPED_UNICODE) : null;
            $badge = trim($_POST['badge'] ?? '') ?: null;
            $in_stock = isset($_POST['in_stock']) ? 1 : 0;
            $image = trim($_POST['image'] ?? '') ?: null;
            $gallery = !empty($_POST['gallery']) ? json_encode(explode("\n", trim($_POST['gallery'])), JSON_UNESCAPED_UNICODE) : null;
            $sort_order = intval($_POST['sort_order'] ?? 0);
            $is_active = isset($_POST['is_active']) ? 1 : 0;
            $is_featured = isset($_POST['is_featured']) ? 1 : 0;
            $status = $_POST['status'] ?? 'active';
            
            if (empty($name)) {
                throw new Exception('اسم المنتج مطلوب');
            }
            
            if (empty($category_id)) {
                throw new Exception('فئة المنتج مطلوبة');
            }
            $database->query(
                "INSERT INTO products (name, description, category_id, price, original_price, rating, reviews_count, features, specifications, badge, in_stock, image, gallery, sort_order, is_active, is_featured, status, created_at) 
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())",
                [$name, $description, $category_id, $price, $original_price, $rating, $reviews_count, $features, $specifications, $badge, $in_stock, $image, $gallery, $sort_order, $is_active, $is_featured, $status]
            );
            
            logActivity('تم إضافة منتج جديد: ' . $name, 'products');
            $message = 'تم إضافة المنتج بنجاح';
            $action = 'list';
            
        } elseif ($action === 'edit' && $productId) {
            $name = trim($_POST['name'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $category_id = trim($_POST['category_id'] ?? '');
            $price = floatval($_POST['price'] ?? 0);
            $original_price = !empty($_POST['original_price']) ? floatval($_POST['original_price']) : null;
            $rating = floatval($_POST['rating'] ?? 0);
            $reviews_count = intval($_POST['reviews_count'] ?? 0);
            $features = !empty($_POST['features']) ? json_encode(explode("\n", trim($_POST['features'])), JSON_UNESCAPED_UNICODE) : null;
            $specifications = !empty($_POST['specifications']) ? json_encode(explode("\n", trim($_POST['specifications'])), JSON_UNESCAPED_UNICODE) : null;
            $badge = trim($_POST['badge'] ?? '') ?: null;
            $in_stock = isset($_POST['in_stock']) ? 1 : 0;
            $image = trim($_POST['image'] ?? '') ?: null;
            $gallery = !empty($_POST['gallery']) ? json_encode(explode("\n", trim($_POST['gallery'])), JSON_UNESCAPED_UNICODE) : null;
            $sort_order = intval($_POST['sort_order'] ?? 0);
            $is_active = isset($_POST['is_active']) ? 1 : 0;
            $is_featured = isset($_POST['is_featured']) ? 1 : 0;
            $status = $_POST['status'] ?? 'active';
            
            if (empty($name)) {
                throw new Exception('اسم المنتج مطلوب');
            }
            
            if (empty($category_id)) {
                throw new Exception('فئة المنتج مطلوبة');
            }
            
            $database->query(
                "UPDATE products SET name = ?, description = ?, category_id = ?, price = ?, original_price = ?, rating = ?, reviews_count = ?, features = ?, specifications = ?, badge = ?, in_stock = ?, image = ?, gallery = ?, sort_order = ?, is_active = ?, is_featured = ?, status = ?, updated_at = NOW() 
                 WHERE id = ?",
                [$name, $description, $category_id, $price, $original_price, $rating, $reviews_count, $features, $specifications, $badge, $in_stock, $image, $gallery, $sort_order, $is_active, $is_featured, $status, $productId]
            );
            
            logActivity('تم تعديل المنتج: ' . $name, 'products');
            $message = 'تم تحديث المنتج بنجاح';
            $action = 'list';
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
        logActivity('خطأ في إدارة المنتجات', 'products');
    }
}

// Handle delete action
if ($action === 'delete' && $productId) {
    try {
        $product = $database->fetch("SELECT name FROM products WHERE id = ?", [$productId]);
        if ($product) {
            $database->query("DELETE FROM products WHERE id = ?", [$productId]);
            logActivity('تم حذف المنتج: ' . $product['name'], 'products');
            $message = 'تم حذف المنتج بنجاح';
        }
        $action = 'list';
    } catch (Exception $e) {
        $error = 'حدث خطأ أثناء حذف المنتج';
        logActivity('خطأ في حذف المنتج', 'products');
    }
}

// Get data for forms
if ($action === 'add' || $action === 'edit') {
    $categories = $database->fetchAll("SELECT id, name FROM product_categories ORDER BY name") ?? [];
    
    if ($action === 'edit' && $productId) {
        $product = $database->fetch("SELECT * FROM products WHERE id = ?", [$productId]);
        if (!$product) {
            $error = 'المنتج غير موجود';
            $action = 'list';
        }
    }
}

// Get products list
if ($action === 'list') {
    $search = $_GET['search'] ?? '';
    $category_filter = $_GET['category'] ?? '';
    $status_filter = $_GET['status'] ?? '';
    
    // Pagination settings
    $page = max(1, intval($_GET['page'] ?? 1));
    $perPage = 10;
    $offset = ($page - 1) * $perPage;
    
    $whereConditions = [];
    $params = [];
    
    if (!empty($search)) {
        $whereConditions[] = "(p.name LIKE ? OR p.description LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    if (!empty($category_filter)) {
        $whereConditions[] = "p.category_id = ?";
        $params[] = $category_filter;
    }
    
    if (!empty($status_filter)) {
        $whereConditions[] = "p.status = ?";
        $params[] = $status_filter;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // Count total records
    $countQuery = "SELECT COUNT(*) as total FROM products p LEFT JOIN product_categories pc ON p.category_id = pc.id $whereClause";
    $totalResult = $database->fetch($countQuery, $params);
    $totalRecords = $totalResult['total'] ?? 0;
    
    // Get paginated products
    $query = "
        SELECT p.*, pc.name as category_name 
        FROM products p 
        LEFT JOIN product_categories pc ON p.category_id = pc.id 
        $whereClause 
        ORDER BY p.created_at DESC
        LIMIT $perPage OFFSET $offset
    ";
    
    $products = $database->fetchAll($query, $params) ?? [];
    $categories = $database->fetchAll("SELECT id, name FROM product_categories ORDER BY name") ?? [];
    
    // Calculate pagination info
    $totalPages = ceil($totalRecords / $perPage);
    $pagination = [
        'current_page' => $page,
        'total_pages' => $totalPages,
        'total_records' => $totalRecords,
        'per_page' => $perPage,
        'has_prev' => $page > 1,
        'has_next' => $page < $totalPages
    ];
}
startLayout();
showPageHeader();
showMessages();
?>


                
                <?php if ($action === 'list'): ?>
                    <!-- Products List -->
                    <div class="bg-white rounded-lg shadow">
                        <div class="p-6 border-b border-gray-200">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-xl font-semibold text-gray-900">قائمة المنتجات</h2>
                                <a href="?action=add" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                                    إضافة منتج جديد
                                </a>
                            </div>
                            
                            <!-- Filters -->
                            <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                                <input type="hidden" name="action" value="list">
                                <input type="text" name="search" value="<?php echo htmlspecialchars($search ?? ''); ?>" 
                                       placeholder="البحث في المنتجات..." class="border rounded-md px-3 py-2">
                                
                                <select name="category" class="border rounded-md px-3 py-2">
                                    <option value="">جميع الفئات</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>" 
                                                <?php echo ($category_filter == $category['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($category['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                
                                <select name="status" class="border rounded-md px-3 py-2">
                                    <option value="">جميع الحالات</option>
                                    <option value="active" <?php echo ($status_filter === 'active') ? 'selected' : ''; ?>>نشط</option>
                                    <option value="inactive" <?php echo ($status_filter === 'inactive') ? 'selected' : ''; ?>>غير نشط</option>
                                </select>
                                
                                <button type="submit" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700">
                                    بحث
                                </button>
                            </form>
                        </div>
                        
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المنتج</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الفئة</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">السعر</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الإنشاء</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <?php if (empty($products)): ?>
                                        <tr>
                                            <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                                لا توجد منتجات
                                            </td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($products as $product): ?>
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div>
                                                        <div class="text-sm font-medium text-gray-900">
                                                            <?php echo htmlspecialchars($product['name']); ?>
                                                        </div>
                                                        <?php if ($product['description']): ?>
                                                            <div class="text-sm text-gray-500">
                                                                <?php echo htmlspecialchars(substr($product['description'], 0, 100)); ?>
                                                                <?php echo strlen($product['description']) > 100 ? '...' : ''; ?>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    <?php echo htmlspecialchars($product['category_name'] ?? 'غير محدد'); ?>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo number_format($product['price'], 2); ?> ريال
                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                                        <?php echo $product['status'] === 'active' ? 'bg-pink-100 text-pink-800' : 'bg-red-100 text-red-800'; ?>">
                                                        <?php echo $product['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    <?php echo formatDate($product['created_at']); ?>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                    <a href="?action=edit&id=<?php echo $product['id']; ?>" 
                                                       class="text-indigo-600 hover:text-indigo-900 ml-3">تعديل</a>
                                                    <a href="?action=delete&id=<?php echo $product['id']; ?>" 
                                                       onclick="return confirm('هل أنت متأكد من حذف هذا المنتج؟')" 
                                                       class="text-red-600 hover:text-red-900">حذف</a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <?php if ($totalRecords > $perPage): ?>
                            <div class="px-6 py-4 border-t border-gray-200">
                                <?php 
                                // Build query parameters for pagination links
                                $queryParams = [];
                                if (!empty($search)) $queryParams['search'] = $search;
                                if (!empty($category_filter)) $queryParams['category'] = $category_filter;
                                if (!empty($status_filter)) $queryParams['status'] = $status_filter;
                                $queryParams['action'] = 'list';
                                
                                echo renderPagination(
                                    $pagination['current_page'],
                                    $pagination['total_pages'],
                                    'products.php',
                                    $queryParams
                                );
                                ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                <?php elseif ($action === 'add' || $action === 'edit'): ?>
                    <!-- Add/Edit Product Form -->
                    <div class="bg-white rounded-lg shadow">
                        <div class="p-6 border-b border-gray-200">
                            <h2 class="text-xl font-semibold text-gray-900">
                                <?php echo $action === 'add' ? 'إضافة منتج جديد' : 'تعديل المنتج'; ?>
                            </h2>
                        </div>
                        
                        <form method="POST" class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">اسم المنتج *</label>
                                    <input type="text" id="name" name="name" required
                                           value="<?php echo htmlspecialchars($product['name'] ?? ''); ?>"
                                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label for="category_id" class="block text-sm font-medium text-gray-700 mb-2">الفئة</label>
                                    <select id="category_id" name="category_id" 
                                            class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="0">بدون فئة</option>
                                        <?php foreach ($categories as $category): ?>
                                            <option value="<?php echo $category['id']; ?>" 
                                                    <?php echo (isset($product) && $product['category_id'] == $category['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($category['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div>
                                    <label for="price" class="block text-sm font-medium text-gray-700 mb-2">السعر *</label>
                                    <input type="number" id="price" name="price" step="0.01" min="0" required
                                           value="<?php echo htmlspecialchars($product['price'] ?? ''); ?>"
                                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label for="original_price" class="block text-sm font-medium text-gray-700 mb-2">السعر الأصلي</label>
                                    <input type="number" id="original_price" name="original_price" step="0.01" min="0"
                                           value="<?php echo htmlspecialchars($product['original_price'] ?? ''); ?>"
                                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label for="rating" class="block text-sm font-medium text-gray-700 mb-2">التقييم (0-5)</label>
                                    <input type="number" id="rating" name="rating" step="0.1" min="0" max="5"
                                           value="<?php echo htmlspecialchars($product['rating'] ?? '0'); ?>"
                                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label for="reviews_count" class="block text-sm font-medium text-gray-700 mb-2">عدد المراجعات</label>
                                    <input type="number" id="reviews_count" name="reviews_count" min="0"
                                           value="<?php echo htmlspecialchars($product['reviews_count'] ?? '0'); ?>"
                                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label for="badge" class="block text-sm font-medium text-gray-700 mb-2">الشارة</label>
                                    <input type="text" id="badge" name="badge"
                                           value="<?php echo htmlspecialchars($product['badge'] ?? ''); ?>"
                                           placeholder="مثل: جديد، مميز، خصم"
                                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label for="image" class="block text-sm font-medium text-gray-700 mb-2">رابط الصورة الرئيسية</label>
                                    <input type="url" id="image" name="image"
                                           value="<?php echo htmlspecialchars($product['image'] ?? ''); ?>"
                                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-2">ترتيب العرض</label>
                                    <input type="number" id="sort_order" name="sort_order" min="0"
                                           value="<?php echo htmlspecialchars($product['sort_order'] ?? '0'); ?>"
                                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">حالة النشر</label>
                                    <select id="status" name="status" 
                                            class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="active" <?php echo (isset($product) && $product['status'] === 'active') ? 'selected' : ''; ?>>نشط</option>
                                        <option value="inactive" <?php echo (isset($product) && $product['status'] === 'inactive') ? 'selected' : ''; ?>>غير نشط</option>
                                        <option value="draft" <?php echo (isset($product) && $product['status'] === 'draft') ? 'selected' : ''; ?>>مسودة</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="in_stock" value="1" 
                                               <?php echo (isset($product) && $product['in_stock']) ? 'checked' : 'checked'; ?>
                                               class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                        <span class="mr-2 text-sm font-medium text-gray-700">متوفر في المخزون</span>
                                    </label>
                                </div>
                                
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="is_active" value="1" 
                                               <?php echo (isset($product) && $product['is_active']) ? 'checked' : 'checked'; ?>
                                               class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                        <span class="mr-2 text-sm font-medium text-gray-700">مفعل</span>
                                    </label>
                                </div>
                                
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="is_featured" value="1" 
                                               <?php echo (isset($product) && $product['is_featured']) ? 'checked' : ''; ?>
                                               class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                        <span class="mr-2 text-sm font-medium text-gray-700">منتج مميز</span>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="mt-6">
                                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                                <textarea id="description" name="description" rows="4"
                                          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                          placeholder="وصف المنتج..."><?php echo htmlspecialchars($product['description'] ?? ''); ?></textarea>
                            </div>
                            
                            <div class="mt-6">
                                <label for="features" class="block text-sm font-medium text-gray-700 mb-2">المميزات (كل ميزة في سطر منفصل)</label>
                                <textarea id="features" name="features" rows="4"
                                          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                          placeholder="ميزة 1\nميزة 2\nميزة 3"><?php 
                                if (isset($product['features']) && $product['features']) {
                                    $features = json_decode($product['features'], true);
                                    echo htmlspecialchars(is_array($features) ? implode("\n", $features) : '');
                                }
                                ?></textarea>
                            </div>
                            
                            <div class="mt-6">
                                <label for="specifications" class="block text-sm font-medium text-gray-700 mb-2">المواصفات (كل مواصفة في سطر منفصل)</label>
                                <textarea id="specifications" name="specifications" rows="4"
                                          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                          placeholder="مواصفة 1\nمواصفة 2\nمواصفة 3"><?php 
                                if (isset($product['specifications']) && $product['specifications']) {
                                    $specifications = json_decode($product['specifications'], true);
                                    echo htmlspecialchars(is_array($specifications) ? implode("\n", $specifications) : '');
                                }
                                ?></textarea>
                            </div>
                            
                            <div class="mt-6">
                                <label for="gallery" class="block text-sm font-medium text-gray-700 mb-2">معرض الصور (رابط كل صورة في سطر منفصل)</label>
                                <textarea id="gallery" name="gallery" rows="4"
                                          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                          placeholder="https://example.com/image1.jpg\nhttps://example.com/image2.jpg"><?php 
                                if (isset($product['gallery']) && $product['gallery']) {
                                    $gallery = json_decode($product['gallery'], true);
                                    echo htmlspecialchars(is_array($gallery) ? implode("\n", $gallery) : '');
                                }
                                ?></textarea>
                            </div>
                            
                            <div class="mt-6 flex justify-end space-x-3 space-x-reverse">
                                <a href="?action=list" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400">
                                    إلغاء
                                </a>
                                <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                                    <?php echo $action === 'add' ? 'إضافة المنتج' : 'حفظ التغييرات'; ?>
                                </button>
                            </div>
                        </form>
                    </div>
                <?php endif; ?>

<?php endLayout(); ?>