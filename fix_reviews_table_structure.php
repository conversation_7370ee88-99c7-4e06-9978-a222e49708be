<?php
/**
 * إصلاح هيكل جدول المراجعات
 * Fix Reviews Table Structure
 */

require_once __DIR__ . '/config/database.php';

try {
    $database = new Database();
    $pdo = $database->getConnection();
    
    echo "<h1>إصلاح هيكل جدول المراجعات</h1>";
    echo "<hr>";
    
    // 1. فحص الجداول الموجودة
    echo "<h2>1. فحص الجداول الموجودة:</h2>";
    $tables = $pdo->query("SHOW TABLES LIKE '%review%'")->fetchAll(PDO::FETCH_COLUMN);
    foreach ($tables as $table) {
        echo "- $table<br>";
    }
    echo "<hr>";
    
    // 2. فحص هيكل جدول reviews الحالي
    echo "<h2>2. هيكل جدول reviews الحالي:</h2>";
    try {
        $columns = $pdo->query("DESCRIBE reviews")->fetchAll(PDO::FETCH_ASSOC);
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } catch (PDOException $e) {
        echo "خطأ في فحص جدول reviews: " . $e->getMessage();
    }
    echo "<hr>";
    
    // 3. إنشاء جدول product_reviews الصحيح
    echo "<h2>3. إنشاء جدول product_reviews:</h2>";
    try {
        $sql = "CREATE TABLE IF NOT EXISTS product_reviews (
            id INT PRIMARY KEY AUTO_INCREMENT,
            product_id INT NOT NULL,
            user_id INT NULL,
            name VARCHAR(255) NULL,
            email VARCHAR(255) NULL,
            rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
            title VARCHAR(255) NULL,
            comment TEXT NOT NULL,
            is_approved BOOLEAN DEFAULT FALSE,
            is_rejected BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_product_id (product_id),
            INDEX idx_user_id (user_id),
            INDEX idx_is_approved (is_approved)
        )";
        
        $pdo->exec($sql);
        echo "✅ تم إنشاء جدول product_reviews بنجاح<br>";
    } catch (PDOException $e) {
        echo "❌ خطأ في إنشاء جدول product_reviews: " . $e->getMessage() . "<br>";
    }
    echo "<hr>";
    
    // 4. تحديث جدول reviews الحالي لإضافة الأعمدة المطلوبة
    echo "<h2>4. تحديث جدول reviews:</h2>";
    
    // إضافة عمود product_id إذا لم يكن موجوداً
    try {
        $pdo->exec("ALTER TABLE reviews ADD COLUMN product_id INT NULL AFTER id");
        echo "✅ تم إضافة عمود product_id<br>";
    } catch (PDOException $e) {
        echo "⚠️ عمود product_id موجود بالفعل أو خطأ: " . $e->getMessage() . "<br>";
    }
    
    // إضافة عمود user_id إذا لم يكن موجوداً
    try {
        $pdo->exec("ALTER TABLE reviews ADD COLUMN user_id INT NULL AFTER product_id");
        echo "✅ تم إضافة عمود user_id<br>";
    } catch (PDOException $e) {
        echo "⚠️ عمود user_id موجود بالفعل أو خطأ: " . $e->getMessage() . "<br>";
    }
    
    // إضافة عمود email إذا لم يكن موجوداً
    try {
        $pdo->exec("ALTER TABLE reviews ADD COLUMN email VARCHAR(255) NULL AFTER user_id");
        echo "✅ تم إضافة عمود email<br>";
    } catch (PDOException $e) {
        echo "⚠️ عمود email موجود بالفعل أو خطأ: " . $e->getMessage() . "<br>";
    }
    
    // إضافة عمود title إذا لم يكن موجوداً
    try {
        $pdo->exec("ALTER TABLE reviews ADD COLUMN title VARCHAR(255) NULL AFTER email");
        echo "✅ تم إضافة عمود title<br>";
    } catch (PDOException $e) {
        echo "⚠️ عمود title موجود بالفعل أو خطأ: " . $e->getMessage() . "<br>";
    }
    
    // تعديل عمود content إلى comment إذا لم يكن موجوداً
    try {
        $pdo->exec("ALTER TABLE reviews ADD COLUMN comment TEXT NULL AFTER title");
        echo "✅ تم إضافة عمود comment<br>";
    } catch (PDOException $e) {
        echo "⚠️ عمود comment موجود بالفعل أو خطأ: " . $e->getMessage() . "<br>";
    }
    
    echo "<hr>";
    
    // 5. فحص الهيكل الجديد
    echo "<h2>5. هيكل جدول reviews بعد التحديث:</h2>";
    try {
        $columns = $pdo->query("DESCRIBE reviews")->fetchAll(PDO::FETCH_ASSOC);
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } catch (PDOException $e) {
        echo "خطأ في فحص جدول reviews: " . $e->getMessage();
    }
    echo "<hr>";
    
    // 6. فحص جدول product_reviews
    echo "<h2>6. هيكل جدول product_reviews:</h2>";
    try {
        $columns = $pdo->query("DESCRIBE product_reviews")->fetchAll(PDO::FETCH_ASSOC);
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } catch (PDOException $e) {
        echo "خطأ في فحص جدول product_reviews: " . $e->getMessage();
    }
    echo "<hr>";
    
    echo "<h2>✅ تم الانتهاء من إصلاح هيكل الجداول</h2>";
    echo "<p><strong>الخطوات التالية:</strong></p>";
    echo "<ul>";
    echo "<li>تحديث وظيفة saveUserReview لاستخدام الجدول الصحيح</li>";
    echo "<li>تحديث API لاستخدام الجدول المناسب</li>";
    echo "<li>اختبار حفظ المراجعات</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "خطأ عام: " . $e->getMessage();
}
?>