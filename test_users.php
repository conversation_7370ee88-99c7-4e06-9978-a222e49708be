<?php
require_once 'config/config.php';

echo "<h1>إدارة المستخدمين - اختبار</h1>";

// عرض المستخدمين الموجودين
echo "<h2>المستخدمون الموجودون:</h2>";
global $database;
$users = $database->fetchAll("SELECT id, name, email, role, is_active FROM users");
if ($users) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>الاسم</th><th>البريد الإلكتروني</th><th>الدور</th><th>نشط</th></tr>";
    foreach ($users as $user) {
        echo "<tr>";
        echo "<td>" . $user['id'] . "</td>";
        echo "<td>" . htmlspecialchars($user['name']) . "</td>";
        echo "<td>" . htmlspecialchars($user['email']) . "</td>";
        echo "<td>" . htmlspecialchars($user['role']) . "</td>";
        echo "<td>" . ($user['is_active'] ? 'نعم' : 'لا') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>لا توجد مستخدمين في قاعدة البيانات</p>";
}

// إنشاء مستخدم تجريبي إذا لم يكن موجوداً
if ($_GET['create_test_user'] ?? false) {
    $test_email = '<EMAIL>';
    $existing = $database->fetch("SELECT id FROM users WHERE email = :email", ['email' => $test_email]);
    
    if (!$existing) {
        $user_id = $database->insert('users', [
            'name' => 'مستخدم تجريبي',
            'email' => $test_email,
            'password' => password_hash('123456', PASSWORD_DEFAULT),
            'role' => 'user',
            'is_active' => 1
        ]);
        
        if ($user_id) {
            echo "<div style='color: green; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0;'>تم إنشاء المستخدم التجريبي بنجاح!</div>";
            echo "<p>البريد الإلكتروني: <EMAIL></p>";
            echo "<p>كلمة المرور: 123456</p>";
        } else {
            echo "<div style='color: red; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0;'>فشل في إنشاء المستخدم التجريبي</div>";
        }
    } else {
        echo "<div style='color: orange; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; margin: 10px 0;'>المستخدم التجريبي موجود بالفعل</div>";
    }
    
    echo "<script>setTimeout(function(){ window.location.href = 'test_users.php'; }, 3000);</script>";
}

echo "<br><br>";
echo "<a href='?create_test_user=1' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>إنشاء مستخدم تجريبي</a>";
echo " ";
echo "<a href='test_login.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>تسجيل الدخول</a>";

// عرض التقييمات الموجودة
echo "<h2>التقييمات الموجودة:</h2>";
$reviews = $database->fetchAll("SELECT * FROM reviews ORDER BY created_at DESC LIMIT 10");
if ($reviews) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>المنتج</th><th>المستخدم</th><th>التقييم</th><th>التعليق</th><th>التاريخ</th></tr>";
    foreach ($reviews as $review) {
        echo "<tr>";
        echo "<td>" . $review['id'] . "</td>";
        echo "<td>" . $review['product_id'] . "</td>";
        echo "<td>" . ($review['user_id'] ?: 'ضيف') . "</td>";
        echo "<td>" . $review['rating'] . "/5</td>";
        echo "<td>" . htmlspecialchars(substr($review['comment'], 0, 50)) . "...</td>";
        echo "<td>" . $review['created_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>لا توجد تقييمات في قاعدة البيانات</p>";
}
?>