# Green Line E-commerce Environment Configuration
# نسخ هذا الملف إلى .env وتعديل القيم حسب البيئة

# ===========================================
# إعدادات التطبيق الأساسية
# ===========================================
APP_NAME="Green Line"
APP_ENV=local
APP_DEBUG=true
APP_URL=http://localhost:8000
APP_KEY=base64:your-32-character-secret-key-here
APP_TIMEZONE=Asia/Riyadh
APP_LOCALE=ar

# ===========================================
# إعدادات قاعدة البيانات
# ===========================================
DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=greenline_db
DB_USERNAME=root
DB_PASSWORD=
DB_CHARSET=utf8mb4
DB_COLLATION=utf8mb4_unicode_ci

# إعدادات اتصال قاعدة البيانات المتقدمة
DB_POOL_SIZE=10
DB_TIMEOUT=30
DB_SSL_MODE=disabled

# Redis Configuration (Optional)
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DATABASE=0
REDIS_PREFIX=greenline:

# Session Configuration
SESSION_DRIVER=file
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_FILES=storage/sessions
SESSION_CONNECTION=default
SESSION_TABLE=sessions
SESSION_STORE=database
SESSION_LOTTERY=[2, 100]
SESSION_COOKIE=greenline_session
SESSION_PATH=/
SESSION_DOMAIN=null
SESSION_SECURE=false
SESSION_HTTP_ONLY=true
SESSION_SAME_SITE=lax

# Security Settings
APP_KEY=base64:your-32-character-secret-key-here
JWT_SECRET=your-jwt-secret-key-here
CSRF_TOKEN_NAME=_token
CSRF_HEADER_NAME=X-CSRF-TOKEN
CSRF_EXPIRE=3600

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_MAX_ATTEMPTS=60
RATE_LIMIT_DECAY_MINUTES=1
RATE_LIMIT_PREFIX=rate_limit:

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Green Line"

# SMS Configuration (Optional)
SMS_DRIVER=twilio
SMS_FROM=+1234567890
TWILIO_SID=your-twilio-sid
TWILIO_TOKEN=your-twilio-token
TWILIO_FROM=your-twilio-phone-number

# File Storage
FILESYSTEM_DRIVER=local
FILE_UPLOAD_PATH=uploads/
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx

# Image Processing
IMAGE_DRIVER=gd
IMAGE_QUALITY=85
IMAGE_MAX_WIDTH=1920
IMAGE_MAX_HEIGHT=1080
THUMBNAIL_WIDTH=300
THUMBNAIL_HEIGHT=300

# Cache Configuration
CACHE_DRIVER=redis
CACHE_PREFIX=greenline_cache:
CACHE_DEFAULT_TTL=3600
CACHE_ENABLED=true

# Logging
LOG_CHANNEL=daily
LOG_LEVEL=debug
LOG_DAYS=14
LOG_PATH=storage/logs/

# Payment Gateways
# PayPal
PAYPAL_MODE=sandbox
PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret

# Stripe
STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret

# Local Payment (Mada, STC Pay, etc.)
MADA_MERCHANT_ID=your-mada-merchant-id
MADA_SECRET_KEY=your-mada-secret-key
STC_PAY_MERCHANT_ID=your-stc-pay-merchant-id
STC_PAY_SECRET_KEY=your-stc-pay-secret-key

# Social Media Integration
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret
TWITTER_API_KEY=your-twitter-api-key
TWITTER_API_SECRET=your-twitter-api-secret
INSTAGRAM_CLIENT_ID=your-instagram-client-id
INSTAGRAM_CLIENT_SECRET=your-instagram-client-secret

# Google Services
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_MAPS_API_KEY=your-google-maps-api-key
GOOGLE_ANALYTICS_ID=your-google-analytics-id
GOOGLE_TAG_MANAGER_ID=your-google-tag-manager-id

# SEO Settings
SEO_ENABLED=true
SEO_DEFAULT_TITLE="Green Line - متجر إلكتروني متطور"
SEO_DEFAULT_DESCRIPTION="متجر Green Line للتجارة الإلكترونية - أفضل المنتجات بأسعار منافسة"
SEO_DEFAULT_KEYWORDS="تجارة إلكترونية, متجر إلكتروني, منتجات, تسوق أونلاين"
SEO_SITEMAP_ENABLED=true
SEO_ROBOTS_ENABLED=true

# API Settings
API_RATE_LIMIT=1000
API_RATE_LIMIT_PERIOD=3600
API_VERSION=v1
API_PREFIX=api

# Third-party Services
# Cloudflare
CLOUDFLARE_API_KEY=your-cloudflare-api-key
CLOUDFLARE_EMAIL=your-cloudflare-email
CLOUDFLARE_ZONE_ID=your-cloudflare-zone-id

# AWS S3 (Optional)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=your-s3-bucket-name
AWS_URL=https://your-bucket.s3.amazonaws.com

# Backup Settings
BACKUP_ENABLED=true
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS=30
BACKUP_PATH=storage/backups/

# Monitoring & Analytics
MONITORING_ENABLED=true
ERROR_REPORTING=true
PERFORMANCE_MONITORING=true

# Development Settings
DEBUG_BAR_ENABLED=true
QUERY_LOG_ENABLED=true
PROFILING_ENABLED=false

# Testing
TEST_DATABASE=greenline_test
TEST_REDIS_DATABASE=1

# Maintenance Mode
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE="الموقع تحت الصيانة - سنعود قريباً"
MAINTENANCE_RETRY_AFTER=3600

# Feature Flags
FEATURE_WISHLIST=true
FEATURE_REVIEWS=true
FEATURE_COUPONS=true
FEATURE_LOYALTY_POINTS=true
FEATURE_MULTI_VENDOR=false
FEATURE_LIVE_CHAT=true
FEATURE_PUSH_NOTIFICATIONS=true

# Localization
SUPPORTED_LOCALES=ar,en
DEFAULT_CURRENCY=SAR
SUPPORTED_CURRENCIES=SAR,USD,EUR
CURRENCY_API_KEY=your-currency-api-key

# Security Headers
SECURITY_HEADERS_ENABLED=true
CSP_ENABLED=true
HSTS_ENABLED=true
XSS_PROTECTION=true
CONTENT_TYPE_NOSNIFF=true
REFERRER_POLICY=strict-origin-when-cross-origin