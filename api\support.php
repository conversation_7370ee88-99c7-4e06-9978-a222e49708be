<?php
/**
 * API لمعالجة طلبات الدعم الفني
 * Support Requests API
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // التحقق من البيانات المطلوبة
    $required_fields = ['name', 'email', 'category', 'subject', 'message'];
    $missing_fields = [];
    
    foreach ($required_fields as $field) {
        if (empty($_POST[$field])) {
            $missing_fields[] = $field;
        }
    }
    
    if (!empty($missing_fields)) {
        echo json_encode([
            'success' => false,
            'message' => 'الحقول التالية مطلوبة: ' . implode(', ', $missing_fields)
        ]);
        exit;
    }
    
    // تنظيف البيانات
    $name = sanitizeInput($_POST['name']);
    $email = filter_var($_POST['email'], FILTER_VALIDATE_EMAIL);
    $phone = sanitizeInput($_POST['phone'] ?? '');
    $category = sanitizeInput($_POST['category']);
    $priority = sanitizeInput($_POST['priority'] ?? 'medium');
    $subject = sanitizeInput($_POST['subject']);
    $message = sanitizeInput($_POST['message']);
    
    // التحقق من صحة البريد الإلكتروني
    if (!$email) {
        echo json_encode([
            'success' => false,
            'message' => 'البريد الإلكتروني غير صحيح'
        ]);
        exit;
    }
    
    // التحقق من فئات الدعم المسموحة
    $allowed_categories = ['technical', 'account', 'billing', 'product', 'order', 'other'];
    if (!in_array($category, $allowed_categories)) {
        $category = 'other';
    }
    
    // التحقق من مستويات الأولوية المسموحة
    $allowed_priorities = ['low', 'medium', 'high', 'urgent'];
    if (!in_array($priority, $allowed_priorities)) {
        $priority = 'medium';
    }
    
    // إنشاء رقم تذكرة فريد
    $ticket_number = 'SUP-' . date('Ymd') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
    
    // الحصول على عنوان IP
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    
    // حفظ طلب الدعم في قاعدة البيانات
    global $database;
    
    $query = "
        INSERT INTO support_tickets (
            ticket_number, name, email, phone, category, priority, 
            subject, message, status, ip_address, created_at
        ) VALUES (
            :ticket_number, :name, :email, :phone, :category, :priority,
            :subject, :message, 'open', :ip_address, NOW()
        )
    ";
    
    $params = [
        ':ticket_number' => $ticket_number,
        ':name' => $name,
        ':email' => $email,
        ':phone' => $phone,
        ':category' => $category,
        ':priority' => $priority,
        ':subject' => $subject,
        ':message' => $message,
        ':ip_address' => $ip_address
    ];
    
    $result = $database->execute($query, $params);
    
    if ($result) {
        // إرسال بريد إلكتروني تأكيدي للعميل
        $customer_email_subject = 'تأكيد استلام طلب الدعم - ' . $ticket_number;
        $customer_email_body = "
            <div dir='rtl' style='font-family: Arial, sans-serif;'>
                <h2>مرحباً {$name}</h2>
                <p>شكراً لك على تواصلك معنا. تم استلام طلب الدعم الخاص بك بنجاح.</p>
                
                <div style='background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>
                    <h3>تفاصيل الطلب:</h3>
                    <p><strong>رقم التذكرة:</strong> {$ticket_number}</p>
                    <p><strong>الموضوع:</strong> {$subject}</p>
                    <p><strong>الفئة:</strong> {$category}</p>
                    <p><strong>الأولوية:</strong> {$priority}</p>
                    <p><strong>تاريخ الإرسال:</strong> " . date('Y-m-d H:i:s') . "</p>
                </div>
                
                <p>سيقوم فريق الدعم الفني بمراجعة طلبك والرد عليك في أقرب وقت ممكن.</p>
                
                <p>يمكنك تتبع حالة طلبك باستخدام رقم التذكرة: <strong>{$ticket_number}</strong></p>
                
                <p>مع تحيات فريق جرين لاين</p>
            </div>
        ";
        
        // إرسال البريد الإلكتروني (يمكن تطوير هذا لاحقاً)
        // sendEmail($email, $customer_email_subject, $customer_email_body);
        
        // إرسال إشعار للإدارة
        $admin_email_subject = 'طلب دعم جديد - ' . $ticket_number;
        $admin_email_body = "
            <div dir='rtl' style='font-family: Arial, sans-serif;'>
                <h2>طلب دعم جديد</h2>
                
                <div style='background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>
                    <h3>تفاصيل الطلب:</h3>
                    <p><strong>رقم التذكرة:</strong> {$ticket_number}</p>
                    <p><strong>الاسم:</strong> {$name}</p>
                    <p><strong>البريد الإلكتروني:</strong> {$email}</p>
                    <p><strong>الهاتف:</strong> {$phone}</p>
                    <p><strong>الفئة:</strong> {$category}</p>
                    <p><strong>الأولوية:</strong> {$priority}</p>
                    <p><strong>الموضوع:</strong> {$subject}</p>
                    <p><strong>الرسالة:</strong></p>
                    <div style='background-color: white; padding: 15px; border: 1px solid #ddd; border-radius: 3px;'>
                        {$message}
                    </div>
                    <p><strong>عنوان IP:</strong> {$ip_address}</p>
                    <p><strong>تاريخ الإرسال:</strong> " . date('Y-m-d H:i:s') . "</p>
                </div>
                
                <p>يرجى مراجعة الطلب والرد عليه في أقرب وقت ممكن.</p>
            </div>
        ";
        
        // إرسال البريد الإلكتروني للإدارة
        // sendEmail(SITE_EMAIL, $admin_email_subject, $admin_email_body);
        
        // تسجيل النشاط
        logActivity('support_ticket_created', 'تم إنشاء تذكرة دعم جديدة: ' . $ticket_number, null, $ip_address);
        
        echo json_encode([
            'success' => true,
            'message' => 'تم إرسال طلب الدعم بنجاح',
            'ticket_number' => $ticket_number,
            'data' => [
                'ticket_number' => $ticket_number,
                'status' => 'open',
                'created_at' => date('Y-m-d H:i:s')
            ]
        ]);
    } else {
        throw new Exception('فشل في حفظ طلب الدعم');
    }
    
} catch (Exception $e) {
    error_log('Support API Error: ' . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ أثناء معالجة طلب الدعم. يرجى المحاولة مرة أخرى.'
    ]);
}

/**
 * دالة لتسجيل النشاطات
 */
function logActivity($action, $description, $user_id = null, $ip_address = null) {
    global $database;
    
    try {
        $query = "
            INSERT INTO activity_logs (action, description, user_id, ip_address, created_at)
            VALUES (:action, :description, :user_id, :ip_address, NOW())
        ";
        
        $params = [
            ':action' => $action,
            ':description' => $description,
            ':user_id' => $user_id,
            ':ip_address' => $ip_address ?? $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ];
        
        $database->execute($query, $params);
    } catch (Exception $e) {
        error_log('Activity Log Error: ' . $e->getMessage());
    }
}

?>