/**
 * Product Detail JavaScript Functions
 * يحتوي على جميع الوظائف المتعلقة بصفحة تفاصيل المنتج
 */

class ProductDetail {
    constructor() {
        this.currentImageIndex = 0;
        this.images = [];
        this.init();
    }

    /**
     * تهيئة الصفحة
     */
    init() {
        this.initTabs();
        this.initImageGallery();
        this.initActions();
        this.initLazyLoading();
    }

    /**
     * تهيئة نظام التبويبات
     */
    initTabs() {
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabPanes = document.querySelectorAll('.tab-pane');

        tabButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const targetTab = button.getAttribute('data-tab');
                
                // إزالة الفئة النشطة من جميع الأزرار والألواح
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabPanes.forEach(pane => pane.classList.remove('active'));
                
                // إضافة الفئة النشطة للزر والوحة المحددة
                button.classList.add('active');
                const targetPane = document.getElementById(targetTab + '-tab');
                if (targetPane) {
                    targetPane.classList.add('active');
                }
            });
        });
    }

    /**
     * تهيئة معرض الصور
     */
    initImageGallery() {
        const mainImage = document.getElementById('main-product-image');
        const thumbnails = document.querySelectorAll('.product-thumbnail');
        
        if (!mainImage || thumbnails.length === 0) return;

        // جمع روابط الصور
        this.images = Array.from(thumbnails).map(thumb => thumb.src);

        thumbnails.forEach((thumbnail, index) => {
            thumbnail.addEventListener('click', () => {
                this.changeMainImage(index);
                this.updateActiveThumbnail(index);
            });
        });

        // إضافة دعم لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft') {
                this.previousImage();
            } else if (e.key === 'ArrowRight') {
                this.nextImage();
            }
        });
    }

    /**
     * تغيير الصورة الرئيسية
     */
    changeMainImage(index) {
        const mainImage = document.getElementById('main-product-image');
        if (mainImage && this.images[index]) {
            mainImage.src = this.images[index];
            this.currentImageIndex = index;
        }
    }

    /**
     * تحديث الصورة المصغرة النشطة
     */
    updateActiveThumbnail(index) {
        const thumbnails = document.querySelectorAll('.product-thumbnail');
        thumbnails.forEach((thumb, i) => {
            if (i === index) {
                thumb.classList.add('active');
            } else {
                thumb.classList.remove('active');
            }
        });
    }

    /**
     * الصورة السابقة
     */
    previousImage() {
        const newIndex = this.currentImageIndex > 0 ? 
            this.currentImageIndex - 1 : 
            this.images.length - 1;
        this.changeMainImage(newIndex);
        this.updateActiveThumbnail(newIndex);
    }

    /**
     * الصورة التالية
     */
    nextImage() {
        const newIndex = this.currentImageIndex < this.images.length - 1 ? 
            this.currentImageIndex + 1 : 
            0;
        this.changeMainImage(newIndex);
        this.updateActiveThumbnail(newIndex);
    }

    /**
     * تهيئة الإجراءات
     */
    initActions() {
        // تهيئة أزرار المشاركة
        const shareButton = document.querySelector('[onclick="shareProduct()"]');
        if (shareButton) {
            shareButton.onclick = () => this.shareProduct();
        }

        // تهيئة أزرار المفضلة
        const wishlistButtons = document.querySelectorAll('[onclick^="toggleWishlist"]');
        wishlistButtons.forEach(button => {
            const productId = button.getAttribute('onclick').match(/\d+/)[0];
            button.onclick = () => this.toggleWishlist(productId);
        });

        // تهيئة أزرار طلب عرض السعر
        const quoteButtons = document.querySelectorAll('[onclick^="requestQuote"]');
        quoteButtons.forEach(button => {
            const productId = button.getAttribute('onclick').match(/\d+/)[0];
            button.onclick = () => this.requestQuote(productId);
        });
    }

    /**
     * مشاركة المنتج
     */
    shareProduct() {
        if (navigator.share) {
            navigator.share({
                title: document.title,
                url: window.location.href
            }).catch(console.error);
        } else {
            // نسخ الرابط إلى الحافظة
            navigator.clipboard.writeText(window.location.href).then(() => {
                this.showNotification('تم نسخ رابط المنتج إلى الحافظة', 'success');
            }).catch(() => {
                this.showNotification('فشل في نسخ الرابط', 'error');
            });
        }
    }

    /**
     * إضافة/إزالة من المفضلة
     */
    toggleWishlist(productId) {
        // إرسال طلب AJAX
        fetch('/api/wishlist.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ product_id: productId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showNotification(data.message, 'success');
                this.updateWishlistButton(productId, data.in_wishlist);
            } else {
                this.showNotification(data.message || 'حدث خطأ', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            this.showNotification('حدث خطأ في الاتصال', 'error');
        });
    }

    /**
     * طلب عرض سعر
     */
    requestQuote(productId) {
        // فتح نموذج طلب عرض السعر
        const modal = document.getElementById('quote-modal');
        if (modal) {
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        } else {
            // إعادة توجيه إلى صفحة التواصل
            window.location.href = `/contact?product_id=${productId}`;
        }
    }

    /**
     * تحديث زر المفضلة
     */
    updateWishlistButton(productId, inWishlist) {
        const button = document.querySelector(`[onclick="toggleWishlist(${productId})"]`);
        if (button) {
            const icon = button.querySelector('svg');
            if (inWishlist) {
                icon.classList.add('fill-current', 'text-red-500');
                icon.classList.remove('text-gray-600');
            } else {
                icon.classList.remove('fill-current', 'text-red-500');
                icon.classList.add('text-gray-600');
            }
        }
    }

    /**
     * عرض إشعار
     */
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            'bg-blue-500 text-white'
        }`;
        notification.textContent = message;

        document.body.appendChild(notification);

        // إزالة الإشعار بعد 3 ثوان
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    /**
     * تهيئة التحميل الكسول للصور
     */
    initLazyLoading() {
        const images = document.querySelectorAll('img[data-src]');
        
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            images.forEach(img => imageObserver.observe(img));
        } else {
            // Fallback للمتصفحات القديمة
            images.forEach(img => {
                img.src = img.dataset.src;
            });
        }
    }

    /**
     * تكبير الصورة
     */
    zoomImage(imageSrc) {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75';
        modal.innerHTML = `
            <div class="relative max-w-4xl max-h-full p-4">
                <img src="${imageSrc}" class="max-w-full max-h-full object-contain">
                <button class="absolute top-4 right-4 text-white text-2xl hover:text-gray-300" onclick="this.parentElement.parentElement.remove()">
                    ×
                </button>
            </div>
        `;

        document.body.appendChild(modal);
        document.body.style.overflow = 'hidden';

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
                document.body.style.overflow = 'auto';
            }
        });
    }
}

// تهيئة الصفحة عند تحميل DOM
document.addEventListener('DOMContentLoaded', () => {
    new ProductDetail();
});

// دوال عامة للتوافق مع الكود الموجود
function requestQuote(productId) {
    if (window.productDetail) {
        window.productDetail.requestQuote(productId);
    }
}

function toggleWishlist(productId) {
    if (window.productDetail) {
        window.productDetail.toggleWishlist(productId);
    }
}

function shareProduct() {
    if (window.productDetail) {
        window.productDetail.shareProduct();
    }
}

function contactUs() {
    window.location.href = '/contact';
}