<?php
/**
 * API لوحة التحكم الأمنية
 * Security Dashboard API
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../includes/database.php';
require_once '../includes/security_monitor.php';

// التحقق من صلاحيات المدير
session_start();
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بالوصول']);
    exit;
}

$monitor = new SecurityMonitor();
$action = $_GET['action'] ?? $_POST['action'] ?? '';

try {
    switch ($action) {
        case 'get_alert':
            $alertId = $_GET['id'] ?? 0;
            $alert = getAlertDetails($alertId);
            echo json_encode(['success' => true, 'alert' => $alert]);
            break;
            
        case 'resolve_alert':
            $data = json_decode(file_get_contents('php://input'), true);
            $alertId = $data['alert_id'] ?? 0;
            $result = resolveAlert($alertId);
            echo json_encode($result);
            break;
            
        case 'get_events':
            $page = $_GET['page'] ?? 1;
            $limit = $_GET['limit'] ?? 20;
            $eventType = $_GET['event_type'] ?? '';
            $severity = $_GET['severity'] ?? '';
            $events = getSecurityEvents($page, $limit, $eventType, $severity);
            echo json_encode(['success' => true, 'events' => $events]);
            break;
            
        case 'get_stats':
            $days = $_GET['days'] ?? 7;
            $stats = $monitor->getSecurityStats($days);
            echo json_encode(['success' => true, 'stats' => $stats]);
            break;
            
        case 'block_ip':
            $data = json_decode(file_get_contents('php://input'), true);
            $ip = $data['ip'] ?? '';
            $duration = $data['duration'] ?? 3600; // ساعة واحدة افتراضياً
            $result = blockIP($ip, $duration);
            echo json_encode($result);
            break;
            
        case 'unblock_ip':
            $data = json_decode(file_get_contents('php://input'), true);
            $ip = $data['ip'] ?? '';
            $result = unblockIP($ip);
            echo json_encode($result);
            break;
            
        case 'get_blocked_ips':
            $blockedIPs = getBlockedIPs();
            echo json_encode(['success' => true, 'blocked_ips' => $blockedIPs]);
            break;
            
        case 'export_events':
            $format = $_GET['format'] ?? 'csv';
            $days = $_GET['days'] ?? 30;
            exportSecurityEvents($format, $days);
            break;
            
        case 'cleanup_old_records':
            $result = cleanupOldRecords();
            echo json_encode($result);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'خطأ في الخادم: ' . $e->getMessage()]);
}

/**
 * الحصول على تفاصيل تنبيه محدد
 */
function getAlertDetails($alertId) {
    global $database;
    
    $alert = $database->fetch("
        SELECT * FROM security_alerts 
        WHERE id = :id
    ", ['id' => $alertId]);
    
    if (!$alert) {
        throw new Exception('التنبيه غير موجود');
    }
    
    return $alert;
}

/**
 * حل تنبيه أمني
 */
function resolveAlert($alertId) {
    global $database;
    
    $userId = $_SESSION['user_id'];
    
    $result = $database->update('security_alerts', [
        'status' => 'resolved',
        'resolved_at' => date('Y-m-d H:i:s'),
        'resolved_by' => $userId
    ], 'id = :id', ['id' => $alertId]);
    
    if ($result) {
        // تسجيل النشاط
        logSecurityEvent('alert_resolved', 'low', [
            'alert_id' => $alertId,
            'resolved_by' => $userId
        ]);
        
        return ['success' => true, 'message' => 'تم حل التنبيه بنجاح'];
    } else {
        return ['success' => false, 'message' => 'فشل في حل التنبيه'];
    }
}

/**
 * الحصول على الأحداث الأمنية مع التصفية
 */
function getSecurityEvents($page, $limit, $eventType = '', $severity = '') {
    global $database;
    
    $offset = ($page - 1) * $limit;
    $conditions = [];
    $params = ['limit' => $limit, 'offset' => $offset];
    
    if ($eventType) {
        $conditions[] = 'event_type = :event_type';
        $params['event_type'] = $eventType;
    }
    
    if ($severity) {
        $conditions[] = 'severity = :severity';
        $params['severity'] = $severity;
    }
    
    $whereClause = $conditions ? 'WHERE ' . implode(' AND ', $conditions) : '';
    
    $events = $database->fetchAll("
        SELECT * FROM security_events 
        {$whereClause}
        ORDER BY created_at DESC 
        LIMIT :limit OFFSET :offset
    ", $params);
    
    $total = $database->fetch("
        SELECT COUNT(*) as count FROM security_events 
        {$whereClause}
    ", array_diff_key($params, ['limit' => '', 'offset' => '']))['count'];
    
    return [
        'events' => $events,
        'total' => $total,
        'page' => $page,
        'pages' => ceil($total / $limit)
    ];
}

/**
 * حظر عنوان IP
 */
function blockIP($ip, $duration) {
    global $database;
    
    if (!filter_var($ip, FILTER_VALIDATE_IP)) {
        return ['success' => false, 'message' => 'عنوان IP غير صحيح'];
    }
    
    $expiresAt = date('Y-m-d H:i:s', time() + $duration);
    
    // إنشاء جدول IP المحظورة إذا لم يكن موجوداً
    $database->query("
        CREATE TABLE IF NOT EXISTS blocked_ips (
            id INT PRIMARY KEY AUTO_INCREMENT,
            ip_address VARCHAR(45) NOT NULL UNIQUE,
            blocked_by INT NOT NULL,
            blocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP NOT NULL,
            reason TEXT,
            INDEX idx_ip_address (ip_address),
            INDEX idx_expires_at (expires_at)
        )
    ");
    
    try {
        $database->insert('blocked_ips', [
            'ip_address' => $ip,
            'blocked_by' => $_SESSION['user_id'],
            'expires_at' => $expiresAt,
            'reason' => 'حظر يدوي من لوحة التحكم الأمنية'
        ]);
        
        // تسجيل النشاط
        logSecurityEvent('ip_blocked', 'medium', [
            'ip_address' => $ip,
            'duration' => $duration,
            'expires_at' => $expiresAt
        ]);
        
        return ['success' => true, 'message' => 'تم حظر عنوان IP بنجاح'];
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
            return ['success' => false, 'message' => 'عنوان IP محظور مسبقاً'];
        }
        throw $e;
    }
}

/**
 * إلغاء حظر عنوان IP
 */
function unblockIP($ip) {
    global $database;
    
    $result = $database->delete('blocked_ips', 'ip_address = :ip', ['ip' => $ip]);
    
    if ($result) {
        // تسجيل النشاط
        logSecurityEvent('ip_unblocked', 'low', [
            'ip_address' => $ip
        ]);
        
        return ['success' => true, 'message' => 'تم إلغاء حظر عنوان IP بنجاح'];
    } else {
        return ['success' => false, 'message' => 'عنوان IP غير محظور'];
    }
}

/**
 * الحصول على قائمة عناوين IP المحظورة
 */
function getBlockedIPs() {
    global $database;
    
    // تنظيف العناوين المنتهية الصلاحية
    $database->delete('blocked_ips', 'expires_at < NOW()');
    
    return $database->fetchAll("
        SELECT bi.*, u.username as blocked_by_username
        FROM blocked_ips bi
        LEFT JOIN users u ON bi.blocked_by = u.id
        WHERE bi.expires_at > NOW()
        ORDER BY bi.blocked_at DESC
    ");
}

/**
 * تصدير الأحداث الأمنية
 */
function exportSecurityEvents($format, $days) {
    global $database;
    
    $since = date('Y-m-d', strtotime("-{$days} days"));
    $events = $database->fetchAll("
        SELECT * FROM security_events 
        WHERE DATE(created_at) >= :since
        ORDER BY created_at DESC
    ", ['since' => $since]);
    
    $filename = "security_events_" . date('Y-m-d') . "." . $format;
    
    if ($format === 'csv') {
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // العناوين
        fputcsv($output, [
            'ID', 'Event Type', 'Severity', 'IP Address', 
            'User ID', 'User Agent', 'Event Data', 'Created At'
        ]);
        
        // البيانات
        foreach ($events as $event) {
            fputcsv($output, [
                $event['id'],
                $event['event_type'],
                $event['severity'],
                $event['ip_address'],
                $event['user_id'],
                $event['user_agent'],
                $event['event_data'],
                $event['created_at']
            ]);
        }
        
        fclose($output);
    } elseif ($format === 'json') {
        header('Content-Type: application/json');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        echo json_encode($events, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }
    
    exit;
}

/**
 * تنظيف السجلات القديمة
 */
function cleanupOldRecords() {
    global $monitor;
    
    try {
        $monitor->cleanupOldRecords();
        
        // تسجيل النشاط
        logSecurityEvent('records_cleanup', 'low', [
            'performed_by' => $_SESSION['user_id']
        ]);
        
        return ['success' => true, 'message' => 'تم تنظيف السجلات القديمة بنجاح'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'فشل في تنظيف السجلات: ' . $e->getMessage()];
    }
}
?>