<?php
/**
 * نظام إدارة الإعدادات المحسن
 * Enhanced Configuration Management System
 * 
 * <AUTHOR> Line Team
 * @version 2.0
 */

class ConfigManager {
    private static $instance = null;
    private $config = [];
    private $database;
    private $cache = [];
    private $cacheExpiry = 3600; // ساعة واحدة
    
    private function __construct() {
        $this->loadStaticConfig();
        $this->loadDatabaseConfig();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * تحميل الإعدادات الثابتة من الملفات
     * Load static configuration from files
     */
    private function loadStaticConfig() {
        // تحميل الإعدادات الأساسية
        $configFiles = [
            'app' => __DIR__ . '/app_config.php',
            'database' => __DIR__ . '/database_config.php',
            'security' => __DIR__ . '/security_config.php',
            'products' => __DIR__ . '/products_config.php'
        ];
        
        foreach ($configFiles as $key => $file) {
            if (file_exists($file)) {
                $this->config[$key] = require $file;
            }
        }
        
        // تحميل متغيرات البيئة
        $this->loadEnvironmentVariables();
    }
    
    /**
     * تحميل متغيرات البيئة
     * Load environment variables
     */
    private function loadEnvironmentVariables() {
        $envFile = __DIR__ . '/.env';
        if (file_exists($envFile)) {
            $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                if (strpos($line, '#') === 0 || strpos($line, '=') === false) {
                    continue; // تخطي التعليقات والأسطر غير الصحيحة
                }
                
                list($key, $value) = explode('=', $line, 2);
                $key = trim($key);
                $value = trim($value, '"\'');
                
                // تحويل القيم المنطقية والرقمية
                $value = $this->convertValue($value);
                
                $_ENV[$key] = $value;
                putenv("$key=$value");
            }
        }
    }
    
    /**
     * تحميل الإعدادات من قاعدة البيانات
     * Load configuration from database
     */
    private function loadDatabaseConfig() {
        try {
            global $database;
            if ($database) {
                $this->database = $database;
                $this->loadDynamicSettings();
            }
        } catch (Exception $e) {
            error_log("Failed to load database config: " . $e->getMessage());
        }
    }
    
    /**
     * تحميل الإعدادات الديناميكية من قاعدة البيانات
     * Load dynamic settings from database
     */
    private function loadDynamicSettings() {
        $cacheKey = 'dynamic_settings';
        $cached = $this->getFromCache($cacheKey);
        
        if ($cached !== null) {
            $this->config['dynamic'] = $cached;
            return;
        }
        
        try {
            $sql = "SELECT setting_key, setting_value, setting_type FROM settings WHERE is_active = 1";
            $settings = $this->database->fetchAll($sql);
            
            $dynamicConfig = [];
            foreach ($settings as $setting) {
                $value = $this->convertSettingValue($setting['setting_value'], $setting['setting_type']);
                $dynamicConfig[$setting['setting_key']] = $value;
            }
            
            $this->config['dynamic'] = $dynamicConfig;
            $this->setCache($cacheKey, $dynamicConfig);
            
        } catch (Exception $e) {
            error_log("Failed to load dynamic settings: " . $e->getMessage());
            $this->config['dynamic'] = [];
        }
    }
    
    /**
     * الحصول على قيمة إعداد
     * Get configuration value
     */
    public function get($key, $default = null) {
        // البحث في متغيرات البيئة أولاً
        if (isset($_ENV[$key])) {
            return $_ENV[$key];
        }
        
        // البحث في الإعدادات الديناميكية
        if (isset($this->config['dynamic'][$key])) {
            return $this->config['dynamic'][$key];
        }
        
        // البحث في الإعدادات الثابتة
        $keys = explode('.', $key);
        $value = $this->config;
        
        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                return $default;
            }
            $value = $value[$k];
        }
        
        return $value;
    }
    
    /**
     * تعيين قيمة إعداد
     * Set configuration value
     */
    public function set($key, $value, $type = 'string', $persistent = false) {
        if ($persistent && $this->database) {
            return $this->setDatabaseSetting($key, $value, $type);
        }
        
        // تعيين في الذاكرة فقط
        $keys = explode('.', $key);
        $config = &$this->config;
        
        foreach ($keys as $k) {
            if (!isset($config[$k]) || !is_array($config[$k])) {
                $config[$k] = [];
            }
            $config = &$config[$k];
        }
        
        $config = $value;
        
        // مسح الكاش
        $this->clearCache('dynamic_settings');
        
        return true;
    }
    
    /**
     * حفظ إعداد في قاعدة البيانات
     * Save setting to database
     */
    private function setDatabaseSetting($key, $value, $type) {
        try {
            $sql = "INSERT INTO settings (setting_key, setting_value, setting_type, is_active) 
                    VALUES (?, ?, ?, 1) 
                    ON DUPLICATE KEY UPDATE 
                    setting_value = VALUES(setting_value), 
                    setting_type = VALUES(setting_type),
                    updated_at = NOW()";
            
            $result = $this->database->execute($sql, [$key, $value, $type]);
            
            if ($result) {
                // تحديث الكاش
                $this->config['dynamic'][$key] = $this->convertSettingValue($value, $type);
                $this->clearCache('dynamic_settings');
                return true;
            }
            
        } catch (Exception $e) {
            error_log("Failed to save setting: " . $e->getMessage());
        }
        
        return false;
    }
    
    /**
     * حذف إعداد
     * Delete setting
     */
    public function delete($key) {
        if ($this->database) {
            try {
                $sql = "UPDATE settings SET is_active = 0 WHERE setting_key = ?";
                $result = $this->database->execute($sql, [$key]);
                
                if ($result) {
                    unset($this->config['dynamic'][$key]);
                    $this->clearCache('dynamic_settings');
                    return true;
                }
            } catch (Exception $e) {
                error_log("Failed to delete setting: " . $e->getMessage());
            }
        }
        
        return false;
    }
    
    /**
     * التحقق من وجود إعداد
     * Check if setting exists
     */
    public function has($key) {
        return $this->get($key) !== null;
    }
    
    /**
     * الحصول على جميع الإعدادات
     * Get all settings
     */
    public function all($section = null) {
        if ($section) {
            return $this->config[$section] ?? [];
        }
        
        return array_merge(
            $this->config,
            ['env' => $_ENV]
        );
    }
    
    /**
     * الحصول على إعدادات بادئة معينة
     * Get settings with specific prefix
     */
    public function getByPrefix($prefix) {
        $result = [];
        
        // البحث في الإعدادات الديناميكية
        foreach ($this->config['dynamic'] ?? [] as $key => $value) {
            if (strpos($key, $prefix) === 0) {
                $result[$key] = $value;
            }
        }
        
        // البحث في متغيرات البيئة
        foreach ($_ENV as $key => $value) {
            if (strpos($key, $prefix) === 0) {
                $result[$key] = $value;
            }
        }
        
        return $result;
    }
    
    /**
     * إعادة تحميل الإعدادات
     * Reload configuration
     */
    public function reload() {
        $this->config = [];
        $this->clearAllCache();
        $this->loadStaticConfig();
        $this->loadDatabaseConfig();
    }
    
    /**
     * تحويل قيمة الإعداد حسب النوع
     * Convert setting value by type
     */
    private function convertSettingValue($value, $type) {
        switch ($type) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'integer':
            case 'number':
                return (int)$value;
            case 'float':
                return (float)$value;
            case 'array':
            case 'json':
                return json_decode($value, true) ?: [];
            default:
                return $value;
        }
    }
    
    /**
     * تحويل القيم التلقائي
     * Auto convert values
     */
    private function convertValue($value) {
        // تحويل القيم المنطقية
        if (in_array(strtolower($value), ['true', 'false'])) {
            return strtolower($value) === 'true';
        }
        
        // تحويل الأرقام
        if (is_numeric($value)) {
            return strpos($value, '.') !== false ? (float)$value : (int)$value;
        }
        
        // تحويل null
        if (strtolower($value) === 'null') {
            return null;
        }
        
        return $value;
    }
    
    /**
     * إدارة الكاش
     * Cache management
     */
    private function getFromCache($key) {
        $cacheFile = $this->getCacheFile($key);
        
        if (file_exists($cacheFile)) {
            $data = json_decode(file_get_contents($cacheFile), true);
            
            if ($data && $data['expires'] > time()) {
                return $data['value'];
            }
            
            unlink($cacheFile);
        }
        
        return null;
    }
    
    private function setCache($key, $value) {
        $cacheFile = $this->getCacheFile($key);
        $cacheDir = dirname($cacheFile);
        
        if (!is_dir($cacheDir)) {
            mkdir($cacheDir, 0755, true);
        }
        
        $data = [
            'value' => $value,
            'expires' => time() + $this->cacheExpiry
        ];
        
        file_put_contents($cacheFile, json_encode($data), LOCK_EX);
    }
    
    private function clearCache($key) {
        $cacheFile = $this->getCacheFile($key);
        if (file_exists($cacheFile)) {
            unlink($cacheFile);
        }
    }
    
    private function clearAllCache() {
        $cacheDir = __DIR__ . '/../cache/config';
        if (is_dir($cacheDir)) {
            $files = glob($cacheDir . '/*.json');
            foreach ($files as $file) {
                unlink($file);
            }
        }
    }
    
    private function getCacheFile($key) {
        return __DIR__ . '/../cache/config/' . md5($key) . '.json';
    }
    
    /**
     * تصدير الإعدادات
     * Export configuration
     */
    public function export($format = 'json') {
        $config = $this->all();
        
        switch ($format) {
            case 'json':
                return json_encode($config, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            case 'php':
                return '<?php return ' . var_export($config, true) . ';';
            case 'env':
                $env = '';
                foreach ($config['env'] ?? [] as $key => $value) {
                    $env .= "$key=" . (is_string($value) ? "\"$value\"" : $value) . "\n";
                }
                return $env;
            default:
                return $config;
        }
    }
    
    /**
     * استيراد الإعدادات
     * Import configuration
     */
    public function import($data, $format = 'json') {
        try {
            switch ($format) {
                case 'json':
                    $config = json_decode($data, true);
                    break;
                case 'php':
                    $config = eval('return ' . $data . ';');
                    break;
                default:
                    throw new Exception("Unsupported format: $format");
            }
            
            if ($config && is_array($config)) {
                foreach ($config as $key => $value) {
                    if ($key !== 'env') { // تجنب استيراد متغيرات البيئة
                        $this->set($key, $value, 'string', true);
                    }
                }
                return true;
            }
            
        } catch (Exception $e) {
            error_log("Failed to import config: " . $e->getMessage());
        }
        
        return false;
    }
    
    /**
     * التحقق من صحة الإعدادات
     * Validate configuration
     */
    public function validate() {
        $errors = [];
        
        // التحقق من الإعدادات المطلوبة
        $required = [
            'app.name',
            'app.url',
            'database.host',
            'database.name',
            'security.encryption_key'
        ];
        
        foreach ($required as $key) {
            if (!$this->has($key)) {
                $errors[] = "Missing required setting: $key";
            }
        }
        
        // التحقق من صحة URL
        $url = $this->get('app.url');
        if ($url && !filter_var($url, FILTER_VALIDATE_URL)) {
            $errors[] = "Invalid URL format for app.url";
        }
        
        // التحقق من مفتاح التشفير
        $encryptionKey = $this->get('security.encryption_key');
        if ($encryptionKey && strlen($encryptionKey) < 32) {
            $errors[] = "Encryption key must be at least 32 characters";
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
}

// إنشاء مثيل عام للوصول السريع
function config($key = null, $default = null) {
    $manager = ConfigManager::getInstance();
    
    if ($key === null) {
        return $manager;
    }
    
    return $manager->get($key, $default);
}

// تهيئة النظام
ConfigManager::getInstance();
?>