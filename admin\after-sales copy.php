<?php
require_once '../config/config.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn()) {
    redirect('/admin/login.php');
}

// إعداد متغيرات الصفحة
$pageTitle = 'خدمة ما بعد البيع';
$pageDescription = 'إدارة محتوى صفحة خدمة ما بعد البيع';
$currentPage = 'content';
$breadcrumbs = [
    ['title' => 'المحتوى الإضافي', 'url' => '#'],
    ['title' => 'خدمة ما بعد البيع']
];

// تضمين التخطيط
require_once 'includes/layout.php';

// بدء التخطيط
startLayout();

$database = new Database();

// معالجة العمليات
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'update_contact_info':
                    $section_key = $_POST['section_key'];
                    $title = trim($_POST['title']);
                    $content = trim($_POST['content']);
                    $data = json_encode($_POST['data'] ?? []);
                    
                    $database->query(
                        "UPDATE contact_info SET title = ?, content = ?, data = ?, updated_at = NOW() 
                         WHERE section_key = ?",
                        [$title, $content, $data, $section_key]
                    );
                    
                    $message = 'تم تحديث معلومات التواصل بنجاح';
                    break;
                    
                case 'upload_pdf':
                    if (isset($_FILES['pdf_file']) && $_FILES['pdf_file']['error'] === UPLOAD_ERR_OK) {
                        $uploadDir = '../uploads/downloads/';
                        if (!is_dir($uploadDir)) {
                            mkdir($uploadDir, 0755, true);
                        }
                        
                        $fileName = time() . '_' . $_FILES['pdf_file']['name'];
                        $filePath = $uploadDir . $fileName;
                        
                        if (move_uploaded_file($_FILES['pdf_file']['tmp_name'], $filePath)) {
                            $title = trim($_POST['pdf_title']);
                            $description = trim($_POST['pdf_description']);
                            
                            $database->query(
                                "INSERT INTO downloads (title, description, file_path, file_type, created_at) 
                                 VALUES (?, ?, ?, 'pdf', NOW())",
                                [$title, $description, 'uploads/downloads/' . $fileName]
                            );
                            
                            $message = 'تم رفع الملف بنجاح';
                        } else {
                            $error = 'فشل في رفع الملف';
                        }
                    } else {
                        $error = 'يرجى اختيار ملف PDF صالح';
                    }
                    break;
                    
                case 'delete_pdf':
                    $id = (int)$_POST['id'];
                    $file = $database->fetch("SELECT file_path FROM downloads WHERE id = ?", [$id]);
                    
                    if ($file && file_exists('../' . $file['file_path'])) {
                        unlink('../' . $file['file_path']);
                    }
                    
                    $database->query("DELETE FROM downloads WHERE id = ?", [$id]);
                    $message = 'تم حذف الملف بنجاح';
                    break;
            }
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// جلب البيانات
// 1. معلومات التواصل
$contactInfo = $database->fetchAll("SELECT * FROM contact_info WHERE is_active = 1");
$contactData = [];
foreach ($contactInfo as $info) {
    $contactData[$info['section_key']] = $info;
}

// 2. أفضل 5 أسئلة شائعة حسب التقييم
$topFaqs = $database->fetchAll(
    "SELECT f.*, 
            COALESCE(f.helpful_count, 0) as helpful_count,
            COALESCE(f.total_ratings, 0) as total_ratings,
            CASE 
                WHEN COALESCE(f.total_ratings, 0) > 0 
                THEN (COALESCE(f.helpful_count, 0) * 100.0 / COALESCE(f.total_ratings, 0))
                ELSE 0 
            END as rating_percentage
     FROM faqs f 
     WHERE f.is_active = 1 
     ORDER BY rating_percentage DESC, helpful_count DESC, total_ratings DESC
     LIMIT 5"
);

// 3. ملفات PDF المرفوعة
$pdfFiles = $database->fetchAll(
    "SELECT * FROM downloads WHERE file_type = 'pdf' ORDER BY created_at DESC"
);

?>

<style>
.after-sales-container {
    background: #f8f9fa;
    min-height: 100vh;
    padding: 20px 0;
}

.card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-bottom: none;
}

.card-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.card-body {
    padding: 25px;
}

.nav-tabs {
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 20px;
}

.nav-tabs .nav-link {
    border: none;
    color: #6c757d;
    font-weight: 500;
    padding: 12px 20px;
    border-radius: 8px 8px 0 0;
    margin-right: 5px;
}

.nav-tabs .nav-link.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: 2px solid transparent;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: block;
}

.form-control {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    padding: 12px 25px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    border: none;
    padding: 8px 15px;
    border-radius: 6px;
    font-size: 12px;
}

.faq-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    border-left: 4px solid #667eea;
}

.faq-question {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.faq-answer {
    color: #6c757d;
    font-size: 14px;
    line-height: 1.6;
}

.faq-stats {
    margin-top: 10px;
    font-size: 12px;
    color: #28a745;
}

.pdf-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.pdf-info h5 {
    margin: 0 0 5px 0;
    color: #495057;
    font-size: 16px;
}

.pdf-info p {
    margin: 0;
    color: #6c757d;
    font-size: 14px;
}

.alert {
    border-radius: 8px;
    border: none;
    padding: 15px 20px;
    margin-bottom: 20px;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}
</style>

<div class="after-sales-container">
    <div class="container-fluid">
        <?php if ($message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <!-- التبويبات الرئيسية -->
        <ul class="nav nav-tabs" id="afterSalesTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="contact-tab" data-bs-toggle="tab" data-bs-target="#contact" type="button" role="tab">
                    <i class="fas fa-phone me-2"></i>طرق التواصل
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="faqs-tab" data-bs-toggle="tab" data-bs-target="#faqs" type="button" role="tab">
                    <i class="fas fa-question-circle me-2"></i>الأسئلة الشائعة
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="hours-tab" data-bs-toggle="tab" data-bs-target="#hours" type="button" role="tab">
                    <i class="fas fa-clock me-2"></i>ساعات العمل
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="downloads-tab" data-bs-toggle="tab" data-bs-target="#downloads" type="button" role="tab">
                    <i class="fas fa-download me-2"></i>مركز التحميل
                </button>
            </li>
        </ul>

        <div class="tab-content" id="afterSalesTabContent">
            <!-- تبويب طرق التواصل -->
            <div class="tab-pane fade show active" id="contact" role="tabpanel">
                <div class="row">
                    <!-- معلومات الهاتف -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h3><i class="fas fa-phone me-2"></i>أرقام الهاتف</h3>
                            </div>
                            <div class="card-body">
                                <?php if (isset($contactData['phone'])): ?>
                                    <form method="POST">
                                        <input type="hidden" name="action" value="update_contact_info">
                                        <input type="hidden" name="section_key" value="phone">
                                        
                                        <div class="form-group">
                                            <label class="form-label">العنوان</label>
                                            <input type="text" name="title" class="form-control" 
                                                   value="<?php echo htmlspecialchars($contactData['phone']['title']); ?>">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label class="form-label">الهاتف الرئيسي</label>
                                            <input type="text" name="data[primary]" class="form-control" 
                                                   value="<?php echo htmlspecialchars(json_decode($contactData['phone']['data'], true)['primary'] ?? ''); ?>">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label class="form-label">الهاتف الثانوي</label>
                                            <input type="text" name="data[secondary]" class="form-control" 
                                                   value="<?php echo htmlspecialchars(json_decode($contactData['phone']['data'], true)['secondary'] ?? ''); ?>">
                                        </div>
                                        
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>حفظ التغييرات
                                        </button>
                                    </form>
                                <?php else: ?>
                                    <p class="text-muted">لا توجد معلومات هاتف محفوظة</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات البريد الإلكتروني -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h3><i class="fas fa-envelope me-2"></i>البريد الإلكتروني</h3>
                            </div>
                            <div class="card-body">
                                <?php if (isset($contactData['email'])): ?>
                                    <form method="POST">
                                        <input type="hidden" name="action" value="update_contact_info">
                                        <input type="hidden" name="section_key" value="email">
                                        
                                        <div class="form-group">
                                            <label class="form-label">العنوان</label>
                                            <input type="text" name="title" class="form-control" 
                                                   value="<?php echo htmlspecialchars($contactData['email']['title']); ?>">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label class="form-label">البريد العام</label>
                                            <input type="email" name="data[general]" class="form-control" 
                                                   value="<?php echo htmlspecialchars(json_decode($contactData['email']['data'], true)['general'] ?? ''); ?>">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label class="form-label">بريد الدعم</label>
                                            <input type="email" name="data[support]" class="form-control" 
                                                   value="<?php echo htmlspecialchars(json_decode($contactData['email']['data'], true)['support'] ?? ''); ?>">
                                        </div>
                                        
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>حفظ التغييرات
                                        </button>
                                    </form>
                                <?php else: ?>
                                    <p class="text-muted">لا توجد معلومات بريد إلكتروني محفوظة</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تبويب الأسئلة الشائعة -->
            <div class="tab-pane fade" id="faqs" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-question-circle me-2"></i>أفضل 5 أسئلة شائعة</h3>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($topFaqs)): ?>
                            <?php foreach ($topFaqs as $faq): ?>
                                <div class="faq-item">
                                    <div class="faq-question"><?php echo htmlspecialchars($faq['question']); ?></div>
                                    <div class="faq-answer"><?php echo nl2br(htmlspecialchars($faq['answer'])); ?></div>
                                    <div class="faq-stats">
                                        <i class="fas fa-thumbs-up text-success"></i>
                                        <?php echo $faq['helpful_count']; ?> من <?php echo $faq['total_ratings']; ?> 
                                        (<?php echo number_format($faq['rating_percentage'], 1); ?>% مفيد)
                                        <span class="badge bg-secondary ms-2"><?php echo htmlspecialchars($faq['category']); ?></span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                            
                            <div class="text-center mt-3">
                                <a href="faqs.php" class="btn btn-outline-primary">
                                    <i class="fas fa-edit me-2"></i>إدارة جميع الأسئلة الشائعة
                                </a>
                            </div>
                        <?php else: ?>
                            <p class="text-muted text-center">لا توجد أسئلة شائعة متاحة</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- تبويب ساعات العمل -->
            <div class="tab-pane fade" id="hours" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-clock me-2"></i>ساعات العمل</h3>
                    </div>
                    <div class="card-body">
                        <?php if (isset($contactData['working_hours'])): ?>
                            <form method="POST">
                                <input type="hidden" name="action" value="update_contact_info">
                                <input type="hidden" name="section_key" value="working_hours">
                                
                                <div class="form-group">
                                    <label class="form-label">العنوان</label>
                                    <input type="text" name="title" class="form-control" 
                                           value="<?php echo htmlspecialchars($contactData['working_hours']['title']); ?>">
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">الأحد - الخميس</label>
                                            <input type="text" name="data[weekdays]" class="form-control" 
                                                   value="<?php echo htmlspecialchars(json_decode($contactData['working_hours']['data'], true)['weekdays'] ?? ''); ?>"
                                                   placeholder="مثال: 8:00 ص - 5:00 م">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">الجمعة</label>
                                            <input type="text" name="data[friday]" class="form-control" 
                                                   value="<?php echo htmlspecialchars(json_decode($contactData['working_hours']['data'], true)['friday'] ?? ''); ?>"
                                                   placeholder="مثال: 2:00 م - 5:00 م">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">السبت</label>
                                    <input type="text" name="data[saturday]" class="form-control" 
                                           value="<?php echo htmlspecialchars(json_decode($contactData['working_hours']['data'], true)['saturday'] ?? ''); ?>"
                                           placeholder="مثال: مغلق أو 9:00 ص - 1:00 م">
                                </div>
                                
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>حفظ التغييرات
                                </button>
                            </form>
                        <?php else: ?>
                            <p class="text-muted">لا توجد معلومات ساعات عمل محفوظة</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- تبويب مركز التحميل -->
            <div class="tab-pane fade" id="downloads" role="tabpanel">
                <div class="row">
                    <!-- رفع ملف جديد -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h3><i class="fas fa-upload me-2"></i>رفع ملف PDF</h3>
                            </div>
                            <div class="card-body">
                                <form method="POST" enctype="multipart/form-data">
                                    <input type="hidden" name="action" value="upload_pdf">
                                    
                                    <div class="form-group">
                                        <label class="form-label">عنوان الملف</label>
                                        <input type="text" name="pdf_title" class="form-control" required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">وصف الملف</label>
                                        <textarea name="pdf_description" class="form-control" rows="3"></textarea>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">ملف PDF</label>
                                        <input type="file" name="pdf_file" class="form-control" accept=".pdf" required>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-upload me-2"></i>رفع الملف
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- قائمة الملفات المرفوعة -->
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h3><i class="fas fa-file-pdf me-2"></i>الملفات المرفوعة</h3>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($pdfFiles)): ?>
                                    <?php foreach ($pdfFiles as $file): ?>
                                        <div class="pdf-item">
                                            <div class="pdf-info">
                                                <h5><?php echo htmlspecialchars($file['title']); ?></h5>
                                                <p><?php echo htmlspecialchars($file['description']); ?></p>
                                                <small class="text-muted">
                                                    <i class="fas fa-calendar me-1"></i>
                                                    <?php echo date('Y-m-d H:i', strtotime($file['created_at'])); ?>
                                                </small>
                                            </div>
                                            <div class="pdf-actions">
                                                <a href="../<?php echo htmlspecialchars($file['file_path']); ?>" 
                                                   target="_blank" class="btn btn-sm btn-outline-primary me-2">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="action" value="delete_pdf">
                                                    <input type="hidden" name="id" value="<?php echo $file['id']; ?>">
                                                    <button type="submit" class="btn btn-sm btn-danger" 
                                                            onclick="return confirm('هل أنت متأكد من حذف هذا الملف؟')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <p class="text-muted text-center">لا توجد ملفات مرفوعة</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تفعيل التبويبات
document.addEventListener('DOMContentLoaded', function() {
    // Bootstrap tabs
    var triggerTabList = [].slice.call(document.querySelectorAll('#afterSalesTabs button'));
    triggerTabList.forEach(function (triggerEl) {
        var tabTrigger = new bootstrap.Tab(triggerEl);
        
        triggerEl.addEventListener('click', function (event) {
            event.preventDefault();
            tabTrigger.show();
        });
    });
});
</script>

<?php
// إنهاء التخطيط
endLayout();
?>