<?php
/**
 * فئة إدارة التكوين المحسنة
 * Enhanced Configuration Manager
 * 
 * تدير جميع إعدادات التطبيق من ملف .env وقاعدة البيانات
 * مع دعم التخزين المؤقت والتحقق من صحة البيانات
 */

class ConfigManager {
    private static $instance = null;
    private $config = [];
    private $envLoaded = false;
    private $cacheEnabled = true;
    private $cacheFile = 'cache/config.cache';
    
    private function __construct() {
        $this->loadEnvironment();
        $this->loadDatabaseConfig();
        $this->setupDefaults();
    }
    
    /**
     * الحصول على مثيل وحيد من الفئة
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * تحميل متغيرات البيئة من ملف .env
     */
    private function loadEnvironment() {
        $envFile = ROOT_PATH . '/.env';
        
        if (!file_exists($envFile)) {
            $envFile = ROOT_PATH . '/.env.example';
        }
        
        if (file_exists($envFile)) {
            $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            
            foreach ($lines as $line) {
                if (strpos($line, '#') === 0 || strpos($line, '=') === false) {
                    continue;
                }
                
                list($key, $value) = explode('=', $line, 2);
                $key = trim($key);
                $value = trim($value, '"\'');
                
                // تحويل القيم المنطقية
                if (in_array(strtolower($value), ['true', 'false'])) {
                    $value = strtolower($value) === 'true';
                } elseif (is_numeric($value)) {
                    $value = is_float($value) ? (float)$value : (int)$value;
                }
                
                $this->config[$key] = $value;
                
                // تعيين متغير البيئة
                if (!isset($_ENV[$key])) {
                    $_ENV[$key] = $value;
                    putenv("$key=$value");
                }
            }
            
            $this->envLoaded = true;
        }
    }
    
    /**
     * تحميل الإعدادات من قاعدة البيانات
     */
    private function loadDatabaseConfig() {
        try {
            if (class_exists('Database')) {
                $db = new Database();
                $settings = $db->fetchAll("SELECT setting_key, setting_value FROM settings");
                
                foreach ($settings as $setting) {
                    $this->config['db_' . $setting['setting_key']] = $setting['setting_value'];
                }
            }
        } catch (Exception $e) {
            // قاعدة البيانات غير متوفرة أو لا توجد جدول الإعدادات
            error_log("Config: Database settings not available - " . $e->getMessage());
        }
    }
    
    /**
     * إعداد القيم الافتراضية
     */
    private function setupDefaults() {
        $defaults = [
            'APP_NAME' => 'Green Line',
            'APP_ENV' => 'production',
            'APP_DEBUG' => false,
            'APP_URL' => 'http://localhost',
            'APP_TIMEZONE' => 'Asia/Riyadh',
            'APP_LOCALE' => 'ar',
            
            'DB_HOST' => 'localhost',
            'DB_PORT' => 3306,
            'DB_DATABASE' => 'greenline_db',
            'DB_USERNAME' => 'root',
            'DB_PASSWORD' => '',
            'DB_CHARSET' => 'utf8mb4',
            
            'SESSION_LIFETIME' => 120,
            'SESSION_COOKIE' => 'greenline_session',
            
            'UPLOAD_MAX_SIZE' => 5242880, // 5MB
            'UPLOAD_ALLOWED_IMAGES' => 'jpg,jpeg,png,gif,webp',
            
            'ITEMS_PER_PAGE' => 12,
            'ADMIN_ITEMS_PER_PAGE' => 20,
            
            'CACHE_DRIVER' => 'file',
            'CACHE_TTL' => 3600,
            
            'RATE_LIMIT_ENABLED' => true,
            'RATE_LIMIT_MAX_ATTEMPTS' => 60,
            
            'SECURITY_HEADERS' => true,
            'XSS_PROTECTION' => true,
        ];
        
        foreach ($defaults as $key => $value) {
            if (!isset($this->config[$key])) {
                $this->config[$key] = $value;
            }
        }
    }
    
    /**
     * الحصول على قيمة إعداد
     */
    public function get($key, $default = null) {
        // البحث في الإعدادات المحملة
        if (isset($this->config[$key])) {
            return $this->config[$key];
        }
        
        // البحث في متغيرات البيئة
        $envValue = getenv($key);
        if ($envValue !== false) {
            return $envValue;
        }
        
        // البحث في إعدادات قاعدة البيانات
        $dbKey = 'db_' . $key;
        if (isset($this->config[$dbKey])) {
            return $this->config[$dbKey];
        }
        
        return $default;
    }
    
    /**
     * تعيين قيمة إعداد
     */
    public function set($key, $value) {
        $this->config[$key] = $value;
        
        // حفظ في قاعدة البيانات إذا كان المفتاح يبدأ بـ db_
        if (strpos($key, 'db_') === 0) {
            $this->saveDatabaseSetting(substr($key, 3), $value);
        }
    }
    
    /**
     * حفظ إعداد في قاعدة البيانات
     */
    private function saveDatabaseSetting($key, $value) {
        try {
            if (class_exists('Database')) {
                $db = new Database();
                $db->execute(
                    "INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) 
                     ON DUPLICATE KEY UPDATE setting_value = ?",
                    [$key, $value, $value]
                );
            }
        } catch (Exception $e) {
            error_log("Config: Failed to save database setting - " . $e->getMessage());
        }
    }
    
    /**
     * التحقق من وجود إعداد
     */
    public function has($key) {
        return isset($this->config[$key]) || getenv($key) !== false;
    }
    
    /**
     * الحصول على جميع الإعدادات
     */
    public function all() {
        return $this->config;
    }
    
    /**
     * الحصول على إعدادات مجموعة معينة
     */
    public function group($prefix) {
        $group = [];
        foreach ($this->config as $key => $value) {
            if (strpos($key, $prefix . '_') === 0) {
                $groupKey = substr($key, strlen($prefix) + 1);
                $group[$groupKey] = $value;
            }
        }
        return $group;
    }
    
    /**
     * تحديث الإعدادات من قاعدة البيانات
     */
    public function refresh() {
        $this->loadDatabaseConfig();
    }
    
    /**
     * التحقق من صحة الإعدادات المطلوبة
     */
    public function validate() {
        $required = [
            'APP_NAME',
            'DB_HOST',
            'DB_DATABASE',
            'DB_USERNAME'
        ];
        
        $missing = [];
        foreach ($required as $key) {
            if (!$this->has($key)) {
                $missing[] = $key;
            }
        }
        
        if (!empty($missing)) {
            throw new Exception('Missing required configuration: ' . implode(', ', $missing));
        }
        
        return true;
    }
    
    /**
     * إنشاء ملف .env من القالب
     */
    public static function createEnvFile() {
        $envExample = ROOT_PATH . '/.env.example';
        $envFile = ROOT_PATH . '/.env';
        
        if (!file_exists($envFile) && file_exists($envExample)) {
            copy($envExample, $envFile);
            
            // إنشاء مفتاح تطبيق عشوائي
            $appKey = 'base64:' . base64_encode(random_bytes(32));
            $encryptionKey = bin2hex(random_bytes(32));
            
            $content = file_get_contents($envFile);
            $content = str_replace('your-32-character-secret-key-here', $appKey, $content);
            $content = str_replace('your-encryption-key-change-in-production', $encryptionKey, $content);
            
            file_put_contents($envFile, $content);
            
            return true;
        }
        
        return false;
    }
    
    /**
     * دوال مساعدة سريعة
     */
    public function isProduction() {
        return $this->get('APP_ENV') === 'production';
    }
    
    public function isDevelopment() {
        return $this->get('APP_ENV') === 'local' || $this->get('APP_ENV') === 'development';
    }
    
    public function isDebugMode() {
        return (bool)$this->get('APP_DEBUG', false);
    }
    
    public function getAppUrl() {
        return rtrim($this->get('APP_URL'), '/');
    }
    
    public function getDatabaseConfig() {
        return [
            'host' => $this->get('DB_HOST'),
            'port' => $this->get('DB_PORT'),
            'database' => $this->get('DB_DATABASE'),
            'username' => $this->get('DB_USERNAME'),
            'password' => $this->get('DB_PASSWORD'),
            'charset' => $this->get('DB_CHARSET'),
        ];
    }
    
    public function getMailConfig() {
        return [
            'host' => $this->get('MAIL_HOST'),
            'port' => $this->get('MAIL_PORT'),
            'username' => $this->get('MAIL_USERNAME'),
            'password' => $this->get('MAIL_PASSWORD'),
            'encryption' => $this->get('MAIL_ENCRYPTION'),
            'from_address' => $this->get('MAIL_FROM_ADDRESS'),
            'from_name' => $this->get('MAIL_FROM_NAME'),
        ];
    }
    
    public function getSocialConfig() {
        return [
            'facebook' => $this->get('SOCIAL_FACEBOOK_URL'),
            'twitter' => $this->get('SOCIAL_TWITTER_URL'),
            'instagram' => $this->get('SOCIAL_INSTAGRAM_URL'),
            'linkedin' => $this->get('SOCIAL_LINKEDIN_URL'),
            'youtube' => $this->get('SOCIAL_YOUTUBE_URL'),
            'whatsapp' => $this->get('SOCIAL_WHATSAPP_NUMBER'),
            'snapchat' => $this->get('SOCIAL_SNAPCHAT_URL'),
            'tiktok' => $this->get('SOCIAL_TIKTOK_URL'),
            'telegram' => $this->get('SOCIAL_TELEGRAM_URL'),
        ];
    }
}

// دوال مساعدة عامة
function config($key = null, $default = null) {
    $config = ConfigManager::getInstance();
    
    if ($key === null) {
        return $config;
    }
    
    return $config->get($key, $default);
}

function env($key, $default = null) {
    $value = getenv($key);
    
    if ($value === false) {
        return $default;
    }
    
    // تحويل القيم المنطقية
    switch (strtolower($value)) {
        case 'true':
        case '(true)':
            return true;
        case 'false':
        case '(false)':
            return false;
        case 'empty':
        case '(empty)':
            return '';
        case 'null':
        case '(null)':
            return null;
    }
    
    // إزالة علامات الاقتباس
    if (strlen($value) > 1 && $value[0] === '"' && $value[-1] === '"') {
        return substr($value, 1, -1);
    }
    
    return $value;
}

?>