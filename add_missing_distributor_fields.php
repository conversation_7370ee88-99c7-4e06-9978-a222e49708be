<?php
require_once 'config/database.php';

echo "بدء إضافة الحقول المفقودة لجدول الموزعين...\n";

try {
    // إضافة الحقول المفقودة
    $fieldsToAdd = [
        'manager' => 'VARCHAR(255) NULL COMMENT "مدير الموزع"',
        'established' => 'VARCHAR(4) NULL COMMENT "سنة التأسيس"',
        'rating' => 'DECIMAL(2,1) DEFAULT 0.0 COMMENT "التقييم"',
        'reviews' => 'INT DEFAULT 0 COMMENT "عدد التقييمات"',
        'coverage' => 'JSON NULL COMMENT "مناطق التغطية"',
        'specialties' => 'JSON NULL COMMENT "التخصصات"',
        'certified' => 'BOOLEAN DEFAULT FALSE COMMENT "معتمد"',
        'featured' => 'BOOLEAN DEFAULT FALSE COMMENT "مميز"'
    ];
    
    foreach ($fieldsToAdd as $fieldName => $fieldDefinition) {
        // التحقق من وجود الحقل
        $checkField = $database->fetchAll("SHOW COLUMNS FROM distributors LIKE '$fieldName'");
        
        if (empty($checkField)) {
            echo "إضافة الحقل: $fieldName...\n";
            $database->query("ALTER TABLE distributors ADD COLUMN $fieldName $fieldDefinition");
            echo "تم إضافة الحقل: $fieldName بنجاح\n";
        } else {
            echo "الحقل $fieldName موجود بالفعل\n";
        }
    }
    
    // إضافة فهارس للحقول الجديدة
    echo "\nإضافة الفهارس...\n";
    $indexes = [
        'idx_rating' => 'rating',
        'idx_certified' => 'certified',
        'idx_featured' => 'featured',
        'idx_established' => 'established'
    ];
    
    foreach ($indexes as $indexName => $column) {
        try {
            $database->query("ALTER TABLE distributors ADD INDEX $indexName ($column)");
            echo "تم إضافة الفهرس: $indexName\n";
        } catch (Exception $e) {
            echo "الفهرس $indexName موجود بالفعل أو حدث خطأ: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n=== هيكل الجدول النهائي ===\n";
    $result = $database->fetchAll('SHOW COLUMNS FROM distributors');
    foreach($result as $column) {
        echo $column['Field'] . ' - ' . $column['Type'] . "\n";
    }
    
    echo "\nتم الانتهاء من إضافة جميع الحقول المفقودة بنجاح!\n";
    
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage() . "\n";
}
?>