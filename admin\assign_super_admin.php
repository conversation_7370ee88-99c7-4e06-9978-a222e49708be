<?php
/**
 * ملف تعيين دور المدير العام
 * Assign Super Admin Role
 */

require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../includes/permissions.php';

// التحقق من تسجيل الدخول
requireLogin();

$message = '';
$error = '';

try {
    // إنشاء مدير الصلاحيات
    $permissionManager = new PermissionManager($database);
    $currentUserId = getCurrentUserId();
    
    if ($_POST && isset($_POST['assign_super_admin'])) {
        // تعيين دور المدير العام
        $result = $permissionManager->assignRole($currentUserId, 'super-admin', $currentUserId);
        
        if ($result['success']) {
            $message = 'تم تعيين دور المدير العام بنجاح';
        } else {
            $error = $result['error'];
        }
    }
    
    // جمع معلومات المستخدم
    $userRoles = $permissionManager->getUserRoles($currentUserId);
    $hasSuperAdmin = $permissionManager->hasRole($currentUserId, 'super-admin');
    $hasAdmin = $permissionManager->hasRole($currentUserId, 'admin');
    
} catch (Exception $e) {
    $error = 'خطأ: ' . $e->getMessage();
}

$pageTitle = 'تعيين دور المدير العام';
$currentPage = 'assign-admin';

require_once 'includes/layout.php';
startLayout();
showPageHeader();
showMessages();
?>

<div class="max-w-2xl mx-auto">
    <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">تعيين دور المدير العام</h2>
        
        <div class="mb-6">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="mr-3">
                        <h3 class="text-sm font-medium text-blue-800">معلومات</h3>
                        <div class="mt-2 text-sm text-blue-700">
                            <p>هذه الصفحة تسمح لك بتعيين دور المدير العام لحسابك الحالي.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- حالة المستخدم الحالية -->
        <div class="mb-6 border border-gray-200 rounded-lg p-4">
            <h3 class="font-medium text-gray-900 mb-3">حالة المستخدم الحالية</h3>
            <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                    <span>معرف المستخدم:</span>
                    <span class="font-medium"><?php echo $currentUserId; ?></span>
                </div>
                <div class="flex justify-between">
                    <span>دور المدير العام:</span>
                    <span class="<?php echo $hasSuperAdmin ? 'text-green-600' : 'text-red-600'; ?>">
                        <?php echo $hasSuperAdmin ? '✓ معين' : '✗ غير معين'; ?>
                    </span>
                </div>
                <div class="flex justify-between">
                    <span>دور المدير:</span>
                    <span class="<?php echo $hasAdmin ? 'text-green-600' : 'text-red-600'; ?>">
                        <?php echo $hasAdmin ? '✓ معين' : '✗ غير معين'; ?>
                    </span>
                </div>
            </div>
        </div>
        
        <!-- الأدوار الحالية -->
        <?php if (!empty($userRoles)): ?>
        <div class="mb-6 border border-gray-200 rounded-lg p-4">
            <h3 class="font-medium text-gray-900 mb-3">الأدوار المعينة حالياً</h3>
            <ul class="space-y-1 text-sm">
                <?php foreach ($userRoles as $role): ?>
                    <li class="flex items-center">
                        <span class="w-2 h-2 bg-green-500 rounded-full ml-2"></span>
                        <?php echo htmlspecialchars($role['name']); ?>
                        <?php if ($role['description']): ?>
                            <span class="text-gray-500 mr-2">(<?php echo htmlspecialchars($role['description']); ?>)</span>
                        <?php endif; ?>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php endif; ?>
        
        <!-- نموذج التعيين -->
        <?php if (!$hasSuperAdmin): ?>
        <form method="POST" onsubmit="return confirm('هل أنت متأكد من تعيين دور المدير العام لحسابك؟')">
            <input type="hidden" name="assign_super_admin" value="1">
            <button type="submit" class="bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
                <i class="fas fa-crown ml-2"></i>
                تعيين دور المدير العام
            </button>
        </form>
        <?php else: ?>
        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="mr-3">
                    <h3 class="text-sm font-medium text-green-800">تم بنجاح</h3>
                    <div class="mt-2 text-sm text-green-700">
                        <p>لديك بالفعل دور المدير العام. يمكنك الآن الوصول إلى جميع وظائف النظام.</p>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
        
        <div class="mt-6 flex space-x-4 space-x-reverse">
            <a href="test_permissions.php" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                <i class="fas fa-vial ml-2"></i>
                اختبار الصلاحيات
            </a>
            <a href="init_permissions.php" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                <i class="fas fa-cogs ml-2"></i>
                تهيئة الصلاحيات
            </a>
            <a href="/admin/" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700">
                <i class="fas fa-home ml-2"></i>
                العودة للوحة التحكم
            </a>
        </div>
    </div>
</div>

<?php
endLayout();
?>