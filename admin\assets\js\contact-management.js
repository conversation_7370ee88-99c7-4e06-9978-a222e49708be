/**
 * JavaScript لصفحة إدارة معلومات التواصل
 * Contact Information Management JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // تفعيل التبويبات
    initializeTabs();
    
    // تفعيل معاينة الخريطة
    initializeMapPreview();
    
    // تفعيل معاينة وسائل التواصل الاجتماعي
    initializeSocialMediaPreview();
    
    // تفعيل التحقق من صحة البيانات
    initializeFormValidation();
    
    // تفعيل الحفظ التلقائي
    initializeAutoSave();
    
    // تفعيل التحديث التلقائي لوصف ساعات العمل
    initializeWorkingHoursAutoUpdate();
});

/**
 * تفعيل نظام التبويبات
 */
function initializeTabs() {
    const tabButtons = document.querySelectorAll('.nav-tabs .nav-link');
    const tabPanes = document.querySelectorAll('.tab-pane');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            // إزالة الفئة النشطة من جميع التبويبات
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('show', 'active'));
            
            // تفعيل التبويب المحدد
            this.classList.add('active');
            const targetTab = document.querySelector(this.getAttribute('href'));
            if (targetTab) {
                targetTab.classList.add('show', 'active');
            }
        });
    });
}

/**
 * معاينة الخريطة
 */
function initializeMapPreview() {
    const mapEmbedTextarea = document.getElementById('map_embed_code');
    const mapPreview = document.getElementById('map_preview');
    
    if (mapEmbedTextarea && mapPreview) {
        mapEmbedTextarea.addEventListener('input', function() {
            const embedCode = this.value.trim();
            if (embedCode && embedCode.includes('<iframe')) {
                mapPreview.innerHTML = embedCode;
                mapPreview.style.display = 'block';
            } else {
                mapPreview.style.display = 'none';
            }
        });
        
        // معاينة أولية
        if (mapEmbedTextarea.value.trim()) {
            mapPreview.innerHTML = mapEmbedTextarea.value;
            mapPreview.style.display = 'block';
        }
    }
}

/**
 * معاينة وسائل التواصل الاجتماعي
 */
function initializeSocialMediaPreview() {
    const socialInputs = document.querySelectorAll('input[name^="social_"]');
    const socialPreview = document.getElementById('social_preview');
    
    if (socialInputs.length && socialPreview) {
        socialInputs.forEach(input => {
            input.addEventListener('input', updateSocialPreview);
        });
        
        updateSocialPreview();
    }
    
    function updateSocialPreview() {
        const socialData = {};
        socialInputs.forEach(input => {
            const platform = input.name.replace('social_', '');
            socialData[platform] = input.value.trim();
        });
        
        let previewHTML = '<div class="social-links-preview">';
        
        Object.entries(socialData).forEach(([platform, url]) => {
            if (url) {
                const icon = getSocialIcon(platform);
                previewHTML += `
                    <a href="${url}" target="_blank" class="social-link me-2">
                        <i class="${icon}"></i> ${platform.charAt(0).toUpperCase() + platform.slice(1)}
                    </a>
                `;
            }
        });
        
        previewHTML += '</div>';
        socialPreview.innerHTML = previewHTML;
    }
    
    function getSocialIcon(platform) {
        const icons = {
            facebook: 'fab fa-facebook-f',
            twitter: 'fab fa-twitter',
            instagram: 'fab fa-instagram',
            linkedin: 'fab fa-linkedin-in',
            youtube: 'fab fa-youtube'
        };
        return icons[platform] || 'fas fa-link';
    }
}

/**
 * التحقق من صحة البيانات
 */
function initializeFormValidation() {
    const form = document.getElementById('contact_form');
    
    if (form) {
        form.addEventListener('submit', function(e) {
            let isValid = true;
            const errors = [];
            
            // التحقق من البريد الإلكتروني
            const emailInputs = form.querySelectorAll('input[type="email"]');
            emailInputs.forEach(input => {
                if (input.value && !isValidEmail(input.value)) {
                    isValid = false;
                    errors.push(`البريد الإلكتروني غير صحيح: ${input.value}`);
                    input.classList.add('is-invalid');
                } else {
                    input.classList.remove('is-invalid');
                }
            });
            
            // التحقق من أرقام الهاتف
            const phoneInputs = form.querySelectorAll('input[name*="phone"], input[name*="mobile"], input[name*="whatsapp"]');
            phoneInputs.forEach(input => {
                if (input.value && !isValidPhone(input.value)) {
                    isValid = false;
                    errors.push(`رقم الهاتف غير صحيح: ${input.value}`);
                    input.classList.add('is-invalid');
                } else {
                    input.classList.remove('is-invalid');
                }
            });
            
            // التحقق من الروابط
            const urlInputs = form.querySelectorAll('input[name*="social_"], input[name*="website"]');
            urlInputs.forEach(input => {
                if (input.value && !isValidURL(input.value)) {
                    isValid = false;
                    errors.push(`الرابط غير صحيح: ${input.value}`);
                    input.classList.add('is-invalid');
                } else {
                    input.classList.remove('is-invalid');
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                showAlert('خطأ في البيانات', errors.join('<br>'), 'danger');
            }
        });
    }
}

/**
 * الحفظ التلقائي
 */
function initializeAutoSave() {
    const form = document.getElementById('contact_form');
    const autoSaveKey = 'contact_info_autosave';
    
    if (form) {
        // استرداد البيانات المحفوظة
        loadAutoSavedData();
        
        // حفظ البيانات عند التغيير
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('input', debounce(saveFormData, 1000));
        });
        
        // مسح البيانات المحفوظة عند الإرسال الناجح
        form.addEventListener('submit', function() {
            localStorage.removeItem(autoSaveKey);
        });
    }
    
    function saveFormData() {
        const formData = new FormData(form);
        const data = {};
        
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }
        
        localStorage.setItem(autoSaveKey, JSON.stringify(data));
        showToast('تم حفظ البيانات تلقائياً', 'info');
    }
    
    function loadAutoSavedData() {
        const savedData = localStorage.getItem(autoSaveKey);
        
        if (savedData) {
            try {
                const data = JSON.parse(savedData);
                
                Object.entries(data).forEach(([name, value]) => {
                    const input = form.querySelector(`[name="${name}"]`);
                    if (input && !input.value) {
                        input.value = value;
                    }
                });
                
                showToast('تم استرداد البيانات المحفوظة تلقائياً', 'success');
            } catch (e) {
                console.error('خطأ في استرداد البيانات المحفوظة:', e);
            }
        }
    }
}

/**
 * وظائف مساعدة
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function isValidPhone(phone) {
    const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
    return phoneRegex.test(phone);
}

function isValidURL(url) {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function showAlert(title, message, type = 'info') {
    const alertHTML = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <strong>${title}:</strong> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    const alertContainer = document.getElementById('alert_container') || document.querySelector('.container-fluid');
    alertContainer.insertAdjacentHTML('afterbegin', alertHTML);
}

function showToast(message, type = 'info') {
    const toastHTML = `
        <div class="toast align-items-center text-white bg-${type} border-0" role="alert">
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;
    
    let toastContainer = document.getElementById('toast_container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast_container';
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }
    
    toastContainer.insertAdjacentHTML('beforeend', toastHTML);
    
    const toastElement = toastContainer.lastElementChild;
    const toast = new bootstrap.Toast(toastElement, { delay: 3000 });
    toast.show();
    
    toastElement.addEventListener('hidden.bs.toast', () => {
        toastElement.remove();
    });
}

/**
 * التحديث التلقائي لوصف ساعات العمل
 */
function initializeWorkingHoursAutoUpdate() {
    // التحقق من وجود عناصر ساعات العمل
    const sundayThursdayOpen = document.getElementById('sunday_thursday_open');
    const sundayThursdayClose = document.getElementById('sunday_thursday_close');
    const fridayOpen = document.getElementById('friday_open');
    const fridayClose = document.getElementById('friday_close');
    const saturdaySelect = document.getElementById('saturday');
    const contentTextarea = document.getElementById('content');
    
    // التحقق من وجود جميع العناصر المطلوبة
    if (!sundayThursdayOpen || !sundayThursdayClose || !fridayOpen || !fridayClose || !saturdaySelect || !contentTextarea) {
        return; // إذا لم تكن في صفحة ساعات العمل
    }
    
    // إضافة مستمعي الأحداث لجميع الحقول
    [sundayThursdayOpen, sundayThursdayClose, fridayOpen, fridayClose, saturdaySelect].forEach(element => {
        element.addEventListener('change', updateWorkingHoursDescription);
    });
    
    // تحديث أولي عند تحميل الصفحة
    updateWorkingHoursDescription();
    
    function updateWorkingHoursDescription() {
        let description = '';
        
        // ساعات العمل للأحد - الخميس
        const sundayThursdayOpenTime = sundayThursdayOpen.value;
        const sundayThursdayCloseTime = sundayThursdayClose.value;
        
        if (sundayThursdayOpenTime && sundayThursdayCloseTime) {
            const openTime = formatTime(sundayThursdayOpenTime);
            const closeTime = formatTime(sundayThursdayCloseTime);
            description += `الأحد - الخميس: ${openTime} - ${closeTime}`;
        } else if (sundayThursdayOpenTime || sundayThursdayCloseTime) {
            description += 'الأحد - الخميس: يرجى تحديد أوقات الفتح والإغلاق';
        } else {
            description += 'الأحد - الخميس: مغلق';
        }
        
        // ساعات العمل للجمعة
        const fridayOpenTime = fridayOpen.value;
        const fridayCloseTime = fridayClose.value;
        
        if (fridayOpenTime && fridayCloseTime) {
            const openTime = formatTime(fridayOpenTime);
            const closeTime = formatTime(fridayCloseTime);
            description += `\nالجمعة: ${openTime} - ${closeTime}`;
        } else if (fridayOpenTime || fridayCloseTime) {
            description += '\nالجمعة: يرجى تحديد أوقات الفتح والإغلاق';
        } else {
            description += '\nالجمعة: مغلق';
        }
        
        // حالة يوم السبت
        const saturdayStatus = saturdaySelect.value;
        if (saturdayStatus === 'open') {
            description += '\nالسبت: مفتوح';
        } else {
            description += '\nالسبت: مغلق';
        }
        
        // تحديث محتوى textarea
        contentTextarea.value = description;
        
        // إضافة تأثير بصري لإظهار التحديث
        contentTextarea.style.backgroundColor = '#f0f9ff';
        setTimeout(() => {
            contentTextarea.style.backgroundColor = '';
        }, 500);
    }
    
    function formatTime(timeString) {
        if (!timeString) return '';
        
        const [hours, minutes] = timeString.split(':');
        const hour = parseInt(hours);
        const minute = minutes;
        
        if (hour === 0) {
            return `12:${minute} ص`;
        } else if (hour < 12) {
            return `${hour}:${minute} ص`;
        } else if (hour === 12) {
            return `12:${minute} م`;
        } else {
            return `${hour - 12}:${minute} م`;
        }
    }
}