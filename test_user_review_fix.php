<?php
session_start();
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// محاكاة تسجيل دخول المستخدم للاختبار
if (!isLoggedIn()) {
    $_SESSION['user_id'] = 1;
    $_SESSION['user_email'] = '<EMAIL>';
    $_SESSION['user_name'] = 'مستخدم تجريبي';
    $_SESSION['user_role'] = 'customer';
}

$currentUser = getCurrentUser();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح حفظ user_id في التقييمات</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-center mb-8">اختبار إصلاح حفظ user_id في التقييمات</h1>
        
        <!-- معلومات المستخدم الحالي -->
        <div class="bg-white p-6 rounded-lg shadow-md mb-8">
            <h2 class="text-xl font-bold mb-4">معلومات المستخدم الحالي</h2>
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <strong>معرف المستخدم:</strong> <?php echo $currentUser['id']; ?>
                </div>
                <div>
                    <strong>الاسم:</strong> <?php echo $currentUser['name']; ?>
                </div>
                <div>
                    <strong>البريد الإلكتروني:</strong> <?php echo $currentUser['email']; ?>
                </div>
                <div>
                    <strong>الدور:</strong> <?php echo $currentUser['role']; ?>
                </div>
            </div>
        </div>

        <!-- نموذج إضافة تقييم تجريبي -->
        <div class="bg-white p-6 rounded-lg shadow-md mb-8">
            <h2 class="text-xl font-bold mb-4">إضافة تقييم تجريبي</h2>
            <form id="reviewForm" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium mb-2">معرف المنتج:</label>
                    <input type="number" name="product_id" value="1" min="1" max="15" 
                           class="w-full p-2 border border-gray-300 rounded-md">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">التقييم (1-5):</label>
                    <select name="rating" class="w-full p-2 border border-gray-300 rounded-md">
                        <option value="5">5 نجوم - ممتاز</option>
                        <option value="4">4 نجوم - جيد جداً</option>
                        <option value="3">3 نجوم - جيد</option>
                        <option value="2">2 نجوم - مقبول</option>
                        <option value="1">1 نجمة - ضعيف</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">عنوان التقييم:</label>
                    <input type="text" name="review_title" value="تقييم تجريبي" 
                           class="w-full p-2 border border-gray-300 rounded-md">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">نص التقييم:</label>
                    <textarea name="review_text" rows="4" 
                              class="w-full p-2 border border-gray-300 rounded-md">هذا تقييم تجريبي لاختبار حفظ user_id في قاعدة البيانات.</textarea>
                </div>
                <button type="submit" 
                        class="bg-blue-500 text-white px-6 py-2 rounded-md hover:bg-blue-600">
                    إرسال التقييم
                </button>
            </form>
            <div id="reviewResult" class="mt-4"></div>
        </div>

        <!-- عرض التقييمات الحالية للمستخدم -->
        <div class="bg-white p-6 rounded-lg shadow-md mb-8">
            <h2 class="text-xl font-bold mb-4">التقييمات الحالية للمستخدم</h2>
            <?php
            global $database;
            $userReviews = $database->fetchAll(
                "SELECT * FROM reviews WHERE user_id = :user_id ORDER BY created_at DESC",
                ['user_id' => $currentUser['id']]
            );
            
            if (empty($userReviews)) {
                echo '<p class="text-gray-500">لا توجد تقييمات للمستخدم الحالي.</p>';
            } else {
                echo '<div class="space-y-4">';
                foreach ($userReviews as $review) {
                    echo '<div class="border border-gray-200 p-4 rounded-md">';
                    echo '<div class="grid grid-cols-2 gap-4 text-sm">';
                    echo '<div><strong>معرف التقييم:</strong> ' . $review['id'] . '</div>';
                    echo '<div><strong>معرف المنتج:</strong> ' . $review['product_id'] . '</div>';
                    echo '<div><strong>معرف المستخدم:</strong> ' . ($review['user_id'] ?? 'NULL') . '</div>';
                    echo '<div><strong>التقييم:</strong> ' . $review['rating'] . ' نجوم</div>';
                    echo '<div><strong>العنوان:</strong> ' . htmlspecialchars($review['title']) . '</div>';
                    echo '<div><strong>التاريخ:</strong> ' . $review['created_at'] . '</div>';
                    echo '</div>';
                    echo '<div class="mt-2"><strong>النص:</strong> ' . htmlspecialchars($review['comment']) . '</div>';
                    echo '</div>';
                }
                echo '</div>';
            }
            ?>
        </div>

        <!-- اختبار دوال التحقق من التقييمات -->
        <div class="bg-white p-6 rounded-lg shadow-md">
            <h2 class="text-xl font-bold mb-4">اختبار دوال التحقق من التقييمات</h2>
            <div class="space-y-4">
                <?php
                for ($product_id = 1; $product_id <= 5; $product_id++) {
                    $hasReviewed = hasUserReviewed($product_id, $currentUser['id']);
                    $userReview = getUserReview($product_id, $currentUser['id']);
                    
                    echo '<div class="border border-gray-200 p-4 rounded-md">';
                    echo '<h3 class="font-bold">المنتج رقم ' . $product_id . '</h3>';
                    echo '<p><strong>هل قام المستخدم بالتقييم:</strong> ' . ($hasReviewed ? 'نعم' : 'لا') . '</p>';
                    if ($userReview) {
                        echo '<p><strong>تفاصيل التقييم:</strong> ' . $userReview['rating'] . ' نجوم - ' . htmlspecialchars($userReview['title']) . '</p>';
                    }
                    echo '</div>';
                }
                ?>
            </div>
        </div>
    </div>

    <script>
    document.getElementById('reviewForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const resultDiv = document.getElementById('reviewResult');
        
        resultDiv.innerHTML = '<div class="text-blue-500">جاري إرسال التقييم...</div>';
        
        fetch('api/review.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                resultDiv.innerHTML = '<div class="text-green-500 p-3 bg-green-100 rounded-md">تم حفظ التقييم بنجاح!</div>';
                // إعادة تحميل الصفحة بعد 2 ثانية لعرض التقييم الجديد
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                resultDiv.innerHTML = '<div class="text-red-500 p-3 bg-red-100 rounded-md">خطأ: ' + data.message + '</div>';
            }
        })
        .catch(error => {
            resultDiv.innerHTML = '<div class="text-red-500 p-3 bg-red-100 rounded-md">حدث خطأ في الاتصال</div>';
            console.error('Error:', error);
        });
    });
    </script>
</body>
</html>