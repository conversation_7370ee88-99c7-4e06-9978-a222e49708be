<?php
require_once 'config/config.php';
require_once 'includes/functions.php';

// جلب المنتجات المتاحة
$productsData = getProducts(['limit' => 5]);
$products = $productsData['products'] ?? [];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API التقييمات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007cba;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #005a87;
        }
        .rating-stars {
            display: flex;
            gap: 5px;
            margin-bottom: 10px;
        }
        .star {
            font-size: 24px;
            color: #ddd;
            cursor: pointer;
            transition: color 0.2s;
        }
        .star.active {
            color: #ffc107;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            display: none;
            text-align: center;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار API التقييمات</h1>
        
        <form id="reviewForm">
            <div class="form-group">
                <label for="product_id">المنتج:</label>
                <select id="product_id" name="product_id" required>
                    <option value="">اختر منتج</option>
                    <?php foreach ($products as $product): ?>
                        <option value="<?php echo $product['id']; ?>">
                            <?php echo htmlspecialchars($product['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-group">
                <label for="customer_name">اسم العميل:</label>
                <input type="text" id="customer_name" name="customer_name" required>
            </div>
            
            <div class="form-group">
                <label for="customer_email">البريد الإلكتروني:</label>
                <input type="email" id="customer_email" name="customer_email" required>
            </div>
            
            <div class="form-group">
                <label>التقييم:</label>
                <div class="rating-stars">
                    <span class="star" data-rating="1">★</span>
                    <span class="star" data-rating="2">★</span>
                    <span class="star" data-rating="3">★</span>
                    <span class="star" data-rating="4">★</span>
                    <span class="star" data-rating="5">★</span>
                </div>
                <input type="hidden" id="rating" name="rating" required>
            </div>
            
            <div class="form-group">
                <label for="review_text">نص التقييم:</label>
                <textarea id="review_text" name="review_text" rows="4" required></textarea>
            </div>
            
            <button type="submit">إرسال التقييم</button>
            
            <div class="loading">
                <p>جاري إرسال التقييم...</p>
            </div>
        </form>
        
        <div id="result" class="result"></div>
    </div>

    <script>
        // التعامل مع نجوم التقييم
        const stars = document.querySelectorAll('.star');
        const ratingInput = document.getElementById('rating');
        
        stars.forEach(star => {
            star.addEventListener('click', function() {
                const rating = this.dataset.rating;
                ratingInput.value = rating;
                
                stars.forEach((s, index) => {
                    if (index < rating) {
                        s.classList.add('active');
                    } else {
                        s.classList.remove('active');
                    }
                });
            });
            
            star.addEventListener('mouseenter', function() {
                const rating = this.dataset.rating;
                
                stars.forEach((s, index) => {
                    if (index < rating) {
                        s.classList.add('active');
                    } else {
                        s.classList.remove('active');
                    }
                });
            });
        });
        
        // إعادة تعيين النجوم عند مغادرة المنطقة
        document.querySelector('.rating-stars').addEventListener('mouseleave', function() {
            const currentRating = ratingInput.value;
            stars.forEach((s, index) => {
                if (index < currentRating) {
                    s.classList.add('active');
                } else {
                    s.classList.remove('active');
                }
            });
        });
        
        // التعامل مع إرسال النموذج
        document.getElementById('reviewForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const loadingDiv = document.querySelector('.loading');
            const resultDiv = document.getElementById('result');
            const submitBtn = document.querySelector('button[type="submit"]');
            
            // إظهار حالة التحميل
            loadingDiv.style.display = 'block';
            submitBtn.disabled = true;
            resultDiv.style.display = 'none';
            
            // إرسال البيانات إلى API
            fetch('api/review.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                loadingDiv.style.display = 'none';
                submitBtn.disabled = false;
                resultDiv.style.display = 'block';
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = '<strong>نجح!</strong> ' + data.message;
                    document.getElementById('reviewForm').reset();
                    ratingInput.value = '';
                    stars.forEach(s => s.classList.remove('active'));
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '<strong>خطأ!</strong> ' + data.message;
                }
            })
            .catch(error => {
                loadingDiv.style.display = 'none';
                submitBtn.disabled = false;
                resultDiv.style.display = 'block';
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '<strong>خطأ!</strong> حدث خطأ في الاتصال: ' + error.message;
                console.error('Error:', error);
            });
        });
    </script>
</body>
</html>