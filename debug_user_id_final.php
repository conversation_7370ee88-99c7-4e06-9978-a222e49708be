<?php
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// تجنب تكرار session_start
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// محاكاة تسجيل دخول المستخدم
if (!isLoggedIn()) {
    $_SESSION['user_id'] = 3;
    $_SESSION['user_name'] = 'المدير العام';
    $_SESSION['user_email'] = '<EMAIL>';
    $_SESSION['user_role'] = 'super-admin';
}

echo "<h1>تشخيص نهائي لمشكلة user_id</h1>";

// 1. فحص جدول reviews
echo "<h2>1. فحص بنية جدول reviews:</h2>";
try {
    global $database;
    $pdo = $database->getConnection();
    
    // عرض بنية الجدول
    $columns = $database->fetchAll("DESCRIBE reviews");
    echo "<div style='background: #e2e3e5; padding: 10px; border-radius: 5px;'>";
    echo "<strong>أعمدة جدول reviews:</strong><br>";
    foreach ($columns as $column) {
        $highlight = ($column['Field'] == 'user_id') ? 'background: yellow;' : '';
        echo "<span style='$highlight'>- " . $column['Field'] . " (" . $column['Type'] . ") " . 
             ($column['Null'] == 'YES' ? 'NULL' : 'NOT NULL') . "</span><br>";
    }
    echo "</div>";
    
    // التحقق من وجود عمود user_id
    $hasUserId = false;
    foreach ($columns as $column) {
        if ($column['Field'] == 'user_id') {
            $hasUserId = true;
            break;
        }
    }
    
    if (!$hasUserId) {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
        echo "❌ عمود user_id غير موجود في جدول reviews!";
        echo "</div>";
        
        // إضافة عمود user_id
        echo "<h3>إضافة عمود user_id:</h3>";
        try {
            $database->query("ALTER TABLE reviews ADD COLUMN user_id INT NULL AFTER id");
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
            echo "✅ تم إضافة عمود user_id بنجاح";
            echo "</div>";
        } catch (Exception $e) {
            echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
            echo "❌ خطأ في إضافة عمود user_id: " . $e->getMessage();
            echo "</div>";
        }
    } else {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
        echo "✅ عمود user_id موجود في جدول reviews";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
    echo "❌ خطأ في فحص جدول reviews: " . $e->getMessage();
    echo "</div>";
}

// 2. حذف جميع المراجعات التجريبية
echo "<h2>2. تنظيف المراجعات التجريبية:</h2>";
try {
    $deleted = $database->query("DELETE FROM reviews WHERE comment LIKE '%تجريبية%' OR comment LIKE '%اختبار%'")->rowCount();
    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; color: #856404;'>";
    echo "تم حذف " . $deleted . " مراجعة تجريبية";
    echo "</div>";
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
    echo "خطأ في حذف المراجعات: " . $e->getMessage();
    echo "</div>";
}

// 3. اختبار حفظ مراجعة جديدة مع user_id
echo "<h2>3. اختبار حفظ مراجعة جديدة:</h2>";

$test_data = [
    'product_id' => 1,
    'user_id' => 3,
    'customer_name' => 'المدير العام',
    'customer_email' => '<EMAIL>',
    'rating' => 5,
    'review_text' => 'مراجعة نهائية لاختبار user_id',
    'review_title' => 'اختبار نهائي'
];

echo "<div style='background: #e2e3e5; padding: 10px; border-radius: 5px; margin-bottom: 10px;'>";
echo "<strong>البيانات المرسلة:</strong><br>";
foreach ($test_data as $key => $value) {
    echo "• $key: $value<br>";
}
echo "</div>";

try {
    // استخدام دالة saveUserReview
    $result = saveUserReview(
        $test_data['product_id'],
        $test_data['user_id'],
        $test_data['customer_name'],
        $test_data['customer_email'],
        $test_data['rating'],
        $test_data['review_text'],
        $test_data['review_title']
    );
    
    if ($result && is_numeric($result)) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
        echo "✅ تم حفظ المراجعة بنجاح - معرف المراجعة: " . $result;
        echo "</div>";
        
        // التحقق من البيانات المحفوظة
        $savedReview = $database->fetch("SELECT * FROM reviews WHERE id = ?", [$result]);
        if ($savedReview) {
            echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin-top: 10px; color: #0c5460;'>";
            echo "<strong>البيانات المحفوظة في قاعدة البيانات:</strong><br>";
            echo "• معرف المراجعة: " . $savedReview['id'] . "<br>";
            echo "• معرف المنتج: " . $savedReview['product_id'] . "<br>";
            echo "• <span style='background: yellow; padding: 2px;'>معرف المستخدم: " . $savedReview['user_id'] . "</span><br>";
            echo "• الاسم: " . $savedReview['name'] . "<br>";
            echo "• البريد: " . $savedReview['email'] . "<br>";
            echo "• التقييم: " . $savedReview['rating'] . "<br>";
            echo "• العنوان: " . $savedReview['title'] . "<br>";
            echo "• المراجعة: " . $savedReview['comment'] . "<br>";
            echo "• معتمدة: " . ($savedReview['is_approved'] ? 'نعم' : 'لا') . "<br>";
            echo "• تاريخ الإنشاء: " . $savedReview['created_at'] . "<br>";
            echo "</div>";
            
            // التحقق من user_id
            if ($savedReview['user_id'] == 3) {
                echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724; margin-top: 10px;'>";
                echo "🎉 <strong>نجح الاختبار! تم حفظ user_id بشكل صحيح</strong>";
                echo "</div>";
            } else {
                echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24; margin-top: 10px;'>";
                echo "❌ فشل الاختبار! user_id لم يتم حفظه بشكل صحيح";
                echo "</div>";
            }
        }
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
        echo "❌ فشل في حفظ المراجعة: " . $result;
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
    echo "❌ خطأ في اختبار الحفظ: " . $e->getMessage();
    echo "<br><strong>Stack trace:</strong><br>" . $e->getTraceAsString();
    echo "</div>";
}

// 4. اختبار مباشر لقاعدة البيانات
echo "<h2>4. اختبار مباشر لقاعدة البيانات:</h2>";
try {
    // حذف أي مراجعة سابقة
    $database->query("DELETE FROM reviews WHERE product_id = 1 AND user_id = 3");
    
    // إدراج مباشر
    $insertResult = $database->insert('reviews', [
        'product_id' => 1,
        'user_id' => 3,
        'name' => 'اختبار مباشر',
        'email' => '<EMAIL>',
        'rating' => 4,
        'title' => 'اختبار مباشر',
        'comment' => 'اختبار إدراج مباشر في قاعدة البيانات',
        'is_approved' => 1,
        'created_at' => date('Y-m-d H:i:s')
    ]);
    
    if ($insertResult) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
        echo "✅ تم الإدراج المباشر بنجاح - معرف المراجعة: " . $insertResult;
        echo "</div>";
        
        // التحقق من البيانات
        $directTest = $database->fetch("SELECT * FROM reviews WHERE id = ?", [$insertResult]);
        if ($directTest && $directTest['user_id'] == 3) {
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724; margin-top: 10px;'>";
            echo "🎉 <strong>الإدراج المباشر نجح! user_id = " . $directTest['user_id'] . "</strong>";
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
    echo "❌ خطأ في الاختبار المباشر: " . $e->getMessage();
    echo "</div>";
}

// 5. عرض جميع المراجعات الحالية
echo "<h2>5. جميع المراجعات في قاعدة البيانات:</h2>";
try {
    $allReviews = $database->fetchAll("SELECT * FROM reviews ORDER BY created_at DESC LIMIT 10");
    if (!empty($allReviews)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>ID</th><th>Product ID</th><th style='background: yellow;'>User ID</th><th>Name</th><th>Email</th><th>Rating</th><th>Title</th><th>Comment</th><th>Approved</th><th>Created</th>";
        echo "</tr>";
        
        foreach ($allReviews as $review) {
            $userIdStyle = $review['user_id'] ? 'background: lightgreen;' : 'background: lightcoral;';
            echo "<tr>";
            echo "<td>" . $review['id'] . "</td>";
            echo "<td>" . $review['product_id'] . "</td>";
            echo "<td style='$userIdStyle'>" . ($review['user_id'] ?: 'NULL') . "</td>";
            echo "<td>" . $review['name'] . "</td>";
            echo "<td>" . $review['email'] . "</td>";
            echo "<td>" . $review['rating'] . "</td>";
            echo "<td>" . substr($review['title'], 0, 20) . "...</td>";
            echo "<td>" . substr($review['comment'], 0, 30) . "...</td>";
            echo "<td>" . ($review['is_approved'] ? 'نعم' : 'لا') . "</td>";
            echo "<td>" . $review['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; color: #856404;'>";
        echo "لا توجد مراجعات في قاعدة البيانات";
        echo "</div>";
    }
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
    echo "❌ خطأ في جلب المراجعات: " . $e->getMessage();
    echo "</div>";
}

// 6. نموذج اختبار نهائي
echo "<h2>6. نموذج اختبار نهائي:</h2>";
?>

<form method="POST" style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin-top: 20px;">
    <input type="hidden" name="final_test" value="1">
    
    <div style="margin-bottom: 15px;">
        <label><strong>معرف المنتج:</strong></label><br>
        <input type="number" name="product_id" value="1" required style="width: 100%; padding: 8px;">
    </div>
    
    <div style="margin-bottom: 15px;">
        <label><strong>التقييم (1-5):</strong></label><br>
        <select name="rating" required style="width: 100%; padding: 8px;">
            <option value="5" selected>5 نجوم</option>
            <option value="4">4 نجوم</option>
            <option value="3">3 نجوم</option>
            <option value="2">2 نجمة</option>
            <option value="1">1 نجمة</option>
        </select>
    </div>
    
    <div style="margin-bottom: 15px;">
        <label><strong>عنوان المراجعة:</strong></label><br>
        <input type="text" name="review_title" value="اختبار نهائي لحفظ user_id" style="width: 100%; padding: 8px;">
    </div>
    
    <div style="margin-bottom: 15px;">
        <label><strong>نص المراجعة:</strong></label><br>
        <textarea name="review_text" required style="width: 100%; padding: 8px; height: 100px;">هذا اختبار نهائي للتأكد من حفظ user_id بشكل صحيح في قاعدة البيانات</textarea>
    </div>
    
    <button type="submit" style="background: #28a745; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;">
        🚀 اختبار نهائي لحفظ user_id
    </button>
</form>

<?php
// معالجة النموذج النهائي
if (isset($_POST['final_test'])) {
    echo "<h3 style='color: #007bff;'>🔍 نتيجة الاختبار النهائي:</h3>";
    
    try {
        // حذف أي مراجعة سابقة
        $database->query("DELETE FROM reviews WHERE product_id = ? AND user_id = ?", [$_POST['product_id'], 3]);
        
        // حفظ المراجعة
        $finalResult = saveUserReview(
            (int)$_POST['product_id'],
            3, // user_id ثابت
            'المدير العام',
            '<EMAIL>',
            (int)$_POST['rating'],
            $_POST['review_text'],
            $_POST['review_title']
        );
        
        if ($finalResult && is_numeric($finalResult)) {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724; border: 2px solid #28a745;'>";
            echo "🎉 <strong>نجح الاختبار النهائي!</strong><br>";
            echo "معرف المراجعة المحفوظة: " . $finalResult;
            echo "</div>";
            
            // التحقق النهائي
            $finalCheck = $database->fetch("SELECT * FROM reviews WHERE id = ?", [$finalResult]);
            if ($finalCheck && $finalCheck['user_id'] == 3) {
                echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin-top: 10px; color: #0c5460; border: 2px solid #17a2b8;'>";
                echo "✅ <strong>تأكيد نهائي: user_id تم حفظه بنجاح!</strong><br>";
                echo "• معرف المستخدم المحفوظ: <span style='background: yellow; padding: 2px; font-weight: bold;'>" . $finalCheck['user_id'] . "</span><br>";
                echo "• اسم المستخدم: " . $finalCheck['name'] . "<br>";
                echo "• التقييم: " . $finalCheck['rating'] . " نجوم<br>";
                echo "• تاريخ الحفظ: " . $finalCheck['created_at'];
                echo "</div>";
                
                echo "<div style='background: #28a745; color: white; padding: 20px; border-radius: 10px; margin-top: 20px; text-align: center;'>";
                echo "<h2>🎊 تم حل المشكلة بنجاح! 🎊</h2>";
                echo "<p>user_id يتم حفظه الآن بشكل صحيح في قاعدة البيانات</p>";
                echo "</div>";
            } else {
                echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24; margin-top: 10px;'>";
                echo "❌ <strong>المشكلة لا تزال موجودة!</strong><br>";
                echo "user_id لم يتم حفظه بشكل صحيح";
                echo "</div>";
            }
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
            echo "❌ فشل الاختبار النهائي: " . $finalResult;
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
        echo "❌ خطأ في الاختبار النهائي: " . $e->getMessage();
        echo "</div>";
    }
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
}

h1, h2, h3 {
    color: #333;
}

table {
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

th {
    background: #007bff;
    color: white;
    padding: 8px;
}

td {
    padding: 6px;
    border-bottom: 1px solid #ddd;
}

form {
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}
</style>