<?php
require_once 'config/config.php';
require_once 'config/database.php';

$database = new Database();

// Get content settings
$contentQuery = "SELECT setting_key, setting_value FROM settings WHERE setting_key LIKE 'distributors_%'";
$contentResults = $database->query($contentQuery)->fetchAll();
$content = [];
foreach ($contentResults as $row) {
    $content[$row['setting_key']] = $row['setting_value'];
}

// Get filter parameters
$regionFilter = $_GET['region'] ?? '';
$distributorTypeFilter = $_GET['type'] ?? '';
$search = $_GET['search'] ?? '';

// Build query conditions
$whereConditions = ["is_active = 1"];
$params = [];

if (!empty($search)) {
    $whereConditions[] = "(name LIKE ? OR region LIKE ? OR address LIKE ?)";
    $searchTerm = "%$search%";
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

if (!empty($regionFilter)) {
    $whereConditions[] = "region = ?";
    $params[] = $regionFilter;
}

if (!empty($distributorTypeFilter)) {
    $whereConditions[] = "distributor_type = ?";
    $params[] = $distributorTypeFilter;
}

$whereClause = 'WHERE ' . implode(' AND ', $whereConditions);

// Get distributors with priority sorting
$query = "SELECT * FROM distributors $whereClause ORDER BY 
    CASE distributor_type 
        WHEN 'رئيسي' THEN 1 
        WHEN 'مميز' THEN 2 
        WHEN 'عادي' THEN 3 
        ELSE 4 
    END, 
    region ASC, 
    sort_order ASC, 
    name ASC";
$distributors = $database->query($query, $params)->fetchAll();



// Get unique regions for filter
$regionsQuery = "SELECT DISTINCT region FROM distributors WHERE is_active = 1 ORDER BY region";
$regions = $database->query($regionsQuery)->fetchAll();

// Group distributors by region
$distributorsByRegion = [];
foreach ($distributors as $distributor) {
    $distributorsByRegion[$distributor['region']][] = $distributor;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>شبكة الموزعين - Green Line</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .distributor-card {
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
        }
        .distributor-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            border-color: #10b981;
        }
        .premium-badge {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        }
        .featured-badge {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }
        .line-clamp-1 {
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        .truncate {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .w-3 {
            width: 0.75rem;
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: repeat(1, minmax(0, 1fr));
            }
        }
        @media (min-width: 768px) and (max-width: 1024px) {
            .md\:grid-cols-2 {
                grid-template-columns: repeat(2, minmax(0, 1fr));
            }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-16">
        <div class="container mx-auto px-4">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-bold mb-4">
                    <?php echo htmlspecialchars($content['distributors_hero_title'] ?? 'شبكة الموزعين'); ?>
                </h1>
                <p class="text-xl md:text-2xl text-blue-100 mb-8">
                    <?php echo htmlspecialchars($content['distributors_hero_subtitle'] ?? 'اعثر على أقرب موزع لمنتجاتنا في منطقتك'); ?>
                </p>
                
                <!-- Search and Filters -->
                <div class="max-w-4xl mx-auto">
                    <form method="GET" class="bg-white rounded-lg p-6 shadow-lg">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div class="md:col-span-2">
                                <input type="text" name="search" value="<?php echo htmlspecialchars($search); ?>" 
                                       placeholder="البحث بالاسم أو المنطقة..." 
                                       class="w-full border border-gray-300 rounded-md px-4 py-3 text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div>
                                <select name="region" class="w-full border border-gray-300 rounded-md px-4 py-3 text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="">جميع المناطق</option>
                                    <?php foreach ($regions as $region): ?>
                                        <option value="<?php echo htmlspecialchars($region['region']); ?>" 
                                                <?php echo $regionFilter === $region['region'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($region['region']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div>
                                <select name="type" class="w-full border border-gray-300 rounded-md px-4 py-3 text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="">جميع التصنيفات</option>
                                    <option value="رئيسي" <?php echo $distributorTypeFilter === 'رئيسي' ? 'selected' : ''; ?>>موزع رئيسي</option>
                                    <option value="مميز" <?php echo $distributorTypeFilter === 'مميز' ? 'selected' : ''; ?>>موزع مميز</option>
                                    <option value="عادي" <?php echo $distributorTypeFilter === 'عادي' ? 'selected' : ''; ?>>موزع عادي</option>
                                </select>
                            </div>
                        </div>
                        <div class="mt-4 text-center">
                            <button type="submit" class="bg-blue-600 text-white px-8 py-3 rounded-md hover:bg-blue-700 transition-colors font-semibold">
                                <i class="fas fa-search ml-2"></i>بحث
                            </button>
                            <button type="button" id="resetFilters" class="bg-gray-500 text-white px-8 py-3 rounded-md hover:bg-gray-600 transition-colors font-semibold mr-2">
                                <i class="fas fa-refresh ml-2"></i>إعادة تعيين
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Search Results Info -->
    <?php 
    $totalDistributors = count($distributors);
    $searchActive = !empty($search) || !empty($regionFilter) || !empty($distributorTypeFilter);
    ?>
    <?php if ($searchActive): ?>
    <section class="bg-gray-100 py-8">
        <div class="container mx-auto px-4">
            <div class="bg-white rounded-lg p-6 shadow-sm">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div class="mb-4 md:mb-0">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">
                            <?php if (!empty($search)): ?>
                                نتائج البحث عن: "<?php echo htmlspecialchars($search); ?>"
                            <?php elseif (!empty($regionFilter)): ?>
                                الموزعين في منطقة: <?php echo htmlspecialchars($regionFilter); ?>
                            <?php elseif (!empty($distributorTypeFilter)): ?>
                                الموزعين من نوع: <?php echo htmlspecialchars($distributorTypeFilter); ?>
                            <?php else: ?>
                                نتائج البحث
                            <?php endif; ?>
                        </h3>
                        <p class="text-gray-600">
                            تم العثور على <span class="font-semibold text-blue-600"><?php echo $totalDistributors; ?></span> موزع
                            <?php if (!empty($regionFilter)): ?>
                                في منطقة <?php echo htmlspecialchars($regionFilter); ?>
                            <?php endif; ?>
                        </p>
                    </div>
                    <div>
                        <a href="distributors.php" class="inline-flex items-center px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors">
                            <i class="fas fa-times ml-2"></i>مسح الفلاتر
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <?php endif; ?>



    <!-- Distributors Section -->
    <section class="py-16">
        <div class="container mx-auto px-4">
            <?php if (!empty($distributorsByRegion)): ?>
                <?php foreach ($distributorsByRegion as $region => $regionDistributors): ?>
                    <div class="mb-12">
                        <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">
                            <i class="fas fa-map-marker-alt text-blue-600 ml-2"></i>
                            <?php echo htmlspecialchars($region); ?>
                        </h2>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                            <?php foreach ($regionDistributors as $distributor): ?>
                                <div class="distributor-card bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100 hover:border-green-200">
                                    <!-- Header with Badge and Image -->
                                    <div class="relative">
                                        <!-- Distributor Type Badge -->
                                        <?php if ($distributor['distributor_type'] === 'رئيسي'): ?>
                                            <div class="absolute top-3 right-3 z-10">
                                                <span class="premium-badge text-white px-2 py-1 rounded-full text-xs font-bold shadow-lg">
                                                    <i class="fas fa-crown ml-1"></i>رئيسي
                                                </span>
                                            </div>
                                        <?php elseif ($distributor['distributor_type'] === 'مميز'): ?>
                                            <div class="absolute top-3 right-3 z-10">
                                                <span class="featured-badge text-white px-2 py-1 rounded-full text-xs font-bold shadow-lg">
                                                    <i class="fas fa-star ml-1"></i>مميز
                                                </span>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <!-- Status Badge -->
                        <div class="absolute top-3 left-3 z-10">
                            <?php if ($distributor['is_active'] == 1): ?>
                                <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium border border-green-200">
                                    <i class="fas fa-check-circle ml-1"></i>نشط
                                </span>
                            <?php else: ?>
                                <span class="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium border border-red-200">
                                    <i class="fas fa-times-circle ml-1"></i>غير نشط
                                </span>
                            <?php endif; ?>
                                        
                                        <!-- Company Image/Logo -->
                                        <div class="h-40 bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
                                            <?php if (!empty($distributor['image'])): ?>
                                                <img src="<?php echo htmlspecialchars($distributor['image']); ?>" 
                                                     alt="<?php echo htmlspecialchars($distributor['name']); ?>"
                                                     class="h-full w-full object-cover">
                                            <?php elseif (!empty($distributor['logo'])): ?>
                                                <img src="<?php echo htmlspecialchars($distributor['logo']); ?>" 
                                                     alt="<?php echo htmlspecialchars($distributor['name']); ?>"
                                                     class="h-24 w-24 object-contain">
                                            <?php else: ?>
                                                <div class="h-20 w-20 bg-green-100 rounded-full flex items-center justify-center">
                                                    <i class="fas fa-building text-2xl text-green-600"></i>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    
                                    <div class="p-4">
                                        <!-- Company Name -->
                                        <h3 class="text-lg font-bold text-gray-900 mb-1 line-clamp-1">
                                            <?php echo htmlspecialchars($distributor['name']); ?>
                                        </h3>
                                        
                                        <!-- Type Badge -->
                                        <?php if (!empty($distributor['type'])): ?>
                                            <span class="inline-block bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs font-medium mb-2">
                                                <?php echo htmlspecialchars($distributor['type']); ?>
                                            </span>
                                        <?php endif; ?>
                                        
                                        <!-- Location -->
                        <div class="flex items-center text-sm text-gray-600 mb-2">
                            <i class="fas fa-map-marker-alt text-gray-400 ml-1"></i>
                            <span>
                                <?php 
                                $locationParts = [];
                                if (!empty($distributor['city'])) {
                                    $locationParts[] = $distributor['city'];
                                }
                                if (!empty($distributor['region']) && $distributor['region'] !== $distributor['city']) {
                                    $locationParts[] = $distributor['region'];
                                }
                                echo htmlspecialchars(implode(' - ', $locationParts));
                                ?>
                            </span>
                        </div>
                        
                        <!-- Status -->
                        <div class="flex items-center text-sm mb-2">
                            <i class="fas fa-info-circle text-gray-400 ml-1"></i>
                            <span class="<?php echo $distributor['is_active'] == 1 ? 'text-green-600 font-medium' : 'text-red-600 font-medium'; ?>">
                                <?php echo $distributor['is_active'] == 1 ? 'نشط ومتاح' : 'غير نشط'; ?>
                            </span>
                        </div>
                                        
                                        <!-- Description -->
                                        <?php if (!empty($distributor['description'])): ?>
                                            <p class="text-sm text-gray-600 mb-3 line-clamp-2">
                                                <?php echo htmlspecialchars($distributor['description']); ?>
                                            </p>
                                        <?php endif; ?>
                                        
                                        <!-- Services/Specializations -->
                                        <?php 
                                        $services = [];
                                        if (!empty($distributor['services'])) {
                                            $servicesData = json_decode($distributor['services'], true);
                                            if (json_last_error() === JSON_ERROR_NONE && is_array($servicesData)) {
                                                $services = $servicesData;
                                            } else {
                                                $services = array_map('trim', explode(',', $distributor['services']));
                                            }
                                        } elseif (!empty($distributor['specializations'])) {
                                            $services = array_map('trim', explode(',', $distributor['specializations']));
                                        }
                                        
                                        if (!empty($services)): 
                                        ?>
                                            <div class="mb-3">
                                                <h4 class="text-xs font-semibold text-gray-700 mb-1">الخدمات:</h4>
                                                <div class="flex flex-wrap gap-1">
                                                    <?php 
                                                    $displayServices = array_slice($services, 0, 3); // عرض أول 3 خدمات فقط
                                                    foreach ($displayServices as $service): 
                                                        if (!empty($service)): 
                                                    ?>
                                                        <span class="bg-green-50 text-green-700 px-2 py-1 rounded text-xs font-medium border border-green-200">
                                                            <?php echo htmlspecialchars($service); ?>
                                                        </span>
                                                    <?php 
                                                        endif;
                                                    endforeach;
                                                    if (count($services) > 3):
                                                    ?>
                                                        <span class="bg-gray-50 text-gray-600 px-2 py-1 rounded text-xs">
                                                            +<?php echo count($services) - 3; ?> أخرى
                                                        </span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <!-- Contact Info -->
                                        <div class="space-y-1 text-xs text-gray-600 mb-3">
                                            <?php if (!empty($distributor['phone'])): ?>
                                                <div class="flex items-center">
                                                    <i class="fas fa-phone text-gray-400 ml-1 w-3"></i>
                                                    <span class="truncate"><?php echo htmlspecialchars($distributor['phone']); ?></span>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <?php if (!empty($distributor['email'])): ?>
                                                <div class="flex items-center">
                                                    <i class="fas fa-envelope text-gray-400 ml-1 w-3"></i>
                                                    <span class="truncate"><?php echo htmlspecialchars($distributor['email']); ?></span>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <?php 
                                            // عرض ساعات العمل من JSON أو النص العادي
                                            $workingHours = '';
                                            if (!empty($distributor['working_hours'])) {
                                                $hoursData = json_decode($distributor['working_hours'], true);
                                                if (json_last_error() === JSON_ERROR_NONE && is_array($hoursData)) {
                                                    // إذا كانت البيانات JSON صالحة
                                                    if (isset($hoursData['from']) && isset($hoursData['to'])) {
                                                        $workingHours = $hoursData['from'] . ' - ' . $hoursData['to'];
                                                    }
                                                } else {
                                                    // إذا كانت نص عادي
                                                    $workingHours = $distributor['working_hours'];
                                                }
                                            }
                                            if (!empty($workingHours)): 
                                            ?>
                                                <div class="flex items-center">
                                                    <i class="fas fa-clock text-gray-400 ml-1 w-3"></i>
                                                    <span class="truncate"><?php echo htmlspecialchars($workingHours); ?></span>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <!-- Manager Info -->
                                        <?php if (!empty($distributor['manager'])): ?>
                                            <div class="flex items-center text-xs text-gray-600 mb-2">
                                                <i class="fas fa-user-tie text-gray-400 ml-1 w-3"></i>
                                                <span class="truncate">المدير: <?php echo htmlspecialchars($distributor['manager']); ?></span>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <!-- Specialties -->
                                        <?php 
                                        $specialties = [];
                                        if (!empty($distributor['specialties'])) {
                                            $specialtiesData = json_decode($distributor['specialties'], true);
                                            if (json_last_error() === JSON_ERROR_NONE && is_array($specialtiesData)) {
                                                $specialties = $specialtiesData;
                                            }
                                        }
                                        
                                        if (!empty($specialties)): 
                                        ?>
                                            <div class="mb-2">
                                                <h4 class="text-xs font-semibold text-gray-700 mb-1">التخصصات:</h4>
                                                <div class="flex flex-wrap gap-1">
                                                    <?php 
                                                    $displaySpecialties = array_slice($specialties, 0, 2); // عرض أول تخصصين فقط
                                                    foreach ($displaySpecialties as $specialty): 
                                                        if (!empty($specialty)): 
                                                    ?>
                                                        <span class="bg-purple-50 text-purple-700 px-2 py-1 rounded text-xs font-medium border border-purple-200">
                                                            <?php echo htmlspecialchars($specialty); ?>
                                                        </span>
                                                    <?php 
                                                        endif;
                                                    endforeach;
                                                    if (count($specialties) > 2):
                                                    ?>
                                                        <span class="bg-gray-50 text-gray-600 px-2 py-1 rounded text-xs">
                                                            +<?php echo count($specialties) - 2; ?> أخرى
                                                        </span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <!-- Coverage Areas -->
                                        <?php 
                                        $coverage = [];
                                        if (!empty($distributor['coverage'])) {
                                            $coverageData = json_decode($distributor['coverage'], true);
                                            if (json_last_error() === JSON_ERROR_NONE && is_array($coverageData)) {
                                                $coverage = $coverageData;
                                            }
                                        }
                                        
                                        if (!empty($coverage)): 
                                        ?>
                                            <div class="mb-3">
                                                <h4 class="text-xs font-semibold text-gray-700 mb-1">التغطية:</h4>
                                                <div class="flex flex-wrap gap-1">
                                                    <?php 
                                                    $displayCoverage = array_slice($coverage, 0, 3); // عرض أول 3 مناطق فقط
                                                    foreach ($displayCoverage as $area): 
                                                        if (!empty($area)): 
                                                    ?>
                                                        <span class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs font-medium border border-blue-200">
                                                            <?php echo htmlspecialchars($area); ?>
                                                        </span>
                                                    <?php 
                                                        endif;
                                                    endforeach;
                                                    if (count($coverage) > 3):
                                                    ?>
                                                        <span class="bg-gray-50 text-gray-600 px-2 py-1 rounded text-xs">
                                                            +<?php echo count($coverage) - 3; ?> أخرى
                                                        </span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <!-- Rating Section -->
                                        <div class="flex items-center justify-between mb-3">
                                            <div class="flex items-center">
                                                <?php 
                                                $rating = floatval($distributor['rating'] ?? 0);
                                                $reviews = intval($distributor['reviews'] ?? 0);
                                                ?>
                                                <div class="flex text-yellow-400">
                                                    <?php for($i = 1; $i <= 5; $i++): ?>
                                                        <?php if ($i <= floor($rating)): ?>
                                                            <i class="fas fa-star text-xs"></i>
                                                        <?php elseif ($i <= ceil($rating) && $rating > floor($rating)): ?>
                                                            <i class="fas fa-star-half-alt text-xs"></i>
                                                        <?php else: ?>
                                                            <i class="far fa-star text-xs"></i>
                                                        <?php endif; ?>
                                                    <?php endfor; ?>
                                                </div>
                                                <?php if ($rating > 0): ?>
                                                    <span class="text-xs text-gray-600 mr-1">
                                                        <?php echo number_format($rating, 1); ?> (<?php echo $reviews; ?> تقييم)
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-xs text-gray-500 mr-1">لا توجد تقييمات</span>
                                                <?php endif; ?>
                                            </div>
                                            <?php if (!empty($distributor['established'])): ?>
                                                <span class="text-xs text-gray-500">تأسيس: <?php echo htmlspecialchars($distributor['established']); ?></span>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <!-- Certification Badge -->
                                        <?php if (!empty($distributor['certified']) && $distributor['certified'] == 1): ?>
                                            <div class="mb-3">
                                                <span class="inline-flex items-center bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium border border-green-200">
                                                    <i class="fas fa-certificate ml-1"></i>معتمد
                                                </span>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <!-- Action Buttons -->
                                        <div class="flex gap-2">
                                            <?php if (!empty($distributor['whatsapp'])): ?>
                                                <a href="https://wa.me/<?php echo htmlspecialchars(str_replace(['+', ' ', '-'], '', $distributor['whatsapp'])); ?>" 
                                                   target="_blank" 
                                                   class="flex-1 bg-green-500 text-white text-center py-2 px-3 rounded text-xs font-medium hover:bg-green-600 transition-colors">
                                                    <i class="fab fa-whatsapp ml-1"></i>واتساب
                                                </a>
                                            <?php endif; ?>
                                            
                                            <?php if (!empty($distributor['phone'])): ?>
                                                <a href="tel:<?php echo htmlspecialchars($distributor['phone']); ?>" 
                                                   class="flex-1 bg-blue-500 text-white text-center py-2 px-3 rounded text-xs font-medium hover:bg-blue-600 transition-colors">
                                                    <i class="fas fa-phone ml-1"></i>اتصال
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="text-center py-16">
                    <div class="max-w-md mx-auto">
                        <i class="fas fa-search text-6xl text-gray-300 mb-4"></i>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">لا توجد موزعين</h3>
                        <p class="text-gray-600 mb-6">
                            <?php if ($searchActive): ?>
                                لم يتم العثور على موزعين يطابقون معايير البحث الخاصة بك.
                                <br><small class="text-gray-500">تم البحث عن: "<?php echo htmlspecialchars($search . ' ' . $regionFilter . ' ' . $distributorTypeFilter); ?>"</small>
                            <?php else: ?>
                                لا توجد موزعين متاحين حالياً.
                            <?php endif; ?>
                        </p>
                        <?php if ($searchActive): ?>
                            <a href="distributors.php" class="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                                <i class="fas fa-refresh ml-2"></i>عرض جميع الموزعين
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Map Section -->
    <?php if (!empty($distributors)): ?>
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">
                <i class="fas fa-map text-blue-600 ml-2"></i>
                خريطة الموزعين
            </h2>
            <div class="bg-white rounded-lg shadow-lg p-4">
                <div id="map" style="height: 500px; width: 100%;"></div>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Partnership Section -->
    <section class="bg-gradient-to-r from-green-600 to-green-800 text-white py-16">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-4">
                <?php echo htmlspecialchars($content['distributors_partnership_title'] ?? 'هل تريد أن تصبح موزعاً لمنتجاتنا؟'); ?>
            </h2>
            <p class="text-xl text-green-100 mb-8">
                <?php echo htmlspecialchars($content['distributors_partnership_subtitle'] ?? 'انضم إلى شبكة موزعينا واستفد من فرص النمو والربح'); ?>
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="#contact" class="bg-white text-green-600 px-8 py-3 rounded-md font-semibold hover:bg-green-50 transition-colors">
                    <?php echo htmlspecialchars($content['distributors_partnership_button1'] ?? 'طلب شراكة'); ?>
                </a>
                <a href="#contact" class="border-2 border-white text-white px-8 py-3 rounded-md font-semibold hover:bg-white hover:text-green-600 transition-colors">
                    <?php echo htmlspecialchars($content['distributors_partnership_button2'] ?? 'تواصل معنا'); ?>
                </a>
            </div>
        </div>
    </section>

    <!-- Google Maps API -->
    <script src="https://maps.googleapis.com/maps/api/js?libraries=places&language=ar"></script>
    
    <script>
        // بيانات الموزعين للخريطة
        const distributors = <?php echo json_encode(array_values($distributors)); ?>;
        
        // إعداد الخريطة
        let map;
        let markers = [];
        let infoWindow;
        
        function initMap() {
            // إعداد الخريطة الأساسية (المملكة العربية السعودية)
            map = new google.maps.Map(document.getElementById('map'), {
                zoom: 6,
                center: { lat: 24.7136, lng: 46.6753 }, // الرياض
                mapTypeId: 'roadmap'
            });
            
            infoWindow = new google.maps.InfoWindow();
            
            // إضافة العلامات للموزعين
            addMarkersToMap();
        }
        
        function addMarkersToMap() {
            // مسح العلامات السابقة
            markers.forEach(marker => marker.setMap(null));
            markers = [];
            
            distributors.forEach(distributor => {
                // استخدام إحداثيات افتراضية إذا لم تكن متوفرة
                let lat = parseFloat(distributor.latitude) || getDefaultLatLng(distributor.region).lat;
                let lng = parseFloat(distributor.longitude) || getDefaultLatLng(distributor.region).lng;
                
                const marker = new google.maps.Marker({
                    position: { lat: lat, lng: lng },
                    map: map,
                    title: distributor.name,
                    icon: {
                        url: getMarkerIcon(distributor.distributor_type),
                        scaledSize: new google.maps.Size(40, 40)
                    }
                });
                
                // محتوى نافذة المعلومات
                const infoContent = `
                    <div style="max-width: 300px; font-family: Arial, sans-serif; direction: rtl;">
                        <h3 style="margin: 0 0 10px 0; color: #1f2937; font-size: 16px; font-weight: bold;">
                            ${distributor.name}
                        </h3>
                        <div style="margin-bottom: 8px;">
                            <span style="background: ${getBadgeColor(distributor.distributor_type)}; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px;">
                                ${distributor.distributor_type}
                            </span>
                        </div>
                        <p style="margin: 5px 0; color: #6b7280; font-size: 14px;">
                            <i class="fas fa-map-marker-alt" style="margin-left: 5px;"></i>
                            ${distributor.region}${distributor.city ? ' - ' + distributor.city : ''}
                        </p>
                        ${distributor.phone ? `
                            <p style="margin: 5px 0; color: #6b7280; font-size: 14px;">
                                <i class="fas fa-phone" style="margin-left: 5px;"></i>
                                <a href="tel:${distributor.phone}" style="color: #3b82f6; text-decoration: none;">${distributor.phone}</a>
                            </p>
                        ` : ''}
                        ${distributor.whatsapp ? `
                            <div style="margin-top: 10px;">
                                <a href="https://wa.me/${distributor.whatsapp.replace(/[^0-9]/g, '')}" 
                                   target="_blank" 
                                   style="background: #25d366; color: white; padding: 8px 12px; border-radius: 6px; text-decoration: none; font-size: 12px; display: inline-block;">
                                    <i class="fab fa-whatsapp" style="margin-left: 5px;"></i>واتساب
                                </a>
                            </div>
                        ` : ''}
                    </div>
                `;
                
                marker.addListener('click', () => {
                    infoWindow.setContent(infoContent);
                    infoWindow.open(map, marker);
                });
                
                markers.push(marker);
            });
            
            // تعديل مستوى التكبير ليشمل جميع العلامات
            if (markers.length > 0) {
                const bounds = new google.maps.LatLngBounds();
                markers.forEach(marker => bounds.extend(marker.getPosition()));
                map.fitBounds(bounds);
                
                // تأكد من عدم التكبير أكثر من اللازم
                google.maps.event.addListenerOnce(map, 'bounds_changed', () => {
                    if (map.getZoom() > 15) {
                        map.setZoom(15);
                    }
                });
            }
        }
        
        function getDefaultLatLng(region) {
            const regionCoords = {
                'الرياض': { lat: 24.7136, lng: 46.6753 },
                'جدة': { lat: 21.4858, lng: 39.1925 },
                'الدمام': { lat: 26.4207, lng: 50.0888 },
                'مكة المكرمة': { lat: 21.3891, lng: 39.8579 },
                'المدينة المنورة': { lat: 24.5247, lng: 39.5692 },
                'الطائف': { lat: 21.2703, lng: 40.4034 },
                'تبوك': { lat: 28.3998, lng: 36.5700 },
                'بريدة': { lat: 26.3260, lng: 43.9750 },
                'خميس مشيط': { lat: 18.3059, lng: 42.7278 },
                'حائل': { lat: 27.5114, lng: 41.6900 }
            };
            return regionCoords[region] || { lat: 24.7136, lng: 46.6753 };
        }
        
        function getMarkerIcon(type) {
            const icons = {
                'رئيسي': 'https://maps.google.com/mapfiles/ms/icons/purple-dot.png',
                'مميز': 'https://maps.google.com/mapfiles/ms/icons/orange-dot.png',
                'عادي': 'https://maps.google.com/mapfiles/ms/icons/blue-dot.png'
            };
            return icons[type] || 'https://maps.google.com/mapfiles/ms/icons/red-dot.png';
        }
        
        function getBadgeColor(type) {
            const colors = {
                'رئيسي': '#8b5cf6',
                'مميز': '#f59e0b',
                'عادي': '#3b82f6'
            };
            return colors[type] || '#6b7280';
        }
        
        // تهيئة الخريطة عند تحميل الصفحة
        window.onload = function() {
            if (typeof google !== 'undefined' && google.maps) {
                initMap();
            }
        };
        
        // إدارة الفلاتر والبحث
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.querySelector('input[name="search"]');
            const regionSelect = document.querySelector('select[name="region"]');
            const typeSelect = document.querySelector('select[name="type"]');
            const resetButton = document.getElementById('resetFilters');
            const form = document.querySelector('form');
            
            // إعادة تعيين الفلاتر
            resetButton.addEventListener('click', function() {
                searchInput.value = '';
                regionSelect.value = '';
                typeSelect.value = '';
                
                // إرسال النموذج لإعادة تحميل الصفحة بدون فلاتر
                window.location.href = window.location.pathname;
            });
            
            // إعادة تعيين حقل البحث عند اختيار "جميع المناطق"
            regionSelect.addEventListener('change', function() {
                if (this.value === '') {
                    // إذا تم اختيار "جميع المناطق"، امسح جميع الفلاتر وأعد تحميل الصفحة
                    searchInput.value = '';
                    typeSelect.value = '';
                    window.location.href = window.location.pathname;
                }
            });
            
            // تم إزالة البحث التلقائي لتجنب التداخل مع عرض البيانات
            // يمكن للمستخدم الضغط على زر البحث أو Enter للبحث
        });
    </script>
</body>
</html>