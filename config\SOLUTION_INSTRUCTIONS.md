# حل مشكلة خطأ SQL 1064 في الإجراء المخزن GetTicketStats

## المشكلة
عند استيراد قاعدة البيانات، يظهر خطأ SQL رقم 1064 في تعريف الإجراء المخزن `GetTicketStats` بسبب ترتيب خاطئ للكلمات المفتاحية.

## الحل المقترح

### الطريقة الأولى: استخدام ملف الإصلاح

1. **تشغيل ملف الإصلاح**:
   ```sql
   source config/fix_procedure.sql;
   ```
   أو
   ```sql
   \. config/fix_procedure.sql
   ```

2. **أو تنفيذ الأوامر يدوياً**:
   ```sql
   DROP PROCEDURE IF EXISTS GetTicketStats;
   source config/support_procedures_fixed.sql;
   ```

### الطريقة الثانية: التنفيذ المباشر

1. **حذف الإجراء القديم**:
   ```sql
   DROP PROCEDURE IF EXISTS GetTicketStats;
   ```

2. **إنشاء الإجراء الجديد**:
   ```sql
   DELIMITER //
   
   CREATE PROCEDURE GetTicketStats()
   READS SQL DATA
   SQL SECURITY DEFINER
   BEGIN
       SELECT 
           COUNT(*) as total_tickets,
           SUM(CASE WHEN status = 'open' THEN 1 ELSE 0 END) as open_tickets,
           SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_tickets,
           SUM(CASE WHEN status = 'resolved' THEN 1 ELSE 0 END) as resolved_tickets,
           SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as closed_tickets,
           SUM(CASE WHEN priority = 'urgent' THEN 1 ELSE 0 END) as urgent_tickets,
           SUM(CASE WHEN priority = 'high' THEN 1 ELSE 0 END) as high_priority_tickets
       FROM support_tickets
       WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY);
   END //
   
   DELIMITER ;
   ```

## التحقق من نجاح الحل

1. **التحقق من وجود الإجراء**:
   ```sql
   SHOW PROCEDURE STATUS WHERE Name = 'GetTicketStats';
   ```

2. **اختبار تشغيل الإجراء**:
   ```sql
   CALL GetTicketStats();
   ```

## ملاحظات مهمة

- **الترتيب الصحيح**: `READS SQL DATA` يجب أن يأتي قبل `SQL SECURITY DEFINER`
- **التوافق**: هذا الحل متوافق مع جميع إصدارات MySQL و phpMyAdmin
- **الأمان**: `SQL SECURITY DEFINER` يضمن تشغيل الإجراء بصلاحيات منشئه

## الملفات المتاحة

- `fix_procedure.sql` - ملف الإصلاح السريع
- `support_procedures_fixed.sql` - ملف الإجراءات المحسنة
- `support_tables.sql` - الملف الأصلي المحدث

## استكشاف الأخطاء

إذا استمر الخطأ:
1. تأكد من وجود جدول `support_tickets`
2. تحقق من صلاحيات المستخدم
3. استخدم `SHOW ERRORS;` لمزيد من التفاصيل

---
**تاريخ الإنشاء**: ديسمبر 2024  
**الحالة**: جاهز للتطبيق