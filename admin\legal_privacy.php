<?php
require_once 'includes/layout.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !hasPermission('admin')) {
    redirect('/admin/login.php');
}

$pageTitle = 'إدارة سياسة الخصوصية';
$currentPage = 'legal';
$pageDescription = 'إدارة محتوى صفحة سياسة الخصوصية';
$breadcrumbs = [
    ['title' => 'الصفحات القانونية', 'url' => '#'],
    ['title' => 'سياسة الخصوصية']
];

require_once '../config/database.php';
$database = new Database();
$pdo = $database->getConnection();

$message = '';
$error = '';

// إنشاء الجدول إذا لم يكن موجوداً
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS legal_pages (
        id INT PRIMARY KEY AUTO_INCREMENT,
        page_type ENUM('privacy', 'terms', 'sitemap') NOT NULL,
        title VARCHAR(255) NOT NULL,
        content LONGTEXT,
        meta_description TEXT,
        meta_keywords TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_page_type (page_type)
    )");

    // إدراج البيانات الافتراضية لسياسة الخصوصية
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM legal_pages WHERE page_type = 'privacy'");
    $stmt->execute();
    if ($stmt->fetchColumn() == 0) {
        $default_content = '
        <div class="space-y-8">
            <section>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">مقدمة</h2>
                <p class="text-gray-600 leading-relaxed">
                    نحن في شركة الخط الأخضر نقدر خصوصيتك ونلتزم بحماية معلوماتك الشخصية. توضح سياسة الخصوصية هذه كيفية جمعنا واستخدامنا وحمايتنا لمعلوماتك الشخصية عند استخدام موقعنا الإلكتروني وخدماتنا.
                </p>
            </section>

            <section>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">المعلومات التي نجمعها</h2>
                <div class="space-y-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-700 mb-2">المعلومات الشخصية</h3>
                        <ul class="list-disc list-inside text-gray-600 space-y-1">
                            <li>الاسم الكامل</li>
                            <li>عنوان البريد الإلكتروني</li>
                            <li>رقم الهاتف</li>
                            <li>العنوان</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-700 mb-2">معلومات الاستخدام</h3>
                        <ul class="list-disc list-inside text-gray-600 space-y-1">
                            <li>عنوان IP</li>
                            <li>نوع المتصفح</li>
                            <li>صفحات الموقع التي تمت زيارتها</li>
                            <li>وقت ومدة الزيارة</li>
                        </ul>
                    </div>
                </div>
            </section>

            <section>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">كيفية استخدام المعلومات</h2>
                <ul class="list-disc list-inside text-gray-600 space-y-2">
                    <li>تقديم وتحسين خدماتنا</li>
                    <li>التواصل معك بخصوص طلباتك واستفساراتك</li>
                    <li>إرسال التحديثات والعروض الخاصة (بموافقتك)</li>
                    <li>تحليل استخدام الموقع لتحسين الأداء</li>
                    <li>الامتثال للمتطلبات القانونية</li>
                </ul>
            </section>

            <section>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">حماية المعلومات</h2>
                <p class="text-gray-600 leading-relaxed mb-4">
                    نتخذ تدابير أمنية مناسبة لحماية معلوماتك الشخصية من الوصول غير المصرح به أو التغيير أو الكشف أو التدمير.
                </p>
                <ul class="list-disc list-inside text-gray-600 space-y-2">
                    <li>تشفير البيانات الحساسة</li>
                    <li>استخدام بروتوكولات أمان متقدمة</li>
                    <li>تقييد الوصول للموظفين المخولين فقط</li>
                    <li>مراقبة النظام بانتظام</li>
                </ul>
            </section>

            <section>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">مشاركة المعلومات</h2>
                <p class="text-gray-600 leading-relaxed">
                    لا نبيع أو نؤجر أو نشارك معلوماتك الشخصية مع أطراف ثالثة إلا في الحالات التالية:
                </p>
                <ul class="list-disc list-inside text-gray-600 space-y-2 mt-4">
                    <li>بموافقتك الصريحة</li>
                    <li>لتقديم الخدمات المطلوبة</li>
                    <li>للامتثال للمتطلبات القانونية</li>
                    <li>لحماية حقوقنا وسلامة المستخدمين</li>
                </ul>
            </section>

            <section>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">حقوقك</h2>
                <p class="text-gray-600 leading-relaxed mb-4">لديك الحق في:</p>
                <ul class="list-disc list-inside text-gray-600 space-y-2">
                    <li>الوصول إلى معلوماتك الشخصية</li>
                    <li>تصحيح أو تحديث معلوماتك</li>
                    <li>حذف معلوماتك الشخصية</li>
                    <li>الاعتراض على معالجة معلوماتك</li>
                    <li>نقل معلوماتك إلى خدمة أخرى</li>
                </ul>
            </section>

            <section>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">ملفات تعريف الارتباط</h2>
                <p class="text-gray-600 leading-relaxed">
                    نستخدم ملفات تعريف الارتباط لتحسين تجربتك على موقعنا. يمكنك التحكم في إعدادات ملفات تعريف الارتباط من خلال متصفحك.
                </p>
            </section>

            <section>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">التحديثات</h2>
                <p class="text-gray-600 leading-relaxed">
                    قد نقوم بتحديث سياسة الخصوصية هذه من وقت لآخر. سنقوم بإشعارك بأي تغييرات مهمة عبر البريد الإلكتروني أو إشعار على موقعنا.
                </p>
            </section>

            <section>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">اتصل بنا</h2>
                <p class="text-gray-600 leading-relaxed">
                    إذا كان لديك أي أسئلة حول سياسة الخصوصية هذه، يرجى التواصل معنا:
                </p>
                <div class="mt-4 p-4 bg-gray-50 rounded-lg">
                    <p class="text-gray-600"><strong>البريد الإلكتروني:</strong> <EMAIL></p>
                    <p class="text-gray-600"><strong>الهاتف:</strong> +966 11 123 4567</p>
                    <p class="text-gray-600"><strong>العنوان:</strong> الرياض، المملكة العربية السعودية</p>
                </div>
            </section>
        </div>';

        $stmt = $pdo->prepare("INSERT INTO legal_pages (page_type, title, content, meta_description, meta_keywords) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([
            'privacy',
            'سياسة الخصوصية',
            $default_content,
            'سياسة الخصوصية لشركة الخط الأخضر - كيفية جمع واستخدام وحماية معلوماتك الشخصية',
            'سياسة الخصوصية, حماية البيانات, الخصوصية, الأمان, المعلومات الشخصية'
        ]);
    }

} catch (PDOException $e) {
    $error = "خطأ في قاعدة البيانات: " . $e->getMessage();
}

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_privacy') {
        try {
            $title = trim($_POST['title'] ?? '');
            $content = $_POST['content'] ?? '';
            $meta_description = trim($_POST['meta_description'] ?? '');
            $meta_keywords = trim($_POST['meta_keywords'] ?? '');
            $is_active = isset($_POST['is_active']) ? 1 : 0;

            if (empty($title)) {
                throw new Exception('عنوان الصفحة مطلوب');
            }

            $stmt = $pdo->prepare("
                UPDATE legal_pages SET 
                    title = ?, 
                    content = ?, 
                    meta_description = ?, 
                    meta_keywords = ?, 
                    is_active = ?
                WHERE page_type = 'privacy'
            ");
            
            if ($stmt->execute([$title, $content, $meta_description, $meta_keywords, $is_active])) {
                $message = 'تم تحديث سياسة الخصوصية بنجاح';
            } else {
                throw new Exception('فشل في تحديث البيانات');
            }

        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}

// جلب البيانات الحالية
$privacy_data = null;
try {
    $stmt = $pdo->prepare("SELECT * FROM legal_pages WHERE page_type = 'privacy'");
    $stmt->execute();
    $privacy_data = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = "خطأ في جلب البيانات: " . $e->getMessage();
}

startLayout($pageTitle, $currentPage, $pageDescription, $breadcrumbs);
showPageHeader();
?>

<div class="container mx-auto px-4 py-6">
    <!-- رسائل النجاح والخطأ -->
    <?php if ($message): ?>
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
            <?php echo htmlspecialchars($message); ?>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <?php echo htmlspecialchars($error); ?>
        </div>
    <?php endif; ?>

    <!-- نموذج تحرير سياسة الخصوصية -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center justify-between mb-6">
            <h1 class="text-2xl font-bold text-gray-800">إدارة سياسة الخصوصية</h1>
            <a href="/privacy" target="_blank" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
                <i class="fas fa-external-link-alt ml-2"></i>
                معاينة الصفحة
            </a>
        </div>

        <form method="POST" class="space-y-6">
            <input type="hidden" name="action" value="update_privacy">

            <!-- عنوان الصفحة -->
            <div>
                <label for="title" class="block text-sm font-medium text-gray-700 mb-2">عنوان الصفحة</label>
                <input type="text" id="title" name="title" 
                       value="<?php echo htmlspecialchars($privacy_data['title'] ?? ''); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                       required>
            </div>

            <!-- محتوى الصفحة -->
            <div>
                <label for="content" class="block text-sm font-medium text-gray-700 mb-2">محتوى الصفحة</label>
                <textarea id="content" name="content" rows="20"
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="محتوى سياسة الخصوصية..."><?php echo htmlspecialchars($privacy_data['content'] ?? ''); ?></textarea>
            </div>

            <!-- وصف الميتا -->
            <div>
                <label for="meta_description" class="block text-sm font-medium text-gray-700 mb-2">وصف الميتا (SEO)</label>
                <textarea id="meta_description" name="meta_description" rows="3"
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="وصف مختصر للصفحة لمحركات البحث..."><?php echo htmlspecialchars($privacy_data['meta_description'] ?? ''); ?></textarea>
            </div>

            <!-- كلمات مفتاحية -->
            <div>
                <label for="meta_keywords" class="block text-sm font-medium text-gray-700 mb-2">الكلمات المفتاحية (SEO)</label>
                <input type="text" id="meta_keywords" name="meta_keywords"
                       value="<?php echo htmlspecialchars($privacy_data['meta_keywords'] ?? ''); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                       placeholder="كلمات مفتاحية مفصولة بفواصل...">
            </div>

            <!-- حالة النشر -->
            <div class="flex items-center">
                <input type="checkbox" id="is_active" name="is_active" 
                       <?php echo ($privacy_data['is_active'] ?? 1) ? 'checked' : ''; ?>
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <label for="is_active" class="mr-2 block text-sm text-gray-700">
                    نشر الصفحة
                </label>
            </div>

            <!-- أزرار الحفظ -->
            <div class="flex justify-end space-x-4 space-x-reverse">
                <button type="button" onclick="history.back()" 
                        class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                    إلغاء
                </button>
                <button type="submit" 
                        class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    <i class="fas fa-save ml-2"></i>
                    حفظ التغييرات
                </button>
            </div>
        </form>
    </div>
</div>

<!-- محرر النصوص المتقدم -->
<script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    ClassicEditor
        .create(document.querySelector('#content'), {
            language: 'ar',
            toolbar: [
                'heading', '|',
                'bold', 'italic', 'underline', '|',
                'link', 'bulletedList', 'numberedList', '|',
                'outdent', 'indent', '|',
                'blockQuote', 'insertTable', '|',
                'undo', 'redo'
            ],
            heading: {
                options: [
                    { model: 'paragraph', title: 'فقرة', class: 'ck-heading_paragraph' },
                    { model: 'heading1', view: 'h1', title: 'عنوان 1', class: 'ck-heading_heading1' },
                    { model: 'heading2', view: 'h2', title: 'عنوان 2', class: 'ck-heading_heading2' },
                    { model: 'heading3', view: 'h3', title: 'عنوان 3', class: 'ck-heading_heading3' }
                ]
            }
        })
        .catch(error => {
            console.error(error);
        });
});
</script>

<?php endLayout(); ?>