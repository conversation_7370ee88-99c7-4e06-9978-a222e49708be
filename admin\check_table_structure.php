<?php
/**
 * Check current table structure
 */

require_once __DIR__ . '/../config/database.php';

try {
    echo "Checking current table structure...\n";
    
    // Check current columns
    $columns = $database->fetchAll("
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_schema = DATABASE() 
        AND table_name = 'contact_messages'
        ORDER BY ordinal_position
    ");
    
    echo "Current contact_messages table columns:\n";
    foreach ($columns as $column) {
        echo "- {$column['column_name']} ({$column['data_type']}) - " . 
             ($column['is_nullable'] === 'YES' ? 'NULL' : 'NOT NULL') . "\n";
    }
    
    // Check if reply column exists
    $replyExists = false;
    $repliedAtExists = false;
    
    foreach ($columns as $column) {
        if ($column['column_name'] === 'reply') {
            $replyExists = true;
        }
        if ($column['column_name'] === 'replied_at') {
            $repliedAtExists = true;
        }
    }
    
    echo "\nMissing columns:\n";
    if (!$replyExists) {
        echo "- reply column is missing\n";
    }
    if (!$repliedAtExists) {
        echo "- replied_at column is missing\n";
    }
    
    if (!$replyExists || !$repliedAtExists) {
        echo "\nAdding missing columns...\n";
        
        if (!$replyExists) {
            echo "Adding reply column...\n";
            $database->query("ALTER TABLE contact_messages ADD COLUMN reply TEXT NULL");
            echo "reply column added.\n";
        }
        
        if (!$repliedAtExists) {
            echo "Adding replied_at column...\n";
            $database->query("ALTER TABLE contact_messages ADD COLUMN replied_at TIMESTAMP NULL");
            echo "replied_at column added.\n";
        }
        
        echo "\nAll missing columns added successfully!\n";
    } else {
        echo "\nAll required columns exist.\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>