<?php
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !hasPermission('admin')) {
    redirect('/admin/login.php');
}

require_once '../config/database.php';

/**
 * صفحة إدارة خدمة ما بعد البيع
 * After Sales Service Management Page
 */

// تعريف متغيرات الصفحة
$pageTitle = 'خدمة ما بعد البيع';
$currentPage = 'after-sales';
$pageDescription = 'إدارة طرق التواصل والدعم الفني للعملاء';
$breadcrumbs = [
    ['title' => 'المحتوى الإضافي'],
    ['title' => 'خدمة ما بعد البيع']
];

// تضمين التخطيط
require_once 'includes/layout.php';

// بدء التخطيط
startLayout();

$message = '';
$error = '';
$success = '';

// معالجة طلبات AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        $database = new Database();
        $pdo = $database->getConnection();
        
        switch ($_POST['action']) {
            case 'update_contact_info':
                $section_key = $_POST['section_key'] ?? '';
                $title = $_POST['title'] ?? '';
                $content = $_POST['content'] ?? '';
                $data = $_POST['data'] ?? '{}';
                
                if (is_array($data)) {
                    $data = json_encode($data, JSON_UNESCAPED_UNICODE);
                }
                
                $stmt = $pdo->prepare("
                    INSERT INTO contact_info (section_key, title, content, data, updated_at) 
                    VALUES (?, ?, ?, ?, NOW()) 
                    ON DUPLICATE KEY UPDATE 
                    title = VALUES(title), 
                    content = VALUES(content), 
                    data = VALUES(data), 
                    updated_at = NOW()
                ");
                
                $stmt->execute([$section_key, $title, $content, $data]);
                
                echo json_encode(['success' => true, 'message' => 'تم حفظ البيانات بنجاح']);
                exit;
                
            case 'upload_file':
                if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
                    throw new Exception('خطأ في رفع الملف');
                }
                
                $file = $_FILES['file'];
                $title = $_POST['file_title'] ?? '';
                $description = $_POST['file_description'] ?? '';
                
                // التحقق من نوع الملف
                $allowedTypes = ['application/pdf'];
                $finfo = finfo_open(FILEINFO_MIME_TYPE);
                $mimeType = finfo_file($finfo, $file['tmp_name']);
                finfo_close($finfo);
                
                if (!in_array($mimeType, $allowedTypes)) {
                    throw new Exception('نوع الملف غير مدعوم. يُسمح فقط بملفات PDF');
                }
                
                // إنشاء مجلد التحميل إذا لم يكن موجوداً
                $uploadDir = '../uploads/support/';
                if (!is_dir($uploadDir)) {
                    mkdir($uploadDir, 0755, true);
                }
                
                // إنشاء اسم فريد للملف
                $fileName = uniqid() . '_' . preg_replace('/[^a-zA-Z0-9._-]/', '', $file['name']);
                $filePath = $uploadDir . $fileName;
                
                if (!move_uploaded_file($file['tmp_name'], $filePath)) {
                    throw new Exception('فشل في حفظ الملف');
                }
                
                // حفظ معلومات الملف في قاعدة البيانات
                $stmt = $pdo->prepare("
                    INSERT INTO support_files (title, description, file_name, file_path, file_size, created_at) 
                    VALUES (?, ?, ?, ?, ?, NOW())
                ");
                
                $stmt->execute([
                    $title,
                    $description,
                    $fileName,
                    'uploads/support/' . $fileName,
                    $file['size']
                ]);
                
                echo json_encode([
                    'success' => true, 
                    'message' => 'تم رفع الملف بنجاح',
                    'file_id' => $pdo->lastInsertId()
                ]);
                exit;
                
            case 'delete_file':
                $fileId = $_POST['file_id'] ?? 0;
                
                // جلب معلومات الملف
                $stmt = $pdo->prepare("SELECT file_path FROM support_files WHERE id = ?");
                $stmt->execute([$fileId]);
                $file = $stmt->fetch();
                
                if ($file) {
                    // حذف الملف من النظام
                    $fullPath = '../' . $file['file_path'];
                    if (file_exists($fullPath)) {
                        unlink($fullPath);
                    }
                    
                    // حذف السجل من قاعدة البيانات
                    $stmt = $pdo->prepare("DELETE FROM support_files WHERE id = ?");
                    $stmt->execute([$fileId]);
                }
                
                echo json_encode(['success' => true, 'message' => 'تم حذف الملف بنجاح']);
                exit;
        }
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        exit;
    }
}

// جلب البيانات
try {
    $database = new Database();
    $pdo = $database->getConnection();
    
    // جلب معلومات التواصل
    $stmt = $pdo->query("SELECT * FROM contact_info ORDER BY section_key");
    $contactInfo = [];
    while ($row = $stmt->fetch()) {
        $contactInfo[$row['section_key']] = $row;
    }
    
    // جلب أفضل 5 أسئلة شائعة
    $stmt = $pdo->query("
        SELECT f.*, 
               COALESCE(AVG(fr.rating), 0) as avg_rating,
               COUNT(fr.id) as rating_count
        FROM faqs f 
        LEFT JOIN faq_ratings fr ON f.id = fr.faq_id 
        WHERE f.is_active = 1 
        GROUP BY f.id 
        ORDER BY avg_rating DESC, rating_count DESC 
        LIMIT 5
    ");
    $topFaqs = $stmt->fetchAll();
    
    // جلب ملفات الدعم (مع معالجة عدم وجود الجدول)
    $supportFiles = [];
    try {
        $stmt = $pdo->query("SELECT * FROM support_files ORDER BY created_at DESC");
        $supportFiles = $stmt->fetchAll();
    } catch (Exception $e) {
        // الجدول غير موجود بعد - سيتم إنشاؤه لاحقاً
        $supportFiles = [];
    }
    
} catch (Exception $e) {
    $error = "خطأ في قاعدة البيانات: " . $e->getMessage();
    $contactInfo = [];
    $topFaqs = [];
    $supportFiles = [];
}

// دالة مساعدة لتنسيق حجم الملف
function formatFileSize($bytes) {
    if ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' ميجابايت';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' كيلوبايت';
    } else {
        return $bytes . ' بايت';
    }
}

// عرض رأس الصفحة والرسائل
showPageHeader();
showMessages($message, $error);

// تعريف التبويبات
$tabs = [
    'contact' => [
        'title' => 'طرق التواصل',
        'icon' => 'fas fa-phone',
        'content' => getContactMethodsTabContent($contactInfo)
    ],
    'faq' => [
        'title' => 'الأسئلة الشائعة',
        'icon' => 'fas fa-question-circle',
        'content' => getFaqTabContent($topFaqs)
    ],
    'hours' => [
        'title' => 'ساعات العمل',
        'icon' => 'fas fa-clock',
        'content' => getWorkingHoursTabContent($contactInfo)
    ],
    'files' => [
        'title' => 'مركز التحميل',
        'icon' => 'fas fa-download',
        'content' => getDownloadCenterTabContent($supportFiles)
    ],
    'support' => [
        'title' => 'طلبات الدعم',
        'icon' => 'fas fa-headset',
        'content' => getSupportRequestsTabContent()
    ]
];

// عرض التبويبات
createTabs($tabs, 'contact');

// دوال محتوى التبويبات
function getContactMethodsTabContent($contactInfo) {
    ob_start();
    ?>
    <div class="row">
        <div class="col-md-6">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-phone ml-1"></i>
                        الدعم الهاتفي
                    </h5>
                </div>
                <div class="card-body">
                    <form class="contact-info-form" data-section="phone">
                        <div class="mb-3">
                            <label class="form-label">العنوان</label>
                            <input type="text" class="form-control" name="title" 
                                   value="<?php echo htmlspecialchars($contactInfo['phone']['title'] ?? 'الدعم الهاتفي'); ?>">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">رقم الهاتف الرئيسي</label>
                            <input type="text" class="form-control" name="data[primary_phone]" 
                                   value="<?php echo htmlspecialchars(json_decode($contactInfo['phone']['data'] ?? '{}', true)['primary_phone'] ?? ''); ?>">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">رقم الهاتف الثانوي</label>
                            <input type="text" class="form-control" name="data[secondary_phone]" 
                                   value="<?php echo htmlspecialchars(json_decode($contactInfo['phone']['data'] ?? '{}', true)['secondary_phone'] ?? ''); ?>">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">وصف الخدمة</label>
                            <textarea class="form-control" name="content" rows="3"><?php echo htmlspecialchars($contactInfo['phone']['content'] ?? 'متاح 24/7'); ?></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save ml-1"></i>
                            حفظ التغييرات
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card border-secondary">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-envelope ml-1"></i>
                        البريد الإلكتروني
                    </h5>
                </div>
                <div class="card-body">
                    <form class="contact-info-form" data-section="email">
                        <div class="mb-3">
                            <label class="form-label">العنوان</label>
                            <input type="text" class="form-control" name="title" 
                                   value="<?php echo htmlspecialchars($contactInfo['email']['title'] ?? 'البريد الإلكتروني'); ?>">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">البريد الإلكتروني العام</label>
                            <input type="email" class="form-control" name="data[general_email]" 
                                   value="<?php echo htmlspecialchars(json_decode($contactInfo['email']['data'] ?? '{}', true)['general_email'] ?? ''); ?>">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">بريد الدعم الفني</label>
                            <input type="email" class="form-control" name="data[support_email]" 
                                   value="<?php echo htmlspecialchars(json_decode($contactInfo['email']['data'] ?? '{}', true)['support_email'] ?? ''); ?>">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">وصف الخدمة</label>
                            <textarea class="form-control" name="content" rows="3"><?php echo htmlspecialchars($contactInfo['email']['content'] ?? 'رد خلال 24 ساعة'); ?></textarea>
                        </div>
                        <button type="submit" class="btn btn-secondary">
                            <i class="fas fa-save ml-1"></i>
                            حفظ التغييرات
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-6 mt-3">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fab fa-whatsapp ml-1"></i>
                        واتساب
                    </h5>
                </div>
                <div class="card-body">
                    <form class="contact-info-form" data-section="whatsapp">
                        <div class="mb-3">
                            <label class="form-label">العنوان</label>
                            <input type="text" class="form-control" name="title" 
                                   value="<?php echo htmlspecialchars($contactInfo['whatsapp']['title'] ?? 'واتساب'); ?>">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">رقم الواتساب</label>
                            <input type="text" class="form-control" name="data[whatsapp_number]" 
                                   value="<?php echo htmlspecialchars(json_decode($contactInfo['whatsapp']['data'] ?? '{}', true)['whatsapp_number'] ?? ''); ?>"
                                   placeholder="966123456789">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">رسالة ترحيبية</label>
                            <textarea class="form-control" name="data[welcome_message]" rows="3"><?php echo htmlspecialchars(json_decode($contactInfo['whatsapp']['data'] ?? '{}', true)['welcome_message'] ?? 'مرحباً، كيف يمكننا مساعدتك؟'); ?></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">وصف الخدمة</label>
                            <textarea class="form-control" name="content" rows="2"><?php echo htmlspecialchars($contactInfo['whatsapp']['content'] ?? 'رد سريع'); ?></textarea>
                        </div>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save ml-1"></i>
                            حفظ التغييرات
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-6 mt-3">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-comments ml-1"></i>
                        المحادثة المباشرة
                    </h5>
                </div>
                <div class="card-body">
                    <form class="contact-info-form" data-section="live_chat">
                        <div class="mb-3">
                            <label class="form-label">العنوان</label>
                            <input type="text" class="form-control" name="title" 
                                   value="<?php echo htmlspecialchars($contactInfo['live_chat']['title'] ?? 'المحادثة المباشرة'); ?>">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">حالة الخدمة</label>
                            <select class="form-control" name="data[status]">
                                <option value="active" <?php echo (json_decode($contactInfo['live_chat']['data'] ?? '{}', true)['status'] ?? '') === 'active' ? 'selected' : ''; ?>>مفعلة</option>
                                <option value="inactive" <?php echo (json_decode($contactInfo['live_chat']['data'] ?? '{}', true)['status'] ?? '') === 'inactive' ? 'selected' : ''; ?>>معطلة</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ساعات العمل</label>
                            <input type="text" class="form-control" name="data[working_hours]" 
                                   value="<?php echo htmlspecialchars(json_decode($contactInfo['live_chat']['data'] ?? '{}', true)['working_hours'] ?? 'من 8 ص - 10 م'); ?>">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">وصف الخدمة</label>
                            <textarea class="form-control" name="content" rows="2"><?php echo htmlspecialchars($contactInfo['live_chat']['content'] ?? 'متاح من 8 ص - 10 م'); ?></textarea>
                        </div>
                        <button type="submit" class="btn btn-info">
                            <i class="fas fa-save ml-1"></i>
                            حفظ التغييرات
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <?php
    return ob_get_clean();
}

function getFaqTabContent($topFaqs) {
    ob_start();
    ?>
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h4>أفضل 5 أسئلة شائعة (حسب التقييم)</h4>
        <a href="faqs.php" class="btn btn-primary">
            <i class="fas fa-cog ml-1"></i>
            إدارة جميع الأسئلة الشائعة
        </a>
    </div>

    <?php if (empty($topFaqs)): ?>
        <div class="alert alert-info">
            <i class="fas fa-info-circle ml-1"></i>
            لا توجد أسئلة شائعة متاحة حالياً. 
            <a href="faqs.php" class="alert-link">إضافة أسئلة جديدة</a>
        </div>
    <?php else: ?>
        <div class="row">
            <?php foreach ($topFaqs as $index => $faq): ?>
                <div class="col-md-6 mb-3">
                    <div class="card border-left-primary">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <span class="badge bg-primary">#<?php echo $index + 1; ?></span>
                                <div class="text-end">
                                    <span class="text-warning">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <i class="fas fa-star<?php echo $i <= round($faq['avg_rating']) ? '' : '-o'; ?>"></i>
                                        <?php endfor; ?>
                                    </span>
                                    <small class="text-muted d-block">
                                        (<?php echo number_format($faq['avg_rating'], 1); ?> - <?php echo $faq['rating_count']; ?> تقييم)
                                    </small>
                                </div>
                            </div>
                            <h6 class="card-title"><?php echo htmlspecialchars($faq['question']); ?></h6>
                            <p class="card-text text-muted small">
                                <?php echo mb_substr(strip_tags($faq['answer']), 0, 100) . '...'; ?>
                            </p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="badge bg-secondary"><?php echo htmlspecialchars($faq['category']); ?></span>
                                <a href="faqs.php?edit=<?php echo $faq['id']; ?>" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-edit"></i>
                                    تعديل
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
    <?php
    return ob_get_clean();
}

function getWorkingHoursTabContent($contactInfo) {
    ob_start();
    ?>
    <div class="card border-success">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0">
                <i class="fas fa-clock ml-1"></i>
                إدارة ساعات العمل
            </h5>
        </div>
        <div class="card-body">
            <form class="contact-info-form" data-section="working_hours">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">العنوان</label>
                            <input type="text" class="form-control" name="title" 
                                   value="<?php echo htmlspecialchars($contactInfo['working_hours']['title'] ?? 'ساعات العمل'); ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">الأحد - الخميس</label>
                            <div class="row">
                                <div class="col-6">
                                    <input type="time" class="form-control" name="data[weekdays_start]" 
                                           value="<?php echo htmlspecialchars(json_decode($contactInfo['working_hours']['data'] ?? '{}', true)['weekdays_start'] ?? '08:00'); ?>">
                                </div>
                                <div class="col-6">
                                    <input type="time" class="form-control" name="data[weekdays_end]" 
                                           value="<?php echo htmlspecialchars(json_decode($contactInfo['working_hours']['data'] ?? '{}', true)['weekdays_end'] ?? '22:00'); ?>">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">الجمعة</label>
                            <div class="row">
                                <div class="col-6">
                                    <input type="time" class="form-control" name="data[friday_start]" 
                                           value="<?php echo htmlspecialchars(json_decode($contactInfo['working_hours']['data'] ?? '{}', true)['friday_start'] ?? '14:00'); ?>">
                                </div>
                                <div class="col-6">
                                    <input type="time" class="form-control" name="data[friday_end]" 
                                           value="<?php echo htmlspecialchars(json_decode($contactInfo['working_hours']['data'] ?? '{}', true)['friday_end'] ?? '22:00'); ?>">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">السبت</label>
                            <div class="row">
                                <div class="col-6">
                                    <input type="time" class="form-control" name="data[saturday_start]" 
                                           value="<?php echo htmlspecialchars(json_decode($contactInfo['working_hours']['data'] ?? '{}', true)['saturday_start'] ?? '09:00'); ?>">
                                </div>
                                <div class="col-6">
                                    <input type="time" class="form-control" name="data[saturday_end]" 
                                           value="<?php echo htmlspecialchars(json_decode($contactInfo['working_hours']['data'] ?? '{}', true)['saturday_end'] ?? '18:00'); ?>">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">ملاحظات إضافية</label>
                            <textarea class="form-control" name="content" rows="5"><?php echo htmlspecialchars($contactInfo['working_hours']['content'] ?? 'الدعم الطارئ: متاح 24/7 للمشاكل العاجلة'); ?></textarea>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">المنطقة الزمنية</label>
                            <select class="form-control" name="data[timezone]">
                                <option value="Asia/Riyadh" <?php echo (json_decode($contactInfo['working_hours']['data'] ?? '{}', true)['timezone'] ?? '') === 'Asia/Riyadh' ? 'selected' : ''; ?>>توقيت الرياض (GMT+3)</option>
                                <option value="Asia/Dubai" <?php echo (json_decode($contactInfo['working_hours']['data'] ?? '{}', true)['timezone'] ?? '') === 'Asia/Dubai' ? 'selected' : ''; ?>>توقيت دبي (GMT+4)</option>
                            </select>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle ml-1"></i>
                            <strong>معاينة:</strong>
                            <div class="mt-2 small">
                                <div>الأحد - الخميس: <span id="weekdays-preview">8:00 ص - 10:00 م</span></div>
                                <div>الجمعة: <span id="friday-preview">2:00 م - 10:00 م</span></div>
                                <div>السبت: <span id="saturday-preview">9:00 ص - 6:00 م</span></div>
                            </div>
                        </div>
                    </div>
                </div>

                <button type="submit" class="btn btn-success">
                    <i class="fas fa-save ml-1"></i>
                    حفظ ساعات العمل
                </button>
            </form>
        </div>
    </div>
    <?php
    return ob_get_clean();
}

function getDownloadCenterTabContent($supportFiles) {
    ob_start();
    ?>
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-file-pdf text-danger ml-1"></i>
                        الملفات المتاحة للتحميل
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($supportFiles)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد ملفات متاحة للتحميل حالياً</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>العنوان</th>
                                        <th>الوصف</th>
                                        <th>حجم الملف</th>
                                        <th>تاريخ الرفع</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($supportFiles as $file): ?>
                                        <tr>
                                            <td>
                                                <i class="fas fa-file-pdf text-danger ml-1"></i>
                                                <?php echo htmlspecialchars($file['title']); ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($file['description']); ?></td>
                                            <td><?php echo formatFileSize($file['file_size']); ?></td>
                                            <td><?php echo date('Y-m-d H:i', strtotime($file['created_at'])); ?></td>
                                            <td>
                                                <a href="<?php echo SITE_URL . '/' . $file['file_path']; ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-download"></i>
                                                </a>
                                                <button class="btn btn-sm btn-outline-danger delete-file-btn" data-file-id="<?php echo $file['id']; ?>">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-upload ml-1"></i>
                        رفع ملف جديد
                    </h5>
                </div>
                <div class="card-body">
                    <form id="uploadFileForm" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label class="form-label">عنوان الملف</label>
                            <input type="text" class="form-control" name="file_title" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">وصف الملف</label>
                            <textarea class="form-control" name="file_description" rows="3"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">اختر ملف PDF</label>
                            <input type="file" class="form-control" name="file" accept=".pdf" required>
                            <div class="form-text">يُسمح فقط بملفات PDF (الحد الأقصى: 10 ميجابايت)</div>
                        </div>
                        
                        <button type="submit" class="btn btn-warning w-100">
                            <i class="fas fa-upload ml-1"></i>
                            رفع الملف
                        </button>
                    </form>
                </div>
            </div>

            <div class="card mt-3 border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle ml-1"></i>
                        معلومات مهمة
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0 small">
                        <li><i class="fas fa-check text-success ml-1"></i> يُسمح فقط بملفات PDF</li>
                        <li><i class="fas fa-check text-success ml-1"></i> الحد الأقصى لحجم الملف: 10 ميجابايت</li>
                        <li><i class="fas fa-check text-success ml-1"></i> الملفات متاحة للتحميل العام</li>
                        <li><i class="fas fa-exclamation-triangle text-warning ml-1"></i> تأكد من عدم وجود معلومات سرية</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <?php
    return ob_get_clean();
}

function getSupportRequestsTabContent() {
    ob_start();
    ?>
    <div class="text-center py-5">
        <i class="fas fa-tools fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">قيد التطوير</h4>
        <p class="text-muted">سيتم إضافة إدارة طلبات الدعم قريباً</p>
        <div class="alert alert-info d-inline-block">
            <i class="fas fa-info-circle ml-1"></i>
            هذا القسم سيتضمن إدارة طلبات الدعم الواردة من العملاء
        </div>
    </div>
    <?php
    return ob_get_clean();
}
?>

<!-- رسائل التنبيه -->
<div id="alertContainer"></div>

<!-- JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // معالجة نماذج معلومات التواصل
    document.querySelectorAll('.contact-info-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            formData.append('action', 'update_contact_info');
            formData.append('section_key', this.dataset.section);
            
            // تحويل البيانات المتداخلة إلى JSON
            const dataFields = {};
            formData.forEach((value, key) => {
                if (key.startsWith('data[') && key.endsWith(']')) {
                    const fieldName = key.slice(5, -1);
                    dataFields[fieldName] = value;
                }
            });
            
            if (Object.keys(dataFields).length > 0) {
                formData.delete('data');
                formData.append('data', JSON.stringify(dataFields));
            }
            
            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                showAlert(data.success ? 'success' : 'danger', data.message);
            })
            .catch(error => {
                showAlert('danger', 'حدث خطأ أثناء حفظ البيانات');
                console.error('Error:', error);
            });
        });
    });

    // معالجة رفع الملفات
    const uploadForm = document.getElementById('uploadFileForm');
    if (uploadForm) {
        uploadForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            formData.append('action', 'upload_file');
            
            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                showAlert(data.success ? 'success' : 'danger', data.message);
                if (data.success) {
                    this.reset();
                    setTimeout(() => location.reload(), 1500);
                }
            })
            .catch(error => {
                showAlert('danger', 'حدث خطأ أثناء رفع الملف');
                console.error('Error:', error);
            });
        });
    }

    // معالجة حذف الملفات
    document.querySelectorAll('.delete-file-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            if (confirm('هل أنت متأكد من حذف هذا الملف؟')) {
                const formData = new FormData();
                formData.append('action', 'delete_file');
                formData.append('file_id', this.dataset.fileId);
                
                fetch('', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    showAlert(data.success ? 'success' : 'danger', data.message);
                    if (data.success) {
                        setTimeout(() => location.reload(), 1500);
                    }
                })
                .catch(error => {
                    showAlert('danger', 'حدث خطأ أثناء حذف الملف');
                    console.error('Error:', error);
                });
            }
        });
    });

    // تحديث معاينة ساعات العمل
    function updateWorkingHoursPreview() {
        const weekdaysStart = document.querySelector('input[name="data[weekdays_start]"]');
        const weekdaysEnd = document.querySelector('input[name="data[weekdays_end]"]');
        const fridayStart = document.querySelector('input[name="data[friday_start]"]');
        const fridayEnd = document.querySelector('input[name="data[friday_end]"]');
        const saturdayStart = document.querySelector('input[name="data[saturday_start]"]');
        const saturdayEnd = document.querySelector('input[name="data[saturday_end]"]');
        
        if (weekdaysStart && weekdaysEnd && fridayStart && fridayEnd && saturdayStart && saturdayEnd) {
            const weekdaysPreview = document.getElementById('weekdays-preview');
            const fridayPreview = document.getElementById('friday-preview');
            const saturdayPreview = document.getElementById('saturday-preview');
            
            if (weekdaysPreview) weekdaysPreview.textContent = formatTime(weekdaysStart.value) + ' - ' + formatTime(weekdaysEnd.value);
            if (fridayPreview) fridayPreview.textContent = formatTime(fridayStart.value) + ' - ' + formatTime(fridayEnd.value);
            if (saturdayPreview) saturdayPreview.textContent = formatTime(saturdayStart.value) + ' - ' + formatTime(saturdayEnd.value);
        }
    }

    function formatTime(time) {
        if (!time) return '';
        const [hours, minutes] = time.split(':');
        const hour = parseInt(hours);
        const ampm = hour >= 12 ? 'م' : 'ص';
        const displayHour = hour > 12 ? hour - 12 : (hour === 0 ? 12 : hour);
        return displayHour + ':' + minutes + ' ' + ampm;
    }

    // ربط أحداث تحديث المعاينة
    document.querySelectorAll('input[type="time"]').forEach(input => {
        input.addEventListener('change', updateWorkingHoursPreview);
    });

    // تحديث المعاينة عند تحميل الصفحة
    updateWorkingHoursPreview();
});

function showAlert(type, message) {
    const alertContainer = document.getElementById('alertContainer');
    if (alertContainer) {
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        alertContainer.appendChild(alert);
        
        setTimeout(() => {
            alert.remove();
        }, 5000);
    }
}
</script>

<?php
// إنهاء التخطيط
endLayout();
?>