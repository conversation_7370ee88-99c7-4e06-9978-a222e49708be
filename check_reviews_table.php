<?php
require_once 'config/config.php';

echo "<h2>فحص هيكل جدول reviews</h2>";

try {
    global $database;
    $columns = $database->fetchAll('DESCRIBE reviews');
    
    echo "<h3>الأعمدة الموجودة في جدول reviews:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // فحص البيانات الموجودة
    echo "<h3>عينة من البيانات الموجودة:</h3>";
    $reviews = $database->fetchAll('SELECT * FROM reviews ORDER BY created_at DESC LIMIT 5');
    
    if (!empty($reviews)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr>";
        foreach (array_keys($reviews[0]) as $key) {
            echo "<th>" . htmlspecialchars($key) . "</th>";
        }
        echo "</tr>";
        
        foreach ($reviews as $review) {
            echo "<tr>";
            foreach ($review as $value) {
                echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>لا توجد بيانات في الجدول</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
