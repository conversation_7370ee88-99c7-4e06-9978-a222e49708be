<?php
/**
 * نظام معالجة الأخطاء والتسجيل المحسن
 * Enhanced Error Handling and Logging System
 * 
 * <AUTHOR> Line Team
 * @version 2.0
 */

class ErrorHandler {
    private static $instance = null;
    private $logger;
    private $config;
    private $isProduction;
    
    private function __construct() {
        $this->config = ConfigManager::getInstance();
        $this->isProduction = $this->config->get('app.environment', 'production') === 'production';
        $this->logger = new EnhancedLogger();
        
        $this->setupErrorHandling();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * إعداد معالجة الأخطاء
     * Setup error handling
     */
    private function setupErrorHandling() {
        // تعيين معالج الأخطاء المخصص
        set_error_handler([$this, 'handleError']);
        set_exception_handler([$this, 'handleException']);
        register_shutdown_function([$this, 'handleShutdown']);
        
        // إعداد مستوى الإبلاغ عن الأخطاء
        if ($this->isProduction) {
            error_reporting(E_ERROR | E_WARNING | E_PARSE);
            ini_set('display_errors', 0);
            ini_set('log_errors', 1);
        } else {
            error_reporting(E_ALL);
            ini_set('display_errors', 1);
        }
    }
    
    /**
     * معالج الأخطاء المخصص
     * Custom error handler
     */
    public function handleError($severity, $message, $file, $line) {
        // تجاهل الأخطاء المكبوتة بـ @
        if (!(error_reporting() & $severity)) {
            return false;
        }
        
        $errorType = $this->getErrorType($severity);
        $context = [
            'file' => $file,
            'line' => $line,
            'severity' => $severity,
            'type' => $errorType,
            'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS)
        ];
        
        // تسجيل الخطأ
        $this->logger->error($message, $context);
        
        // عرض الخطأ في بيئة التطوير
        if (!$this->isProduction) {
            $this->displayError($errorType, $message, $file, $line);
        }
        
        // إيقاف التنفيذ للأخطاء الحرجة
        if (in_array($severity, [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_USER_ERROR])) {
            $this->handleFatalError($message, $file, $line);
        }
        
        return true;
    }
    
    /**
     * معالج الاستثناءات
     * Exception handler
     */
    public function handleException($exception) {
        $context = [
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'code' => $exception->getCode(),
            'trace' => $exception->getTraceAsString(),
            'previous' => $exception->getPrevious()
        ];
        
        // تسجيل الاستثناء
        $this->logger->error($exception->getMessage(), $context);
        
        // عرض صفحة خطأ مناسبة
        $this->displayExceptionPage($exception);
    }
    
    /**
     * معالج إيقاف التشغيل
     * Shutdown handler
     */
    public function handleShutdown() {
        $error = error_get_last();
        
        if ($error && in_array($error['type'], [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_PARSE])) {
            $this->handleFatalError($error['message'], $error['file'], $error['line']);
        }
    }
    
    /**
     * معالجة الأخطاء الحرجة
     * Handle fatal errors
     */
    private function handleFatalError($message, $file, $line) {
        $context = [
            'file' => $file,
            'line' => $line,
            'type' => 'FATAL_ERROR',
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true)
        ];
        
        $this->logger->critical($message, $context);
        
        // إرسال إشعار للمطورين
        $this->notifyDevelopers($message, $context);
        
        // عرض صفحة خطأ للمستخدم
        if ($this->isProduction) {
            $this->displayProductionErrorPage();
        } else {
            $this->displayDevelopmentErrorPage($message, $file, $line);
        }
        
        exit(1);
    }
    
    /**
     * عرض الخطأ في بيئة التطوير
     * Display error in development environment
     */
    private function displayError($type, $message, $file, $line) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px; border: 1px solid #f5c6cb; border-radius: 4px;'>";
        echo "<strong>$type:</strong> $message<br>";
        echo "<strong>File:</strong> $file<br>";
        echo "<strong>Line:</strong> $line";
        echo "</div>";
    }
    
    /**
     * عرض صفحة الاستثناء
     * Display exception page
     */
    private function displayExceptionPage($exception) {
        if ($this->isProduction) {
            $this->displayProductionErrorPage();
        } else {
            $this->displayDevelopmentExceptionPage($exception);
        }
    }
    
    /**
     * عرض صفحة خطأ الإنتاج
     * Display production error page
     */
    private function displayProductionErrorPage() {
        http_response_code(500);
        
        $errorPage = $this->config->get('app.error_page', __DIR__ . '/../views/errors/500.php');
        
        if (file_exists($errorPage)) {
            include $errorPage;
        } else {
            echo $this->getDefaultErrorPage();
        }
    }
    
    /**
     * عرض صفحة خطأ التطوير
     * Display development error page
     */
    private function displayDevelopmentErrorPage($message, $file, $line) {
        echo $this->getDevelopmentErrorTemplate($message, $file, $line);
    }
    
    /**
     * عرض صفحة استثناء التطوير
     * Display development exception page
     */
    private function displayDevelopmentExceptionPage($exception) {
        echo $this->getDevelopmentExceptionTemplate($exception);
    }
    
    /**
     * الحصول على نوع الخطأ
     * Get error type
     */
    private function getErrorType($severity) {
        $types = [
            E_ERROR => 'ERROR',
            E_WARNING => 'WARNING',
            E_PARSE => 'PARSE_ERROR',
            E_NOTICE => 'NOTICE',
            E_CORE_ERROR => 'CORE_ERROR',
            E_CORE_WARNING => 'CORE_WARNING',
            E_COMPILE_ERROR => 'COMPILE_ERROR',
            E_COMPILE_WARNING => 'COMPILE_WARNING',
            E_USER_ERROR => 'USER_ERROR',
            E_USER_WARNING => 'USER_WARNING',
            E_USER_NOTICE => 'USER_NOTICE',
            E_STRICT => 'STRICT',
            E_RECOVERABLE_ERROR => 'RECOVERABLE_ERROR',
            E_DEPRECATED => 'DEPRECATED',
            E_USER_DEPRECATED => 'USER_DEPRECATED'
        ];
        
        return $types[$severity] ?? 'UNKNOWN';
    }
    
    /**
     * إشعار المطورين
     * Notify developers
     */
    private function notifyDevelopers($message, $context) {
        $emails = $this->config->get('app.developer_emails', []);
        
        if (!empty($emails)) {
            $subject = 'خطأ حرج في ' . $this->config->get('app.name', 'التطبيق');
            $body = $this->formatErrorEmail($message, $context);
            
            foreach ($emails as $email) {
                mail($email, $subject, $body, 'Content-Type: text/html; charset=UTF-8');
            }
        }
    }
    
    /**
     * تنسيق بريد الخطأ
     * Format error email
     */
    private function formatErrorEmail($message, $context) {
        $html = "<h2>تفاصيل الخطأ</h2>";
        $html .= "<p><strong>الرسالة:</strong> " . htmlspecialchars($message) . "</p>";
        $html .= "<p><strong>الملف:</strong> " . htmlspecialchars($context['file']) . "</p>";
        $html .= "<p><strong>السطر:</strong> " . $context['line'] . "</p>";
        $html .= "<p><strong>الوقت:</strong> " . date('Y-m-d H:i:s') . "</p>";
        $html .= "<p><strong>الخادم:</strong> " . ($_SERVER['SERVER_NAME'] ?? 'غير محدد') . "</p>";
        $html .= "<p><strong>المسار:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'غير محدد') . "</p>";
        
        if (isset($context['memory_usage'])) {
            $html .= "<p><strong>استخدام الذاكرة:</strong> " . $this->formatBytes($context['memory_usage']) . "</p>";
            $html .= "<p><strong>ذروة الذاكرة:</strong> " . $this->formatBytes($context['peak_memory']) . "</p>";
        }
        
        return $html;
    }
    
    /**
     * تنسيق البايتات
     * Format bytes
     */
    private function formatBytes($bytes) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= (1 << (10 * $pow));
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
    
    /**
     * صفحة الخطأ الافتراضية
     * Default error page
     */
    private function getDefaultErrorPage() {
        return '<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خطأ في الخادم</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f5f5f5; }
        .error-container { max-width: 500px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .error-icon { font-size: 64px; color: #dc3545; margin-bottom: 20px; }
        h1 { color: #333; margin-bottom: 20px; }
        p { color: #666; line-height: 1.6; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">⚠️</div>
        <h1>عذراً، حدث خطأ</h1>
        <p>نعتذر عن هذا الإزعاج. حدث خطأ غير متوقع في الخادم.</p>
        <p>يرجى المحاولة مرة أخرى لاحقاً أو الاتصال بالدعم الفني إذا استمرت المشكلة.</p>
        <a href="/" class="btn">العودة للصفحة الرئيسية</a>
    </div>
</body>
</html>';
    }
    
    /**
     * قالب خطأ التطوير
     * Development error template
     */
    private function getDevelopmentErrorTemplate($message, $file, $line) {
        return '<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خطأ في التطوير</title>
    <style>
        body { font-family: monospace; margin: 0; padding: 20px; background: #1a1a1a; color: #fff; }
        .error-header { background: #dc3545; padding: 20px; margin-bottom: 20px; border-radius: 5px; }
        .error-details { background: #333; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .code-context { background: #222; padding: 15px; border-radius: 5px; overflow-x: auto; }
        .line-number { color: #666; margin-right: 10px; }
        .error-line { background: #dc3545; color: white; }
        pre { margin: 0; white-space: pre-wrap; }
    </style>
</head>
<body>
    <div class="error-header">
        <h1>خطأ حرج</h1>
        <p>' . htmlspecialchars($message) . '</p>
    </div>
    <div class="error-details">
        <p><strong>الملف:</strong> ' . htmlspecialchars($file) . '</p>
        <p><strong>السطر:</strong> ' . $line . '</p>
        <p><strong>الوقت:</strong> ' . date('Y-m-d H:i:s') . '</p>
    </div>
    <div class="code-context">
        <h3>سياق الكود:</h3>
        <pre>' . $this->getCodeContext($file, $line) . '</pre>
    </div>
</body>
</html>';
    }
    
    /**
     * قالب استثناء التطوير
     * Development exception template
     */
    private function getDevelopmentExceptionTemplate($exception) {
        return '<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استثناء غير معالج</title>
    <style>
        body { font-family: monospace; margin: 0; padding: 20px; background: #1a1a1a; color: #fff; }
        .exception-header { background: #dc3545; padding: 20px; margin-bottom: 20px; border-radius: 5px; }
        .exception-details { background: #333; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .stack-trace { background: #222; padding: 15px; border-radius: 5px; overflow-x: auto; }
        pre { margin: 0; white-space: pre-wrap; }
    </style>
</head>
<body>
    <div class="exception-header">
        <h1>استثناء غير معالج: ' . get_class($exception) . '</h1>
        <p>' . htmlspecialchars($exception->getMessage()) . '</p>
    </div>
    <div class="exception-details">
        <p><strong>الملف:</strong> ' . htmlspecialchars($exception->getFile()) . '</p>
        <p><strong>السطر:</strong> ' . $exception->getLine() . '</p>
        <p><strong>الكود:</strong> ' . $exception->getCode() . '</p>
    </div>
    <div class="stack-trace">
        <h3>تتبع المكدس:</h3>
        <pre>' . htmlspecialchars($exception->getTraceAsString()) . '</pre>
    </div>
</body>
</html>';
    }
    
    /**
     * الحصول على سياق الكود
     * Get code context
     */
    private function getCodeContext($file, $line, $context = 5) {
        if (!file_exists($file)) {
            return 'الملف غير موجود';
        }
        
        $lines = file($file);
        $start = max(0, $line - $context - 1);
        $end = min(count($lines), $line + $context);
        
        $output = '';
        for ($i = $start; $i < $end; $i++) {
            $lineNumber = $i + 1;
            $isErrorLine = $lineNumber == $line;
            $class = $isErrorLine ? 'error-line' : '';
            
            $output .= sprintf(
                '<span class="%s"><span class="line-number">%d:</span> %s</span>',
                $class,
                $lineNumber,
                htmlspecialchars($lines[$i])
            );
        }
        
        return $output;
    }
}

/**
 * نظام التسجيل المحسن
 * Enhanced Logging System
 */
class EnhancedLogger {
    private $logPath;
    private $maxFileSize;
    private $maxFiles;
    
    public function __construct() {
        $this->logPath = __DIR__ . '/../logs';
        $this->maxFileSize = 10 * 1024 * 1024; // 10MB
        $this->maxFiles = 10;
        
        $this->ensureLogDirectory();
    }
    
    /**
     * تسجيل رسالة معلومات
     * Log info message
     */
    public function info($message, $context = []) {
        $this->log('INFO', $message, $context);
    }
    
    /**
     * تسجيل رسالة تحذير
     * Log warning message
     */
    public function warning($message, $context = []) {
        $this->log('WARNING', $message, $context);
    }
    
    /**
     * تسجيل رسالة خطأ
     * Log error message
     */
    public function error($message, $context = []) {
        $this->log('ERROR', $message, $context);
    }
    
    /**
     * تسجيل رسالة حرجة
     * Log critical message
     */
    public function critical($message, $context = []) {
        $this->log('CRITICAL', $message, $context);
    }
    
    /**
     * تسجيل رسالة تصحيح
     * Log debug message
     */
    public function debug($message, $context = []) {
        $this->log('DEBUG', $message, $context);
    }
    
    /**
     * تسجيل الرسالة
     * Log message
     */
    private function log($level, $message, $context = []) {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => $level,
            'message' => $message,
            'context' => $context,
            'memory_usage' => memory_get_usage(true),
            'request_id' => $this->getRequestId()
        ];
        
        $logFile = $this->getLogFile($level);
        $this->writeToFile($logFile, $logEntry);
        
        // تدوير الملفات إذا لزم الأمر
        $this->rotateLogFile($logFile);
    }
    
    /**
     * الحصول على ملف السجل
     * Get log file
     */
    private function getLogFile($level) {
        $date = date('Y-m-d');
        return $this->logPath . '/' . strtolower($level) . '_' . $date . '.log';
    }
    
    /**
     * الكتابة في الملف
     * Write to file
     */
    private function writeToFile($file, $entry) {
        $line = json_encode($entry, JSON_UNESCAPED_UNICODE) . "\n";
        file_put_contents($file, $line, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * تدوير ملف السجل
     * Rotate log file
     */
    private function rotateLogFile($file) {
        if (!file_exists($file) || filesize($file) < $this->maxFileSize) {
            return;
        }
        
        // إنشاء نسخة مضغوطة
        $rotatedFile = $file . '.' . time() . '.gz';
        $gz = gzopen($rotatedFile, 'w9');
        gzwrite($gz, file_get_contents($file));
        gzclose($gz);
        
        // حذف الملف الأصلي
        unlink($file);
        
        // حذف الملفات القديمة
        $this->cleanOldLogs();
    }
    
    /**
     * تنظيف السجلات القديمة
     * Clean old logs
     */
    private function cleanOldLogs() {
        $files = glob($this->logPath . '/*.gz');
        
        if (count($files) > $this->maxFiles) {
            // ترتيب حسب وقت التعديل
            usort($files, function($a, $b) {
                return filemtime($a) - filemtime($b);
            });
            
            // حذف الملفات الزائدة
            $filesToDelete = array_slice($files, 0, count($files) - $this->maxFiles);
            foreach ($filesToDelete as $file) {
                unlink($file);
            }
        }
    }
    
    /**
     * التأكد من وجود مجلد السجلات
     * Ensure log directory exists
     */
    private function ensureLogDirectory() {
        if (!is_dir($this->logPath)) {
            mkdir($this->logPath, 0755, true);
        }
    }
    
    /**
     * الحصول على معرف الطلب
     * Get request ID
     */
    private function getRequestId() {
        static $requestId = null;
        
        if ($requestId === null) {
            $requestId = uniqid();
        }
        
        return $requestId;
    }
}

// تهيئة معالج الأخطاء
ErrorHandler::getInstance();
?>