<?php
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

$test_result = '';

if ($_POST && isset($_POST['test_api'])) {
    // إعداد البيانات للاختبار
    $postData = [
        'product_id' => $_POST['product_id'] ?? 1,
        'rating' => $_POST['rating'] ?? 5,
        'review_text' => $_POST['review_text'] ?? 'تقييم تجريبي من صفحة الاختبار المباشر'
    ];
    
    // إذا كان المستخدم مسجل دخول، لا نحتاج اسم وبريد
    if (!isLoggedIn()) {
        $postData['customer_name'] = $_POST['customer_name'] ?? 'مستخدم تجريبي';
        $postData['customer_email'] = $_POST['customer_email'] ?? '<EMAIL>';
    }
    
    // إرسال طلب POST إلى API
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, SITE_URL . '/api/review.php');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/x-www-form-urlencoded',
        'Cookie: ' . $_SERVER['HTTP_COOKIE'] ?? ''
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    $test_result = [
        'http_code' => $httpCode,
        'response' => $response,
        'post_data' => $postData
    ];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API التقييمات مباشرة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار API التقييمات مباشرة</h1>
        
        <!-- معلومات المستخدم الحالي -->
        <div class="card">
            <h2>حالة المستخدم الحالي</h2>
            <?php if (isLoggedIn()): ?>
                <?php $user = getCurrentUser(); ?>
                <div style="background: #d1ecf1; padding: 15px; border-radius: 4px;">
                    <p><strong>مسجل الدخول:</strong> نعم</p>
                    <p><strong>الاسم:</strong> <?php echo $user['name']; ?></p>
                    <p><strong>البريد:</strong> <?php echo $user['email']; ?></p>
                    <p><strong>المعرف:</strong> <?php echo $user['id']; ?></p>
                </div>
            <?php else: ?>
                <div style="background: #fff3cd; padding: 15px; border-radius: 4px;">
                    <p><strong>مسجل الدخول:</strong> لا</p>
                    <p>سيتم اختبار API كزائر</p>
                    <a href="test_login_simple.php" style="background: #007bff; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px;">تسجيل الدخول</a>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- نموذج الاختبار -->
        <div class="card">
            <h2>اختبار إرسال تقييم</h2>
            <form method="POST">
                <input type="hidden" name="test_api" value="1">
                
                <div class="form-group">
                    <label>معرف المنتج:</label>
                    <input type="number" name="product_id" value="1" min="1" max="15">
                </div>
                
                <div class="form-group">
                    <label>التقييم:</label>
                    <select name="rating">
                        <option value="5">5 نجوم</option>
                        <option value="4">4 نجوم</option>
                        <option value="3">3 نجوم</option>
                        <option value="2">نجمتان</option>
                        <option value="1">نجمة واحدة</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>نص التقييم:</label>
                    <textarea name="review_text" rows="3">تقييم تجريبي من صفحة الاختبار المباشر - <?php echo date('Y-m-d H:i:s'); ?></textarea>
                </div>
                
                <?php if (!isLoggedIn()): ?>
                <div class="form-group">
                    <label>اسم العميل:</label>
                    <input type="text" name="customer_name" value="مستخدم تجريبي">
                </div>
                
                <div class="form-group">
                    <label>البريد الإلكتروني:</label>
                    <input type="email" name="customer_email" value="test<?php echo rand(1000, 9999); ?>@example.com">
                </div>
                <?php endif; ?>
                
                <button type="submit">اختبار API</button>
            </form>
        </div>
        
        <!-- نتائج الاختبار -->
        <?php if ($test_result): ?>
        <div class="card">
            <h2>نتائج الاختبار</h2>
            
            <div class="result <?php echo $test_result['http_code'] == 200 ? 'success' : 'error'; ?>">
                <p><strong>HTTP Code:</strong> <?php echo $test_result['http_code']; ?></p>
                
                <h3>البيانات المرسلة:</h3>
                <pre><?php echo json_encode($test_result['post_data'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE); ?></pre>
                
                <h3>استجابة API:</h3>
                <pre><?php echo htmlspecialchars($test_result['response']); ?></pre>
                
                <?php 
                $responseData = json_decode($test_result['response'], true);
                if ($responseData): 
                ?>
                <h3>البيانات المفسرة:</h3>
                <pre><?php echo json_encode($responseData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE); ?></pre>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- روابط مفيدة -->
        <div class="card">
            <h2>روابط مفيدة</h2>
            <a href="test_user_review_fix.php" style="background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">صفحة الاختبار الأصلية</a>
            <a href="products/1" style="background: #17a2b8; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">صفحة المنتج</a>
            <a href="test_main_page_auth.php" style="background: #6f42c1; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">اختبار المصادقة</a>
            <a href="admin/reviews.php" style="background: #fd7e14; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">إدارة التقييمات</a>
        </div>
    </div>
</body>
</html>