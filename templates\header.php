<?php
// الحصول على الإعدادات
$settings = getSettings();
$siteName = getSetting('site_name', 'Green Line');
$primaryColor = getSetting('primary_color', '#059669');
$secondaryColor = getSetting('secondary_color', '#10b981');
$fontFamily = getSetting('font_family', 'Noto Kufi Arabic');
$fontWeight = getSetting('font_weight', '400');
$fontSize = getSetting('font_size', '16');
$fontUrl = getSetting('font_url', 'https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@100;200;300;400;500;600;700;800;900&display=swap');
$logo = getSetting('site_logo', '/assets/images/logo.png');
$currentUser = getCurrentUser();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' . $siteName : $siteName; ?></title>
    
    <!-- Meta Tags -->
    <meta name="description" content="<?php echo getSetting('site_description', 'شركة رائدة في مجال المكيفات وأنظمة التبريد'); ?>">
    <meta name="keywords" content="<?php echo getSetting('seo_keywords', 'مكيفات، تكييف، تبريد، صيانة مكيفات، تركيب مكيفات، السعودية'); ?>">
    <meta name="author" content="<?php echo $siteName; ?>">
    
    <!-- Open Graph -->
    <meta property="og:title" content="<?php echo isset($pageTitle) ? $pageTitle . ' - ' . $siteName : $siteName; ?>">
    <meta property="og:description" content="<?php echo getSetting('site_description', 'شركة رائدة في مجال المكيفات وأنظمة التبريد'); ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo SITE_URL . $_SERVER['REQUEST_URI']; ?>">
    <meta property="og:image" content="<?php echo SITE_URL . $logo; ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo getSetting('favicon', '/assets/images/favicon.ico'); ?>">
    
    <!-- Fonts -->
    <link href="<?php echo $fontUrl; ?>" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer">
    <!-- Fallback for Font Awesome -->
    <script>
        // التحقق من تحميل Font Awesome
        document.addEventListener('DOMContentLoaded', function() {
            // إنشاء عنصر اختبار
            var testElement = document.createElement('i');
            testElement.className = 'fas fa-home';
            testElement.style.position = 'absolute';
            testElement.style.left = '-9999px';
            document.body.appendChild(testElement);
            
            // التحقق من تحميل الخط
            setTimeout(function() {
                var computedStyle = window.getComputedStyle(testElement, ':before');
                var content = computedStyle.getPropertyValue('content');
                
                // إذا لم يتم تحميل Font Awesome، استخدم نسخة احتياطية
                if (!content || content === 'none' || content === '""') {
                    console.warn('Font Awesome لم يتم تحميله، جاري تحميل نسخة احتياطية...');
                    var fallbackLink = document.createElement('link');
                    fallbackLink.rel = 'stylesheet';
                    fallbackLink.href = 'https://use.fontawesome.com/releases/v6.4.0/css/all.css';
                    fallbackLink.crossOrigin = 'anonymous';
                    document.head.appendChild(fallbackLink);
                    
                    // إذا فشلت النسخة الاحتياطية أيضاً، استخدم رموز Unicode
                    setTimeout(function() {
                        var testElement2 = document.createElement('i');
                        testElement2.className = 'fas fa-home';
                        testElement2.style.position = 'absolute';
                        testElement2.style.left = '-9999px';
                        document.body.appendChild(testElement2);
                        
                        setTimeout(function() {
                            var computedStyle2 = window.getComputedStyle(testElement2, ':before');
                            var content2 = computedStyle2.getPropertyValue('content');
                            
                            if (!content2 || content2 === 'none' || content2 === '""') {
                                console.warn('جميع مصادر Font Awesome فشلت، جاري استخدام رموز Unicode...');
                                // إضافة CSS للرموز البديلة
                                var style = document.createElement('style');
                                style.textContent = `
                                    .fas.fa-home:before { content: "🏠"; }
                                    .fas.fa-user:before { content: "👤"; }
                                    .fas.fa-envelope:before { content: "✉️"; }
                                    .fas.fa-phone:before { content: "📞"; }
                                    .fas.fa-map-marker-alt:before { content: "📍"; }
                                    .fas.fa-star:before { content: "⭐"; }
                                    .fas.fa-heart:before { content: "❤️"; }
                                    .fas.fa-check:before { content: "✅"; }
                                    .fas.fa-times:before { content: "❌"; }
                                    .fas.fa-search:before { content: "🔍"; }
                                    .fas.fa-edit:before { content: "✏️"; }
                                    .fas.fa-trash:before { content: "🗑️"; }
                                    .fas.fa-save:before { content: "💾"; }
                                    .fas.fa-plus:before { content: "➕"; }
                                    .fas.fa-minus:before { content: "➖"; }
                                    .fas.fa-arrow-left:before { content: "⬅️"; }
                                    .fas.fa-arrow-right:before { content: "➡️"; }
                                    .fas.fa-chevron-down:before { content: "⬇️"; }
                                    .fas.fa-chevron-up:before { content: "⬆️"; }
                                    .fas.fa-cog:before { content: "⚙️"; }
                                    .fas.fa-users:before { content: "👥"; }
                                    .fas.fa-building:before { content: "🏢"; }
                                    .fas.fa-handshake:before { content: "🤝"; }
                                    .fas.fa-lightbulb:before { content: "💡"; }
                                    .fas.fa-award:before { content: "🏆"; }
                                    .fas.fa-shield-alt:before { content: "🛡️"; }
                                    .fas.fa-rocket:before { content: "🚀"; }
                                    .fas.fa-gem:before { content: "💎"; }
                                    .fas.fa-clock:before { content: "⏰"; }
                                    .fas.fa-calendar:before { content: "📅"; }
                                    .fas.fa-file:before { content: "📄"; }
                                    .fas.fa-folder:before { content: "📁"; }
                                    .fas.fa-download:before { content: "⬇️"; }
                                    .fas.fa-upload:before { content: "⬆️"; }
                                    .fab.fa-facebook-f:before { content: "📘"; }
                                    .fab.fa-twitter:before { content: "🐦"; }
                                    .fab.fa-instagram:before { content: "📷"; }
                                    .fab.fa-linkedin-in:before { content: "💼"; }
                                    .fab.fa-youtube:before { content: "📺"; }
                                    .fab.fa-whatsapp:before { content: "💬"; }
                                `;
                                document.head.appendChild(style);
                            }
                            document.body.removeChild(testElement2);
                        }, 1000);
                    }, 2000);
                }
                document.body.removeChild(testElement);
            }, 1000);
        });
    </script>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS Variables -->
    <style>
        :root {
            --primary-color: <?php echo $primaryColor; ?>;
            --secondary-color: <?php echo $secondaryColor; ?>;
            --accent-color: <?php echo getSetting('accent_color', '#34d399'); ?>;
            --font-family: '<?php echo $fontFamily; ?>', sans-serif;
            --font-weight: <?php echo $fontWeight; ?>;
            --font-size: <?php echo $fontSize; ?>px;
        }
        
        body {
            font-family: var(--font-family);
            font-weight: var(--font-weight);
            font-size: var(--font-size);
        }
        
        /* تطبيق إعدادات الخط على جميع العناصر */
        * {
            font-family: var(--font-family);
        }
        
        /* أحجام الخطوط النسبية */
        h1 { font-size: calc(var(--font-size) * 2.25); font-weight: var(--font-weight); }
        h2 { font-size: calc(var(--font-size) * 1.875); font-weight: var(--font-weight); }
        h3 { font-size: calc(var(--font-size) * 1.5); font-weight: var(--font-weight); }
        h4 { font-size: calc(var(--font-size) * 1.25); font-weight: var(--font-weight); }
        h5 { font-size: calc(var(--font-size) * 1.125); font-weight: var(--font-weight); }
        h6 { font-size: var(--font-size); font-weight: var(--font-weight); }
        
        p, div, span, a, button, input, textarea, select, label {
            font-size: var(--font-size);
            font-weight: var(--font-weight);
        }
        
        .bg-primary { background-color: var(--primary-color); }
        .bg-secondary { background-color: var(--secondary-color); }
        .bg-accent { background-color: var(--accent-color); }
        .text-primary { color: var(--primary-color); }
        .text-secondary { color: var(--secondary-color); }
        .text-accent { color: var(--accent-color); }
        .border-primary { border-color: var(--primary-color); }
        .border-secondary { border-color: var(--secondary-color); }
        
        /* Custom gradient backgrounds */
        .bg-gradient-to-br.from-primary.to-secondary {
            background: linear-gradient(to bottom right, var(--primary-color), var(--secondary-color));
        }
        .bg-gradient-to-r.from-primary.to-secondary {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
        }
        
        /* Custom animations */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .slide-in {
            animation: slideIn 0.3s ease-out;
        }
        
        @keyframes slideIn {
            from { transform: translateX(-100%); }
            to { transform: translateX(0); }
        }
        
        /* Navbar sticky */
        .navbar-sticky {
            backdrop-filter: blur(10px);
            background-color: rgba(255, 255, 255, 0.95);
        }
        
        /* Mobile menu */
        .mobile-menu {
            transform: translateX(100%);
            transition: transform 0.3s ease-in-out;
        }
        
        .mobile-menu.active {
            transform: translateX(0);
        }
    </style>
    
    <!-- Additional CSS -->
    <?php if (isset($additionalCSS)): ?>
        <?php echo $additionalCSS; ?>
    <?php endif; ?>
</head>
<body class="bg-gray-50 text-gray-900">
    
    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50 navbar-sticky">
        <div class="w-full px-4">
            <div class="flex justify-between items-center h-16">
                
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="<?php echo SITE_URL; ?>" class="flex items-center">
                        <?php if (!empty($logo) && $logo !== '/assets/images/logo.png'): ?>
                            <img src="<?php echo SITE_URL . '/' . $logo; ?>" alt="<?php echo $siteName; ?>" class="h-10 w-auto ml-3">
                            <span class="text-xl font-bold text-primary mr-2"><?php echo $siteName; ?></span>
                        <?php else: ?>
                            <span class="text-xl font-bold text-primary"><?php echo $siteName; ?></span>
                        <?php endif; ?>
                    </a>
                </div>
                
                <!-- Desktop Menu -->
                <div class="hidden md:block">
                    <div class="mr-10 flex items-baseline space-x-4 space-x-reverse">
                        <a href="<?php echo SITE_URL; ?>" class="nav-link px-3 py-2 rounded-md text-sm font-medium hover:text-primary transition-colors <?php echo ($page ?? '') === 'home' ? 'text-primary' : 'text-black'; ?>">
                            الرئيسية
                        </a>
                        <a href="<?php echo SITE_URL; ?>/about" class="nav-link px-3 py-2 rounded-md text-sm font-medium hover:text-primary transition-colors <?php echo ($page ?? '') === 'about' ? 'text-primary' : 'text-black'; ?>">
                            عن الشركة
                        </a>
                        <a href="<?php echo SITE_URL; ?>/products" class="nav-link px-3 py-2 rounded-md text-sm font-medium hover:text-primary transition-colors <?php echo ($page ?? '') === 'products' ? 'text-primary' : 'text-black'; ?>">
                            المنتجات
                        </a>
                        <a href="<?php echo SITE_URL; ?>/device-features" class="nav-link px-3 py-2 rounded-md text-sm font-medium hover:text-primary transition-colors <?php echo ($page ?? '') === 'device-features' ? 'text-primary' : 'text-black'; ?>">
                            مميزات الجهاز
                        </a>
                        <a href="<?php echo SITE_URL; ?>/distributors" class="nav-link px-3 py-2 rounded-md text-sm font-medium hover:text-primary transition-colors <?php echo ($page ?? '') === 'distributors' ? 'text-primary' : 'text-black'; ?>">
                            الموزعين
                        </a>
                        <a href="<?php echo SITE_URL; ?>/support" class="nav-link px-3 py-2 rounded-md text-sm font-medium hover:text-primary transition-colors <?php echo ($page ?? '') === 'support' ? 'text-primary' : 'text-black'; ?>">
                            خدمة ما بعد البيع
                        </a>
                        <a href="<?php echo SITE_URL; ?>/faq" class="nav-link px-3 py-2 rounded-md text-sm font-medium hover:text-primary transition-colors <?php echo ($page ?? '') === 'faq' ? 'text-primary' : 'text-black'; ?>">
                            الأسئلة الشائعة
                        </a>
                        <a href="<?php echo SITE_URL; ?>/contact" class="nav-link px-3 py-2 rounded-md text-sm font-medium hover:text-primary transition-colors <?php echo ($page ?? '') === 'contact' ? 'text-primary' : 'text-black'; ?>">
                            تواصل معنا
                        </a>
                    </div>
                </div>
                
                <!-- User Menu -->
                <div class="hidden md:block">
                    <div class="mr-4 flex items-center md:mr-6">
                        <?php if ($currentUser): ?>
                            <!-- Logged in user menu -->
                            <div class="relative group">
                                <button class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                    <span class="ml-2 text-gray-700"><?php echo htmlspecialchars($currentUser['name']); ?></span>
                                    <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>
                                
                                <!-- Dropdown menu -->
                                <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                                    <a href="<?php echo SITE_URL; ?>/profile" class="block px-4 py-2 text-sm text-black hover:text-primary hover:bg-gray-100">الملف الشخصي</a>
                                    <?php if ($currentUser && ($currentUser['role'] === 'admin' || $currentUser['role'] === 'super-admin' || hasPermission('admin'))): ?>
                                        <a href="<?php echo ADMIN_URL; ?>" class="block px-4 py-2 text-sm text-black hover:text-primary hover:bg-gray-100">لوحة الإدارة</a>
                                    <?php endif; ?>
                                    <a href="<?php echo SITE_URL; ?>/auth/logout" class="block px-4 py-2 text-sm text-black hover:text-primary hover:bg-gray-100">تسجيل الخروج</a>
                                </div>
                            </div>
                        <?php else: ?>
                            <!-- Guest user buttons -->
                            <a href="<?php echo SITE_URL; ?>/auth/login" class="ml-3 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-black bg-white border-primary hover:bg-primary hover:text-white active:bg-primary active:text-white focus:bg-primary focus:text-white transition-colors">
                                تسجيل الدخول
                            </a>
                            <a href="<?php echo SITE_URL; ?>/auth/register" class="mr-3 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-secondary transition-colors">
                                إنشاء حساب
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-button" class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary">
                        <svg class="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile menu -->
        <div id="mobile-menu" class="md:hidden mobile-menu fixed top-0 right-0 h-full w-64 bg-white shadow-lg z-50">
            <div class="p-4">
                <div class="flex justify-between items-center mb-6">
                    <span class="text-lg font-bold text-primary"><?php echo $siteName; ?></span>
                    <button id="mobile-menu-close" class="text-gray-500 hover:text-gray-700">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                
                <nav class="space-y-2">
                    <a href="<?php echo SITE_URL; ?>" class="block px-3 py-2 rounded-md text-base font-medium <?php echo ($page ?? '') === 'home' ? 'text-primary' : 'text-black'; ?> hover:text-primary hover:bg-gray-50">الرئيسية</a>
                    <a href="<?php echo SITE_URL; ?>/about" class="block px-3 py-2 rounded-md text-base font-medium <?php echo ($page ?? '') === 'about' ? 'text-primary' : 'text-black'; ?> hover:text-primary hover:bg-gray-50">عن الشركة</a>
                    <a href="<?php echo SITE_URL; ?>/products" class="block px-3 py-2 rounded-md text-base font-medium <?php echo ($page ?? '') === 'products' ? 'text-primary' : 'text-black'; ?> hover:text-primary hover:bg-gray-50">المنتجات</a>
                    <a href="<?php echo SITE_URL; ?>/device-features" class="block px-3 py-2 rounded-md text-base font-medium <?php echo ($page ?? '') === 'device-features' ? 'text-primary' : 'text-black'; ?> hover:text-primary hover:bg-gray-50">مميزات الجهاز</a>
                    <a href="<?php echo SITE_URL; ?>/distributors" class="block px-3 py-2 rounded-md text-base font-medium <?php echo ($page ?? '') === 'distributors' ? 'text-primary' : 'text-black'; ?> hover:text-primary hover:bg-gray-50">الموزعين</a>
                    <a href="<?php echo SITE_URL; ?>/support" class="block px-3 py-2 rounded-md text-base font-medium <?php echo ($page ?? '') === 'support' ? 'text-primary' : 'text-black'; ?> hover:text-primary hover:bg-gray-50">خدمة ما بعد البيع</a>
                    <a href="<?php echo SITE_URL; ?>/faq" class="block px-3 py-2 rounded-md text-base font-medium <?php echo ($page ?? '') === 'faq' ? 'text-primary' : 'text-black'; ?> hover:text-primary hover:bg-gray-50">الأسئلة الشائعة</a>
                    <a href="<?php echo SITE_URL; ?>/contact" class="block px-3 py-2 rounded-md text-base font-medium <?php echo ($page ?? '') === 'contact' ? 'text-primary' : 'text-black'; ?> hover:text-primary hover:bg-gray-50">تواصل معنا</a>
                    
                    <?php if ($currentUser): ?>
                        <hr class="my-4">
                        <a href="<?php echo SITE_URL; ?>/profile" class="block px-3 py-2 rounded-md text-base font-medium text-black hover:text-primary hover:bg-gray-50">الملف الشخصي</a>
                        <?php if ($currentUser && ($currentUser['role'] === 'admin' || $currentUser['role'] === 'super-admin' || hasPermission('admin'))): ?>
                            <a href="<?php echo ADMIN_URL; ?>" class="block px-3 py-2 rounded-md text-base font-medium text-black hover:text-primary hover:bg-gray-50">لوحة الإدارة</a>
                        <?php endif; ?>
                        <a href="<?php echo SITE_URL; ?>/auth/logout" class="block px-3 py-2 rounded-md text-base font-medium text-black hover:text-primary hover:bg-gray-50">تسجيل الخروج</a>
                    <?php else: ?>
                        <hr class="my-4">
                        <a href="<?php echo SITE_URL; ?>/auth/login" class="block px-3 py-2 rounded-md text-base font-medium text-white bg-primary hover:bg-secondary">تسجيل الدخول</a>
                        <a href="<?php echo SITE_URL; ?>/auth/register" class="block px-3 py-2 rounded-md text-base font-medium text-primary border border-primary hover:bg-primary hover:text-white">إنشاء حساب</a>
                    <?php endif; ?>
                </nav>
            </div>
        </div>
    </nav>
    
    <!-- Mobile menu overlay -->
    <div id="mobile-menu-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden"></div>
    
    <!-- Main Content -->
    <main class="min-h-screen">
        
        <!-- Flash Messages -->
        <?php if (isset($_SESSION['success'])): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mx-4 mt-4 fade-in" role="alert">
                <span class="block sm:inline"><?php echo $_SESSION['success']; unset($_SESSION['success']); ?></span>
            </div>
        <?php endif; ?>
        
        <?php if (isset($_SESSION['error'])): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mx-4 mt-4 fade-in" role="alert">
                <span class="block sm:inline"><?php echo $_SESSION['error']; unset($_SESSION['error']); ?></span>
            </div>
        <?php endif; ?>
        
        <?php if (isset($_SESSION['warning'])): ?>
            <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative mx-4 mt-4 fade-in" role="alert">
                <span class="block sm:inline"><?php echo $_SESSION['warning']; unset($_SESSION['warning']); ?></span>
            </div>
        <?php endif; ?>
        
        <?php if (isset($_SESSION['info'])): ?>
            <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded relative mx-4 mt-4 fade-in" role="alert">
                <span class="block sm:inline"><?php echo $_SESSION['info']; unset($_SESSION['info']); ?></span>
            </div>
        <?php endif; ?>

<script>
// Mobile menu functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    const mobileMenuClose = document.getElementById('mobile-menu-close');
    const mobileMenuOverlay = document.getElementById('mobile-menu-overlay');
    
    function openMobileMenu() {
        mobileMenu.classList.add('active');
        mobileMenuOverlay.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    }
    
    function closeMobileMenu() {
        mobileMenu.classList.remove('active');
        mobileMenuOverlay.classList.add('hidden');
        document.body.style.overflow = 'auto';
    }
    
    mobileMenuButton.addEventListener('click', openMobileMenu);
    mobileMenuClose.addEventListener('click', closeMobileMenu);
    mobileMenuOverlay.addEventListener('click', closeMobileMenu);
    
    // Auto-hide flash messages
    setTimeout(function() {
        const alerts = document.querySelectorAll('[role="alert"]');
        alerts.forEach(function(alert) {
            alert.style.opacity = '0';
            setTimeout(function() {
                alert.remove();
            }, 300);
        });
    }, 5000);
});
</script>