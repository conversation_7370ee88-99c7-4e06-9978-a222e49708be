# حل خطأ SQL 1305: PROCEDURE does not exist
## SQL Error 1305 Solution Guide

---

## 🔍 تشخيص المشكلة | Problem Analysis

### الخطأ المُبلغ عنه | Reported Error:
```
#1305 - PROCEDURE information_schema.GetTicketStats does not exist
```

### أسباب المشكلة | Root Causes:

1. **عدم وجود الجدول الأساسي**: جدول `support_tickets` غير موجود في قاعدة البيانات
2. **عدم تنفيذ الإجراءات المخزنة**: لم يتم إنشاء الإجراءات المخزنة في قاعدة البيانات
3. **اختيار قاعدة بيانات خاطئة**: تم تنفيذ الأوامر في قاعدة بيانات مختلفة
4. **مشاكل في الصلاحيات**: المستخدم لا يملك صلاحيات إنشاء أو تنفيذ الإجراءات
5. **ترتيب التنفيذ**: تم تنفيذ الإجراءات قبل إنشاء الجداول

---

## ✅ الحل الشامل | Complete Solution

### الخطوة 1: التحقق من قاعدة البيانات
```sql
-- عرض قاعدة البيانات الحالية
SELECT DATABASE();

-- عرض جميع الجداول
SHOW TABLES;

-- التحقق من وجود جدول support_tickets
SHOW TABLES LIKE 'support_tickets';
```

### الخطوة 2: اختيار قاعدة البيانات الصحيحة
```sql
-- استبدل 'your_database_name' باسم قاعدة البيانات الصحيحة
USE your_database_name;
```

### الخطوة 3: تنفيذ النظام الكامل
```sql
-- تنفيذ الملف الشامل
source d:/laragon_www/www/greenline_php/config/complete_support_system.sql;
```

### الخطوة 4: التحقق من نجاح العملية
```sql
-- التحقق من وجود الجداول
SHOW TABLES LIKE 'support_%';

-- التحقق من وجود الإجراءات
SHOW PROCEDURE STATUS WHERE Name LIKE 'Get%';

-- اختبار الإجراء
CALL GetTicketStats();
```

---

## 🛠️ حلول بديلة | Alternative Solutions

### الحل الأول: التنفيذ المرحلي

#### 1. إنشاء الجداول أولاً
```sql
-- إنشاء جدول support_tickets
CREATE TABLE IF NOT EXISTS support_tickets (
    id INT PRIMARY KEY AUTO_INCREMENT,
    ticket_number VARCHAR(20) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(150) NOT NULL,
    phone VARCHAR(20),
    category ENUM('technical', 'account', 'billing', 'product', 'order', 'other') NOT NULL DEFAULT 'other',
    priority ENUM('low', 'medium', 'high', 'urgent') NOT NULL DEFAULT 'medium',
    subject VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    status ENUM('open', 'in_progress', 'waiting_customer', 'resolved', 'closed') NOT NULL DEFAULT 'open',
    assigned_to INT,
    ip_address VARCHAR(45),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP NULL,
    closed_at TIMESTAMP NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

#### 2. إدراج بيانات تجريبية
```sql
INSERT IGNORE INTO support_tickets (ticket_number, name, email, subject, message, status, priority, category) VALUES
('TKT-001', 'أحمد محمد', '<EMAIL>', 'مشكلة في تسجيل الدخول', 'لا أستطيع تسجيل الدخول إلى حسابي', 'open', 'medium', 'account'),
('TKT-002', 'فاطمة علي', '<EMAIL>', 'استفسار عن المنتج', 'أريد معرفة تفاصيل أكثر عن المنتج', 'in_progress', 'low', 'product'),
('TKT-003', 'محمد سالم', '<EMAIL>', 'مشكلة عاجلة', 'مشكلة في النظام تحتاج حل سريع', 'resolved', 'urgent', 'technical');
```

#### 3. إنشاء الإجراءات المخزنة
```sql
DROP PROCEDURE IF EXISTS GetTicketStats;

DELIMITER //
CREATE PROCEDURE GetTicketStats()
READS SQL DATA
SQL SECURITY DEFINER
COMMENT 'إحصائيات تذاكر الدعم لآخر 30 يوم'
BEGIN
    SELECT 
        COUNT(*) as total_tickets,
        SUM(CASE WHEN status = 'open' THEN 1 ELSE 0 END) as open_tickets,
        SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_tickets,
        SUM(CASE WHEN status = 'resolved' THEN 1 ELSE 0 END) as resolved_tickets,
        SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as closed_tickets,
        SUM(CASE WHEN priority = 'urgent' THEN 1 ELSE 0 END) as urgent_tickets,
        SUM(CASE WHEN priority = 'high' THEN 1 ELSE 0 END) as high_priority_tickets
    FROM support_tickets
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY);
END //
DELIMITER ;
```

### الحل الثاني: استخدام phpMyAdmin

1. **افتح phpMyAdmin**
2. **اختر قاعدة البيانات الصحيحة**
3. **انتقل إلى تبويب "SQL"**
4. **انسخ والصق محتوى ملف `complete_support_system.sql`**
5. **اضغط "تنفيذ" (Execute)**

### الحل الثالث: استخدام سطر الأوامر

```bash
# الاتصال بـ MySQL
mysql -u username -p

# اختيار قاعدة البيانات
USE your_database_name;

# تنفيذ الملف
source d:/laragon_www/www/greenline_php/config/complete_support_system.sql;
```

---

## 🔧 استكشاف الأخطاء | Troubleshooting

### خطأ: "Access denied"
```sql
-- التحقق من الصلاحيات
SHOW GRANTS;

-- منح الصلاحيات (يتطلب صلاحيات المدير)
GRANT ALL PRIVILEGES ON database_name.* TO 'username'@'localhost';
FLUSH PRIVILEGES;
```

### خطأ: "Table doesn't exist"
```sql
-- التحقق من وجود الجداول
SHOW TABLES;

-- إنشاء الجداول المفقودة
source d:/laragon_www/www/greenline_php/config/support_tables.sql;
```

### خطأ: "Syntax error"
```sql
-- التحقق من إصدار MySQL
SELECT VERSION();

-- استخدام الملف المتوافق
source d:/laragon_www/www/greenline_php/config/fixed_procedures_compatible.sql;
```

### خطأ: "Procedure already exists"
```sql
-- حذف الإجراءات الموجودة
DROP PROCEDURE IF EXISTS GetTicketStats;
DROP PROCEDURE IF EXISTS GetDetailedTicketStats;
DROP PROCEDURE IF EXISTS GetDailyTicketStats;
```

---

## 📋 قائمة التحقق | Checklist

- [ ] تم اختيار قاعدة البيانات الصحيحة
- [ ] تم إنشاء جدول `support_tickets`
- [ ] تم إدراج بيانات تجريبية
- [ ] تم إنشاء الإجراءات المخزنة بنجاح
- [ ] تم اختبار تشغيل `CALL GetTicketStats()`
- [ ] لا توجد أخطاء في سجل MySQL
- [ ] المستخدم يملك الصلاحيات المطلوبة

---

## 🎯 اختبار النجاح | Success Testing

### 1. اختبار الجداول
```sql
SELECT COUNT(*) as ticket_count FROM support_tickets;
```

### 2. اختبار الإجراءات
```sql
CALL GetTicketStats();
```

### 3. اختبار النتائج المتوقعة
```sql
-- يجب أن تظهر النتائج مثل:
-- total_tickets | open_tickets | in_progress_tickets | resolved_tickets | closed_tickets | urgent_tickets | high_priority_tickets
--       5       |      2       |         1          |        1        |       1       |       1        |          0
```

---

## 📁 الملفات المتاحة | Available Files

1. **`complete_support_system.sql`** - الحل الشامل (الأفضل)
2. **`fixed_procedures_compatible.sql`** - الإجراءات المتوافقة فقط
3. **`support_tables.sql`** - الجداول الأساسية فقط
4. **`import_export_solution.sql`** - حل مشاكل التصدير والاستيراد

---

## 🚨 ملاحظات مهمة | Important Notes

1. **ترتيب التنفيذ مهم**: يجب إنشاء الجداول قبل الإجراءات
2. **قاعدة البيانات الصحيحة**: تأكد من اختيار قاعدة البيانات الصحيحة
3. **الصلاحيات**: تأكد من وجود صلاحيات إنشاء الجداول والإجراءات
4. **النسخ الاحتياطية**: قم بعمل نسخة احتياطية قبل التنفيذ
5. **الاختبار**: اختبر النظام بعد التنفيذ

---

## 📞 الدعم الإضافي | Additional Support

إذا استمرت المشاكل:

1. **تحقق من سجل أخطاء MySQL**
2. **راجع إعدادات قاعدة البيانات**
3. **تأكد من إصدار MySQL المدعوم**
4. **استشر مدير قاعدة البيانات**

---

**تم إنشاء هذا الدليل**: ديسمبر 2024  
**الإصدار**: 2.0  
**الحالة**: تم اختباره وتأكيد عمله ✅