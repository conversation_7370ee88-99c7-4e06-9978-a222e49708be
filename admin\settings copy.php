<?php
/**
 * صفحة إدارة الإعدادات
 * Settings Management Page
 */

require_once 'includes/layout.php';

$currentPage = 'settings';
$pageTitle = 'إدارة الإعدادات';
$pageDescription = 'إدارة إعدادات الموقع والنظام العامة';
$breadcrumbs = [
    ['title' => 'الإعدادات']
];
$message = '';
$messageType = '';

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // حفظ الإعدادات العامة
        if (isset($_POST['general_settings'])) {
            setSetting('site_name', $_POST['site_name'], 'string');
            setSetting('site_description', $_POST['site_description'], 'string');
            setSetting('contact_phone', $_POST['contact_phone'], 'string');
            setSetting('contact_email', $_POST['contact_email'], 'string');
            setSetting('whatsapp_number', $_POST['whatsapp_number'], 'string');
            setSetting('address', $_POST['address'], 'string');
            
            // معالجة رفع الشعار
            if (isset($_FILES['site_logo']) && $_FILES['site_logo']['error'] === UPLOAD_ERR_OK) {
                $uploadDir = '../uploads/';
                if (!is_dir($uploadDir)) {
                    mkdir($uploadDir, 0755, true);
                }
                
                $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
                $fileType = $_FILES['site_logo']['type'];
                
                if (in_array($fileType, $allowedTypes)) {
                    $fileExtension = pathinfo($_FILES['site_logo']['name'], PATHINFO_EXTENSION);
                    $fileName = 'logo_' . time() . '.' . $fileExtension;
                    $uploadPath = $uploadDir . $fileName;
                    
                    if (move_uploaded_file($_FILES['site_logo']['tmp_name'], $uploadPath)) {
                        // حذف الشعار القديم إذا كان موجوداً
                        $oldLogo = getSetting('site_logo');
                        if ($oldLogo && file_exists('../' . $oldLogo)) {
                            unlink('../' . $oldLogo);
                        }
                        
                        setSetting('site_logo', 'uploads/' . $fileName, 'string');
                    } else {
                        $message = 'حدث خطأ أثناء رفع الشعار';
                        $messageType = 'error';
                    }
                } else {
                    $message = 'نوع الملف غير مدعوم. يرجى رفع صورة بصيغة JPG, PNG, GIF, WebP أو SVG';
                    $messageType = 'error';
                }
            }
            
            if (empty($message)) {
                $message = 'تم حفظ الإعدادات العامة بنجاح';
                $messageType = 'success';
            }
        }
        
        // حفظ إعدادات التصميم
        if (isset($_POST['design_settings'])) {
            setSetting('primary_color', $_POST['primary_color'], 'string');
            setSetting('secondary_color', $_POST['secondary_color'], 'string');
            setSetting('accent_color', $_POST['accent_color'], 'string');
            setSetting('font_family', $_POST['font_family'], 'string');
            setSetting('font_url', $_POST['font_url'], 'string');
            
            $message = 'تم حفظ إعدادات التصميم بنجاح';
            $messageType = 'success';
        }
        
        // حفظ إعدادات البريد الإلكتروني
        if (isset($_POST['email_settings'])) {
            setSetting('smtp_host', $_POST['smtp_host'], 'string');
            setSetting('smtp_port', $_POST['smtp_port'], 'number');
            setSetting('smtp_username', $_POST['smtp_username'], 'string');
            setSetting('smtp_password', $_POST['smtp_password'], 'string');
            setSetting('smtp_from_email', $_POST['smtp_from_email'], 'string');
            setSetting('smtp_from_name', $_POST['smtp_from_name'], 'string');
            setSetting('smtp_encryption', $_POST['smtp_encryption'], 'string');
            
            $message = 'تم حفظ إعدادات البريد الإلكتروني بنجاح';
            $messageType = 'success';
        }
        
        // حفظ إعدادات الأمان
        if (isset($_POST['security_settings'])) {
            setSetting('enable_2fa', isset($_POST['enable_2fa']), 'boolean');
            setSetting('session_timeout', $_POST['session_timeout'], 'number');
            setSetting('max_login_attempts', $_POST['max_login_attempts'], 'number');
            setSetting('password_min_length', $_POST['password_min_length'], 'number');
            setSetting('require_strong_password', isset($_POST['require_strong_password']), 'boolean');
            
            $message = 'تم حفظ إعدادات الأمان بنجاح';
            $messageType = 'success';
        }
        
        // حفظ إعدادات النظام
        if (isset($_POST['system_settings'])) {
            setSetting('maintenance_mode', isset($_POST['maintenance_mode']), 'boolean');
            setSetting('debug_mode', isset($_POST['debug_mode']), 'boolean');
            setSetting('cache_enabled', isset($_POST['cache_enabled']), 'boolean');
            setSetting('backup_frequency', $_POST['backup_frequency'], 'string');
            setSetting('timezone', $_POST['timezone'], 'string');
            setSetting('language', $_POST['language'], 'string');
            
            $message = 'تم حفظ إعدادات النظام بنجاح';
            $messageType = 'success';
        }
        
    } catch (Exception $e) {
        $message = 'حدث خطأ أثناء حفظ الإعدادات: ' . $e->getMessage();
        $messageType = 'error';
    }
}

// الحصول على التبويب النشط
$activeTab = $_GET['tab'] ?? 'general';

// الحصول على جميع الإعدادات
$settings = getSettings();

startLayout();
showPageHeader();
showMessages();

// عرض رسائل التنبيه المخصصة
if ($message): ?>
<div class="mb-6">
    <div class="<?php echo $messageType === 'success' ? 'bg-green-50 border-green-200 text-green-800' : 'bg-red-50 border-red-200 text-red-800'; ?> border rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <?php if ($messageType === 'success'): ?>
                <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                <?php else: ?>
                <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                </svg>
                <?php endif; ?>
            </div>
            <div class="mr-3">
                <p class="text-sm font-medium"><?php echo htmlspecialchars($message); ?></p>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

                <!-- التبويبات -->
                <div class="bg-white rounded-lg shadow">
                    <div class="border-b border-gray-200">
                        <nav class="-mb-px flex space-x-8 space-x-reverse px-6" aria-label="Tabs">
                            <a href="?tab=general" class="<?php echo $activeTab === 'general' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?> whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                                الإعدادات العامة
                            </a>
                            <a href="?tab=design" class="<?php echo $activeTab === 'design' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?> whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                                التصميم والألوان
                            </a>
                            <a href="?tab=email" class="<?php echo $activeTab === 'email' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?> whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                                البريد الإلكتروني
                            </a>
                            <a href="?tab=security" class="<?php echo $activeTab === 'security' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?> whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                                الأمان
                            </a>
                            <a href="?tab=system" class="<?php echo $activeTab === 'system' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?> whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                                النظام
                            </a>
                        </nav>
                    </div>

                    <div class="p-6">
                        <?php if ($activeTab === 'general'): ?>
                        <!-- الإعدادات العامة -->
                        <form method="POST" enctype="multipart/form-data" class="space-y-6">
                            <input type="hidden" name="general_settings" value="1">
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="site_name" class="block text-sm font-medium text-gray-700 mb-2">اسم الموقع</label>
                                    <input type="text" id="site_name" name="site_name" value="<?php echo htmlspecialchars($settings['site_name'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                </div>
                                
                                <div>
                                    <label for="contact_phone" class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                                    <input type="text" id="contact_phone" name="contact_phone" value="<?php echo htmlspecialchars($settings['contact_phone'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label for="contact_email" class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                                    <input type="email" id="contact_email" name="contact_email" value="<?php echo htmlspecialchars($settings['contact_email'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label for="whatsapp_number" class="block text-sm font-medium text-gray-700 mb-2">رقم الواتساب</label>
                                    <input type="text" id="whatsapp_number" name="whatsapp_number" value="<?php echo htmlspecialchars($settings['whatsapp_number'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                            </div>
                            
                            <div>
                                <label for="site_description" class="block text-sm font-medium text-gray-700 mb-2">وصف الموقع</label>
                                <textarea id="site_description" name="site_description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo htmlspecialchars($settings['site_description'] ?? ''); ?></textarea>
                            </div>
                            
                            <div>
                                <label for="address" class="block text-sm font-medium text-gray-700 mb-2">العنوان</label>
                                <textarea id="address" name="address" rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo htmlspecialchars($settings['address'] ?? ''); ?></textarea>
                            </div>
                            
                            <!-- حقل تحميل الشعار -->
                            <div>
                                <label for="site_logo" class="block text-sm font-medium text-gray-700 mb-2">شعار الموقع</label>
                                <?php if (!empty($settings['site_logo'])): ?>
                                <div class="mb-3">
                                    <img src="../<?php echo htmlspecialchars($settings['site_logo']); ?>" alt="شعار الموقع الحالي" class="h-16 w-auto object-contain border border-gray-300 rounded-md">
                                    <p class="text-sm text-gray-500 mt-1">الشعار الحالي</p>
                                </div>
                                <?php endif; ?>
                                <input type="file" id="site_logo" name="site_logo" accept="image/*" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <p class="text-sm text-gray-500 mt-1">الصيغ المدعومة: JPG, PNG, GIF, WebP, SVG (الحد الأقصى: 2MB)</p>
                            </div>
                            
                            <div class="flex justify-end">
                                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200">
                                    حفظ الإعدادات العامة
                                </button>
                            </div>
                        </form>
                        
                        <?php elseif ($activeTab === 'design'): ?>
                        <!-- إعدادات التصميم -->
                        <form method="POST" class="space-y-6">
                            <input type="hidden" name="design_settings" value="1">
                            
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div>
                                    <label for="primary_color" class="block text-sm font-medium text-gray-700 mb-2">اللون الأساسي</label>
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <input type="color" id="primary_color" name="primary_color" value="<?php echo htmlspecialchars($settings['primary_color'] ?? '#059669'); ?>" class="w-12 h-10 border border-gray-300 rounded cursor-pointer">
                                        <input type="text" value="<?php echo htmlspecialchars($settings['primary_color'] ?? '#059669'); ?>" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" readonly>
                                    </div>
                                </div>
                                
                                <div>
                                    <label for="secondary_color" class="block text-sm font-medium text-gray-700 mb-2">اللون الثانوي</label>
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <input type="color" id="secondary_color" name="secondary_color" value="<?php echo htmlspecialchars($settings['secondary_color'] ?? '#10b981'); ?>" class="w-12 h-10 border border-gray-300 rounded cursor-pointer">
                                        <input type="text" value="<?php echo htmlspecialchars($settings['secondary_color'] ?? '#10b981'); ?>" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" readonly>
                                    </div>
                                </div>
                                
                                <div>
                                    <label for="accent_color" class="block text-sm font-medium text-gray-700 mb-2">لون التمييز</label>
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <input type="color" id="accent_color" name="accent_color" value="<?php echo htmlspecialchars($settings['accent_color'] ?? '#34d399'); ?>" class="w-12 h-10 border border-gray-300 rounded cursor-pointer">
                                        <input type="text" value="<?php echo htmlspecialchars($settings['accent_color'] ?? '#34d399'); ?>" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" readonly>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="font_family" class="block text-sm font-medium text-gray-700 mb-2">نوع الخط</label>
                                    <select id="font_family" name="font_family" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="Noto Kufi Arabic" <?php echo ($settings['font_family'] ?? '') === 'Noto Kufi Arabic' ? 'selected' : ''; ?>>Noto Kufi Arabic</option>
                                        <option value="Cairo" <?php echo ($settings['font_family'] ?? '') === 'Cairo' ? 'selected' : ''; ?>>Cairo</option>
                                        <option value="Amiri" <?php echo ($settings['font_family'] ?? '') === 'Amiri' ? 'selected' : ''; ?>>Amiri</option>
                                        <option value="Tajawal" <?php echo ($settings['font_family'] ?? '') === 'Tajawal' ? 'selected' : ''; ?>>Tajawal</option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label for="font_url" class="block text-sm font-medium text-gray-700 mb-2">رابط الخط</label>
                                    <input type="url" id="font_url" name="font_url" value="<?php echo htmlspecialchars($settings['font_url'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                            </div>
                            
                            <div class="flex justify-end">
                                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200">
                                    حفظ إعدادات التصميم
                                </button>
                            </div>
                        </form>
                        
                        <?php elseif ($activeTab === 'email'): ?>
                        <!-- إعدادات البريد الإلكتروني -->
                        <form method="POST" class="space-y-6">
                            <input type="hidden" name="email_settings" value="1">
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="smtp_host" class="block text-sm font-medium text-gray-700 mb-2">خادم SMTP</label>
                                    <input type="text" id="smtp_host" name="smtp_host" value="<?php echo htmlspecialchars($settings['smtp_host'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label for="smtp_port" class="block text-sm font-medium text-gray-700 mb-2">منفذ SMTP</label>
                                    <input type="number" id="smtp_port" name="smtp_port" value="<?php echo htmlspecialchars($settings['smtp_port'] ?? '587'); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label for="smtp_username" class="block text-sm font-medium text-gray-700 mb-2">اسم المستخدم</label>
                                    <input type="text" id="smtp_username" name="smtp_username" value="<?php echo htmlspecialchars($settings['smtp_username'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label for="smtp_password" class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور</label>
                                    <input type="password" id="smtp_password" name="smtp_password" value="<?php echo htmlspecialchars($settings['smtp_password'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label for="smtp_from_email" class="block text-sm font-medium text-gray-700 mb-2">البريد المرسل</label>
                                    <input type="email" id="smtp_from_email" name="smtp_from_email" value="<?php echo htmlspecialchars($settings['smtp_from_email'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label for="smtp_from_name" class="block text-sm font-medium text-gray-700 mb-2">اسم المرسل</label>
                                    <input type="text" id="smtp_from_name" name="smtp_from_name" value="<?php echo htmlspecialchars($settings['smtp_from_name'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                            </div>
                            
                            <div>
                                <label for="smtp_encryption" class="block text-sm font-medium text-gray-700 mb-2">نوع التشفير</label>
                                <select id="smtp_encryption" name="smtp_encryption" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="tls" <?php echo ($settings['smtp_encryption'] ?? '') === 'tls' ? 'selected' : ''; ?>>TLS</option>
                                    <option value="ssl" <?php echo ($settings['smtp_encryption'] ?? '') === 'ssl' ? 'selected' : ''; ?>>SSL</option>
                                    <option value="none" <?php echo ($settings['smtp_encryption'] ?? '') === 'none' ? 'selected' : ''; ?>>بدون تشفير</option>
                                </select>
                            </div>
                            
                            <div class="flex justify-end">
                                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200">
                                    حفظ إعدادات البريد الإلكتروني
                                </button>
                            </div>
                        </form>
                        
                        <?php elseif ($activeTab === 'security'): ?>
                        <!-- إعدادات الأمان -->
                        <form method="POST" class="space-y-6">
                            <input type="hidden" name="security_settings" value="1">
                            
                            <div class="space-y-4">
                                <div class="flex items-center">
                                    <input type="checkbox" id="enable_2fa" name="enable_2fa" <?php echo ($settings['enable_2fa'] ?? false) ? 'checked' : ''; ?> class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <label for="enable_2fa" class="mr-2 block text-sm text-gray-900">تفعيل المصادقة الثنائية</label>
                                </div>
                                
                                <div class="flex items-center">
                                    <input type="checkbox" id="require_strong_password" name="require_strong_password" <?php echo ($settings['require_strong_password'] ?? false) ? 'checked' : ''; ?> class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <label for="require_strong_password" class="mr-2 block text-sm text-gray-900">طلب كلمة مرور قوية</label>
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div>
                                    <label for="session_timeout" class="block text-sm font-medium text-gray-700 mb-2">انتهاء الجلسة (دقيقة)</label>
                                    <input type="number" id="session_timeout" name="session_timeout" value="<?php echo htmlspecialchars($settings['session_timeout'] ?? '30'); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label for="max_login_attempts" class="block text-sm font-medium text-gray-700 mb-2">محاولات تسجيل الدخول</label>
                                    <input type="number" id="max_login_attempts" name="max_login_attempts" value="<?php echo htmlspecialchars($settings['max_login_attempts'] ?? '5'); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label for="password_min_length" class="block text-sm font-medium text-gray-700 mb-2">الحد الأدنى لطول كلمة المرور</label>
                                    <input type="number" id="password_min_length" name="password_min_length" value="<?php echo htmlspecialchars($settings['password_min_length'] ?? '8'); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                            </div>
                            
                            <div class="flex justify-end">
                                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200">
                                    حفظ إعدادات الأمان
                                </button>
                            </div>
                        </form>
                        
                        <?php elseif ($activeTab === 'system'): ?>
                        <!-- إعدادات النظام -->
                        <form method="POST" class="space-y-6">
                            <input type="hidden" name="system_settings" value="1">
                            
                            <div class="space-y-4">
                                <div class="flex items-center">
                                    <input type="checkbox" id="maintenance_mode" name="maintenance_mode" <?php echo ($settings['maintenance_mode'] ?? false) ? 'checked' : ''; ?> class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <label for="maintenance_mode" class="mr-2 block text-sm text-gray-900">وضع الصيانة</label>
                                </div>
                                
                                <div class="flex items-center">
                                    <input type="checkbox" id="debug_mode" name="debug_mode" <?php echo ($settings['debug_mode'] ?? false) ? 'checked' : ''; ?> class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <label for="debug_mode" class="mr-2 block text-sm text-gray-900">وضع التطوير</label>
                                </div>
                                
                                <div class="flex items-center">
                                    <input type="checkbox" id="cache_enabled" name="cache_enabled" <?php echo ($settings['cache_enabled'] ?? false) ? 'checked' : ''; ?> class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <label for="cache_enabled" class="mr-2 block text-sm text-gray-900">تفعيل التخزين المؤقت</label>
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div>
                                    <label for="backup_frequency" class="block text-sm font-medium text-gray-700 mb-2">تكرار النسخ الاحتياطي</label>
                                    <select id="backup_frequency" name="backup_frequency" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="daily" <?php echo ($settings['backup_frequency'] ?? '') === 'daily' ? 'selected' : ''; ?>>يومي</option>
                                        <option value="weekly" <?php echo ($settings['backup_frequency'] ?? '') === 'weekly' ? 'selected' : ''; ?>>أسبوعي</option>
                                        <option value="monthly" <?php echo ($settings['backup_frequency'] ?? '') === 'monthly' ? 'selected' : ''; ?>>شهري</option>
                                        <option value="manual" <?php echo ($settings['backup_frequency'] ?? '') === 'manual' ? 'selected' : ''; ?>>يدوي</option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label for="timezone" class="block text-sm font-medium text-gray-700 mb-2">المنطقة الزمنية</label>
                                    <select id="timezone" name="timezone" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="Asia/Riyadh" <?php echo ($settings['timezone'] ?? '') === 'Asia/Riyadh' ? 'selected' : ''; ?>>الرياض</option>
                                        <option value="Asia/Dubai" <?php echo ($settings['timezone'] ?? '') === 'Asia/Dubai' ? 'selected' : ''; ?>>دبي</option>
                                        <option value="Asia/Kuwait" <?php echo ($settings['timezone'] ?? '') === 'Asia/Kuwait' ? 'selected' : ''; ?>>الكويت</option>
                                        <option value="Asia/Qatar" <?php echo ($settings['timezone'] ?? '') === 'Asia/Qatar' ? 'selected' : ''; ?>>قطر</option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label for="language" class="block text-sm font-medium text-gray-700 mb-2">اللغة</label>
                                    <select id="language" name="language" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="ar" <?php echo ($settings['language'] ?? '') === 'ar' ? 'selected' : ''; ?>>العربية</option>
                                        <option value="en" <?php echo ($settings['language'] ?? '') === 'en' ? 'selected' : ''; ?>>English</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="flex justify-end">
                                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200">
                                    حفظ إعدادات النظام
                                </button>
                            </div>
                        </form>
                        <?php endif; ?>
                    </div>
                </div>

<script>
// تحديث قيم الألوان عند تغييرها
document.addEventListener('DOMContentLoaded', function() {
    const colorInputs = document.querySelectorAll('input[type="color"]');
    
    colorInputs.forEach(input => {
        input.addEventListener('change', function() {
            const textInput = this.parentElement.querySelector('input[type="text"]');
            if (textInput) {
                textInput.value = this.value;
            }
        });
    });
    
    // تحديث رابط الخط عند تغيير نوع الخط
    const fontFamilySelect = document.getElementById('font_family');
    const fontUrlInput = document.getElementById('font_url');
    
    if (fontFamilySelect && fontUrlInput) {
        // روابط الخطوط من Google Fonts
        const fontUrls = {
            'Noto Kufi Arabic': 'https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@100;200;300;400;500;600;700;800;900&display=swap',
            'Cairo': 'https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap',
            'Amiri': 'https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&display=swap',
            'Tajawal': 'https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap'
        };
        
        fontFamilySelect.addEventListener('change', function() {
            const selectedFont = this.value;
            if (fontUrls[selectedFont]) {
                fontUrlInput.value = fontUrls[selectedFont];
            }
        });
    }
});
</script>

<?php endLayout(); ?>