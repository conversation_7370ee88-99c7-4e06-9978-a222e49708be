<?php
/**
 * API للإشعارات
 * Notifications API
 */

// منع عرض أخطاء PHP
error_reporting(0);
ini_set('display_errors', 0);

// تنظيف أي مخارج سابقة
ob_clean();

require_once '../../config/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/middleware.php';

// التحقق من تسجيل الدخول أولاً
if (!isLoggedIn()) {
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'success' => false,
        'message' => 'يجب تسجيل الدخول أولاً'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

header('Content-Type: application/json; charset=utf-8');

try {
    // جلب الإشعارات الحديثة
    $notifications = [];
    
    // جلب الرسائل الجديدة
    $stmt = $database->prepare("
        SELECT COUNT(*) as count 
        FROM contact_messages 
        WHERE status = 'new' AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    ");
    $stmt->execute();
    $newMessages = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;
    
    // جلب المراجعات الجديدة
    $stmt = $database->prepare("
        SELECT COUNT(*) as count 
        FROM reviews 
        WHERE status = 'pending' AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    ");
    $stmt->execute();
    $newReviews = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;
    
    // جلب طلبات الخدمة الجديدة
    $stmt = $database->prepare("
        SELECT COUNT(*) as count 
        FROM service_requests 
        WHERE status = 'pending' AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    ");
    $stmt->execute();
    $newServiceRequests = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;
    
    $totalNotifications = $newMessages + $newReviews + $newServiceRequests;
    
    echo json_encode([
        'success' => true,
        'notifications' => $notifications,
        'counts' => [
            'messages' => $newMessages,
            'reviews' => $newReviews,
            'service_requests' => $newServiceRequests,
            'total' => $totalNotifications
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في جلب الإشعارات'
    ], JSON_UNESCAPED_UNICODE);
}
?>