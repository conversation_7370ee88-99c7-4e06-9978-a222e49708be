/**
 * Admin Panel JavaScript
 * Handles all interactive functionality for the admin panel
 */

// Global admin object
const Admin = {
    // Configuration
    config: {
        apiUrl: '/api/',
        refreshInterval: 30000, // 30 seconds
        notificationDuration: 5000, // 5 seconds
        debounceDelay: 300,
        maxFileSize: 10 * 1024 * 1024, // 10MB
        allowedImageTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
    },

    // State management
    state: {
        sidebarOpen: false,
        currentPage: 1,
        searchQuery: '',
        filters: {},
        notifications: [],
        uploads: new Map()
    },

    // Initialize admin panel
    init() {
        this.initEventListeners();
        this.initComponents();
        this.startAutoRefresh();
        this.loadNotifications();
        console.log('Admin panel initialized');
    },

    // Event listeners
    initEventListeners() {
        // Sidebar toggle
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-sidebar-toggle]')) {
                this.toggleSidebar();
            }
        });

        // Mobile menu overlay
        document.addEventListener('click', (e) => {
            if (e.target.matches('.sidebar-overlay')) {
                this.closeSidebar();
            }
        });

        // Form submissions
        document.addEventListener('submit', (e) => {
            if (e.target.matches('.admin-form')) {
                this.handleFormSubmit(e);
            }
        });

        // Search functionality
        document.addEventListener('input', (e) => {
            if (e.target.matches('[data-search]')) {
                this.debounce(() => this.handleSearch(e.target), this.config.debounceDelay)();
            }
        });

        // Filter changes
        document.addEventListener('change', (e) => {
            if (e.target.matches('[data-filter]')) {
                this.handleFilterChange(e.target);
            }
        });

        // Modal triggers
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-modal-open]')) {
                this.openModal(e.target.dataset.modalOpen);
            }
            if (e.target.matches('[data-modal-close]')) {
                this.closeModal();
            }
        });

        // Confirmation dialogs
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-confirm]')) {
                e.preventDefault();
                this.showConfirmDialog(e.target.dataset.confirm, () => {
                    if (e.target.href) {
                        window.location.href = e.target.href;
                    } else if (e.target.form) {
                        e.target.form.submit();
                    }
                });
            }
        });

        // File uploads
        document.addEventListener('change', (e) => {
            if (e.target.matches('input[type="file"]')) {
                this.handleFileUpload(e.target);
            }
        });

        // Drag and drop
        document.addEventListener('dragover', (e) => {
            if (e.target.matches('.file-upload-admin')) {
                e.preventDefault();
                e.target.classList.add('dragover');
            }
        });

        document.addEventListener('dragleave', (e) => {
            if (e.target.matches('.file-upload-admin')) {
                e.target.classList.remove('dragover');
            }
        });

        document.addEventListener('drop', (e) => {
            if (e.target.matches('.file-upload-admin')) {
                e.preventDefault();
                e.target.classList.remove('dragover');
                this.handleFileDrop(e.target, e.dataTransfer.files);
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });

        // Auto-save for forms
        document.addEventListener('input', (e) => {
            if (e.target.matches('[data-autosave]')) {
                this.debounce(() => this.autoSave(e.target), 1000)();
            }
        });

        // Notification dismissal
        document.addEventListener('click', (e) => {
            if (e.target.matches('.notification-dismiss')) {
                this.dismissNotification(e.target.closest('.notification'));
            }
        });
    },

    // Initialize components
    initComponents() {
        this.initDataTables();
        this.initCharts();
        this.initTooltips();
        this.initDatePickers();
        this.initRichTextEditors();
        this.initImagePreviews();
        this.initSortables();
    },

    // Sidebar management
    toggleSidebar() {
        this.state.sidebarOpen = !this.state.sidebarOpen;
        const sidebar = document.querySelector('.admin-sidebar');
        const overlay = document.querySelector('.sidebar-overlay');
        
        if (this.state.sidebarOpen) {
            sidebar?.classList.add('open');
            if (!overlay) {
                const overlayEl = document.createElement('div');
                overlayEl.className = 'sidebar-overlay fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden';
                document.body.appendChild(overlayEl);
            }
        } else {
            sidebar?.classList.remove('open');
            overlay?.remove();
        }
    },

    closeSidebar() {
        this.state.sidebarOpen = false;
        document.querySelector('.admin-sidebar')?.classList.remove('open');
        document.querySelector('.sidebar-overlay')?.remove();
    },

    // Form handling
    async handleFormSubmit(e) {
        e.preventDefault();
        const form = e.target;
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn?.textContent;

        try {
            // Show loading state
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>جاري الحفظ...';
            }

            // Validate form
            if (!this.validateForm(form)) {
                throw new Error('يرجى تصحيح الأخطاء في النموذج');
            }

            // Prepare form data
            const formData = new FormData(form);
            const url = form.action || window.location.href;
            const method = form.method || 'POST';

            // Submit form
            const response = await fetch(url, {
                method: method,
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('تم الحفظ بنجاح', 'success');
                
                // Handle redirect
                if (result.redirect) {
                    setTimeout(() => {
                        window.location.href = result.redirect;
                    }, 1000);
                } else if (form.dataset.reload) {
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                }
            } else {
                throw new Error(result.message || 'حدث خطأ أثناء الحفظ');
            }
        } catch (error) {
            this.showNotification(error.message, 'error');
        } finally {
            // Reset button state
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.textContent = originalText;
            }
        }
    },

    // Form validation
    validateForm(form) {
        let isValid = true;
        const inputs = form.querySelectorAll('input, textarea, select');

        inputs.forEach(input => {
            const errorEl = input.parentNode.querySelector('.form-error');
            
            // Remove previous errors
            input.classList.remove('error');
            if (errorEl) errorEl.remove();

            // Required field validation
            if (input.hasAttribute('required') && !input.value.trim()) {
                this.showFieldError(input, 'هذا الحقل مطلوب');
                isValid = false;
                return;
            }

            // Email validation
            if (input.type === 'email' && input.value) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(input.value)) {
                    this.showFieldError(input, 'يرجى إدخال بريد إلكتروني صحيح');
                    isValid = false;
                }
            }

            // URL validation
            if (input.type === 'url' && input.value) {
                try {
                    new URL(input.value);
                } catch {
                    this.showFieldError(input, 'يرجى إدخال رابط صحيح');
                    isValid = false;
                }
            }

            // Number validation
            if (input.type === 'number' && input.value) {
                const min = parseFloat(input.min);
                const max = parseFloat(input.max);
                const value = parseFloat(input.value);
                
                if (!isNaN(min) && value < min) {
                    this.showFieldError(input, `القيمة يجب أن تكون أكبر من أو تساوي ${min}`);
                    isValid = false;
                }
                
                if (!isNaN(max) && value > max) {
                    this.showFieldError(input, `القيمة يجب أن تكون أقل من أو تساوي ${max}`);
                    isValid = false;
                }
            }

            // Custom validation
            const customValidation = input.dataset.validation;
            if (customValidation && input.value) {
                if (!this.customValidations[customValidation](input.value)) {
                    this.showFieldError(input, input.dataset.validationMessage || 'قيمة غير صحيحة');
                    isValid = false;
                }
            }
        });

        return isValid;
    },

    // Custom validations
    customValidations: {
        phone: (value) => /^[0-9+\-\s()]+$/.test(value),
        slug: (value) => /^[a-z0-9-]+$/.test(value),
        color: (value) => /^#[0-9A-Fa-f]{6}$/.test(value)
    },

    // Show field error
    showFieldError(input, message) {
        input.classList.add('error');
        const errorEl = document.createElement('div');
        errorEl.className = 'form-error';
        errorEl.textContent = message;
        input.parentNode.appendChild(errorEl);
    },

    // Search functionality
    handleSearch(input) {
        const query = input.value.trim();
        const target = input.dataset.search;
        
        this.state.searchQuery = query;
        
        if (target === 'table') {
            this.searchTable(query);
        } else if (target === 'ajax') {
            this.searchAjax(query, input.dataset.url);
        }
    },

    // Table search
    searchTable(query) {
        const table = document.querySelector('.table-admin tbody');
        if (!table) return;

        const rows = table.querySelectorAll('tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            const matches = text.includes(query.toLowerCase());
            row.style.display = matches ? '' : 'none';
        });
    },

    // AJAX search
    async searchAjax(query, url) {
        try {
            const response = await fetch(`${url}?search=${encodeURIComponent(query)}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.updateSearchResults(result.data);
            }
        } catch (error) {
            console.error('Search error:', error);
        }
    },

    // Update search results
    updateSearchResults(data) {
        const container = document.querySelector('[data-search-results]');
        if (!container) return;

        container.innerHTML = data.html || '';
        this.initComponents(); // Re-initialize components for new content
    },

    // Filter handling
    handleFilterChange(select) {
        const filterName = select.name;
        const filterValue = select.value;
        
        this.state.filters[filterName] = filterValue;
        
        if (select.dataset.autoSubmit) {
            this.applyFilters();
        }
    },

    // Apply filters
    async applyFilters() {
        const params = new URLSearchParams(this.state.filters);
        const url = `${window.location.pathname}?${params.toString()}`;
        
        try {
            const response = await fetch(url, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.updateContent(result.data);
                // Update URL without reload
                history.pushState(null, '', url);
            }
        } catch (error) {
            console.error('Filter error:', error);
        }
    },

    // Update content
    updateContent(data) {
        const container = document.querySelector('[data-content]');
        if (container && data.html) {
            container.innerHTML = data.html;
            this.initComponents();
        }
    },

    // Modal management
    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (!modal) return;

        modal.classList.add('show');
        document.body.style.overflow = 'hidden';
        
        // Focus first input
        const firstInput = modal.querySelector('input, textarea, select');
        if (firstInput) {
            setTimeout(() => firstInput.focus(), 100);
        }
    },

    closeModal() {
        const modals = document.querySelectorAll('.modal-admin.show');
        modals.forEach(modal => {
            modal.classList.remove('show');
        });
        document.body.style.overflow = '';
    },

    // Confirmation dialog
    showConfirmDialog(message, callback) {
        if (confirm(message)) {
            callback();
        }
    },

    // File upload handling
    handleFileUpload(input) {
        const files = Array.from(input.files);
        
        files.forEach(file => {
            if (this.validateFile(file)) {
                this.uploadFile(file, input);
            }
        });
    },

    // File drop handling
    handleFileDrop(dropZone, files) {
        const fileArray = Array.from(files);
        const input = dropZone.querySelector('input[type="file"]');
        
        if (input) {
            // Update input files
            const dt = new DataTransfer();
            fileArray.forEach(file => dt.items.add(file));
            input.files = dt.files;
            
            this.handleFileUpload(input);
        }
    },

    // File validation
    validateFile(file) {
        // Size check
        if (file.size > this.config.maxFileSize) {
            this.showNotification(`حجم الملف كبير جداً. الحد الأقصى ${this.formatFileSize(this.config.maxFileSize)}`, 'error');
            return false;
        }

        // Type check for images
        if (file.type.startsWith('image/') && !this.config.allowedImageTypes.includes(file.type)) {
            this.showNotification('نوع الصورة غير مدعوم', 'error');
            return false;
        }

        return true;
    },

    // Upload file
    async uploadFile(file, input) {
        const uploadId = Date.now() + Math.random();
        const formData = new FormData();
        formData.append('file', file);
        formData.append('upload_type', input.dataset.uploadType || 'general');

        // Create progress indicator
        const progressEl = this.createProgressIndicator(file.name, uploadId);
        
        try {
            const response = await fetch('/admin/upload.php', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();

            if (result.success) {
                this.handleUploadSuccess(result.data, input, progressEl);
            } else {
                throw new Error(result.message || 'فشل في رفع الملف');
            }
        } catch (error) {
            this.handleUploadError(error.message, progressEl);
        }
    },

    // Create progress indicator
    createProgressIndicator(filename, uploadId) {
        const container = document.querySelector('.upload-progress') || this.createUploadContainer();
        
        const progressEl = document.createElement('div');
        progressEl.className = 'upload-item';
        progressEl.dataset.uploadId = uploadId;
        progressEl.innerHTML = `
            <div class="flex items-center justify-between p-3 bg-white border rounded">
                <div class="flex items-center">
                    <i class="fas fa-file mr-2"></i>
                    <span class="text-sm">${filename}</span>
                </div>
                <div class="flex items-center">
                    <div class="progress-admin w-20 mr-2">
                        <div class="progress-admin-bar" style="width: 0%"></div>
                    </div>
                    <button type="button" class="text-red-500 hover:text-red-700" onclick="Admin.cancelUpload('${uploadId}')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;
        
        container.appendChild(progressEl);
        return progressEl;
    },

    // Create upload container
    createUploadContainer() {
        const container = document.createElement('div');
        container.className = 'upload-progress fixed bottom-4 right-4 w-80 max-h-60 overflow-y-auto z-50';
        document.body.appendChild(container);
        return container;
    },

    // Handle upload success
    handleUploadSuccess(data, input, progressEl) {
        // Update progress to 100%
        const progressBar = progressEl.querySelector('.progress-admin-bar');
        progressBar.style.width = '100%';
        
        // Show success state
        setTimeout(() => {
            progressEl.querySelector('.fa-file').className = 'fas fa-check text-green-500';
            progressEl.querySelector('button').remove();
        }, 500);
        
        // Remove after delay
        setTimeout(() => {
            progressEl.remove();
        }, 3000);

        // Update input value if needed
        if (input.dataset.target) {
            const target = document.querySelector(input.dataset.target);
            if (target) {
                target.value = data.url || data.path;
            }
        }

        // Show preview if image
        if (data.type === 'image') {
            this.showImagePreview(data.url, input);
        }

        this.showNotification('تم رفع الملف بنجاح', 'success');
    },

    // Handle upload error
    handleUploadError(message, progressEl) {
        progressEl.querySelector('.fa-file').className = 'fas fa-exclamation-triangle text-red-500';
        progressEl.querySelector('.progress-admin').innerHTML = '<span class="text-red-500 text-xs">فشل</span>';
        
        setTimeout(() => {
            progressEl.remove();
        }, 5000);

        this.showNotification(message, 'error');
    },

    // Cancel upload
    cancelUpload(uploadId) {
        const progressEl = document.querySelector(`[data-upload-id="${uploadId}"]`);
        if (progressEl) {
            progressEl.remove();
        }
        
        // Cancel actual upload if possible
        if (this.state.uploads.has(uploadId)) {
            const controller = this.state.uploads.get(uploadId);
            controller.abort();
            this.state.uploads.delete(uploadId);
        }
    },

    // Show image preview
    showImagePreview(url, input) {
        const previewContainer = input.parentNode.querySelector('.image-preview') || this.createPreviewContainer(input);
        
        previewContainer.innerHTML = `
            <div class="relative inline-block">
                <img src="${url}" alt="Preview" class="w-20 h-20 object-cover rounded border">
                <button type="button" class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs" onclick="this.parentNode.parentNode.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
    },

    // Create preview container
    createPreviewContainer(input) {
        const container = document.createElement('div');
        container.className = 'image-preview mt-2';
        input.parentNode.appendChild(container);
        return container;
    },

    // Keyboard shortcuts
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + S to save
        if ((e.ctrlKey || e.metaKey) && e.key === 's') {
            e.preventDefault();
            const form = document.querySelector('.admin-form');
            if (form) {
                form.dispatchEvent(new Event('submit', { bubbles: true }));
            }
        }

        // Escape to close modal
        if (e.key === 'Escape') {
            this.closeModal();
        }

        // Ctrl/Cmd + K for search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.querySelector('[data-search]');
            if (searchInput) {
                searchInput.focus();
            }
        }
    },

    // Auto-save functionality
    autoSave(input) {
        const form = input.closest('form');
        if (!form) return;

        const formData = new FormData(form);
        const autoSaveUrl = form.dataset.autosaveUrl || '/admin/autosave.php';

        fetch(autoSaveUrl, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        }).then(response => response.json())
          .then(result => {
              if (result.success) {
                  this.showAutoSaveIndicator();
              }
          })
          .catch(error => {
              console.error('Auto-save error:', error);
          });
    },

    // Show auto-save indicator
    showAutoSaveIndicator() {
        const indicator = document.querySelector('.autosave-indicator') || this.createAutoSaveIndicator();
        indicator.textContent = 'تم الحفظ تلقائياً';
        indicator.classList.add('show');
        
        setTimeout(() => {
            indicator.classList.remove('show');
        }, 2000);
    },

    // Create auto-save indicator
    createAutoSaveIndicator() {
        const indicator = document.createElement('div');
        indicator.className = 'autosave-indicator fixed top-4 right-4 bg-green-500 text-white px-3 py-1 rounded text-sm opacity-0 transition-opacity';
        document.body.appendChild(indicator);
        return indicator;
    },

    // Notification system
    showNotification(message, type = 'info', duration = null) {
        const notification = this.createNotification(message, type);
        const container = this.getNotificationContainer();
        
        container.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        // Auto dismiss
        const dismissDuration = duration || this.config.notificationDuration;
        setTimeout(() => {
            this.dismissNotification(notification);
        }, dismissDuration);
        
        return notification;
    },

    // Create notification element
    createNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `notification alert-admin alert-admin-${type} transform translate-x-full transition-transform duration-300`;
        
        const icons = {
            success: 'fa-check-circle',
            error: 'fa-exclamation-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle'
        };
        
        notification.innerHTML = `
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <i class="fas ${icons[type] || icons.info} mr-2"></i>
                    <span>${message}</span>
                </div>
                <button type="button" class="notification-dismiss ml-2 text-lg leading-none">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        return notification;
    },

    // Get notification container
    getNotificationContainer() {
        let container = document.querySelector('.notification-container');
        
        if (!container) {
            container = document.createElement('div');
            container.className = 'notification-container fixed top-4 left-4 w-80 z-50 space-y-2';
            document.body.appendChild(container);
        }
        
        return container;
    },

    // Dismiss notification
    dismissNotification(notification) {
        notification.classList.remove('show');
        notification.classList.add('translate-x-full');
        
        setTimeout(() => {
            notification.remove();
        }, 300);
    },

    // Load notifications
    async loadNotifications() {
        try {
            const response = await fetch('/admin/api/notifications.php', {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.updateNotificationCounts(result.data);
            }
        } catch (error) {
            console.error('Failed to load notifications:', error);
        }
    },

    // Update notification counts
    updateNotificationCounts(data) {
        const counters = {
            messages: document.querySelector('[data-count="messages"]'),
            reviews: document.querySelector('[data-count="reviews"]'),
            requests: document.querySelector('[data-count="requests"]')
        };
        
        Object.keys(counters).forEach(key => {
            const counter = counters[key];
            if (counter && data[key] !== undefined) {
                counter.textContent = data[key];
                counter.style.display = data[key] > 0 ? '' : 'none';
            }
        });
    },

    // Auto-refresh functionality
    startAutoRefresh() {
        setInterval(() => {
            this.loadNotifications();
            this.refreshDashboardStats();
        }, this.config.refreshInterval);
    },

    // Refresh dashboard stats
    async refreshDashboardStats() {
        if (!document.querySelector('.dashboard-stats')) return;
        
        try {
            const response = await fetch('/admin/api/stats.php', {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.updateDashboardStats(result.data);
            }
        } catch (error) {
            console.error('Failed to refresh stats:', error);
        }
    },

    // Update dashboard stats
    updateDashboardStats(data) {
        Object.keys(data).forEach(key => {
            const element = document.querySelector(`[data-stat="${key}"]`);
            if (element) {
                this.animateNumber(element, parseInt(data[key]));
            }
        });
    },

    // Animate number
    animateNumber(element, targetValue) {
        const currentValue = parseInt(element.textContent) || 0;
        const increment = (targetValue - currentValue) / 20;
        let current = currentValue;
        
        const timer = setInterval(() => {
            current += increment;
            if ((increment > 0 && current >= targetValue) || (increment < 0 && current <= targetValue)) {
                current = targetValue;
                clearInterval(timer);
            }
            element.textContent = Math.round(current);
        }, 50);
    },

    // Initialize data tables
    initDataTables() {
        const tables = document.querySelectorAll('.data-table');
        
        tables.forEach(table => {
            // Add sorting functionality
            const headers = table.querySelectorAll('th[data-sort]');
            headers.forEach(header => {
                header.style.cursor = 'pointer';
                header.addEventListener('click', () => {
                    this.sortTable(table, header.dataset.sort);
                });
            });
        });
    },

    // Sort table
    sortTable(table, column) {
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        const isAscending = table.dataset.sortDirection !== 'asc';
        
        rows.sort((a, b) => {
            const aValue = a.querySelector(`[data-sort-value="${column}"]`)?.textContent || a.cells[0].textContent;
            const bValue = b.querySelector(`[data-sort-value="${column}"]`)?.textContent || b.cells[0].textContent;
            
            if (isAscending) {
                return aValue.localeCompare(bValue, 'ar', { numeric: true });
            } else {
                return bValue.localeCompare(aValue, 'ar', { numeric: true });
            }
        });
        
        // Update table
        rows.forEach(row => tbody.appendChild(row));
        table.dataset.sortDirection = isAscending ? 'asc' : 'desc';
        
        // Update header indicators
        table.querySelectorAll('th').forEach(th => th.classList.remove('sort-asc', 'sort-desc'));
        const header = table.querySelector(`th[data-sort="${column}"]`);
        header.classList.add(isAscending ? 'sort-asc' : 'sort-desc');
    },

    // Initialize charts
    initCharts() {
        // Chart.js initialization would go here
        // This is a placeholder for chart functionality
    },

    // Initialize tooltips
    initTooltips() {
        const tooltipElements = document.querySelectorAll('[data-tooltip]');
        
        tooltipElements.forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                this.showTooltip(e.target, e.target.dataset.tooltip);
            });
            
            element.addEventListener('mouseleave', () => {
                this.hideTooltip();
            });
        });
    },

    // Show tooltip
    showTooltip(element, text) {
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip-admin';
        tooltip.textContent = text;
        
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
    },

    // Hide tooltip
    hideTooltip() {
        const tooltip = document.querySelector('.tooltip-admin');
        if (tooltip) {
            tooltip.remove();
        }
    },

    // Initialize date pickers
    initDatePickers() {
        // Date picker initialization would go here
        // This is a placeholder for date picker functionality
    },

    // Initialize rich text editors
    initRichTextEditors() {
        // Rich text editor initialization would go here
        // This is a placeholder for WYSIWYG editor functionality
    },

    // Initialize image previews
    initImagePreviews() {
        const imageInputs = document.querySelectorAll('input[type="file"][accept*="image"]');
        
        imageInputs.forEach(input => {
            input.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        this.showImagePreview(e.target.result, input);
                    };
                    reader.readAsDataURL(file);
                }
            });
        });
    },

    // Initialize sortables
    initSortables() {
        // Sortable list initialization would go here
        // This is a placeholder for drag-and-drop sorting functionality
    },

    // Utility functions
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    formatFileSize(bytes) {
        if (bytes === 0) return '0 بايت';
        const k = 1024;
        const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    formatDate(date, format = 'Y-m-d H:i:s') {
        const d = new Date(date);
        const formats = {
            'Y': d.getFullYear(),
            'm': String(d.getMonth() + 1).padStart(2, '0'),
            'd': String(d.getDate()).padStart(2, '0'),
            'H': String(d.getHours()).padStart(2, '0'),
            'i': String(d.getMinutes()).padStart(2, '0'),
            's': String(d.getSeconds()).padStart(2, '0')
        };
        
        return format.replace(/[Ymdis]/g, match => formats[match]);
    }
};

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => Admin.init());
} else {
    Admin.init();
}

// Export for global access
window.Admin = Admin;