<?php
session_start();
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

echo "<h1>🔧 اختبار إصلاح مشكلة user_id النهائي</h1>";

// محاكاة تسجيل دخول المستخدم
if (!isLoggedIn()) {
    $_SESSION['user_id'] = 3;
    $_SESSION['user_name'] = 'المدير العام';
    $_SESSION['user_email'] = '<EMAIL>';
    $_SESSION['user_role'] = 'super-admin';
    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; color: #856404; margin: 10px 0;'>";
    echo "⚠️ تم محاكاة تسجيل دخول المستخدم للاختبار";
    echo "</div>";
}

echo "<h2>1. فحص حالة المستخدم الحالي:</h2>";
$isLoggedIn = isLoggedIn();
$current_user = $isLoggedIn ? getCurrentUser() : null;

echo "<div style='background: " . ($isLoggedIn ? '#d4edda' : '#f8d7da') . "; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<strong>حالة تسجيل الدخول:</strong> " . ($isLoggedIn ? 'مسجل الدخول ✅' : 'غير مسجل الدخول ❌') . "<br>";
if ($isLoggedIn && $current_user) {
    echo "<strong>معرف المستخدم:</strong> " . $current_user['id'] . "<br>";
    echo "<strong>اسم المستخدم:</strong> " . $current_user['name'] . "<br>";
    echo "<strong>البريد الإلكتروني:</strong> " . $current_user['email'] . "<br>";
}
echo "</div>";

if (!$isLoggedIn) {
    echo "<p style='color: red;'>❌ لا يمكن إجراء الاختبار بدون تسجيل دخول</p>";
    exit;
}

echo "<h2>2. اختبار حفظ تقييم جديد:</h2>";

// حذف أي تقييم سابق للاختبار
try {
    global $database;
    $deleted = $database->query("DELETE FROM reviews WHERE product_id = ? AND user_id = ?", [1, $current_user['id']]);
    echo "<p>✅ تم حذف التقييمات السابقة للاختبار</p>";
} catch (Exception $e) {
    echo "<p style='color: orange;'>⚠️ تحذير: " . $e->getMessage() . "</p>";
}

// بيانات الاختبار
$test_data = [
    'product_id' => 1,
    'user_id' => $current_user['id'],
    'customer_name' => $current_user['name'],
    'customer_email' => $current_user['email'],
    'rating' => 5,
    'review_title' => 'اختبار إصلاح user_id النهائي',
    'review_text' => 'هذا تقييم لاختبار إصلاح مشكلة user_id في ' . date('Y-m-d H:i:s')
];

echo "<div style='background: #e7f3ff; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<strong>بيانات الاختبار:</strong><br>";
foreach ($test_data as $key => $value) {
    echo "• <strong>$key:</strong> $value<br>";
}
echo "</div>";

// اختبار دالة saveUserReview
echo "<h3>أ. اختبار دالة saveUserReview:</h3>";
try {
    $result = saveUserReview(
        $test_data['product_id'],
        $test_data['user_id'],
        $test_data['customer_name'],
        $test_data['customer_email'],
        $test_data['rating'],
        $test_data['review_text'],
        $test_data['review_title']
    );
    
    if (is_numeric($result) && $result > 0) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
        echo "✅ نجح حفظ التقييم! معرف التقييم: $result";
        echo "</div>";
        
        // التحقق من البيانات المحفوظة
        $saved_review = $database->fetch("SELECT * FROM reviews WHERE id = ?", [$result]);
        if ($saved_review) {
            echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "<strong>البيانات المحفوظة:</strong><br>";
            echo "• <strong>ID:</strong> " . $saved_review['id'] . "<br>";
            echo "• <strong>Product ID:</strong> " . $saved_review['product_id'] . "<br>";
            echo "• <strong style='color: " . ($saved_review['user_id'] ? 'green' : 'red') . ";'>User ID:</strong> " . ($saved_review['user_id'] ?: 'NULL') . "<br>";
            echo "• <strong>Name:</strong> " . $saved_review['name'] . "<br>";
            echo "• <strong>Email:</strong> " . $saved_review['email'] . "<br>";
            echo "• <strong>Rating:</strong> " . $saved_review['rating'] . "<br>";
            echo "• <strong>Title:</strong> " . $saved_review['title'] . "<br>";
            echo "• <strong>Comment:</strong> " . substr($saved_review['comment'], 0, 50) . "...<br>";
            echo "• <strong>Is Approved:</strong> " . ($saved_review['is_approved'] ? 'نعم' : 'لا') . "<br>";
            echo "• <strong>Created At:</strong> " . $saved_review['created_at'] . "<br>";
            echo "</div>";
            
            if ($saved_review['user_id'] == $current_user['id']) {
                echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
                echo "🎉 <strong>نجح الإصلاح!</strong> تم حفظ user_id بشكل صحيح: " . $saved_review['user_id'];
                echo "</div>";
            } else {
                echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
                echo "❌ <strong>فشل الإصلاح!</strong> user_id المحفوظ: " . ($saved_review['user_id'] ?: 'NULL') . " بدلاً من " . $current_user['id'];
                echo "</div>";
            }
        }
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
        echo "❌ فشل في حفظ التقييم: " . (is_string($result) ? $result : 'خطأ غير محدد');
        echo "</div>";
    }
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
    echo "❌ خطأ في التنفيذ: " . $e->getMessage();
    echo "</div>";
}

echo "<h2>3. اختبار API مباشرة:</h2>";

// محاكاة طلب POST
$_POST = [
    'product_id' => 1,
    'rating' => 4,
    'review_text' => 'اختبار API بعد الإصلاح',
    'review_title' => 'اختبار API'
];

echo "<div style='background: #e7f3ff; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<strong>بيانات POST المحاكاة:</strong><br>";
foreach ($_POST as $key => $value) {
    echo "• <strong>$key:</strong> $value<br>";
}
echo "</div>";

// تضمين منطق API
ob_start();
try {
    // نسخ منطق API
    $product_id = (int)$_POST['product_id'];
    $rating = (int)$_POST['rating'];
    $review_text = $_POST['review_text'];
    $review_title = $_POST['review_title'] ?? '';
    
    $user_id = $current_user['id'];
    $customer_name = $current_user['name'];
    $customer_email = $current_user['email'];
    
    // حذف أي تقييم سابق
    $database->query("DELETE FROM reviews WHERE product_id = ? AND user_id = ?", [$product_id, $user_id]);
    
    $review_id = saveUserReview($product_id, $user_id, $customer_name, $customer_email, $rating, $review_text, $review_title);
    
    if ($review_id && is_numeric($review_id)) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
        echo "✅ نجح API! معرف التقييم: $review_id";
        echo "</div>";
        
        // فحص البيانات المحفوظة
        $api_saved = $database->fetch("SELECT * FROM reviews WHERE id = ?", [$review_id]);
        if ($api_saved && $api_saved['user_id'] == $current_user['id']) {
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
            echo "🎉 <strong>API يعمل بشكل صحيح!</strong> user_id محفوظ: " . $api_saved['user_id'];
            echo "</div>";
        }
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
        echo "❌ فشل API: " . (is_string($review_id) ? $review_id : 'خطأ غير محدد');
        echo "</div>";
    }
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
    echo "❌ خطأ في API: " . $e->getMessage();
    echo "</div>";
}
$api_output = ob_get_clean();
echo $api_output;

echo "<h2>4. الخلاصة:</h2>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>✅ الإصلاحات المطبقة:</h3>";
echo "<ul>";
echo "<li>تم تعديل صفحة product_detail.php لاستخدام \$current_user بدلاً من \$_SESSION مباشرة</li>";
echo "<li>تم إضافة شرط للتأكد من وجود المستخدم قبل إرسال user_id</li>";
echo "<li>تم استخدام getCurrentUser() للحصول على بيانات المستخدم الصحيحة</li>";
echo "</ul>";

echo "<h3>🔍 النتائج المتوقعة:</h3>";
echo "<ul>";
echo "<li>عمود user_id يجب أن يحتوي على معرف المستخدم الصحيح</li>";
echo "<li>التقييمات من المستخدمين المسجلين تحفظ مع user_id صحيح</li>";
echo "<li>API يعمل بشكل صحيح مع المستخدمين المسجلين</li>";
echo "</ul>";
echo "</div>";

echo "<h2>5. اختبار الصفحة الفعلية:</h2>";
echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; color: #856404;'>";
echo "<strong>للاختبار النهائي:</strong><br>";
echo "1. اذهب إلى صفحة منتج: <a href='" . SITE_URL . "/products/1' target='_blank'>" . SITE_URL . "/products/1</a><br>";
echo "2. تأكد من تسجيل الدخول<br>";
echo "3. أضف تقييم جديد<br>";
echo "4. تحقق من أن user_id محفوظ بشكل صحيح في قاعدة البيانات<br>";
echo "</div>";
?>
