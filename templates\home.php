<?php
$pageTitle = 'الرئيسية';
$page = 'home';

// جلب البيانات المطلوبة
$heroSlides = getHeroSlides();

// جلب إعدادات فئات المنتجات
$categoriesCount = getSetting('categories_count', 6);
$showCategoriesSection = getSetting('categories_show_section', 1);

// جلب إعدادات المنتجات المميزة
$featuredProductsCount = getSetting('featured_products_count', 6);
$showFeaturedProductsSection = getSetting('featured_products_show_section', 1);

// جلب المنتجات المميزة بناءً على الإعدادات
$featuredProducts = getFeaturedProducts($featuredProductsCount);

$features = getWhyChooseUsFeatures();
$whyChooseUsSettings = getHomeWhyChooseUsSettings();
$statistics = getStatistics();

// جلب عدد التقييمات من الإعدادات
$testimonialsCount = getSetting('testimonials_count', 6);

// جلب إعداد إظهار قسم التقييمات
$showTestimonialsSection = getSetting('testimonials_show_section', 1);

// جلب فلاتر التقييمات المحفوظة
$savedFilters = getSetting('testimonials_filters', ['all']);
$filters = is_array($savedFilters) ? $savedFilters : ['all'];
if (empty($filters)) {
    $filters = ['all'];
}

// تطبيق الفلاتر وجلب التقييمات (فقط إذا كان القسم مفعل)
$testimonials = [];
if ($showTestimonialsSection) {
    foreach ($filters as $filter) {
        switch ($filter) {
            case 'all':
                $allTestimonials = getTestimonials($testimonialsCount, 'all');
                $testimonials = array_merge($testimonials, $allTestimonials);
                break;
            case 'best':
                $bestTestimonials = getTestimonials($testimonialsCount, 'best');
                $testimonials = array_merge($testimonials, $bestTestimonials);
                break;
            case 'random':
                $randomTestimonials = getTestimonials(3, 'random');
                $testimonials = array_merge($testimonials, $randomTestimonials);
                break;
        }
    }

    // إزالة التكرارات
    $uniqueTestimonials = [];
    $seenIds = [];
    foreach ($testimonials as $testimonial) {
        if (!in_array($testimonial['id'], $seenIds)) {
            $seenIds[] = $testimonial['id'];
            $uniqueTestimonials[] = $testimonial;
        }
    }
    $testimonials = array_slice($uniqueTestimonials, 0, $testimonialsCount);
}

// جلب فئات المنتجات بناءً على الإعدادات
$categories = [];
if ($showCategoriesSection) {
    $categories = getProductCategories($categoriesCount);
}

// جلب إعدادات قسم الاستشارة المجانية
$consultationSection = getConsultationSection();

include 'header.php';
?>

<!-- تضمين ملف أشكال الأيقونات -->
<link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/icon-shapes.css">

<?php
?>

<!-- Hero Section -->
<section class="relative overflow-hidden">
    <?php if (!empty($heroSlides)): ?>
        <div id="hero-slider" class="relative h-96 md:h-[500px] lg:h-[600px]">
            <?php foreach ($heroSlides as $index => $slide): ?>
                <div class="hero-slide absolute inset-0 transition-opacity duration-1000 <?php echo $index === 0 ? 'opacity-100' : 'opacity-0'; ?>" 
                     style="background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), url('<?php echo $slide['image']; ?>') center/cover;">
                    <div class="absolute inset-0 flex items-center justify-center">
                        <div class="text-center text-white max-w-4xl mx-auto px-4">
                            <h1 class="text-4xl md:text-6xl font-bold mb-6 fade-in">
                                <?php echo htmlspecialchars($slide['title']); ?>
                            </h1>
                            <p class="text-xl md:text-2xl mb-8 fade-in" style="animation-delay: 0.2s;">
                                <?php echo htmlspecialchars($slide['subtitle']); ?>
                            </p>
                            <?php if (!empty($slide['button_text']) && !empty($slide['button_link'])): ?>
                                <a href="<?php echo $slide['button_link']; ?>" 
                                   class="inline-block bg-primary hover:bg-secondary text-white font-bold py-4 px-8 rounded-lg transition-all duration-300 transform hover:scale-105 fade-in" 
                                   style="animation-delay: 0.4s;">
                                    <?php echo htmlspecialchars($slide['button_text']); ?>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
            
            <!-- Navigation Arrows -->
            <?php if (count($heroSlides) > 1): ?>
                <button id="prev-slide" class="absolute top-1/2 left-4 transform -translate-y-1/2 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-3 rounded-full transition-all duration-300">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </button>
                <button id="next-slide" class="absolute top-1/2 right-4 transform -translate-y-1/2 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-3 rounded-full transition-all duration-300">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>
                
                <!-- Slide Indicators -->
                <div class="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-2 space-x-reverse">
                    <?php foreach ($heroSlides as $index => $slide): ?>
                        <button class="slide-indicator w-3 h-3 rounded-full transition-all duration-300 <?php echo $index === 0 ? 'bg-white' : 'bg-white bg-opacity-50'; ?>" 
                                data-slide="<?php echo $index; ?>"></button>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    <?php else: ?>
        <!-- Default Hero -->
        <div class="bg-gradient-to-r from-primary to-secondary h-96 md:h-[500px] lg:h-[600px] flex items-center justify-center">
            <div class="text-center text-white max-w-4xl mx-auto px-4">
                <h1 class="text-4xl md:text-6xl font-bold mb-6">
                    مرحباً بكم في <?php echo getSetting('site_name', 'Green Line'); ?>
                </h1>
                <p class="text-xl md:text-2xl mb-8">
                    <?php echo getSetting('site_description', 'شركة رائدة في مجال المكيفات وأنظمة التبريد'); ?>
                </p>
                <a href="<?php echo SITE_URL; ?>/products" class="inline-block bg-white text-primary font-bold py-4 px-8 rounded-lg hover:bg-gray-100 transition-all duration-300 transform hover:scale-105">
                    تصفح منتجاتنا
                </a>
            </div>
        </div>
    <?php endif; ?>
</section>

<!-- Features Section -->
<?php if ($whyChooseUsSettings['is_visible'] && !empty($features)): ?>
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4"><?php echo htmlspecialchars($whyChooseUsSettings['title'] ?: 'لماذا تختارنا؟'); ?></h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto"><?php echo htmlspecialchars($whyChooseUsSettings['subtitle'] ?: 'نقدم أفضل الحلول والخدمات في مجال التكييف والتبريد'); ?></p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php 
            $maxItems = $whyChooseUsSettings['max_items'] ?? 6;
            $displayFeatures = array_slice($features, 0, $maxItems);
            foreach ($displayFeatures as $feature): ?>
                <div class="text-center p-6 rounded-lg hover:shadow-lg transition-all duration-300 transform hover:-translate-y-2" style="background-color: <?php echo htmlspecialchars($feature['background_color'] ?? 'transparent'); ?>">
                    <?php 
                    $iconShape = $feature['icon_shape'] ?? 'rounded';
                    $shapeClass = 'icon-shape-' . $iconShape;
                    ?>
                    <div class="w-16 h-16 icon-container <?php echo $shapeClass; ?> flex items-center justify-center mx-auto mb-4" style="--icon-bg-color: <?php echo htmlspecialchars($feature['icon_background_color'] ?? '#10B981'); ?>; background-color: <?php echo htmlspecialchars($feature['icon_background_color'] ?? '#10B981'); ?>">
                        <i class="<?php echo $feature['icon']; ?> text-2xl" style="color: <?php echo htmlspecialchars($feature['icon_color'] ?? '#FFFFFF'); ?>"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3"><?php echo htmlspecialchars($feature['title']); ?></h3>
                    <p class="text-gray-600"><?php echo htmlspecialchars($feature['description']); ?></p>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Product Categories -->
<?php if ($showCategoriesSection && !empty($categories)): ?>
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">فئات المنتجات</h2>
            <p class="text-xl text-gray-600">تصفح مجموعتنا المتنوعة من المنتجات</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php foreach ($categories as $category): ?>
                <a href="<?php echo SITE_URL; ?>/products?category=<?php echo $category['id']; ?>" 
                   class="group relative overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                    <div class="aspect-w-16 aspect-h-9">
                        <img src="<?php echo $category['image'] ?: '/assets/images/default-category.jpg'; ?>" 
                             alt="<?php echo htmlspecialchars($category['name']); ?>" 
                             class="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300">
                    </div>
                    <div class="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-60"></div>
                    <div class="absolute bottom-0 left-0 right-0 p-6">
                        <h3 class="text-xl font-bold text-white mb-2"><?php echo htmlspecialchars($category['name']); ?></h3>
                        <p class="text-gray-200 text-sm"><?php echo htmlspecialchars($category['description']); ?></p>
                    </div>
                </a>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Featured Products -->
<?php if ($showFeaturedProductsSection && !empty($featuredProducts)): ?>
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">المنتجات المميزة</h2>
            <p class="text-xl text-gray-600">اكتشف أحدث وأفضل منتجاتنا</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php foreach ($featuredProducts as $product): ?>
                <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="relative">
                        <img src="<?php echo !empty($product['image']) ? $product['image'] : '/assets/images/default-product.svg'; ?>" 
                             alt="<?php echo htmlspecialchars($product['name'] ?? ''); ?>" 
                             class="w-full h-48 object-cover">
                        <?php if (!empty($product['is_featured'])): ?>
                            <span class="absolute top-2 right-2 bg-primary text-white px-2 py-1 rounded-full text-xs font-semibold">
                                مميز
                            </span>
                        <?php endif; ?>
                    </div>
                    
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">
                            <?php echo htmlspecialchars($product['name'] ?? ''); ?>
                        </h3>
                        <p class="text-gray-600 mb-4 line-clamp-2">
                            <?php 
                            $description = $product['description'] ?? '';
                            echo htmlspecialchars(strlen($description) > 100 ? substr($description, 0, 100) . '...' : $description); 
                            ?>
                        </p>
                        
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <?php if (!empty($product['rating']) && $product['rating'] > 0): ?>
                                    <div class="flex items-center ml-2">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <svg class="w-4 h-4 <?php echo $i <= $product['rating'] ? 'text-yellow-400' : 'text-gray-300'; ?>" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                            </svg>
                                        <?php endfor; ?>
                                        <span class="text-sm text-gray-500 mr-1">(<?php echo $product['rating'] ?? 0; ?>)</span>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <a href="<?php echo SITE_URL; ?>/products/<?php echo $product['id'] ?? '#'; ?>" 
                               class="bg-primary hover:bg-secondary text-white px-4 py-2 rounded-lg transition-colors duration-300">
                                عرض التفاصيل
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <div class="text-center mt-12">
            <a href="<?php echo SITE_URL; ?>/products" 
               class="inline-block bg-primary hover:bg-secondary text-white font-bold py-3 px-8 rounded-lg transition-all duration-300 transform hover:scale-105">
                عرض جميع المنتجات
            </a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Statistics Section -->
<?php if (!empty($statistics)): ?>
<section class="py-16 bg-primary text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <?php foreach ($statistics as $stat): ?>
                <div class="text-center">
                    <div class="text-4xl md:text-5xl font-bold mb-2 counter" data-target="<?php echo $stat['value']; ?>">
                        0
                    </div>
                    <div class="text-xl font-semibold mb-1"><?php echo htmlspecialchars($stat['title']); ?></div>
                    <div class="text-sm opacity-80"><?php echo isset($stat['description']) ? htmlspecialchars($stat['description']) : ''; ?></div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Testimonials Section -->
<?php if ($showTestimonialsSection && !empty($testimonials)): ?>
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">آراء عملائنا</h2>
            <p class="text-xl text-gray-600">ماذا يقول عملاؤنا عن خدماتنا</p>
            

        </div>
        
        <div id="testimonials-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php foreach ($testimonials as $testimonial): ?>
                <div class="testimonial-card bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300" 
                     data-rating="<?php echo $testimonial['rating']; ?>">
                    <div class="flex items-center mb-4">
                        <?php for ($i = 1; $i <= 5; $i++): ?>
                            <svg class="w-5 h-5 <?php echo $i <= $testimonial['rating'] ? 'text-yellow-400' : 'text-gray-300'; ?>" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                        <?php endfor; ?>
                    </div>
                    
                    <p class="text-gray-600 mb-4 italic">
                        "<?php echo htmlspecialchars($testimonial['content']); ?>"
                    </p>
                    
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-primary bg-opacity-10 rounded-full flex items-center justify-center ml-3">
                            <?php if (!empty($testimonial['image'])): ?>
                                <img src="<?php echo htmlspecialchars($testimonial['image']); ?>" 
                                     alt="<?php echo htmlspecialchars($testimonial['name']); ?>" 
                                     class="w-full h-full rounded-full object-cover">
                            <?php else: ?>
                                <i class="fa-solid fa-user text-white text-lg"></i>
                            <?php endif; ?>
                        </div>
                        <div>
                            <div class="font-semibold text-gray-900"><?php echo htmlspecialchars($testimonial['name']); ?></div>
                            <div class="text-sm text-gray-500"><?php echo htmlspecialchars($testimonial['position']); ?></div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Call to Action Section -->
<?php if ($consultationSection['is_active']): ?>
<section class="py-16 text-white" style="background-color: <?php echo htmlspecialchars($consultationSection['background_color']); ?>; color: <?php echo htmlspecialchars($consultationSection['text_color']); ?>">
    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl md:text-4xl font-bold mb-4" style="color: <?php echo htmlspecialchars($consultationSection['text_color']); ?>">
            <?php echo htmlspecialchars($consultationSection['title']); ?>
        </h2>
        <?php if (!empty($consultationSection['subtitle'])): ?>
            <p class="text-xl mb-4 opacity-90" style="color: <?php echo htmlspecialchars($consultationSection['text_color']); ?>">
                <?php echo htmlspecialchars($consultationSection['subtitle']); ?>
            </p>
        <?php endif; ?>
        <?php if (!empty($consultationSection['description'])): ?>
            <p class="text-lg mb-8 opacity-80" style="color: <?php echo htmlspecialchars($consultationSection['text_color']); ?>">
                <?php echo htmlspecialchars($consultationSection['description']); ?>
            </p>
        <?php endif; ?>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <?php if (!empty($consultationSection['button_text']) && !empty($consultationSection['button_link'])): ?>
                <a href="<?php echo htmlspecialchars($consultationSection['button_link']); ?>" 
                   class="inline-block bg-white text-primary font-bold py-3 px-8 rounded-lg hover:bg-gray-100 transition-all duration-300 transform hover:scale-105">
                    <?php echo htmlspecialchars($consultationSection['button_text']); ?>
                </a>
            <?php endif; ?>
            <?php if (getSetting('whatsapp_number')): ?>
                <a href="https://wa.me/<?php echo getSetting('whatsapp_number'); ?>" 
                   target="_blank"
                   class="inline-block bg-transparent border-2 border-white font-bold py-3 px-8 rounded-lg hover:bg-white hover:text-primary transition-all duration-300 transform hover:scale-105" 
                   style="border-color: <?php echo htmlspecialchars($consultationSection['text_color']); ?>; color: <?php echo htmlspecialchars($consultationSection['text_color']); ?>">
                    واتساب
                </a>
            <?php endif; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<script>
// Hero slider functionality
document.addEventListener('DOMContentLoaded', function() {
    const slides = document.querySelectorAll('.hero-slide');
    const indicators = document.querySelectorAll('.slide-indicator');
    const prevButton = document.getElementById('prev-slide');
    const nextButton = document.getElementById('next-slide');
    let currentSlide = 0;
    let slideInterval;
    
    if (slides.length > 1) {
        function showSlide(index) {
            slides.forEach((slide, i) => {
                slide.classList.toggle('opacity-100', i === index);
                slide.classList.toggle('opacity-0', i !== index);
            });
            
            indicators.forEach((indicator, i) => {
                indicator.classList.toggle('bg-white', i === index);
                indicator.classList.toggle('bg-white', i !== index);
                indicator.classList.toggle('bg-opacity-50', i !== index);
            });
        }
        
        function nextSlide() {
            currentSlide = (currentSlide + 1) % slides.length;
            showSlide(currentSlide);
        }
        
        function prevSlide() {
            currentSlide = (currentSlide - 1 + slides.length) % slides.length;
            showSlide(currentSlide);
        }
        
        function startSlideshow() {
            slideInterval = setInterval(nextSlide, 5000);
        }
        
        function stopSlideshow() {
            clearInterval(slideInterval);
        }
        
        // Event listeners
        if (nextButton) nextButton.addEventListener('click', () => { stopSlideshow(); nextSlide(); startSlideshow(); });
        if (prevButton) prevButton.addEventListener('click', () => { stopSlideshow(); prevSlide(); startSlideshow(); });
        
        indicators.forEach((indicator, index) => {
            indicator.addEventListener('click', () => {
                stopSlideshow();
                currentSlide = index;
                showSlide(currentSlide);
                startSlideshow();
            });
        });
        
        // Start slideshow
        startSlideshow();
        
        // Pause on hover
        const heroSlider = document.getElementById('hero-slider');
        if (heroSlider) {
            heroSlider.addEventListener('mouseenter', stopSlideshow);
            heroSlider.addEventListener('mouseleave', startSlideshow);
        }
    }
    
    // Counter animation
    const counters = document.querySelectorAll('.counter');
    const observerOptions = {
        threshold: 0.5,
        rootMargin: '0px 0px -100px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const counter = entry.target;
                const target = parseInt(counter.dataset.target);
                const duration = 2000;
                const step = target / (duration / 16);
                let current = 0;
                
                const timer = setInterval(() => {
                    current += step;
                    if (current >= target) {
                        counter.textContent = target.toLocaleString('ar-SA');
                        clearInterval(timer);
                    } else {
                        counter.textContent = Math.floor(current).toLocaleString('ar-SA');
                    }
                }, 16);
                
                observer.unobserve(counter);
            }
        });
    }, observerOptions);
    
    counters.forEach(counter => observer.observe(counter));
    
});


</script>

<?php include __DIR__ . '/footer.php'; ?>