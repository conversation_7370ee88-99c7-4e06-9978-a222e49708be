<?php
require_once 'config/config.php';

echo "<h2>فحص المنتجات الموجودة</h2>";

try {
    global $database;
    $products = $database->fetchAll('SELECT id, name, is_active, is_visible FROM products ORDER BY id LIMIT 10');
    
    if (!empty($products)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>اسم المنتج</th><th>نشط</th><th>مرئي</th></tr>";
        
        foreach ($products as $product) {
            echo "<tr>";
            echo "<td>" . $product['id'] . "</td>";
            echo "<td>" . htmlspecialchars($product['name']) . "</td>";
            echo "<td>" . ($product['is_active'] ? 'نعم' : 'لا') . "</td>";
            echo "<td>" . ($product['is_visible'] ? 'نعم' : 'لا') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h3>أول منتج نشط ومرئي:</h3>";
        $activeProduct = null;
        foreach ($products as $product) {
            if ($product['is_active'] && $product['is_visible']) {
                $activeProduct = $product;
                break;
            }
        }
        
        if ($activeProduct) {
            echo "<p><strong>المنتج المقترح للاختبار:</strong> ID " . $activeProduct['id'] . " - " . htmlspecialchars($activeProduct['name']) . "</p>";
        } else {
            echo "<p style='color: red;'>لا يوجد منتج نشط ومرئي للاختبار</p>";
        }
        
    } else {
        echo "<p>لا توجد منتجات في قاعدة البيانات</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
