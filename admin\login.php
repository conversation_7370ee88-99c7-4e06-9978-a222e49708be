<?php
require_once '../config/config.php';
require_once '../includes/auth.php';

// Redirect if already logged in
if (isLoggedIn() && hasPermission('admin')) {
    redirect('/admin/');
}

// Redirect to unified auth system
redirect(SITE_URL . '/auth/login?redirect=' . urlencode(SITE_URL . '/admin'));

$error = '';
$success = '';

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']);
    
    if (empty($email) || empty($password)) {
        $error = 'يرجى إدخال البريد الإلكتروني وكلمة المرور';
    } else {
        $result = loginUser($email, $password, $remember);
        
        if ($result['success']) {
            // Check if user has admin permission
            if (hasPermission('admin')) {
                logActivity('admin_login', 'Admin logged in: ' . $email);
                redirect('/admin/');
            } else {
                logout();
                $error = 'ليس لديك صلاحية للوصول إلى لوحة التحكم';
            }
        } else {
            $error = $result['message'];
            logActivity('admin_login_failed', 'Failed admin login attempt: ' . $email);
        }
    }
}

// Handle password reset request
if (isset($_GET['action']) && $_GET['action'] === 'reset' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['reset_email'] ?? '');
    
    if (empty($email)) {
        $error = 'يرجى إدخال البريد الإلكتروني';
    } else {
        $result = requestPasswordReset($email);
        if ($result['success']) {
            $success = 'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني';
        } else {
            $error = $result['message'];
        }
    }
}

$pageTitle = 'تسجيل الدخول - لوحة التحكم';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo getSetting('site_name'); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }
        .login-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .glass-effect {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
    </style>
</head>
<body class="login-bg min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <!-- Logo and Header -->
        <div class="text-center">
            <div class="mx-auto h-20 w-20 bg-white rounded-full flex items-center justify-center shadow-lg">
                <svg class="h-10 w-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
            </div>
            <h2 class="mt-6 text-3xl font-bold text-white">
                لوحة التحكم
            </h2>
            <p class="mt-2 text-sm text-white opacity-80">
                <?php echo getSetting('site_name'); ?>
            </p>
        </div>

        <!-- Login Form -->
        <div class="glass-effect rounded-lg shadow-xl p-8">
            <?php if ($error): ?>
            <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                <span class="block sm:inline"><?php echo htmlspecialchars($error); ?></span>
            </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
            <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                <span class="block sm:inline"><?php echo htmlspecialchars($success); ?></span>
            </div>
            <?php endif; ?>

            <?php if (!isset($_GET['action']) || $_GET['action'] !== 'reset'): ?>
            <!-- Login Form -->
            <form method="POST" action="" class="space-y-6">
                <div>
                    <label for="email" class="block text-sm font-medium text-white mb-2">
                        البريد الإلكتروني
                    </label>
                    <input id="email" name="email" type="email" required 
                           class="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                           placeholder="أدخل بريدك الإلكتروني"
                           value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>">
                </div>
                
                <div>
                    <label for="password" class="block text-sm font-medium text-white mb-2">
                        كلمة المرور
                    </label>
                    <input id="password" name="password" type="password" required 
                           class="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                           placeholder="أدخل كلمة المرور">
                </div>
                
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input id="remember" name="remember" type="checkbox" 
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="remember" class="mr-2 block text-sm text-white">
                            تذكرني
                        </label>
                    </div>
                    
                    <div class="text-sm">
                        <a href="?action=reset" class="font-medium text-white hover:text-blue-200">
                            نسيت كلمة المرور؟
                        </a>
                    </div>
                </div>
                
                <div>
                    <button type="submit" 
                            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out">
                        <span class="absolute right-0 inset-y-0 flex items-center pr-3">
                            <svg class="h-5 w-5 text-blue-500 group-hover:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                            </svg>
                        </span>
                        تسجيل الدخول
                    </button>
                </div>
            </form>
            
            <?php else: ?>
            <!-- Password Reset Form -->
            <form method="POST" action="?action=reset" class="space-y-6">
                <div class="text-center mb-6">
                    <h3 class="text-lg font-medium text-white">إعادة تعيين كلمة المرور</h3>
                    <p class="text-sm text-white opacity-80 mt-2">
                        أدخل بريدك الإلكتروني وسنرسل لك رابط إعادة تعيين كلمة المرور
                    </p>
                </div>
                
                <div>
                    <label for="reset_email" class="block text-sm font-medium text-white mb-2">
                        البريد الإلكتروني
                    </label>
                    <input id="reset_email" name="reset_email" type="email" required 
                           class="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                           placeholder="أدخل بريدك الإلكتروني">
                </div>
                
                <div class="flex space-x-4 space-x-reverse">
                    <button type="submit" 
                            class="flex-1 flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out">
                        إرسال الرابط
                    </button>
                    
                    <a href="login.php" 
                       class="flex-1 flex justify-center py-2 px-4 border border-white text-sm font-medium rounded-md text-white hover:bg-white hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white transition duration-150 ease-in-out">
                        العودة
                    </a>
                </div>
            </form>
            <?php endif; ?>
        </div>
        
        <!-- Footer -->
        <div class="text-center">
            <p class="text-white opacity-60 text-sm">
                &copy; <?php echo date('Y'); ?> <?php echo getSetting('site_name'); ?>. جميع الحقوق محفوظة.
            </p>
        </div>
    </div>
    
    <script>
        // Auto-focus on first input
        document.addEventListener('DOMContentLoaded', function() {
            const firstInput = document.querySelector('input[type="email"]');
            if (firstInput) {
                firstInput.focus();
            }
        });
        
        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const email = document.getElementById('email')?.value;
            const password = document.getElementById('password')?.value;
            const resetEmail = document.getElementById('reset_email')?.value;
            
            if (email !== undefined && password !== undefined) {
                // Login form validation
                if (!email.trim() || !password.trim()) {
                    e.preventDefault();
                    alert('يرجى إدخال البريد الإلكتروني وكلمة المرور');
                    return;
                }
                
                if (!isValidEmail(email)) {
                    e.preventDefault();
                    alert('يرجى إدخال بريد إلكتروني صحيح');
                    return;
                }
            } else if (resetEmail !== undefined) {
                // Reset form validation
                if (!resetEmail.trim()) {
                    e.preventDefault();
                    alert('يرجى إدخال البريد الإلكتروني');
                    return;
                }
                
                if (!isValidEmail(resetEmail)) {
                    e.preventDefault();
                    alert('يرجى إدخال بريد إلكتروني صحيح');
                    return;
                }
            }
        });
        
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
        
        // Show loading state on form submission
        document.querySelector('form').addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            
            submitBtn.textContent = 'جاري التحميل...';
            submitBtn.disabled = true;
            
            // Re-enable button after 5 seconds (in case of error)
            setTimeout(() => {
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }, 5000);
        });
        
        // Hide alerts after 5 seconds
        setTimeout(() => {
            const alerts = document.querySelectorAll('[role="alert"]');
            alerts.forEach(alert => {
                alert.style.transition = 'opacity 0.5s';
                alert.style.opacity = '0';
                setTimeout(() => {
                    alert.remove();
                }, 500);
            });
        }, 5000);
    </script>
</body>
</html>