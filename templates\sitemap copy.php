<?php
/**
 * Sitemap Page Template
 * Displays a comprehensive site map with all pages and sections
 */

require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/LegalPageManager.php';

// التأكد من توفر متغير قاعدة البيانات
global $database;
if (!isset($database)) {
    require_once 'config/database.php';
}

// إنشاء مدير الصفحات القانونية
$legalManager = new LegalPageManager($database);

// جلب بيانات صفحة خريطة الموقع
$pageData = $legalManager->getPage('sitemap');

// تعيين meta tags ديناميكية
$page_title = $pageData['title'];
$meta_description = $pageData['meta_description'];
$meta_keywords = $pageData['meta_keywords'];
$page = 'sitemap';

include 'header.php'; 
?>

<!-- Page Title Section -->
<section class="bg-primary py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-bold text-white mb-4"><?php echo htmlspecialchars($pageData['title']); ?></h1>
            <p class="text-xl text-white/90"><?php echo htmlspecialchars($pageData['meta_description']); ?></p>
        </div>
    </div>
</section>

<!-- Sitemap Content -->
<section class="py-16 bg-gray-50">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white rounded-lg shadow-lg p-8">
            
            <!-- المحتوى الديناميكي من قاعدة البيانات -->
            <div class="prose prose-lg max-w-none">
                <?php echo $pageData['content']; ?>
            </div>
            
            <!-- تاريخ آخر تحديث -->
            <?php if (isset($pageData['updated_at'])): ?>
            <div class="mt-8 pt-6 border-t border-gray-200">
                <p class="text-sm text-gray-500">
                    آخر تحديث: <?php echo date('d/m/Y', strtotime($pageData['updated_at'])); ?>
                </p>
            </div>
            <?php endif; ?>
        </div>
        
        <!-- روابط ذات صلة -->
        <div class="mt-8 bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">صفحات ذات صلة</h3>
            <div class="grid md:grid-cols-2 gap-4">
                <a href="terms.php" class="block p-4 border border-gray-200 rounded-lg hover:border-green-500 hover:bg-green-50 transition-colors">
                    <h4 class="font-medium text-gray-900">الشروط والأحكام</h4>
                    <p class="text-sm text-gray-600 mt-1">اطلع على شروط استخدام الموقع</p>
                </a>
                <a href="privacy.php" class="block p-4 border border-gray-200 rounded-lg hover:border-green-500 hover:bg-green-50 transition-colors">
                    <h4 class="font-medium text-gray-900">سياسة الخصوصية</h4>
                    <p class="text-sm text-gray-600 mt-1">تعرف على كيفية حماية بياناتك</p>
                </a>
            </div>
        </div>
    </div>
</section>

<!-- JavaScript for search functionality -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('input[placeholder="ابحث عن صفحة أو منتج..."]');
    const searchButton = document.querySelector('.fa-search').parentElement;
    
    function performSearch() {
        const query = searchInput.value.trim();
        if (query) {
            // Redirect to search results or filter current page
            window.location.href = '<?php echo SITE_URL; ?>/search?q=' + encodeURIComponent(query);
        }
    }
    
    searchButton.addEventListener('click', performSearch);
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    });
});
</script>

<?php include 'footer.php'; ?>