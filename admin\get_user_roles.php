<?php
/**
 * API لجلب أدوار المستخدم
 * Get User Roles API
 */

// منع عرض أخطاء PHP
error_reporting(0);
ini_set('display_errors', 0);

// تنظيف أي مخارج سابقة
ob_clean();

require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/middleware.php';

// التحقق من تسجيل الدخول أولاً
if (!isLoggedIn()) {
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'success' => false,
        'message' => 'يجب تسجيل الدخول أولاً'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// التحقق من صلاحية إدارة المستخدمين
if (!hasPermission('manage-users')) {
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'success' => false,
        'message' => 'ليس لديك صلاحية للوصول إلى هذه الصفحة'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

header('Content-Type: application/json');

try {
    $userId = $_GET['user_id'] ?? null;
    
    if (!$userId) {
        throw new Exception('معرف المستخدم مطلوب');
    }
    
    // جلب معلومات المستخدم
    $stmt = $database->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        throw new Exception('المستخدم غير موجود');
    }
    
    // جلب أدوار المستخدم
    $stmt = $database->prepare("
        SELECT r.* 
        FROM roles r 
        JOIN user_roles ur ON r.id = ur.role_id 
        WHERE ur.user_id = ?
        ORDER BY r.name
    ");
    $stmt->execute([$userId]);
    $roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'user' => $user,
        'roles' => $roles
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>