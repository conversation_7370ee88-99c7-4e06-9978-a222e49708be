<?php
/**
 * صفحة المصادقة
 * Authentication Page
 */

// التحقق من الإجراء المطلوب
$auth_action = isset($action) ? $action : 'login';

// معالجة طلبات POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($auth_action === 'login') {
        handleLogin();
    } elseif ($auth_action === 'register') {
        handleRegister();
    } elseif ($auth_action === 'forgot-password') {
        handleForgotPassword();
    }
}

// عرض النموذج المناسب
switch ($auth_action) {
    case 'login':
        showLoginForm();
        break;
    case 'register':
        showRegisterForm();
        break;
    case 'forgot-password':
        showForgotPasswordForm();
        break;
    case 'logout':
        handleLogout();
        break;
    default:
        showLoginForm();
        break;
}

/**
 * معالجة تسجيل الدخول
 */
function handleLogin() {
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']);
    
    if (empty($email) || empty($password)) {
        $error = 'يرجى إدخال البريد الإلكتروني وكلمة المرور';
        showLoginForm($error);
        return;
    }
    
    if (login($email, $password, $remember)) {
        // تحديد وجهة التحويل بناءً على دور المستخدم
        $user = getCurrentUser();
        $defaultRedirect = SITE_URL;
        
        if ($user && ($user['role'] === 'admin' || $user['role'] === 'super-admin' || hasPermission('admin'))) {
            $defaultRedirect = SITE_URL . '/admin';
        }
        
        $redirect = $_GET['redirect'] ?? $defaultRedirect;
        redirect($redirect);
    } else {
        $error = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
        showLoginForm($error);
    }
}

/**
 * معالجة التسجيل
 */
function handleRegister() {
    $name = $_POST['name'] ?? '';
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    
    $errors = [];
    
    if (empty($name)) {
        $errors[] = 'يرجى إدخال الاسم';
    }
    
    if (empty($email)) {
        $errors[] = 'يرجى إدخال البريد الإلكتروني';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'البريد الإلكتروني غير صحيح';
    }
    
    if (empty($password)) {
        $errors[] = 'يرجى إدخال كلمة المرور';
    } elseif (strlen($password) < 6) {
        $errors[] = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }
    
    if ($password !== $confirm_password) {
        $errors[] = 'كلمة المرور وتأكيد كلمة المرور غير متطابقتان';
    }
    
    if (!empty($errors)) {
        showRegisterForm($errors);
        return;
    }
    
    if (register($name, $email, $password)) {
        $success = 'تم إنشاء الحساب بنجاح. يمكنك الآن تسجيل الدخول.';
        showLoginForm(null, $success);
    } else {
        $errors[] = 'حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى.';
        showRegisterForm($errors);
    }
}

/**
 * معالجة نسيان كلمة المرور
 */
function handleForgotPassword() {
    $email = $_POST['email'] ?? '';
    
    if (empty($email)) {
        $error = 'يرجى إدخال البريد الإلكتروني';
        showForgotPasswordForm($error);
        return;
    }
    
    if (resetPassword($email)) {
        $success = 'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني';
        showForgotPasswordForm(null, $success);
    } else {
        $error = 'البريد الإلكتروني غير موجود';
        showForgotPasswordForm($error);
    }
}



/**
 * عرض نموذج تسجيل الدخول
 */
function showLoginForm($error = null, $success = null) {
    include 'header.php';
    ?>
    <div class="min-h-screen flex items-center justify-center auth-gradient py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <div class="bg-white rounded-2xl card-shadow p-8 animate-fade-in">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-2">تسجيل الدخول</h2>
                    <p class="text-gray-600">مرحباً بك مرة أخرى</p>
                </div>
                
                <?php if ($error): ?>
                    <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-6">
                        <?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" class="space-y-6">
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                        <input type="email" id="email" name="email" required 
                               value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none input-focus transition-all duration-200 text-right">
                    </div>
                    
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور</label>
                        <input type="password" id="password" name="password" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none input-focus transition-all duration-200 text-right">
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" name="remember" value="1" id="remember" 
                               class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                        <label for="remember" class="mr-2 block text-sm text-gray-700 cursor-pointer">تذكرني</label>
                    </div>
                    
                    <button type="submit" 
                            class="w-full py-3 px-4 btn-gradient text-white font-semibold rounded-lg transition-all duration-200 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                        تسجيل الدخول
                    </button>
                </form>
                
                <div class="text-center mt-6 space-y-2">
                    <a href="<?php echo SITE_URL; ?>/auth/forgot-password" 
                       class="text-indigo-600 hover:text-indigo-500 text-sm font-medium transition-colors duration-200">نسيت كلمة المرور؟</a>
                    <br>
                    <a href="<?php echo SITE_URL; ?>/auth/register" 
                       class="text-indigo-600 hover:text-indigo-500 text-sm font-medium transition-colors duration-200">إنشاء حساب جديد</a>
                </div>
            </div>
        </div>
    </div>
    
    <style>
    /* Custom styles for enhanced Tailwind components */
    .auth-gradient {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .btn-gradient {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .btn-gradient:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        transform: translateY(-1px);
    }
    
    .input-focus:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    
    .card-shadow {
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }
    
    .animate-fade-in {
        animation: fadeIn 0.6s ease-in-out;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
    </style>
    
    <?php
    include 'footer.php';
}

/**
 * عرض نموذج التسجيل
 */
function showRegisterForm($errors = []) {
    include 'header.php';
    ?>
    <div class="min-h-screen flex items-center justify-center auth-gradient py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <div class="bg-white rounded-2xl card-shadow p-8 animate-fade-in">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-2">إنشاء حساب جديد</h2>
                    <p class="text-gray-600">انضم إلينا اليوم</p>
                </div>
                
                <?php if (!empty($errors)): ?>
                    <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
                        <?php foreach ($errors as $error): ?>
                            <div class="mb-1"><?php echo htmlspecialchars($error); ?></div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" class="space-y-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل</label>
                        <input type="text" id="name" name="name" required 
                               value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none input-focus transition-all duration-200 text-right">
                    </div>
                    
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                        <input type="email" id="email" name="email" required 
                               value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none input-focus transition-all duration-200 text-right">
                    </div>
                    
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور</label>
                        <input type="password" id="password" name="password" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none input-focus transition-all duration-200 text-right">
                    </div>
                    
                    <div>
                        <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-2">تأكيد كلمة المرور</label>
                        <input type="password" id="confirm_password" name="confirm_password" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none input-focus transition-all duration-200 text-right">
                    </div>
                    
                    <button type="submit" 
                            class="w-full py-3 px-4 btn-gradient text-white font-semibold rounded-lg transition-all duration-200 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                        إنشاء الحساب
                    </button>
                </form>
                
                <div class="text-center mt-6">
                    <a href="<?php echo SITE_URL; ?>/auth/login" 
                       class="text-indigo-600 hover:text-indigo-500 text-sm font-medium transition-colors duration-200">لديك حساب بالفعل؟ سجل الدخول</a>
                </div>
            </div>
        </div>
    </div>
    
    <style>
    /* Custom styles for enhanced Tailwind components */
    .auth-gradient {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .btn-gradient {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .btn-gradient:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        transform: translateY(-1px);
    }
    
    .input-focus:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    
    .card-shadow {
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }
    
    .animate-fade-in {
        animation: fadeIn 0.6s ease-in-out;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
    </style>
    
    <?php
    include 'footer.php';
}

/**
 * عرض نموذج نسيان كلمة المرور
 */
function showForgotPasswordForm($error = null, $success = null) {
    include 'header.php';
    ?>
    <div class="min-h-screen flex items-center justify-center auth-gradient py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <div class="bg-white rounded-2xl card-shadow p-8 animate-fade-in">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-2">نسيت كلمة المرور؟</h2>
                    <p class="text-gray-600">سنرسل لك رابط إعادة تعيين كلمة المرور</p>
                </div>
                
                <?php if ($error): ?>
                    <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-6">
                        <?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" class="space-y-6">
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                        <input type="email" id="email" name="email" required 
                               value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none input-focus transition-all duration-200 text-right">
                    </div>
                    
                    <button type="submit" 
                            class="w-full py-3 px-4 btn-gradient text-white font-semibold rounded-lg transition-all duration-200 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                        إرسال رابط الإعادة
                    </button>
                </form>
                
                <div class="text-center mt-6">
                    <a href="<?php echo SITE_URL; ?>/auth/login" 
                       class="text-indigo-600 hover:text-indigo-500 text-sm font-medium transition-colors duration-200">العودة لتسجيل الدخول</a>
                </div>
            </div>
        </div>
    </div>
    
    <style>
    /* Custom styles for enhanced Tailwind components */
    .auth-gradient {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .btn-gradient {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .btn-gradient:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        transform: translateY(-1px);
    }
    
    .input-focus:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    
    .card-shadow {
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }
    
    .animate-fade-in {
        animation: fadeIn 0.6s ease-in-out;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
    </style>
    
    <?php
    include 'footer.php';
}
?>