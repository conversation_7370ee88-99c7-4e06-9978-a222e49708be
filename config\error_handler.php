<?php
/**
 * Enhanced <PERSON>rro<PERSON> Hand<PERSON> and <PERSON><PERSON>
 * Provides better error tracking and debugging capabilities
 */

class ErrorHandler {
    private static $logFile;
    private static $isProduction = false;
    
    public static function init($logFile = null, $isProduction = false) {
        self::$logFile = $logFile ?: __DIR__ . '/../logs/error.log';
        self::$isProduction = $isProduction;
        
        // Ensure log directory exists
        $logDir = dirname(self::$logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        // Set custom error and exception handlers
        set_error_handler([self::class, 'handleError']);
        set_exception_handler([self::class, 'handleException']);
        register_shutdown_function([self::class, 'handleFatalError']);
    }
    
    public static function handleError($severity, $message, $file, $line) {
        if (!(error_reporting() & $severity)) {
            return false;
        }
        
        $errorInfo = [
            'type' => 'Error',
            'severity' => self::getSeverityName($severity),
            'message' => $message,
            'file' => $file,
            'line' => $line,
            'timestamp' => date('Y-m-d H:i:s'),
            'url' => $_SERVER['REQUEST_URI'] ?? 'CLI',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'CLI'
        ];
        
        self::logError($errorInfo);
        
        if (!self::$isProduction) {
            self::displayError($errorInfo);
        }
        
        return true;
    }
    
    public static function handleException($exception) {
        $errorInfo = [
            'type' => 'Exception',
            'class' => get_class($exception),
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
            'timestamp' => date('Y-m-d H:i:s'),
            'url' => $_SERVER['REQUEST_URI'] ?? 'CLI',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'CLI'
        ];
        
        self::logError($errorInfo);
        
        if (!self::$isProduction) {
            self::displayError($errorInfo);
        } else {
            echo "An error occurred. Please try again later.";
        }
    }
    
    public static function handleFatalError() {
        $error = error_get_last();
        if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
            $errorInfo = [
                'type' => 'Fatal Error',
                'message' => $error['message'],
                'file' => $error['file'],
                'line' => $error['line'],
                'timestamp' => date('Y-m-d H:i:s'),
                'url' => $_SERVER['REQUEST_URI'] ?? 'CLI'
            ];
            
            self::logError($errorInfo);
        }
    }
    
    private static function logError($errorInfo) {
        $logEntry = sprintf(
            "[%s] %s: %s in %s:%d\n",
            $errorInfo['timestamp'],
            $errorInfo['type'],
            $errorInfo['message'],
            $errorInfo['file'],
            $errorInfo['line']
        );
        
        if (isset($errorInfo['trace'])) {
            $logEntry .= "Stack trace:\n" . $errorInfo['trace'] . "\n";
        }
        
        $logEntry .= "URL: " . $errorInfo['url'] . "\n";
        $logEntry .= str_repeat('-', 80) . "\n";
        
        file_put_contents(self::$logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
    
    private static function displayError($errorInfo) {
        if (php_sapi_name() === 'cli') {
            echo "\n" . $errorInfo['type'] . ": " . $errorInfo['message'] . "\n";
            echo "File: " . $errorInfo['file'] . ":" . $errorInfo['line'] . "\n\n";
        } else {
            echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; margin: 10px; border: 1px solid #f5c6cb; border-radius: 4px;'>";
            echo "<h4>" . htmlspecialchars($errorInfo['type']) . "</h4>";
            echo "<p><strong>Message:</strong> " . htmlspecialchars($errorInfo['message']) . "</p>";
            echo "<p><strong>File:</strong> " . htmlspecialchars($errorInfo['file']) . ":" . $errorInfo['line'] . "</p>";
            if (isset($errorInfo['trace'])) {
                echo "<details><summary>Stack Trace</summary><pre>" . htmlspecialchars($errorInfo['trace']) . "</pre></details>";
            }
            echo "</div>";
        }
    }
    
    private static function getSeverityName($severity) {
        $severities = [
            E_ERROR => 'E_ERROR',
            E_WARNING => 'E_WARNING',
            E_PARSE => 'E_PARSE',
            E_NOTICE => 'E_NOTICE',
            E_CORE_ERROR => 'E_CORE_ERROR',
            E_CORE_WARNING => 'E_CORE_WARNING',
            E_COMPILE_ERROR => 'E_COMPILE_ERROR',
            E_COMPILE_WARNING => 'E_COMPILE_WARNING',
            E_USER_ERROR => 'E_USER_ERROR',
            E_USER_WARNING => 'E_USER_WARNING',
            E_USER_NOTICE => 'E_USER_NOTICE',
            E_STRICT => 'E_STRICT',
            E_RECOVERABLE_ERROR => 'E_RECOVERABLE_ERROR',
            E_DEPRECATED => 'E_DEPRECATED',
            E_USER_DEPRECATED => 'E_USER_DEPRECATED'
        ];
        
        return $severities[$severity] ?? 'UNKNOWN';
    }
    
    public static function logCustom($message, $level = 'INFO') {
        $logEntry = sprintf(
            "[%s] %s: %s\n",
            date('Y-m-d H:i:s'),
            $level,
            $message
        );
        
        file_put_contents(self::$logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
}

// Initialize error handler
ErrorHandler::init();
?>