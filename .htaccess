# Green Line PHP Application - Simple URL Rewriting
RewriteEngine On

# Prevent access to sensitive files
<FilesMatch "\.(env|log|sql|md|txt|json)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Prevent access to config and includes directories
RewriteRule ^(config|includes)/ - [F,L]

# API Routes - Direct to api folder
RewriteRule ^api/(.*)$ api/$1 [L,QSA]

# Admin Routes - Direct to admin folder
RewriteRule ^admin/?$ admin/index.php [L,QSA]
RewriteRule ^admin/(.*)$ admin/$1 [L,QSA]

# Public Routes - Handle through main index.php
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/(api|admin|assets|uploads)/
RewriteRule ^(.*)$ index.php [L,QSA]

# Error Pages
ErrorDocument 404 /public/404.php
ErrorDocument 403 /public/404.php
ErrorDocument 500 /public/404.php

# Disable server signature
ServerSignature Off