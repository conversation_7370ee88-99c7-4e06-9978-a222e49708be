<?php
/**
 * إنشاء الجداول المطلوبة للنظام
 * Create required tables for the system
 */
require_once __DIR__ . '/../config/config.php';

try {
    // إنشاء جدول warranty_plans إذا لم يكن موجوداً
    $sql = "CREATE TABLE IF NOT EXISTS warranty_plans (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        price VARCHAR(100),
        features TEXT,
        is_featured TINYINT(1) DEFAULT 0,
        is_popular TINYINT(1) DEFAULT 0,
        is_visible TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);
    
    // إنشاء جدول support_files إذا لم يكن موجوداً
    $sql = "CREATE TABLE IF NOT EXISTS support_files (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        file_path VARCHAR(500) NOT NULL,
        file_size INT,
        upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);
    
    // التأكد من وجود جدول contact_info بالهيكل الصحيح
    $stmt = $pdo->query("SHOW TABLES LIKE 'contact_info'");
    if ($stmt->rowCount() == 0) {
        // إنشاء جدول contact_info إذا لم يكن موجوداً
        $sql = "CREATE TABLE contact_info (
            id INT AUTO_INCREMENT PRIMARY KEY,
            section_key VARCHAR(100) NOT NULL UNIQUE,
            title VARCHAR(255) NOT NULL,
            content TEXT,
            data JSON,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        $pdo->exec($sql);
        echo "تم إنشاء جدول contact_info بنجاح!<br>";
    } else {
        echo "جدول contact_info موجود بالفعل.<br>";
    }
    
    echo "تم إنشاء جميع الجداول المطلوبة بنجاح!";
    
} catch (Exception $e) {
    echo "خطأ في إنشاء الجداول: " . $e->getMessage();
}
?>