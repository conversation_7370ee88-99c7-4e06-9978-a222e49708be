<?php
/**
 * نظام مراقبة الأمان
 * Security Monitoring System
 */

require_once __DIR__ . '/auth.php';

class SecurityMonitor {
    private $database;
    private $config;
    private $logFile;
    
    public function __construct() {
        global $database;
        $this->database = $database;
        $this->config = include '../config/security_config.php';
        $this->logFile = '../logs/security_' . date('Y-m-d') . '.log';
        
        $this->initializeDatabase();
    }
    
    /**
     * إنشاء جداول المراقبة
     */
    private function initializeDatabase() {
        $this->database->query("
            CREATE TABLE IF NOT EXISTS security_events (
                id INT PRIMARY KEY AUTO_INCREMENT,
                event_type VARCHAR(50) NOT NULL,
                severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
                ip_address VARCHAR(45),
                user_id INT NULL,
                user_agent TEXT,
                event_data JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_event_type (event_type),
                INDEX idx_severity (severity),
                INDEX idx_ip_address (ip_address),
                INDEX idx_created_at (created_at)
            )
        ");
        
        $this->database->query("
            CREATE TABLE IF NOT EXISTS security_alerts (
                id INT PRIMARY KEY AUTO_INCREMENT,
                alert_type VARCHAR(50) NOT NULL,
                title VARCHAR(255) NOT NULL,
                description TEXT,
                severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
                status ENUM('new', 'investigating', 'resolved', 'false_positive') DEFAULT 'new',
                event_count INT DEFAULT 1,
                first_occurrence TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_occurrence TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                resolved_at TIMESTAMP NULL,
                resolved_by INT NULL,
                INDEX idx_alert_type (alert_type),
                INDEX idx_status (status),
                INDEX idx_severity (severity)
            )
        ");
    }
    
    /**
     * تسجيل حدث أمني
     */
    public function logSecurityEvent($eventType, $severity = 'medium', $eventData = []) {
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        $userId = getCurrentUserId() ?? null;
        
        // تسجيل في قاعدة البيانات
        $this->database->insert('security_events', [
            'event_type' => $eventType,
            'severity' => $severity,
            'ip_address' => $ip,
            'user_id' => $userId,
            'user_agent' => $userAgent,
            'event_data' => json_encode($eventData)
        ]);
        
        // تسجيل في ملف السجل
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'event_type' => $eventType,
            'severity' => $severity,
            'ip' => $ip,
            'user_id' => $userId,
            'data' => $eventData
        ];
        
        file_put_contents($this->logFile, json_encode($logEntry) . "\n", FILE_APPEND | LOCK_EX);
        
        // فحص إذا كان يجب إنشاء تنبيه
        $this->checkForAlert($eventType, $severity, $eventData);
    }
    
    /**
     * فحص الحاجة لإنشاء تنبيه أمني
     */
    private function checkForAlert($eventType, $severity, $eventData) {
        $alertRules = [
            'failed_login' => ['threshold' => 5, 'window' => 900], // 5 محاولات في 15 دقيقة
            'rate_limit_exceeded' => ['threshold' => 3, 'window' => 3600], // 3 تجاوزات في ساعة
            'suspicious_activity' => ['threshold' => 1, 'window' => 0], // فوري
            'malware_detected' => ['threshold' => 1, 'window' => 0], // فوري
            'admin_access' => ['threshold' => 10, 'window' => 3600] // 10 وصولات إدارية في ساعة
        ];
        
        if (!isset($alertRules[$eventType])) {
            return;
        }
        
        $rule = $alertRules[$eventType];
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        
        // عد الأحداث في النافزة الزمنية
        $since = date('Y-m-d H:i:s', time() - $rule['window']);
        $count = $this->database->fetch("
            SELECT COUNT(*) as count 
            FROM security_events 
            WHERE event_type = :event_type 
            AND ip_address = :ip 
            AND created_at >= :since
        ", [
            'event_type' => $eventType,
            'ip' => $ip,
            'since' => $since
        ])['count'];
        
        if ($count >= $rule['threshold']) {
            $this->createAlert($eventType, $severity, $count, $eventData);
        }
    }
    
    /**
     * إنشاء تنبيه أمني
     */
    private function createAlert($alertType, $severity, $eventCount, $eventData) {
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        
        // فحص إذا كان هناك تنبيه مفتوح مماثل
        $existingAlert = $this->database->fetch("
            SELECT id FROM security_alerts 
            WHERE alert_type = :alert_type 
            AND status IN ('new', 'investigating')
            AND JSON_EXTRACT(event_data, '$.ip') = :ip
            ORDER BY first_occurrence DESC 
            LIMIT 1
        ", [
            'alert_type' => $alertType,
            'ip' => $ip
        ]);
        
        if ($existingAlert) {
            // تحديث التنبيه الموجود
            $this->database->update('security_alerts', [
                'event_count' => $eventCount,
                'last_occurrence' => date('Y-m-d H:i:s')
            ], 'id = :id', ['id' => $existingAlert['id']]);
        } else {
            // إنشاء تنبيه جديد
            $title = $this->getAlertTitle($alertType, $eventCount);
            $description = $this->getAlertDescription($alertType, $eventData);
            
            $alertId = $this->database->insert('security_alerts', [
                'alert_type' => $alertType,
                'title' => $title,
                'description' => $description,
                'severity' => $severity,
                'event_count' => $eventCount,
                'event_data' => json_encode(array_merge($eventData, ['ip' => $ip]))
            ]);
            
            // إرسال إشعار للمدير
            $this->sendAlertNotification($alertId, $title, $description, $severity);
        }
    }
    
    /**
     * إنشاء عنوان التنبيه
     */
    private function getAlertTitle($alertType, $eventCount) {
        $titles = [
            'failed_login' => "محاولات تسجيل دخول فاشلة متعددة ({$eventCount} محاولات)",
            'rate_limit_exceeded' => "تجاوز حدود معدل الطلبات ({$eventCount} مرات)",
            'suspicious_activity' => "نشاط مشبوه مكتشف",
            'malware_detected' => "تم اكتشاف برمجيات ضارة",
            'admin_access' => "وصول إداري مكثف ({$eventCount} مرات)"
        ];
        
        return $titles[$alertType] ?? "تنبيه أمني: {$alertType}";
    }
    
    /**
     * إنشاء وصف التنبيه
     */
    private function getAlertDescription($alertType, $eventData) {
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $time = date('Y-m-d H:i:s');
        
        $descriptions = [
            'failed_login' => "تم رصد محاولات تسجيل دخول فاشلة متعددة من عنوان IP: {$ip} في {$time}",
            'rate_limit_exceeded' => "تم تجاوز حدود معدل الطلبات من عنوان IP: {$ip} في {$time}",
            'suspicious_activity' => "تم رصد نشاط مشبوه من عنوان IP: {$ip} في {$time}",
            'malware_detected' => "تم اكتشاف محتوى ضار من عنوان IP: {$ip} في {$time}",
            'admin_access' => "تم رصد وصول إداري مكثف من عنوان IP: {$ip} في {$time}"
        ];
        
        return $descriptions[$alertType] ?? "حدث أمني من النوع {$alertType} من {$ip} في {$time}";
    }
    
    /**
     * إرسال إشعار التنبيه
     */
    private function sendAlertNotification($alertId, $title, $description, $severity) {
        if (!$this->config['monitoring']['enabled'] || !$this->config['monitoring']['alert_email']) {
            return;
        }
        
        $adminEmail = $this->config['monitoring']['alert_email'];
        $subject = "[تنبيه أمني - {$severity}] {$title}";
        
        $body = "
            <h2 style='color: red;'>تنبيه أمني</h2>
            <p><strong>المستوى:</strong> {$severity}</p>
            <p><strong>العنوان:</strong> {$title}</p>
            <p><strong>الوصف:</strong> {$description}</p>
            <p><strong>رقم التنبيه:</strong> #{$alertId}</p>
            <p><strong>الوقت:</strong> " . date('Y-m-d H:i:s') . "</p>
            <hr>
            <p>يرجى مراجعة لوحة التحكم الإدارية للمزيد من التفاصيل.</p>
        ";
        
        sendEmail($adminEmail, $subject, $body);
    }
    
    /**
     * الحصول على التنبيهات النشطة
     */
    public function getActiveAlerts($limit = 10) {
        return $this->database->fetchAll("
            SELECT * FROM security_alerts 
            WHERE status IN ('new', 'investigating')
            ORDER BY severity DESC, first_occurrence DESC 
            LIMIT :limit
        ", ['limit' => $limit]);
    }
    
    /**
     * الحصول على إحصائيات الأمان
     */
    public function getSecurityStats($days = 7) {
        $since = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        return [
            'total_events' => $this->database->fetch("
                SELECT COUNT(*) as count FROM security_events 
                WHERE created_at >= :since
            ", ['since' => $since])['count'],
            
            'events_by_type' => $this->database->fetchAll("
                SELECT event_type, COUNT(*) as count 
                FROM security_events 
                WHERE created_at >= :since
                GROUP BY event_type 
                ORDER BY count DESC
            ", ['since' => $since]),
            
            'events_by_severity' => $this->database->fetchAll("
                SELECT severity, COUNT(*) as count 
                FROM security_events 
                WHERE created_at >= :since
                GROUP BY severity 
                ORDER BY FIELD(severity, 'critical', 'high', 'medium', 'low')
            ", ['since' => $since]),
            
            'top_ips' => $this->database->fetchAll("
                SELECT ip_address, COUNT(*) as count 
                FROM security_events 
                WHERE created_at >= :since
                GROUP BY ip_address 
                ORDER BY count DESC 
                LIMIT 10
            ", ['since' => $since])
        ];
    }
    
    /**
     * تنظيف السجلات القديمة
     */
    public function cleanupOldRecords($days = 30) {
        $cutoff = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        // حذف الأحداث القديمة
        $this->database->query("
            DELETE FROM security_events 
            WHERE created_at < :cutoff
        ", ['cutoff' => $cutoff]);
        
        // حذف التنبيهات المحلولة القديمة
        $this->database->query("
            DELETE FROM security_alerts 
            WHERE status = 'resolved' 
            AND first_occurrence < :cutoff
        ", ['cutoff' => $cutoff]);
    }
}

// دوال مساعدة عامة
function logSecurityEvent($eventType, $severity = 'medium', $eventData = []) {
    static $monitor = null;
    if ($monitor === null) {
        $monitor = new SecurityMonitor();
    }
    $monitor->logSecurityEvent($eventType, $severity, $eventData);
}
?>