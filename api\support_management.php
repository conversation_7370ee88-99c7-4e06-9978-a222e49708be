<?php
session_start();
require_once '../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح بالوصول']);
    exit;
}

header('Content-Type: application/json');

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'update_section':
            updateSection($pdo);
            break;
            
        case 'get_section_data':
            getSectionData($pdo);
            break;
            
        case 'add_warranty_plan':
            addWarrantyPlan($pdo);
            break;
            
        case 'update_warranty_plan':
            updateWarrantyPlan($pdo);
            break;
            
        case 'delete_warranty_plan':
            deleteWarrantyPlan($pdo);
            break;
            
        case 'toggle_warranty_visibility':
            toggleWarrantyVisibility($pdo);
            break;
            
        case 'upload_support_file':
            uploadSupportFile($pdo);
            break;
            
        case 'delete_support_file':
            deleteSupportFile($pdo);
            break;
            
        case 'toggle_file_visibility':
            toggleFileVisibility($pdo);
            break;
            
        case 'get_testimonials':
            getTestimonials($pdo);
            break;
            
        case 'get_faqs':
            getFaqs($pdo);
            break;
            
        case 'update_testimonials_settings':
            updateTestimonialsSettings($pdo);
            break;
            
        case 'update_faqs_settings':
            updateFaqsSettings($pdo);
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح']);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'خطأ في الخادم: ' . $e->getMessage()]);
}

function updateSection($pdo) {
    $section = $_POST['section'] ?? '';
    $data = $_POST;
    unset($data['action'], $data['section']);
    
    try {
        // تحديث البيانات في جدول contact_info
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $value = json_encode($value, JSON_UNESCAPED_UNICODE);
            }
            
            $stmt = $pdo->prepare("
                INSERT INTO contact_info (section, field_name, field_value) 
                VALUES (?, ?, ?) 
                ON DUPLICATE KEY UPDATE field_value = VALUES(field_value)
            ");
            $stmt->execute([$section, $key, $value]);
        }
        
        echo json_encode(['success' => true, 'message' => 'تم حفظ البيانات بنجاح']);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'خطأ في حفظ البيانات: ' . $e->getMessage()]);
    }
}

function getSectionData($pdo) {
    $section = $_POST['section'] ?? '';
    
    try {
        $stmt = $pdo->prepare("SELECT field_name, field_value FROM contact_info WHERE section = ?");
        $stmt->execute([$section]);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $data = [];
        foreach ($results as $row) {
            $data[$row['field_name']] = $row['field_value'];
        }
        
        echo json_encode(['success' => true, 'data' => $data]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'خطأ في جلب البيانات: ' . $e->getMessage()]);
    }
}

function addWarrantyPlan($pdo) {
    $title = $_POST['title'] ?? '';
    $price = $_POST['price'] ?? '';
    $description = $_POST['description'] ?? '';
    $features = $_POST['features'] ?? '';
    $is_featured = isset($_POST['is_featured']) ? 1 : 0;
    $is_popular = isset($_POST['is_popular']) ? 1 : 0;
    $is_visible = isset($_POST['is_visible']) ? 1 : 0;
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO warranty_plans (title, price, description, features, is_featured, is_popular, is_visible) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([$title, $price, $description, $features, $is_featured, $is_popular, $is_visible]);
        
        echo json_encode(['success' => true, 'message' => 'تم إضافة خطة الضمان بنجاح']);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'خطأ في إضافة خطة الضمان: ' . $e->getMessage()]);
    }
}

function updateWarrantyPlan($pdo) {
    $id = $_POST['id'] ?? 0;
    $title = $_POST['title'] ?? '';
    $price = $_POST['price'] ?? '';
    $description = $_POST['description'] ?? '';
    $features = $_POST['features'] ?? '';
    $is_featured = isset($_POST['is_featured']) ? 1 : 0;
    $is_popular = isset($_POST['is_popular']) ? 1 : 0;
    $is_visible = isset($_POST['is_visible']) ? 1 : 0;
    
    try {
        $stmt = $pdo->prepare("
            UPDATE warranty_plans 
            SET title = ?, price = ?, description = ?, features = ?, is_featured = ?, is_popular = ?, is_visible = ? 
            WHERE id = ?
        ");
        $stmt->execute([$title, $price, $description, $features, $is_featured, $is_popular, $is_visible, $id]);
        
        echo json_encode(['success' => true, 'message' => 'تم تحديث خطة الضمان بنجاح']);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'خطأ في تحديث خطة الضمان: ' . $e->getMessage()]);
    }
}

function deleteWarrantyPlan($pdo) {
    $id = $_POST['id'] ?? 0;
    
    try {
        $stmt = $pdo->prepare("DELETE FROM warranty_plans WHERE id = ?");
        $stmt->execute([$id]);
        
        echo json_encode(['success' => true, 'message' => 'تم حذف خطة الضمان بنجاح']);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'خطأ في حذف خطة الضمان: ' . $e->getMessage()]);
    }
}

function toggleWarrantyVisibility($pdo) {
    $id = $_POST['id'] ?? 0;
    
    try {
        $stmt = $pdo->prepare("UPDATE warranty_plans SET is_visible = NOT is_visible WHERE id = ?");
        $stmt->execute([$id]);
        
        echo json_encode(['success' => true, 'message' => 'تم تحديث حالة الإظهار بنجاح']);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'خطأ في تحديث حالة الإظهار: ' . $e->getMessage()]);
    }
}

function uploadSupportFile($pdo) {
    $title = $_POST['title'] ?? '';
    $description = $_POST['description'] ?? '';
    
    if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
        echo json_encode(['success' => false, 'message' => 'خطأ في رفع الملف']);
        return;
    }
    
    $file = $_FILES['file'];
    $allowedTypes = ['pdf', 'doc', 'docx', 'txt', 'zip', 'rar'];
    $maxSize = 10 * 1024 * 1024; // 10MB
    
    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    if (!in_array($fileExtension, $allowedTypes)) {
        echo json_encode(['success' => false, 'message' => 'نوع الملف غير مدعوم']);
        return;
    }
    
    if ($file['size'] > $maxSize) {
        echo json_encode(['success' => false, 'message' => 'حجم الملف كبير جداً']);
        return;
    }
    
    try {
        $uploadDir = '../uploads/support_files/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        $fileName = time() . '_' . $file['name'];
        $filePath = $uploadDir . $fileName;
        
        if (move_uploaded_file($file['tmp_name'], $filePath)) {
            $stmt = $pdo->prepare("
                INSERT INTO support_files (title, description, file_name, file_path, file_size, is_visible) 
                VALUES (?, ?, ?, ?, ?, 1)
            ");
            $stmt->execute([$title, $description, $fileName, $filePath, $file['size']]);
            
            echo json_encode(['success' => true, 'message' => 'تم رفع الملف بنجاح']);
        } else {
            echo json_encode(['success' => false, 'message' => 'خطأ في حفظ الملف']);
        }
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'خطأ في رفع الملف: ' . $e->getMessage()]);
    }
}

function deleteSupportFile($pdo) {
    $id = $_POST['id'] ?? 0;
    
    try {
        // جلب معلومات الملف
        $stmt = $pdo->prepare("SELECT file_path FROM support_files WHERE id = ?");
        $stmt->execute([$id]);
        $file = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($file) {
            // حذف الملف من النظام
            if (file_exists($file['file_path'])) {
                unlink($file['file_path']);
            }
            
            // حذف السجل من قاعدة البيانات
            $stmt = $pdo->prepare("DELETE FROM support_files WHERE id = ?");
            $stmt->execute([$id]);
            
            echo json_encode(['success' => true, 'message' => 'تم حذف الملف بنجاح']);
        } else {
            echo json_encode(['success' => false, 'message' => 'الملف غير موجود']);
        }
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'خطأ في حذف الملف: ' . $e->getMessage()]);
    }
}

function toggleFileVisibility($pdo) {
    $id = $_POST['id'] ?? 0;
    
    try {
        $stmt = $pdo->prepare("UPDATE support_files SET is_visible = NOT is_visible WHERE id = ?");
        $stmt->execute([$id]);
        
        echo json_encode(['success' => true, 'message' => 'تم تحديث حالة الإظهار بنجاح']);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'خطأ في تحديث حالة الإظهار: ' . $e->getMessage()]);
    }
}

function getTestimonials($pdo) {
    try {
        $stmt = $pdo->query("SELECT * FROM testimonials ORDER BY created_at DESC");
        $testimonials = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode(['success' => true, 'data' => $testimonials]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'خطأ في جلب التقييمات: ' . $e->getMessage()]);
    }
}

function getFaqs($pdo) {
    try {
        $stmt = $pdo->query("SELECT * FROM faqs ORDER BY sort_order ASC");
        $faqs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode(['success' => true, 'data' => $faqs]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'خطأ في جلب الأسئلة الشائعة: ' . $e->getMessage()]);
    }
}

function updateTestimonialsSettings($pdo) {
    $show_section = isset($_POST['show_testimonials_section']) ? 1 : 0;
    $testimonials_count = $_POST['testimonials_count'] ?? 6;
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO contact_info (section, field_name, field_value) 
            VALUES ('testimonials', 'show_section', ?) 
            ON DUPLICATE KEY UPDATE field_value = VALUES(field_value)
        ");
        $stmt->execute([$show_section]);
        
        $stmt = $pdo->prepare("
            INSERT INTO contact_info (section, field_name, field_value) 
            VALUES ('testimonials', 'display_count', ?) 
            ON DUPLICATE KEY UPDATE field_value = VALUES(field_value)
        ");
        $stmt->execute([$testimonials_count]);
        
        echo json_encode(['success' => true, 'message' => 'تم حفظ إعدادات التقييمات بنجاح']);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'خطأ في حفظ إعدادات التقييمات: ' . $e->getMessage()]);
    }
}

function updateFaqsSettings($pdo) {
    $show_section = isset($_POST['show_faqs_section']) ? 1 : 0;
    $faqs_count = $_POST['faqs_count'] ?? 5;
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO contact_info (section, field_name, field_value) 
            VALUES ('faqs', 'show_section', ?) 
            ON DUPLICATE KEY UPDATE field_value = VALUES(field_value)
        ");
        $stmt->execute([$show_section]);
        
        $stmt = $pdo->prepare("
            INSERT INTO contact_info (section, field_name, field_value) 
            VALUES ('faqs', 'display_count', ?) 
            ON DUPLICATE KEY UPDATE field_value = VALUES(field_value)
        ");
        $stmt->execute([$faqs_count]);
        
        echo json_encode(['success' => true, 'message' => 'تم حفظ إعدادات الأسئلة الشائعة بنجاح']);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'خطأ في حفظ إعدادات الأسئلة الشائعة: ' . $e->getMessage()]);
    }
}
?>