<?php
/**
 * دوال مساعدة للتعامل مع بيانات POST بشكل آمن
 * Helper functions for safely handling POST data
 */

/**
 * الحصول على قيمة من $_POST مع قيمة افتراضية
 * Get a value from $_POST with a default value
 * 
 * @param string $key المفتاح المطلوب
 * @param mixed $default القيمة الافتراضية
 * @return mixed
 */
function getPost($key, $default = '') {
    return $_POST[$key] ?? $default;
}

/**
 * الحصول على قيمة من $_POST وتنظيفها
 * Get a value from $_POST and sanitize it
 * 
 * @param string $key المفتاح المطلوب
 * @param mixed $default القيمة الافتراضية
 * @return string
 */
function getPostSanitized($key, $default = '') {
    $value = $_POST[$key] ?? $default;
    return is_string($value) ? trim(htmlspecialchars($value, ENT_QUOTES, 'UTF-8')) : $value;
}

/**
 * الحصول على قيمة رقمية من $_POST
 * Get a numeric value from $_POST
 * 
 * @param string $key المفتاح المطلوب
 * @param int|float $default القيمة الافتراضية
 * @param string $type نوع الرقم (int أو float)
 * @return int|float
 */
function getPostNumeric($key, $default = 0, $type = 'int') {
    $value = $_POST[$key] ?? $default;
    
    if ($type === 'float') {
        return floatval($value);
    }
    
    return intval($value);
}

/**
 * التحقق من وجود checkbox في $_POST
 * Check if a checkbox exists in $_POST
 * 
 * @param string $key المفتاح المطلوب
 * @return bool
 */
function getPostCheckbox($key) {
    return isset($_POST[$key]);
}

/**
 * الحصول على مصفوفة من $_POST مع التحقق من صحتها
 * Get an array from $_POST with validation
 * 
 * @param string $key المفتاح المطلوب
 * @param array $default القيمة الافتراضية
 * @return array
 */
function getPostArray($key, $default = []) {
    $value = $_POST[$key] ?? $default;
    return is_array($value) ? $value : $default;
}

/**
 * الحصول على قيمة من مصفوفة متداخلة في $_POST
 * Get a value from a nested array in $_POST
 * 
 * @param string $parentKey المفتاح الرئيسي
 * @param string $childKey المفتاح الفرعي
 * @param mixed $default القيمة الافتراضية
 * @return mixed
 */
function getPostNested($parentKey, $childKey, $default = '') {
    return $_POST[$parentKey][$childKey] ?? $default;
}

/**
 * التحقق من صحة البريد الإلكتروني من $_POST
 * Validate email from $_POST
 * 
 * @param string $key المفتاح المطلوب
 * @return string|false
 */
function getPostEmail($key) {
    $email = getPost($key);
    return filter_var($email, FILTER_VALIDATE_EMAIL) ? $email : false;
}

/**
 * التحقق من صحة URL من $_POST
 * Validate URL from $_POST
 * 
 * @param string $key المفتاح المطلوب
 * @return string|false
 */
function getPostUrl($key) {
    $url = getPost($key);
    return filter_var($url, FILTER_VALIDATE_URL) ? $url : false;
}

/**
 * الحصول على قيمة من $_POST مع تحديد الحد الأقصى للطول
 * Get a value from $_POST with maximum length limit
 * 
 * @param string $key المفتاح المطلوب
 * @param int $maxLength الحد الأقصى للطول
 * @param string $default القيمة الافتراضية
 * @return string
 */
function getPostWithLimit($key, $maxLength, $default = '') {
    $value = getPostSanitized($key, $default);
    return mb_substr($value, 0, $maxLength, 'UTF-8');
}

/**
 * التحقق من وجود جميع المفاتيح المطلوبة في $_POST
 * Check if all required keys exist in $_POST
 * 
 * @param array $requiredKeys المفاتيح المطلوبة
 * @return bool
 */
function hasRequiredPostData($requiredKeys) {
    foreach ($requiredKeys as $key) {
        if (!isset($_POST[$key]) || empty($_POST[$key])) {
            return false;
        }
    }
    return true;
}

/**
 * الحصول على جميع بيانات $_POST مع تنظيفها
 * Get all $_POST data with sanitization
 * 
 * @param array $excludeKeys المفاتيح المستبعدة
 * @return array
 */
function getAllPostDataSanitized($excludeKeys = []) {
    $sanitizedData = [];
    
    foreach ($_POST as $key => $value) {
        if (!in_array($key, $excludeKeys)) {
            if (is_array($value)) {
                $sanitizedData[$key] = array_map(function($item) {
                    return is_string($item) ? trim(htmlspecialchars($item, ENT_QUOTES, 'UTF-8')) : $item;
                }, $value);
            } else {
                $sanitizedData[$key] = is_string($value) ? trim(htmlspecialchars($value, ENT_QUOTES, 'UTF-8')) : $value;
            }
        }
    }
    
    return $sanitizedData;
}