/* Admin Panel Custom CSS */

/* Base Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 0;
    background-color: #f9fafb;
}

/* RTL Support */
html[dir="rtl"] {
    direction: rtl;
}

/* Custom Variables */
:root {
    --admin-primary: #2563eb;
    --admin-primary-dark: #1d4ed8;
    --admin-secondary: #10b981;
    --admin-danger: #ef4444;
    --admin-warning: #f59e0b;
    --admin-success: #10b981;
    --admin-info: #3b82f6;
    --admin-dark: #1f2937;
    --admin-light: #f9fafb;
    --admin-border: #e5e7eb;
    --admin-text: #374151;
    --admin-text-light: #6b7280;
    --admin-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    --admin-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --admin-radius: 0.5rem;
    --admin-transition: all 0.2s ease;
}

/* Layout */
.admin-layout {
    display: flex;
    min-height: 100vh;
}

.admin-sidebar {
    width: 256px;
    background-color: var(--admin-dark);
    color: white;
    flex-shrink: 0;
    transition: var(--admin-transition);
}

.admin-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.admin-header {
    background-color: white;
    border-bottom: 1px solid var(--admin-border);
    padding: 1rem 1.5rem;
    box-shadow: var(--admin-shadow);
}

.admin-content {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
    background-color: var(--admin-light);
}

/* Cards */
.admin-card {
    background-color: white;
    border-radius: var(--admin-radius);
    box-shadow: var(--admin-shadow);
    border: 1px solid var(--admin-border);
    overflow: hidden;
    transition: var(--admin-transition);
}

.admin-card:hover {
    box-shadow: var(--admin-shadow-lg);
}

.admin-card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--admin-border);
    background-color: #f8fafc;
}

.admin-card-body {
    padding: 1.5rem;
}

.admin-card-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--admin-border);
    background-color: #f8fafc;
}

/* Buttons */
.btn-admin {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: var(--admin-radius);
    border: 1px solid transparent;
    cursor: pointer;
    transition: var(--admin-transition);
    text-decoration: none;
    line-height: 1.5;
}

.btn-admin:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.btn-admin-primary {
    background-color: var(--admin-primary);
    color: white;
    border-color: var(--admin-primary);
}

.btn-admin-primary:hover {
    background-color: var(--admin-primary-dark);
    border-color: var(--admin-primary-dark);
    color: white;
}

.btn-admin-secondary {
    background-color: var(--admin-secondary);
    color: white;
    border-color: var(--admin-secondary);
}

.btn-admin-secondary:hover {
    background-color: #059669;
    border-color: #059669;
    color: white;
}

.btn-admin-danger {
    background-color: var(--admin-danger);
    color: white;
    border-color: var(--admin-danger);
}

.btn-admin-danger:hover {
    background-color: #dc2626;
    border-color: #dc2626;
    color: white;
}

.btn-admin-warning {
    background-color: var(--admin-warning);
    color: white;
    border-color: var(--admin-warning);
}

.btn-admin-warning:hover {
    background-color: #d97706;
    border-color: #d97706;
    color: white;
}

.btn-admin-outline {
    background-color: transparent;
    color: var(--admin-primary);
    border-color: var(--admin-primary);
}

.btn-admin-outline:hover {
    background-color: var(--admin-primary);
    color: white;
}

.btn-admin-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
}

.btn-admin-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
}

/* Forms */
.form-admin {
    max-width: 100%;
}

.form-group-admin {
    margin-bottom: 1.5rem;
}

.form-label-admin {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--admin-text);
    font-size: 0.875rem;
}

.form-input-admin {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--admin-border);
    border-radius: var(--admin-radius);
    font-size: 0.875rem;
    transition: var(--admin-transition);
    background-color: white;
}

.form-input-admin:focus {
    outline: none;
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input-admin.error {
    border-color: var(--admin-danger);
}

.form-textarea-admin {
    resize: vertical;
    min-height: 100px;
}

.form-select-admin {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: left 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-left: 2.5rem;
}

.form-error {
    color: var(--admin-danger);
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

.form-help {
    color: var(--admin-text-light);
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

/* Tables */
.table-admin {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
    border-radius: var(--admin-radius);
    overflow: hidden;
    box-shadow: var(--admin-shadow);
}

.table-admin th {
    background-color: #f8fafc;
    padding: 0.75rem 1rem;
    text-align: right;
    font-weight: 600;
    color: var(--admin-text);
    border-bottom: 1px solid var(--admin-border);
    font-size: 0.875rem;
}

.table-admin td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--admin-border);
    color: var(--admin-text);
    font-size: 0.875rem;
}

.table-admin tbody tr:hover {
    background-color: #f9fafb;
}

.table-admin tbody tr:last-child td {
    border-bottom: none;
}

/* Status Badges */
.badge-admin {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 9999px;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.badge-admin-success {
    background-color: #d1fae5;
    color: #065f46;
}

.badge-admin-warning {
    background-color: #fef3c7;
    color: #92400e;
}

.badge-admin-danger {
    background-color: #fee2e2;
    color: #991b1b;
}

.badge-admin-info {
    background-color: #dbeafe;
    color: #1e40af;
}

.badge-admin-secondary {
    background-color: #f3f4f6;
    color: #374151;
}

/* Alerts */
.alert-admin {
    padding: 1rem;
    border-radius: var(--admin-radius);
    border: 1px solid transparent;
    margin-bottom: 1rem;
}

.alert-admin-success {
    background-color: #d1fae5;
    border-color: #a7f3d0;
    color: #065f46;
}

.alert-admin-warning {
    background-color: #fef3c7;
    border-color: #fde68a;
    color: #92400e;
}

.alert-admin-danger {
    background-color: #fee2e2;
    border-color: #fecaca;
    color: #991b1b;
}

.alert-admin-info {
    background-color: #dbeafe;
    border-color: #bfdbfe;
    color: #1e40af;
}

/* Pagination */
.pagination-admin {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-top: 2rem;
}

.pagination-admin a,
.pagination-admin span {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--admin-border);
    border-radius: var(--admin-radius);
    color: var(--admin-text);
    text-decoration: none;
    transition: var(--admin-transition);
    min-width: 2.5rem;
}

.pagination-admin a:hover {
    background-color: var(--admin-primary);
    border-color: var(--admin-primary);
    color: white;
}

.pagination-admin .current {
    background-color: var(--admin-primary);
    border-color: var(--admin-primary);
    color: white;
}

.pagination-admin .disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-admin .disabled:hover {
    background-color: transparent;
    border-color: var(--admin-border);
    color: var(--admin-text);
}

/* Statistics Cards */
.stat-card {
    background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-primary-dark) 100%);
    color: white;
    padding: 1.5rem;
    border-radius: var(--admin-radius);
    box-shadow: var(--admin-shadow);
    transition: var(--admin-transition);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--admin-shadow-lg);
}

.stat-card-success {
    background: linear-gradient(135deg, var(--admin-success) 0%, #059669 100%);
}

.stat-card-warning {
    background: linear-gradient(135deg, var(--admin-warning) 0%, #d97706 100%);
}

.stat-card-danger {
    background: linear-gradient(135deg, var(--admin-danger) 0%, #dc2626 100%);
}

.stat-card-info {
    background: linear-gradient(135deg, var(--admin-info) 0%, #1d4ed8 100%);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

/* Loading States */
.loading-admin {
    position: relative;
    overflow: hidden;
}

.loading-admin::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading-admin 1.5s infinite;
}

@keyframes loading-admin {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Dropdowns */
.dropdown-admin {
    position: relative;
    display: inline-block;
}

.dropdown-admin-content {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 160px;
    background-color: white;
    border: 1px solid var(--admin-border);
    border-radius: var(--admin-radius);
    box-shadow: var(--admin-shadow-lg);
    z-index: 1000;
    padding: 0.5rem 0;
}

.dropdown-admin-item {
    display: block;
    padding: 0.5rem 1rem;
    color: var(--admin-text);
    text-decoration: none;
    transition: var(--admin-transition);
}

.dropdown-admin-item:hover {
    background-color: #f3f4f6;
    color: var(--admin-text);
}

/* Modal */
.modal-admin {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1050;
    opacity: 0;
    visibility: hidden;
    transition: var(--admin-transition);
}

.modal-admin.show {
    opacity: 1;
    visibility: visible;
}

.modal-admin-content {
    background-color: white;
    border-radius: var(--admin-radius);
    box-shadow: var(--admin-shadow-lg);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: var(--admin-transition);
}

.modal-admin.show .modal-admin-content {
    transform: scale(1);
}

.modal-admin-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--admin-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-admin-body {
    padding: 1.5rem;
}

.modal-admin-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--admin-border);
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
}

/* Tabs */
.tabs-admin {
    border-bottom: 1px solid var(--admin-border);
    margin-bottom: 1.5rem;
}

.tabs-admin-nav {
    display: flex;
    gap: 0;
}

.tabs-admin-item {
    padding: 0.75rem 1.5rem;
    border-bottom: 2px solid transparent;
    color: var(--admin-text-light);
    text-decoration: none;
    transition: var(--admin-transition);
    cursor: pointer;
}

.tabs-admin-item:hover {
    color: var(--admin-text);
    border-bottom-color: var(--admin-border);
}

.tabs-admin-item.active {
    color: var(--admin-primary);
    border-bottom-color: var(--admin-primary);
}

/* File Upload */
.file-upload-admin {
    border: 2px dashed var(--admin-border);
    border-radius: var(--admin-radius);
    padding: 2rem;
    text-align: center;
    transition: var(--admin-transition);
    cursor: pointer;
}

.file-upload-admin:hover {
    border-color: var(--admin-primary);
    background-color: #f8fafc;
}

.file-upload-admin.dragover {
    border-color: var(--admin-primary);
    background-color: rgba(59, 130, 246, 0.05);
}

/* Progress Bar */
.progress-admin {
    width: 100%;
    height: 0.5rem;
    background-color: #e5e7eb;
    border-radius: 9999px;
    overflow: hidden;
}

.progress-admin-bar {
    height: 100%;
    background-color: var(--admin-primary);
    transition: width 0.3s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-sidebar {
        position: fixed;
        top: 0;
        right: -256px;
        height: 100vh;
        z-index: 1000;
        transition: right 0.3s ease;
    }
    
    .admin-sidebar.open {
        right: 0;
    }
    
    .admin-content {
        padding: 1rem;
    }
    
    .admin-card-body {
        padding: 1rem;
    }
    
    .table-admin {
        font-size: 0.75rem;
    }
    
    .table-admin th,
    .table-admin td {
        padding: 0.5rem;
    }
    
    .btn-admin {
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
}

@media (max-width: 640px) {
    .pagination-admin {
        flex-wrap: wrap;
        gap: 0.25rem;
    }
    
    .pagination-admin a,
    .pagination-admin span {
        padding: 0.25rem 0.5rem;
        min-width: 2rem;
        font-size: 0.75rem;
    }
    
    .modal-admin-content {
        width: 95%;
        margin: 1rem;
    }
    
    .modal-admin-header,
    .modal-admin-body,
    .modal-admin-footer {
        padding: 1rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --admin-light: #1f2937;
        --admin-border: #374151;
        --admin-text: #f9fafb;
        --admin-text-light: #d1d5db;
    }
    
    .admin-card,
    .form-input-admin,
    .table-admin,
    .dropdown-admin-content,
    .modal-admin-content {
        background-color: #1f2937;
        color: #f9fafb;
    }
    
    .admin-card-header,
    .admin-card-footer,
    .table-admin th {
        background-color: #374151;
    }
}

/* Print Styles */
@media print {
    .admin-sidebar,
    .admin-header,
    .btn-admin,
    .pagination-admin {
        display: none !important;
    }
    
    .admin-content {
        padding: 0;
        background-color: white;
    }
    
    .admin-card {
        box-shadow: none;
        border: 1px solid #ccc;
    }
}

/* Utility Classes */
.text-admin-primary { color: var(--admin-primary); }
.text-admin-secondary { color: var(--admin-secondary); }
.text-admin-danger { color: var(--admin-danger); }
.text-admin-warning { color: var(--admin-warning); }
.text-admin-success { color: var(--admin-success); }
.text-admin-muted { color: var(--admin-text-light); }

.bg-admin-primary { background-color: var(--admin-primary); }
.bg-admin-secondary { background-color: var(--admin-secondary); }
.bg-admin-danger { background-color: var(--admin-danger); }
.bg-admin-warning { background-color: var(--admin-warning); }
.bg-admin-success { background-color: var(--admin-success); }
.bg-admin-light { background-color: var(--admin-light); }

.border-admin { border: 1px solid var(--admin-border); }
.border-admin-primary { border-color: var(--admin-primary); }
.border-admin-danger { border-color: var(--admin-danger); }

.rounded-admin { border-radius: var(--admin-radius); }
.shadow-admin { box-shadow: var(--admin-shadow); }
.shadow-admin-lg { box-shadow: var(--admin-shadow-lg); }

.transition-admin { transition: var(--admin-transition); }

.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }

.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }

.hidden { display: none !important; }
.block { display: block !important; }
.inline-block { display: inline-block !important; }
.flex { display: flex !important; }
.inline-flex { display: inline-flex !important; }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }

.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }

.m-0 { margin: 0; }
.m-1 { margin: 0.25rem; }
.m-2 { margin: 0.5rem; }
.m-3 { margin: 0.75rem; }
.m-4 { margin: 1rem; }

.p-0 { padding: 0; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: 0.25rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-3 { margin-right: 0.75rem; }
.mr-4 { margin-right: 1rem; }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: 0.25rem; }
.ml-2 { margin-left: 0.5rem; }
.ml-3 { margin-left: 0.75rem; }
.ml-4 { margin-left: 1rem; }