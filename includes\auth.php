<?php
/**
 * نظام المصادقة والتحقق من الهوية
 * Authentication and Authorization System
 */

// تضمين الملفات المطلوبة
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/functions.php';
require_once __DIR__ . '/permissions.php';

// دالة isLoggedIn() متوفرة في config.php

/**
 * دالة الحصول على بيانات المستخدم الحالي
 */
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    return [
        'id' => $_SESSION['user_id'],
        'email' => $_SESSION['user_email'] ?? '',
        'name' => $_SESSION['user_name'] ?? '',
        'role' => $_SESSION['user_role'] ?? 'user'
    ];
}

/**
 * دالة التحقق من صلاحيات المدير
 */
function isAdmin() {
    return isLoggedIn() && isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

/**
 * دالة الحصول على معرف المستخدم الحالي
 */
function getCurrentUserId() {
    return isLoggedIn() ? $_SESSION['user_id'] : null;
}

/**
 * دالة تسجيل الدخول
 */
function login($email, $password, $remember = false) {
    global $database;
    
    // البحث عن المستخدم
    $user = $database->fetch(
        "SELECT * FROM users WHERE email = :email AND is_active = 1",
        ['email' => $email]
    );
    
    if (!$user) {
        return false;
    }
    
    // التحقق من كلمة المرور
    if (!password_verify($password, $user['password'])) {
        logActivity('failed_login_attempt', 'users', $user['id']);
        return false;
    }
    
    // إنشاء الجلسة
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['user_email'] = $user['email'];
    $_SESSION['user_name'] = $user['name'];
    $_SESSION['user_role'] = $user['role'];
    $_SESSION['login_time'] = time();
    
    // إنشاء رمز التذكر إذا طُلب
    if ($remember) {
        $token = generateToken();
        $database->update('users', 
            ['remember_token' => $token],
            'id = :id',
            ['id' => $user['id']]
        );
        
        // إنشاء كوكي للتذكر (30 يوم)
        setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/', '', false, true);
    }
    
    // تسجيل النشاط
    logActivity('user_login', 'users', $user['id']);
    
    return true;
}

/**
 * دالة تسجيل الدخول للإدارة
 */
function loginUser($email, $password, $remember = false) {
    $result = login($email, $password, $remember);
    
    if ($result) {
        return ['success' => true, 'message' => 'تم تسجيل الدخول بنجاح'];
    } else {
        return ['success' => false, 'message' => 'البريد الإلكتروني أو كلمة المرور غير صحيحة'];
    }
}

/**
 * دالة طلب إعادة تعيين كلمة المرور
 */
function requestPasswordReset($email) {
    $result = resetPassword($email);
    
    if ($result) {
        return ['success' => true, 'message' => 'تم إرسال رابط إعادة تعيين كلمة المرور'];
    } else {
        return ['success' => false, 'message' => 'البريد الإلكتروني غير مسجل'];
    }
}

/**
 * دالة تسجيل الخروج
 */
function logout() {
    global $database;
    
    if (isLoggedIn()) {
        $user = getCurrentUser();
        
        // حذف رمز التذكر
        $database->update('users',
            ['remember_token' => null],
            'id = :id',
            ['id' => $user['id']]
        );
        
        // تسجيل النشاط
        logActivity('user_logout', 'users', $user['id']);
    }
    
    // إنهاء الجلسة
    session_destroy();
    
    // حذف كوكي التذكر
    if (isset($_COOKIE['remember_token'])) {
        setcookie('remember_token', '', time() - 3600, '/', '', false, true);
    }
    
    return true;
}

/**
 * دالة التسجيل
 */
function register($name, $email, $password, $phone = '') {
    global $database;
    
    // التحقق من عدم وجود البريد الإلكتروني مسبقاً
    $existing = $database->fetch(
        "SELECT id FROM users WHERE email = :email",
        ['email' => $email]
    );
    
    if ($existing) {
        return false;
    }
    
    // تشفير كلمة المرور
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
    
    // إدراج المستخدم الجديد
    $userId = $database->insert('users', [
        'name' => sanitizeInput($name),
        'email' => sanitizeInput($email),
        'password' => $hashedPassword,
        'phone' => sanitizeInput($phone),
        'role' => 'user',
        'is_active' => true
    ]);
    
    if ($userId) {
        // تسجيل النشاط
        logActivity('user_registration', 'users', $userId);
        
        // إرسال بريد ترحيب (اختياري)
        sendWelcomeEmail($email, $name);
        
        return true;
    }
    
    return false;
}

/**
 * دالة التحقق من صحة بيانات التسجيل
 */
function validateRegistrationData($data) {
    $errors = [];
    
    // التحقق من الاسم
    if (empty($data['name']) || strlen(trim($data['name'])) < 2) {
        $errors['name'] = 'الاسم مطلوب ويجب أن يكون أكثر من حرفين';
    }
    
    // التحقق من البريد الإلكتروني
    if (empty($data['email']) || !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
        $errors['email'] = 'البريد الإلكتروني غير صحيح';
    }
    
    // التحقق من كلمة المرور
    if (empty($data['password']) || strlen($data['password']) < 6) {
        $errors['password'] = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }
    
    // التحقق من تأكيد كلمة المرور
    if (isset($data['password_confirmation']) && $data['password'] !== $data['password_confirmation']) {
        $errors['password_confirmation'] = 'تأكيد كلمة المرور غير متطابق';
    }
    
    // التحقق من رقم الهاتف (اختياري)
    if (!empty($data['phone']) && !preg_match('/^[0-9+\-\s]+$/', $data['phone'])) {
        $errors['phone'] = 'رقم الهاتف غير صحيح';
    }
    
    return [
        'valid' => empty($errors),
        'errors' => $errors
    ];
}

/**
 * دالة تغيير كلمة المرور
 */
function changePassword($userId, $currentPassword, $newPassword) {
    global $database;
    
    // الحصول على المستخدم
    $user = $database->fetch(
        "SELECT * FROM users WHERE id = :id",
        ['id' => $userId]
    );
    
    if (!$user) {
        return ['success' => false, 'error' => 'المستخدم غير موجود'];
    }
    
    // التحقق من كلمة المرور الحالية
    if (!password_verify($currentPassword, $user['password'])) {
        return ['success' => false, 'error' => 'كلمة المرور الحالية غير صحيحة'];
    }
    
    // التحقق من كلمة المرور الجديدة
    if (strlen($newPassword) < 6) {
        return ['success' => false, 'error' => 'كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل'];
    }
    
    // تشفير كلمة المرور الجديدة
    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
    
    // تحديث كلمة المرور
    $updated = $database->update('users',
        ['password' => $hashedPassword],
        'id = :id',
        ['id' => $userId]
    );
    
    if ($updated) {
        // تسجيل النشاط
        logActivity('password_changed', 'users', $userId);
        return ['success' => true];
    }
    
    return ['success' => false, 'error' => 'فشل في تغيير كلمة المرور'];
}

/**
 * دالة إعادة تعيين كلمة المرور
 */
function resetPassword($email) {
    global $database;
    
    // البحث عن المستخدم
    $user = $database->fetch(
        "SELECT * FROM users WHERE email = :email AND is_active = 1",
        ['email' => $email]
    );
    
    if (!$user) {
        return false;
    }
    
    // إنشاء رمز إعادة التعيين
    $resetToken = generateToken();
    $resetExpiry = date('Y-m-d H:i:s', time() + 3600); // ساعة واحدة
    
    // حفظ الرمز في قاعدة البيانات
    $database->update('users',
        [
            'remember_token' => $resetToken,
            'updated_at' => date('Y-m-d H:i:s')
        ],
        'id = :id',
        ['id' => $user['id']]
    );
    
    // إرسال بريد إعادة التعيين
    $resetLink = SITE_URL . '/auth/reset-password?token=' . $resetToken;
    $sent = sendPasswordResetEmail($user['email'], $user['name'], $resetLink);
    
    if ($sent) {
        logActivity('password_reset_requested', 'users', $user['id']);
        return true;
    }
    
    return false;
}

/**
 * دالة التحقق من رمز إعادة التعيين
 */
function verifyResetToken($token) {
    global $database;
    
    $user = $database->fetch(
        "SELECT * FROM users WHERE remember_token = :token AND is_active = 1",
        ['token' => $token]
    );
    
    if (!$user) {
        return null;
    }
    
    // التحقق من انتهاء صلاحية الرمز (ساعة واحدة)
    $tokenAge = time() - strtotime($user['updated_at']);
    if ($tokenAge > 3600) {
        return null;
    }
    
    return $user;
}

/**
 * دالة تحديث كلمة المرور بالرمز
 */
function updatePasswordWithToken($token, $newPassword) {
    global $database;
    
    $user = verifyResetToken($token);
    if (!$user) {
        return ['success' => false, 'error' => 'رمز إعادة التعيين غير صحيح أو منتهي الصلاحية'];
    }
    
    // التحقق من كلمة المرور الجديدة
    if (strlen($newPassword) < 6) {
        return ['success' => false, 'error' => 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'];
    }
    
    // تشفير كلمة المرور الجديدة
    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
    
    // تحديث كلمة المرور وحذف الرمز
    $updated = $database->update('users',
        [
            'password' => $hashedPassword,
            'remember_token' => null
        ],
        'id = :id',
        ['id' => $user['id']]
    );
    
    if ($updated) {
        logActivity('password_reset_completed', 'users', $user['id']);
        return ['success' => true];
    }
    
    return ['success' => false, 'error' => 'فشل في تحديث كلمة المرور'];
}

/**
 * دالة التحقق من التذكر التلقائي
 */
function checkRememberToken() {
    if (!isLoggedIn() && isset($_COOKIE['remember_token'])) {
        global $database;
        
        $user = $database->fetch(
            "SELECT * FROM users WHERE remember_token = :token AND is_active = 1",
            ['token' => $_COOKIE['remember_token']]
        );
        
        if ($user) {
            // إنشاء جلسة جديدة
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_email'] = $user['email'];
            $_SESSION['user_name'] = $user['name'];
            $_SESSION['user_role'] = $user['role'];
            $_SESSION['login_time'] = time();
            
            logActivity('auto_login_remember_token', 'users', $user['id']);
            return true;
        } else {
            // حذف الكوكي إذا كان الرمز غير صحيح
            setcookie('remember_token', '', time() - 3600, '/', '', false, true);
        }
    }
    
    return false;
}

/**
 * دالة التحقق من انتهاء الجلسة
 */
function checkSessionExpiry() {
    if (isLoggedIn()) {
        $loginTime = $_SESSION['login_time'] ?? 0;
        $currentTime = time();
        
        // إذا انتهت صلاحية الجلسة (حسب SESSION_LIFETIME)
        if (($currentTime - $loginTime) > SESSION_LIFETIME) {
            logout();
            return false;
        }
        
        // تحديث وقت آخر نشاط
        $_SESSION['last_activity'] = $currentTime;
    }
    
    return true;
}

/**
 * دالة إرسال بريد الترحيب
 */
function sendWelcomeEmail($email, $name) {
    $subject = 'مرحباً بك في ' . getSetting('site_name', 'Green Line');
    $message = "
    <html>
    <body dir='rtl'>
        <h2>مرحباً {$name}</h2>
        <p>نرحب بك في موقع " . getSetting('site_name', 'Green Line') . "</p>
        <p>يمكنك الآن تصفح منتجاتنا وإضافة تقييماتك.</p>
        <p>شكراً لانضمامك إلينا!</p>
    </body>
    </html>
    ";
    
    return sendEmail($email, $subject, $message, true);
}

/**
 * دالة إرسال بريد إعادة تعيين كلمة المرور
 */
function sendPasswordResetEmail($email, $name, $resetLink) {
    $subject = 'إعادة تعيين كلمة المرور - ' . getSetting('site_name', 'Green Line');
    $message = "
    <html>
    <body dir='rtl'>
        <h2>مرحباً {$name}</h2>
        <p>تلقينا طلباً لإعادة تعيين كلمة المرور الخاصة بك.</p>
        <p>اضغط على الرابط التالي لإعادة تعيين كلمة المرور:</p>
        <p><a href='{$resetLink}'>إعادة تعيين كلمة المرور</a></p>
        <p>إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذا البريد.</p>
        <p>ملاحظة: هذا الرابط صالح لمدة ساعة واحدة فقط.</p>
    </body>
    </html>
    ";
    
    return sendEmail($email, $subject, $message, true);
}

/**
 * معالجة تسجيل الخروج
 */
function handleLogout() {
    logout();
    redirect(SITE_URL . '/auth/login');
}

// التحقق من التذكر التلقائي عند تحميل الصفحة
checkRememberToken();

// التحقق من انتهاء الجلسة
checkSessionExpiry();

?>