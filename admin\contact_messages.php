<?php
/**
 * صفحة إدارة رسائل التواصل
 * Contact Messages Management
 */

// تعريف متغيرات الصفحة
$pageTitle = 'إدارة رسائل التواصل';
$currentPage = 'contact_messages';
$pageDescription = 'إدارة ومتابعة رسائل العملاء والاستفسارات';
$breadcrumbs = [
    ['title' => 'التواصل', 'url' => '/admin/contact_messages.php'],
    ['title' => 'رسائل التواصل']
];

// تضمين التخطيط
require_once 'includes/layout.php';

// تضمين قاعدة البيانات
require_once '../config/database.php';

// معالجة تصدير CSV
if (isset($_GET['export']) && $_GET['export'] === 'csv') {
    // إعداد رؤوس الاستجابة لتنزيل ملف CSV
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="contact_messages_' . date('Y-m-d') . '.csv"');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    // فتح مخرج CSV
    $output = fopen('php://output', 'w');
    
    // إضافة BOM لدعم اللغة العربية في Excel
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // كتابة عناوين الأعمدة
    fputcsv($output, [
        'الرقم',
        'الاسم',
        'البريد الإلكتروني',
        'الهاتف',
        'الموضوع',
        'الرسالة',
        'الحالة',
        'الرد',
        'تاريخ الإنشاء',
        'تاريخ الرد'
    ]);
    
    // جلب البيانات وكتابتها
    $query = "SELECT * FROM contact_messages ORDER BY created_at DESC";
    $messages = $database->fetchAll($query);
    
    foreach ($messages as $message) {
        fputcsv($output, [
            $message['id'],
            $message['name'],
            $message['email'],
            $message['phone'],
            $message['subject'],
            $message['message'],
            $message['status'] === 'unread' ? 'غير مقروءة' : ($message['status'] === 'read' ? 'مقروءة' : 'تم الرد'),
            $message['reply'],
            $message['created_at'],
            $message['replied_at']
        ]);
    }
    
    fclose($output);
    exit;
}

// معالجة الرد على الرسالة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['reply_message'])) {
    $message_id = (int)$_POST['message_id'];
    $reply = trim($_POST['reply']);
    
    if ($message_id && $reply) {
        // تحديث الرسالة
        $update_query = "UPDATE contact_messages SET 
                        status = 'replied', 
                        reply = ?, 
                        replied_at = NOW() 
                        WHERE id = ?";
        
        if ($database->query($update_query, [$reply, $message_id])) {
            // جلب بيانات الرسالة لإرسال البريد
            $message_data = $database->fetch("SELECT * FROM contact_messages WHERE id = ?", [$message_id]);
            
            if ($message_data && function_exists('sendEmail')) {
                // إرسال الرد بالبريد الإلكتروني
                $email_subject = "رد على استفسارك: " . $message_data['subject'];
                $email_body = "
                <div dir='rtl' style='font-family: Arial, sans-serif;'>
                    <h2>شكراً لتواصلك معنا</h2>
                    <p>عزيزي/عزيزتي {$message_data['name']},</p>
                    <p>نشكرك على تواصلك معنا، وفيما يلي ردنا على استفسارك:</p>
                    
                    <div style='background: #f8f9fa; padding: 15px; border-right: 4px solid #28a745; margin: 20px 0;'>
                        <strong>استفسارك:</strong><br>
                        {$message_data['message']}
                    </div>
                    
                    <div style='background: #e8f5e8; padding: 15px; border-right: 4px solid #28a745; margin: 20px 0;'>
                        <strong>ردنا:</strong><br>
                        {$reply}
                    </div>
                    
                    <p>إذا كان لديك أي استفسارات إضافية، لا تتردد في التواصل معنا.</p>
                    <p>مع أطيب التحيات،<br>فريق خدمة العملاء</p>
                </div>";
                
                if (sendEmail($message_data['email'], $email_subject, $email_body)) {
                    $success = "تم إرسال الرد بنجاح وإرسال بريد إلكتروني للعميل";
                } else {
                    $success = "تم حفظ الرد بنجاح ولكن فشل إرسال البريد الإلكتروني";
                }
            } else {
                $success = "تم حفظ الرد بنجاح";
            }
        } else {
            $error = "حدث خطأ أثناء حفظ الرد";
        }
    } else {
        $error = "يرجى كتابة الرد";
    }
}

// معالجة تحديث حالة الرسالة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    $message_id = (int)$_POST['message_id'];
    $new_status = $_POST['status'];
    
    if ($message_id && in_array($new_status, ['unread', 'read', 'replied'])) {
        $update_query = "UPDATE contact_messages SET status = ? WHERE id = ?";
        if ($database->query($update_query, [$new_status, $message_id])) {
            $success = "تم تحديث حالة الرسالة بنجاح";
        } else {
            $error = "حدث خطأ أثناء تحديث الحالة";
        }
    }
}

// معالجة البحث والتصفية
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$subject_filter = $_GET['subject'] ?? '';
$date_filter = $_GET['date'] ?? '';

// بناء استعلام البحث
$where_conditions = [];
$params = [];

if ($search) {
    $where_conditions[] = "(name LIKE ? OR email LIKE ? OR message LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($status_filter) {
    $where_conditions[] = "status = ?";
    $params[] = $status_filter;
}

if ($subject_filter) {
    $where_conditions[] = "subject = ?";
    $params[] = $subject_filter;
}

if ($date_filter) {
    switch ($date_filter) {
        case 'today':
            $where_conditions[] = "DATE(created_at) = CURDATE()";
            break;
        case 'yesterday':
            $where_conditions[] = "DATE(created_at) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)";
            break;
        case 'week':
            $where_conditions[] = "created_at >= DATE_SUB(NOW(), INTERVAL 1 WEEK)";
            break;
        case 'month':
            $where_conditions[] = "created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)";
            break;
    }
}

$where_clause = $where_conditions ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// جلب الرسائل
$query = "SELECT * FROM contact_messages $where_clause ORDER BY created_at DESC";
$messages = $database->fetchAll($query, $params);

// جلب الإحصائيات
$total_messages = $database->fetch("SELECT COUNT(*) as count FROM contact_messages")['count'];
$unread_messages = $database->fetch("SELECT COUNT(*) as count FROM contact_messages WHERE status IN ('new', 'unread')")['count'];
$read_messages = $database->fetch("SELECT COUNT(*) as count FROM contact_messages WHERE status = 'read'")['count'];
$replied_messages = $database->fetch("SELECT COUNT(*) as count FROM contact_messages WHERE status = 'replied'")['count'];

$stats = [];
$stats['all'] = $total_messages;
$stats['unread'] = $unread_messages;
$stats['read'] = $read_messages;
$stats['replied'] = $replied_messages;

// إحصائيات إضافية
$stats['today'] = $database->fetch("SELECT COUNT(*) as count FROM contact_messages WHERE DATE(created_at) = CURDATE()")['count'];

// متوسط وقت الرد (بالساعات)
$avg_response = $database->fetch("
    SELECT AVG(TIMESTAMPDIFF(HOUR, created_at, replied_at)) as avg_hours 
    FROM contact_messages 
    WHERE status = 'replied' AND replied_at IS NOT NULL
");
$stats['avg_response_hours'] = $avg_response['avg_hours'] ? round($avg_response['avg_hours'], 1) : 0;

// معدل الرد
$stats['response_rate'] = $stats['all'] > 0 ? round(($stats['replied'] / $stats['all']) * 100, 1) : 0;

// جلب المواضيع المختلفة
$subjects = $database->fetchAll("SELECT DISTINCT subject, COUNT(*) as count FROM contact_messages GROUP BY subject ORDER BY count DESC");

// بدء التخطيط
startLayout();

// عرض رأس الصفحة والرسائل
showPageHeader();
showMessages();
?>

<!-- أزرار العمليات -->
<div class="mb-6">
    <div class="flex items-center justify-between">
        <div class="flex gap-3">
            <a href="?<?php echo http_build_query(array_merge($_GET, ['export' => 'csv'])); ?>" 
               class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                <i class="fas fa-download ml-2"></i>
                تصدير CSV
            </a>
        </div>
    </div>
</div>

<!-- الإحصائيات السريعة -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">إجمالي الرسائل</p>
                <p class="text-3xl font-bold text-gray-900"><?php echo number_format($stats['all']); ?></p>
            </div>
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-envelope text-blue-600 text-xl"></i>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">غير مقروءة</p>
                <p class="text-3xl font-bold text-red-600"><?php echo number_format($stats['unread']); ?></p>
            </div>
            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-envelope text-red-600 text-xl"></i>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">تم الرد عليها</p>
                <p class="text-3xl font-bold text-green-600"><?php echo number_format($stats['replied']); ?></p>
            </div>
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-reply text-green-600 text-xl"></i>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">رسائل اليوم</p>
                <p class="text-3xl font-bold text-purple-600"><?php echo number_format($stats['today']); ?></p>
            </div>
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-calendar-day text-purple-600 text-xl"></i>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات تفصيلية -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">معدل الأداء</h3>
        <div class="space-y-4">
            <div class="flex justify-between items-center">
                <span class="text-gray-600">متوسط وقت الرد</span>
                <span class="font-bold text-blue-600"><?php echo $stats['avg_response_hours']; ?> ساعة</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-gray-600">معدل الرد</span>
                <span class="font-bold text-green-600"><?php echo $stats['response_rate']; ?>%</span>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">توزيع الحالات</h3>
        <div class="space-y-3">
            <div class="flex justify-between items-center">
                <span class="text-gray-600">مقروءة</span>
                <span class="font-bold"><?php echo $stats['read']; ?></span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-gray-600">غير مقروءة</span>
                <span class="font-bold text-red-600"><?php echo $stats['unread']; ?></span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-gray-600">تم الرد</span>
                <span class="font-bold text-green-600"><?php echo $stats['replied']; ?></span>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">المواضيع الشائعة</h3>
        <div class="space-y-2">
            <?php foreach (array_slice($subjects, 0, 3) as $subject): ?>
                <div class="flex justify-between items-center text-sm">
                    <span class="text-gray-600 truncate"><?php echo htmlspecialchars($subject['subject']); ?></span>
                    <span class="font-bold"><?php echo $subject['count']; ?></span>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>

<!-- شريط البحث والتصفية -->
<div class="bg-white rounded-lg shadow-sm p-6 mb-6">
    <form method="GET" class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                <input type="text" name="search" value="<?php echo htmlspecialchars($search); ?>" 
                       placeholder="البحث في الاسم، البريد، أو الرسالة..."
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">جميع الحالات</option>
                    <option value="unread" <?php echo $status_filter === 'unread' ? 'selected' : ''; ?>>غير مقروءة</option>
                    <option value="read" <?php echo $status_filter === 'read' ? 'selected' : ''; ?>>مقروءة</option>
                    <option value="replied" <?php echo $status_filter === 'replied' ? 'selected' : ''; ?>>تم الرد</option>
                </select>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">الموضوع</label>
                <select name="subject" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">جميع المواضيع</option>
                    <?php foreach ($subjects as $subject): ?>
                        <option value="<?php echo htmlspecialchars($subject['subject']); ?>" 
                                <?php echo $subject_filter === $subject['subject'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($subject['subject']); ?> (<?php echo $subject['count']; ?>)
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">التاريخ</label>
                <select name="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">جميع التواريخ</option>
                    <option value="today" <?php echo $date_filter === 'today' ? 'selected' : ''; ?>>اليوم</option>
                    <option value="yesterday" <?php echo $date_filter === 'yesterday' ? 'selected' : ''; ?>>أمس</option>
                    <option value="week" <?php echo $date_filter === 'week' ? 'selected' : ''; ?>>هذا الأسبوع</option>
                    <option value="month" <?php echo $date_filter === 'month' ? 'selected' : ''; ?>>هذا الشهر</option>
                </select>
            </div>
        </div>
        
        <div class="flex gap-3">
            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                <i class="fas fa-search ml-2"></i>
                بحث
            </button>
            <a href="?" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500">
                <i class="fas fa-redo ml-2"></i>
                إعادة تعيين
            </a>
        </div>
    </form>
</div>

<!-- جدول الرسائل -->
<div class="bg-white rounded-lg shadow-sm overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">رسائل التواصل</h3>
    </div>
    
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المرسل</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الموضوع</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الرسالة</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التاريخ</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <?php if (empty($messages)): ?>
                    <tr>
                        <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                            <i class="fas fa-inbox text-4xl mb-2"></i>
                            <p>لا توجد رسائل</p>
                        </td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($messages as $index => $message): ?>
                        <tr class="hover:bg-gray-50 <?php echo $message['status'] === 'unread' || $message['status'] === 'new' ? 'bg-blue-50' : ''; ?>" data-message-id="<?php echo $message['id']; ?>">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo $index + 1; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div>
                                    <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($message['name']); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo htmlspecialchars($message['email']); ?></div>
                                    <?php if ($message['phone']): ?>
                                        <div class="text-sm text-gray-500"><?php echo htmlspecialchars($message['phone']); ?></div>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900"><?php echo htmlspecialchars($message['subject']); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900 max-w-xs truncate" title="<?php echo htmlspecialchars($message['message']); ?>">
                                    <?php echo htmlspecialchars(substr($message['message'], 0, 100)); ?>
                                    <?php if (strlen($message['message']) > 100): ?>...<?php endif; ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php
                                $status_classes = [
                                    'new' => 'bg-blue-100 text-blue-800',
                                    'unread' => 'bg-red-100 text-red-800',
                                    'read' => 'bg-yellow-100 text-yellow-800',
                                    'replied' => 'bg-green-100 text-green-800'
                                ];
                                $status_text = [
                                    'new' => 'جديدة',
                                    'unread' => 'غير مقروءة',
                                    'read' => 'مقروءة',
                                    'replied' => 'تم الرد'
                                ];
                                
                                // التأكد من وجود الحالة في المصفوفة
                                $message_status = $message['status'] ?? 'new';
                                $status_class = $status_classes[$message_status] ?? $status_classes['new'];
                                $status_display = $status_text[$message_status] ?? $status_text['new'];
                                ?>
                                <span class="status-badge inline-flex px-2 py-1 text-xs font-semibold rounded-full <?php echo $status_class; ?>">
                                    <?php echo $status_display; ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo date('Y-m-d H:i', strtotime($message['created_at'])); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="viewMessage(<?php echo $message['id']; ?>)" 
                                        class="text-blue-600 hover:text-blue-900 ml-3">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <?php if ($message['status'] !== 'replied'): ?>
                                    <button onclick="replyMessage(<?php echo $message['id']; ?>)" 
                                            class="text-green-600 hover:text-green-900 ml-3">
                                        <i class="fas fa-reply"></i>
                                    </button>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- Modal لعرض الرسالة -->
<div id="messageModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-screen overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">تفاصيل الرسالة</h3>
                <button onclick="closeModal()" class="absolute top-4 left-4 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full p-2 transition-colors duration-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="messageContent" class="p-6">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end">
                <button onclick="closeModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors duration-200">
                    إغلاق
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal للرد -->
<div id="replyModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">الرد على الرسالة</h3>
                <button onclick="closeReplyModal()" class="absolute top-4 left-4 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full p-2 transition-colors duration-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form method="POST">
                <div class="p-6">
                    <input type="hidden" name="reply_message" value="1">
                    <input type="hidden" name="message_id" id="replyMessageId">
                    
                    <div id="originalMessage" class="mb-4 p-4 bg-gray-50 rounded-lg">
                        <!-- سيتم تحميل الرسالة الأصلية هنا -->
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الرد</label>
                        <textarea name="reply" rows="6" required
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="اكتب ردك هنا..."></textarea>
                    </div>
                </div>
                <div class="px-6 py-4 border-t border-gray-200 flex justify-end gap-3">
                    <button type="button" onclick="closeReplyModal()" 
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors duration-200">
                        إلغاء
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200">
                        <i class="fas fa-paper-plane ml-2"></i>
                        إرسال الرد
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// بيانات الرسائل لـ JavaScript
const messages = <?php echo json_encode($messages); ?>;

function viewMessage(messageId) {
    const message = messages.find(m => m.id == messageId);
    if (!message) return;
    
    document.body.style.overflow = 'hidden';
    
    const content = `
        <div class="space-y-4">
            <div>
                <h4 class="font-medium text-gray-900">معلومات المرسل</h4>
                <div class="mt-2 text-sm text-gray-600">
                    <p><strong>الاسم:</strong> ${message.name}</p>
                    <p><strong>البريد الإلكتروني:</strong> ${message.email}</p>
                    ${message.phone ? `<p><strong>الهاتف:</strong> ${message.phone}</p>` : ''}
                </div>
            </div>
            
            <div>
                <h4 class="font-medium text-gray-900">الموضوع</h4>
                <p class="mt-2 text-sm text-gray-600">${message.subject}</p>
            </div>
            
            <div>
                <h4 class="font-medium text-gray-900">الرسالة</h4>
                <div class="mt-2 p-4 bg-gray-50 rounded-lg text-sm text-gray-700">
                    ${message.message.replace(/\n/g, '<br>')}
                </div>
            </div>
            
            ${message.reply ? `
                <div>
                    <h4 class="font-medium text-gray-900">الرد</h4>
                    <div class="mt-2 p-4 bg-green-50 rounded-lg text-sm text-gray-700">
                        ${message.reply.replace(/\n/g, '<br>')}
                    </div>
                    <p class="mt-2 text-xs text-gray-500">تم الرد في: ${message.replied_at}</p>
                </div>
            ` : ''}
            
            <div class="text-xs text-gray-500">
                <p>تاريخ الإرسال: ${message.created_at}</p>
            </div>
        </div>
    `;
    
    document.getElementById('messageContent').innerHTML = content;
    document.getElementById('messageModal').classList.remove('hidden');
    
    // تحديث حالة الرسالة إلى مقروءة إذا لم تكن كذلك
    if (message.status === 'unread' || message.status === 'new') {
        updateMessageStatus(messageId, 'read');
    }
}

function replyMessage(messageId) {
    const message = messages.find(m => m.id == messageId);
    if (!message) return;
    
    document.body.style.overflow = 'hidden';
    
    document.getElementById('replyMessageId').value = messageId;
    
    const originalContent = `
        <h5 class="font-medium text-gray-900 mb-2">الرسالة الأصلية:</h5>
        <div class="text-sm text-gray-600">
            <p><strong>من:</strong> ${message.name} (${message.email})</p>
            <p><strong>الموضوع:</strong> ${message.subject}</p>
            <div class="mt-2 p-3 bg-white rounded border">
                ${message.message.replace(/\n/g, '<br>')}
            </div>
        </div>
    `;
    
    document.getElementById('originalMessage').innerHTML = originalContent;
    document.getElementById('replyModal').classList.remove('hidden');
}

function updateMessageStatus(messageId, status) {
    // استخدام AJAX لتحديث الحالة بدون إعادة تحميل الصفحة
    fetch(window.location.href, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `update_status=1&message_id=${messageId}&status=${status}`
    })
    .then(response => response.text())
    .then(data => {
        // تحديث حالة الرسالة في البيانات المحلية
        const message = messages.find(m => m.id == messageId);
        if (message) {
            message.status = status;
        }
        
        // تحديث عرض الحالة في الجدول إذا كان مرئياً
        const statusElement = document.querySelector(`tr[data-message-id="${messageId}"] .status-badge`);
        if (statusElement) {
            updateStatusDisplay(statusElement, status);
        }
        
        console.log('تم تحديث حالة الرسالة بنجاح');
    })
    .catch(error => {
        console.error('خطأ في تحديث حالة الرسالة:', error);
    });
}

function updateStatusDisplay(element, status) {
    // إزالة الفئات القديمة
    element.className = element.className.replace(/bg-\w+-100 text-\w+-800/g, '');
    
    // إضافة الفئات الجديدة حسب الحالة
    const statusClasses = {
        'new': 'bg-blue-100 text-blue-800',
        'unread': 'bg-red-100 text-red-800',
        'read': 'bg-yellow-100 text-yellow-800',
        'replied': 'bg-green-100 text-green-800'
    };
    
    const statusText = {
        'new': 'جديدة',
        'unread': 'غير مقروءة',
        'read': 'مقروءة',
        'replied': 'تم الرد'
    };
    
    element.className += ' ' + (statusClasses[status] || statusClasses['new']);
    element.textContent = statusText[status] || statusText['new'];
}

// إغلاق النوافذ المنبثقة عند النقر خارجها
window.onclick = function(event) {
    const messageModal = document.getElementById('messageModal');
    const replyModal = document.getElementById('replyModal');
    
    if (event.target === messageModal) {
        closeModal();
    }
    if (event.target === replyModal) {
        closeReplyModal();
    }
}

// إغلاق النوافذ المنبثقة بمفتاح Escape
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeModal();
        closeReplyModal();
    }
});

function closeModal() {
    document.getElementById('messageModal').classList.add('hidden');
    document.body.style.overflow = 'auto';
}

function closeReplyModal() {
    document.getElementById('replyModal').classList.add('hidden');
    document.body.style.overflow = 'auto';
}

// إغلاق النوافذ المنبثقة عند النقر خارجها
document.getElementById('messageModal').addEventListener('click', function(e) {
    if (e.target === this) closeModal();
});

document.getElementById('replyModal').addEventListener('click', function(e) {
    if (e.target === this) closeReplyModal();
});
</script>

<?php
// إنهاء التخطيط
endLayout();
?>