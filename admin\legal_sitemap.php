<?php
require_once 'includes/layout.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !hasPermission('admin')) {
    redirect('/admin/login.php');
}

$pageTitle = 'إدارة خريطة الموقع';
$currentPage = 'legal';
$pageDescription = 'إدارة محتوى صفحة خريطة الموقع';
$breadcrumbs = [
    ['title' => 'الصفحات القانونية', 'url' => '#'],
    ['title' => 'خريطة الموقع']
];

require_once '../config/database.php';
$database = new Database();
$pdo = $database->getConnection();

$message = '';
$error = '';

// إنشاء الجدول إذا لم يكن موجوداً
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS legal_pages (
        id INT PRIMARY KEY AUTO_INCREMENT,
        page_type ENUM('privacy', 'terms', 'sitemap') NOT NULL,
        title VARCHAR(255) NOT NULL,
        content LONGTEXT,
        meta_description TEXT,
        meta_keywords TEXT,
        contact_phone VARCHAR(50),
        contact_email VARCHAR(100),
        contact_address TEXT,
        facebook_url VARCHAR(255),
        twitter_url VARCHAR(255),
        instagram_url VARCHAR(255),
        linkedin_url VARCHAR(255),
        youtube_url VARCHAR(255),
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_page_type (page_type)
    )");

    // إضافة الأعمدة الجديدة إذا لم تكن موجودة
    $columns_to_add = [
        'contact_phone' => 'VARCHAR(50)',
        'contact_email' => 'VARCHAR(100)', 
        'contact_address' => 'TEXT',
        'facebook_url' => 'VARCHAR(255)',
        'twitter_url' => 'VARCHAR(255)',
        'instagram_url' => 'VARCHAR(255)',
        'linkedin_url' => 'VARCHAR(255)',
        'youtube_url' => 'VARCHAR(255)'
    ];

    foreach ($columns_to_add as $column => $type) {
        try {
            $pdo->exec("ALTER TABLE legal_pages ADD COLUMN $column $type");
        } catch (PDOException $e) {
            // العمود موجود بالفعل، تجاهل الخطأ
        }
    }

    // إدراج البيانات الافتراضية لخريطة الموقع
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM legal_pages WHERE page_type = 'sitemap'");
    $stmt->execute();
    if ($stmt->fetchColumn() == 0) {
        $default_content = '
        <div class="space-y-8">
            <section>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">الصفحات الرئيسية</h2>
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="space-y-3">
                        <h3 class="text-lg font-semibold text-gray-700">الصفحات العامة</h3>
                        <ul class="space-y-2">
                            <li><a href="/" class="text-blue-600 hover:text-blue-800 transition-colors">الصفحة الرئيسية</a></li>
                            <li><a href="/about" class="text-blue-600 hover:text-blue-800 transition-colors">من نحن</a></li>
                            <li><a href="/contact" class="text-blue-600 hover:text-blue-800 transition-colors">اتصل بنا</a></li>
                            <li><a href="/faq" class="text-blue-600 hover:text-blue-800 transition-colors">الأسئلة الشائعة</a></li>
                        </ul>
                    </div>
                    <div class="space-y-3">
                        <h3 class="text-lg font-semibold text-gray-700">المنتجات والخدمات</h3>
                        <ul class="space-y-2">
                            <li><a href="/products" class="text-blue-600 hover:text-blue-800 transition-colors">جميع المنتجات</a></li>
                            <li><a href="/categories" class="text-blue-600 hover:text-blue-800 transition-colors">فئات المنتجات</a></li>
                            <li><a href="/services" class="text-blue-600 hover:text-blue-800 transition-colors">خدماتنا</a></li>
                            <li><a href="/distributors" class="text-blue-600 hover:text-blue-800 transition-colors">الموزعين</a></li>
                        </ul>
                    </div>
                </div>
            </section>

            <section>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">فئات المنتجات</h2>
                <div class="grid md:grid-cols-3 gap-6">
                    <div class="space-y-3">
                        <h3 class="text-lg font-semibold text-gray-700">الأجهزة الطبية</h3>
                        <ul class="space-y-2">
                            <li><a href="/products/medical-devices" class="text-blue-600 hover:text-blue-800 transition-colors">أجهزة التشخيص</a></li>
                            <li><a href="/products/monitoring" class="text-blue-600 hover:text-blue-800 transition-colors">أجهزة المراقبة</a></li>
                            <li><a href="/products/surgical" class="text-blue-600 hover:text-blue-800 transition-colors">الأدوات الجراحية</a></li>
                            <li><a href="/products/laboratory" class="text-blue-600 hover:text-blue-800 transition-colors">معدات المختبر</a></li>
                        </ul>
                    </div>
                    <div class="space-y-3">
                        <h3 class="text-lg font-semibold text-gray-700">المستلزمات الطبية</h3>
                        <ul class="space-y-2">
                            <li><a href="/products/disposables" class="text-blue-600 hover:text-blue-800 transition-colors">المستلزمات المستهلكة</a></li>
                            <li><a href="/products/protective" class="text-blue-600 hover:text-blue-800 transition-colors">معدات الحماية</a></li>
                            <li><a href="/products/sterilization" class="text-blue-600 hover:text-blue-800 transition-colors">أدوات التعقيم</a></li>
                            <li><a href="/products/consumables" class="text-blue-600 hover:text-blue-800 transition-colors">المواد الاستهلاكية</a></li>
                        </ul>
                    </div>
                    <div class="space-y-3">
                        <h3 class="text-lg font-semibold text-gray-700">الأثاث الطبي</h3>
                        <ul class="space-y-2">
                            <li><a href="/products/beds" class="text-blue-600 hover:text-blue-800 transition-colors">الأسرة الطبية</a></li>
                            <li><a href="/products/chairs" class="text-blue-600 hover:text-blue-800 transition-colors">الكراسي الطبية</a></li>
                            <li><a href="/products/tables" class="text-blue-600 hover:text-blue-800 transition-colors">الطاولات الطبية</a></li>
                            <li><a href="/products/cabinets" class="text-blue-600 hover:text-blue-800 transition-colors">خزائن التخزين</a></li>
                        </ul>
                    </div>
                </div>
            </section>

            <section>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">الخدمات</h2>
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="space-y-3">
                        <h3 class="text-lg font-semibold text-gray-700">خدمات ما قبل البيع</h3>
                        <ul class="space-y-2">
                            <li><a href="/services/consultation" class="text-blue-600 hover:text-blue-800 transition-colors">الاستشارات الفنية</a></li>
                            <li><a href="/services/planning" class="text-blue-600 hover:text-blue-800 transition-colors">التخطيط والتصميم</a></li>
                            <li><a href="/services/quotation" class="text-blue-600 hover:text-blue-800 transition-colors">عروض الأسعار</a></li>
                            <li><a href="/services/demo" class="text-blue-600 hover:text-blue-800 transition-colors">العروض التوضيحية</a></li>
                        </ul>
                    </div>
                    <div class="space-y-3">
                        <h3 class="text-lg font-semibold text-gray-700">خدمات ما بعد البيع</h3>
                        <ul class="space-y-2">
                            <li><a href="/services/installation" class="text-blue-600 hover:text-blue-800 transition-colors">التركيب والتشغيل</a></li>
                            <li><a href="/services/training" class="text-blue-600 hover:text-blue-800 transition-colors">التدريب</a></li>
                            <li><a href="/services/maintenance" class="text-blue-600 hover:text-blue-800 transition-colors">الصيانة</a></li>
                            <li><a href="/services/support" class="text-blue-600 hover:text-blue-800 transition-colors">الدعم الفني</a></li>
                        </ul>
                    </div>
                </div>
            </section>

            <section>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">معلومات الشركة</h2>
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="space-y-3">
                        <h3 class="text-lg font-semibold text-gray-700">عن الشركة</h3>
                        <ul class="space-y-2">
                            <li><a href="/about/history" class="text-blue-600 hover:text-blue-800 transition-colors">تاريخ الشركة</a></li>
                            <li><a href="/about/vision" class="text-blue-600 hover:text-blue-800 transition-colors">الرؤية والرسالة</a></li>
                            <li><a href="/about/team" class="text-blue-600 hover:text-blue-800 transition-colors">فريق العمل</a></li>
                            <li><a href="/about/certificates" class="text-blue-600 hover:text-blue-800 transition-colors">الشهادات والاعتمادات</a></li>
                        </ul>
                    </div>
                    <div class="space-y-3">
                        <h3 class="text-lg font-semibold text-gray-700">التواصل</h3>
                        <ul class="space-y-2">
                            <li><a href="/contact/offices" class="text-blue-600 hover:text-blue-800 transition-colors">مكاتبنا</a></li>
                            <li><a href="/contact/distributors" class="text-blue-600 hover:text-blue-800 transition-colors">شبكة الموزعين</a></li>
                            <li><a href="/contact/careers" class="text-blue-600 hover:text-blue-800 transition-colors">الوظائف</a></li>
                            <li><a href="/contact/media" class="text-blue-600 hover:text-blue-800 transition-colors">الإعلام</a></li>
                        </ul>
                    </div>
                </div>
            </section>

            <section>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">الدعم والمساعدة</h2>
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="space-y-3">
                        <h3 class="text-lg font-semibold text-gray-700">المساعدة</h3>
                        <ul class="space-y-2">
                            <li><a href="/help/how-to-order" class="text-blue-600 hover:text-blue-800 transition-colors">كيفية الطلب</a></li>
                            <li><a href="/help/payment" class="text-blue-600 hover:text-blue-800 transition-colors">طرق الدفع</a></li>
                            <li><a href="/help/shipping" class="text-blue-600 hover:text-blue-800 transition-colors">الشحن والتوصيل</a></li>
                            <li><a href="/help/returns" class="text-blue-600 hover:text-blue-800 transition-colors">الإرجاع والاستبدال</a></li>
                        </ul>
                    </div>
                    <div class="space-y-3">
                        <h3 class="text-lg font-semibold text-gray-700">الموارد</h3>
                        <ul class="space-y-2">
                            <li><a href="/resources/manuals" class="text-blue-600 hover:text-blue-800 transition-colors">أدلة المستخدم</a></li>
                            <li><a href="/resources/videos" class="text-blue-600 hover:text-blue-800 transition-colors">مقاطع الفيديو</a></li>
                            <li><a href="/resources/downloads" class="text-blue-600 hover:text-blue-800 transition-colors">التحميلات</a></li>
                            <li><a href="/resources/news" class="text-blue-600 hover:text-blue-800 transition-colors">الأخبار والمقالات</a></li>
                        </ul>
                    </div>
                </div>
            </section>

            <section>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">الصفحات القانونية</h2>
                <div class="space-y-3">
                    <ul class="space-y-2">
                        <li><a href="/privacy" class="text-blue-600 hover:text-blue-800 transition-colors">سياسة الخصوصية</a></li>
                        <li><a href="/terms" class="text-blue-600 hover:text-blue-800 transition-colors">الشروط والأحكام</a></li>
                        <li><a href="/sitemap" class="text-blue-600 hover:text-blue-800 transition-colors">خريطة الموقع</a></li>
                        <li><a href="/cookies" class="text-blue-600 hover:text-blue-800 transition-colors">سياسة ملفات تعريف الارتباط</a></li>
                    </ul>
                </div>
            </section>

            <section>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">حساب المستخدم</h2>
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="space-y-3">
                        <h3 class="text-lg font-semibold text-gray-700">إدارة الحساب</h3>
                        <ul class="space-y-2">
                            <li><a href="/account/login" class="text-blue-600 hover:text-blue-800 transition-colors">تسجيل الدخول</a></li>
                            <li><a href="/account/register" class="text-blue-600 hover:text-blue-800 transition-colors">إنشاء حساب جديد</a></li>
                            <li><a href="/account/profile" class="text-blue-600 hover:text-blue-800 transition-colors">الملف الشخصي</a></li>
                            <li><a href="/account/password" class="text-blue-600 hover:text-blue-800 transition-colors">تغيير كلمة المرور</a></li>
                        </ul>
                    </div>
                    <div class="space-y-3">
                        <h3 class="text-lg font-semibold text-gray-700">الطلبات</h3>
                        <ul class="space-y-2">
                            <li><a href="/account/orders" class="text-blue-600 hover:text-blue-800 transition-colors">طلباتي</a></li>
                            <li><a href="/account/wishlist" class="text-blue-600 hover:text-blue-800 transition-colors">قائمة الرغبات</a></li>
                            <li><a href="/account/addresses" class="text-blue-600 hover:text-blue-800 transition-colors">دفتر العناوين</a></li>
                            <li><a href="/account/invoices" class="text-blue-600 hover:text-blue-800 transition-colors">الفواتير</a></li>
                        </ul>
                    </div>
                </div>
            </section>

            <section class="bg-gray-50 p-6 rounded-lg">
                <h2 class="text-2xl font-bold text-gray-800 mb-4">معلومات إضافية</h2>
                <div class="space-y-4">
                    <p class="text-gray-600">
                        <strong>آخر تحديث:</strong> ' . date('Y-m-d') . '
                    </p>
                    <p class="text-gray-600">
                        <strong>إجمالي الصفحات:</strong> أكثر من 100 صفحة
                    </p>
                    <p class="text-gray-600">
                        <strong>اللغات المدعومة:</strong> العربية، الإنجليزية
                    </p>
                    <p class="text-gray-600">
                        <strong>تحديث المحتوى:</strong> يتم تحديث المحتوى بانتظام لضمان دقة المعلومات
                    </p>
                </div>
            </section>
        </div>';

        $stmt = $pdo->prepare("INSERT INTO legal_pages (page_type, title, content, meta_description, meta_keywords, contact_phone, contact_email, contact_address, facebook_url, twitter_url, instagram_url, linkedin_url, youtube_url) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([
            'sitemap',
            'خريطة الموقع',
            $default_content,
            'خريطة موقع شركة الخط الأخضر - دليل شامل لجميع صفحات وأقسام الموقع',
            'خريطة الموقع, دليل الموقع, فهرس الصفحات, التنقل, الأقسام',
            '+966 11 123 4567',
            '<EMAIL>',
            'الرياض، المملكة العربية السعودية',
            '#',
            '#',
            '#',
            '#',
            '#'
        ]);
    }

} catch (PDOException $e) {
    $error = "خطأ في قاعدة البيانات: " . $e->getMessage();
}

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_sitemap') {
        try {
            $title = trim($_POST['title'] ?? '');
            $content = $_POST['content'] ?? '';
            $meta_description = trim($_POST['meta_description'] ?? '');
            $meta_keywords = trim($_POST['meta_keywords'] ?? '');
            $contact_phone = trim($_POST['contact_phone'] ?? '');
            $contact_email = trim($_POST['contact_email'] ?? '');
            $contact_address = trim($_POST['contact_address'] ?? '');
            $facebook_url = trim($_POST['facebook_url'] ?? '');
            $twitter_url = trim($_POST['twitter_url'] ?? '');
            $instagram_url = trim($_POST['instagram_url'] ?? '');
            $linkedin_url = trim($_POST['linkedin_url'] ?? '');
            $youtube_url = trim($_POST['youtube_url'] ?? '');
            $is_active = isset($_POST['is_active']) ? 1 : 0;

            if (empty($title)) {
                throw new Exception('عنوان الصفحة مطلوب');
            }

            $stmt = $pdo->prepare("
                UPDATE legal_pages SET 
                    title = ?, 
                    content = ?, 
                    meta_description = ?, 
                    meta_keywords = ?, 
                    contact_phone = ?,
                    contact_email = ?,
                    contact_address = ?,
                    facebook_url = ?,
                    twitter_url = ?,
                    instagram_url = ?,
                    linkedin_url = ?,
                    youtube_url = ?,
                    is_active = ?
                WHERE page_type = 'sitemap'
            ");
            
            if ($stmt->execute([$title, $content, $meta_description, $meta_keywords, $contact_phone, $contact_email, $contact_address, $facebook_url, $twitter_url, $instagram_url, $linkedin_url, $youtube_url, $is_active])) {
                $message = 'تم تحديث خريطة الموقع بنجاح';
            } else {
                throw new Exception('فشل في تحديث البيانات');
            }

        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}

// جلب البيانات الحالية
$sitemap_data = null;
try {
    $stmt = $pdo->prepare("SELECT * FROM legal_pages WHERE page_type = 'sitemap'");
    $stmt->execute();
    $sitemap_data = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = "خطأ في جلب البيانات: " . $e->getMessage();
}

startLayout($pageTitle, $currentPage, $pageDescription, $breadcrumbs);
showPageHeader();
?>

<div class="container mx-auto px-4 py-6">
    <!-- رسائل النجاح والخطأ -->
    <?php if ($message): ?>
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
            <?php echo htmlspecialchars($message); ?>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <?php echo htmlspecialchars($error); ?>
        </div>
    <?php endif; ?>

    <!-- نموذج تحرير خريطة الموقع -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center justify-between mb-6">
            <h1 class="text-2xl font-bold text-gray-800">إدارة خريطة الموقع</h1>
            <a href="/sitemap" target="_blank" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
                <i class="fas fa-external-link-alt ml-2"></i>
                معاينة الصفحة
            </a>
        </div>

        <form method="POST" class="space-y-6">
            <input type="hidden" name="action" value="update_sitemap">

            <!-- عنوان الصفحة -->
            <div>
                <label for="title" class="block text-sm font-medium text-gray-700 mb-2">عنوان الصفحة</label>
                <input type="text" id="title" name="title" 
                       value="<?php echo htmlspecialchars($sitemap_data['title'] ?? ''); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                       required>
            </div>

            <!-- محتوى الصفحة -->
            <div>
                <label for="content" class="block text-sm font-medium text-gray-700 mb-2">محتوى الصفحة</label>
                <textarea id="content" name="content" rows="20"
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="محتوى خريطة الموقع..."><?php echo htmlspecialchars($sitemap_data['content'] ?? ''); ?></textarea>
            </div>

            <!-- وصف الميتا -->
            <div>
                <label for="meta_description" class="block text-sm font-medium text-gray-700 mb-2">وصف الميتا (SEO)</label>
                <textarea id="meta_description" name="meta_description" rows="3"
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="وصف مختصر للصفحة لمحركات البحث..."><?php echo htmlspecialchars($sitemap_data['meta_description'] ?? ''); ?></textarea>
            </div>

            <!-- كلمات مفتاحية -->
            <div>
                <label for="meta_keywords" class="block text-sm font-medium text-gray-700 mb-2">الكلمات المفتاحية (SEO)</label>
                <input type="text" id="meta_keywords" name="meta_keywords"
                       value="<?php echo htmlspecialchars($sitemap_data['meta_keywords'] ?? ''); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                       placeholder="كلمات مفتاحية مفصولة بفواصل...">
            </div>

            <!-- قسم معلومات الاتصال -->
            <div class="border-t pt-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">معلومات الاتصال</h3>
                
                <!-- رقم الهاتف -->
                <div class="mb-4">
                    <label for="contact_phone" class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                    <input type="text" id="contact_phone" name="contact_phone"
                           value="<?php echo htmlspecialchars($sitemap_data['contact_phone'] ?? ''); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="+966 11 123 4567">
                </div>

                <!-- البريد الإلكتروني -->
                <div class="mb-4">
                    <label for="contact_email" class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                    <input type="email" id="contact_email" name="contact_email"
                           value="<?php echo htmlspecialchars($sitemap_data['contact_email'] ?? ''); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="<EMAIL>">
                </div>

                <!-- العنوان -->
                <div class="mb-4">
                    <label for="contact_address" class="block text-sm font-medium text-gray-700 mb-2">العنوان</label>
                    <textarea id="contact_address" name="contact_address" rows="2"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              placeholder="الرياض، المملكة العربية السعودية"><?php echo htmlspecialchars($sitemap_data['contact_address'] ?? ''); ?></textarea>
                </div>
            </div>

            <!-- قسم روابط التواصل الاجتماعي -->
            <div class="border-t pt-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">روابط التواصل الاجتماعي</h3>
                
                <!-- فيسبوك -->
                <div class="mb-4">
                    <label for="facebook_url" class="block text-sm font-medium text-gray-700 mb-2">رابط فيسبوك</label>
                    <input type="url" id="facebook_url" name="facebook_url"
                           value="<?php echo htmlspecialchars($sitemap_data['facebook_url'] ?? ''); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="https://facebook.com/greenline">
                </div>

                <!-- تويتر -->
                <div class="mb-4">
                    <label for="twitter_url" class="block text-sm font-medium text-gray-700 mb-2">رابط تويتر</label>
                    <input type="url" id="twitter_url" name="twitter_url"
                           value="<?php echo htmlspecialchars($sitemap_data['twitter_url'] ?? ''); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="https://twitter.com/greenline">
                </div>

                <!-- إنستغرام -->
                <div class="mb-4">
                    <label for="instagram_url" class="block text-sm font-medium text-gray-700 mb-2">رابط إنستغرام</label>
                    <input type="url" id="instagram_url" name="instagram_url"
                           value="<?php echo htmlspecialchars($sitemap_data['instagram_url'] ?? ''); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="https://instagram.com/greenline">
                </div>

                <!-- لينكد إن -->
                <div class="mb-4">
                    <label for="linkedin_url" class="block text-sm font-medium text-gray-700 mb-2">رابط لينكد إن</label>
                    <input type="url" id="linkedin_url" name="linkedin_url"
                           value="<?php echo htmlspecialchars($sitemap_data['linkedin_url'] ?? ''); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="https://linkedin.com/company/greenline">
                </div>

                <!-- يوتيوب -->
                <div class="mb-4">
                    <label for="youtube_url" class="block text-sm font-medium text-gray-700 mb-2">رابط يوتيوب</label>
                    <input type="url" id="youtube_url" name="youtube_url"
                           value="<?php echo htmlspecialchars($sitemap_data['youtube_url'] ?? ''); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="https://youtube.com/c/greenline">
                </div>
            </div>

            <!-- حالة النشر -->
            <div class="flex items-center">
                <input type="checkbox" id="is_active" name="is_active" 
                       <?php echo ($sitemap_data['is_active'] ?? 1) ? 'checked' : ''; ?>
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <label for="is_active" class="mr-2 block text-sm text-gray-700">
                    نشر الصفحة
                </label>
            </div>

            <!-- أزرار الحفظ -->
            <div class="flex justify-end space-x-4 space-x-reverse">
                <button type="button" onclick="history.back()" 
                        class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                    إلغاء
                </button>
                <button type="submit" 
                        class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    <i class="fas fa-save ml-2"></i>
                    حفظ التغييرات
                </button>
            </div>
        </form>
    </div>
</div>

<!-- محرر النصوص المتقدم -->
<script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    ClassicEditor
        .create(document.querySelector('#content'), {
            language: 'ar',
            toolbar: [
                'heading', '|',
                'bold', 'italic', 'underline', '|',
                'link', 'bulletedList', 'numberedList', '|',
                'outdent', 'indent', '|',
                'blockQuote', 'insertTable', '|',
                'undo', 'redo'
            ],
            heading: {
                options: [
                    { model: 'paragraph', title: 'فقرة', class: 'ck-heading_paragraph' },
                    { model: 'heading1', view: 'h1', title: 'عنوان 1', class: 'ck-heading_heading1' },
                    { model: 'heading2', view: 'h2', title: 'عنوان 2', class: 'ck-heading_heading2' },
                    { model: 'heading3', view: 'h3', title: 'عنوان 3', class: 'ck-heading_heading3' }
                ]
            }
        })
        .catch(error => {
            console.error(error);
        });
});
</script>

<?php endLayout(); ?>