<?php
require_once 'config/database.php';

// قراءة ملف JSON
$jsonFile = 'faqs.json';
if (!file_exists($jsonFile)) {
    die("ملف JSON غير موجود: $jsonFile");
}

$jsonContent = file_get_contents($jsonFile);
$data = json_decode($jsonContent, true);

if (!$data || !isset($data['faqs'])) {
    die("خطأ في قراءة ملف JSON أو البيانات غير صحيحة");
}

try {
    // الاتصال بقاعدة البيانات باستخدام كلاس Database
    $database = new Database();
    $pdo = $database->getConnection();
    
    // تنظيف الجدول أولاً (اختياري - احذف هذا السطر إذا كنت تريد الاحتفاظ بالبيانات الموجودة)
    // $pdo->exec("TRUNCATE TABLE FAQs");
    
    // إعداد استعلام الإدراج
    $sql = "INSERT INTO FAQs (question, answer, category, sort_order, is_active, created_at, updated_at) 
            VALUES (:question, :answer, :category, :sort_order, :is_active, :created_at, :updated_at)";
    
    $stmt = $pdo->prepare($sql);
    
    $insertedCount = 0;
    $skippedCount = 0;
    
    foreach ($data['faqs'] as $faq) {
        // تحويل التصنيف من الإنجليزية إلى العربية
        $categoryMap = [
            'general' => 'أسئلة عامة',
            'installation' => 'التركيب',
            'maintenance' => 'الصيانة',
            'warranty' => 'الضمان',
            'technical' => 'مشاكل تقنية'
        ];
        
        $category = isset($categoryMap[$faq['category']]) ? $categoryMap[$faq['category']] : $faq['category'];
        
        // تحويل التاريخ
        $createdAt = date('Y-m-d H:i:s', strtotime($faq['createdAt']));
        $updatedAt = date('Y-m-d H:i:s');
        
        // التحقق من وجود السؤال مسبقاً لتجنب التكرار
        $checkSql = "SELECT COUNT(*) FROM FAQs WHERE question = :question";
        $checkStmt = $pdo->prepare($checkSql);
        $checkStmt->bindParam(':question', $faq['question']);
        $checkStmt->execute();
        
        if ($checkStmt->fetchColumn() > 0) {
            echo "تم تخطي السؤال (موجود مسبقاً): " . substr($faq['question'], 0, 50) . "...\n";
            $skippedCount++;
            continue;
        }
        
        // إدراج البيانات
        $stmt->bindParam(':question', $faq['question']);
        $stmt->bindParam(':answer', $faq['answer']);
        $stmt->bindParam(':category', $category);
        $stmt->bindParam(':sort_order', $faq['orderIndex']);
        $stmt->bindParam(':is_active', $faq['isActive'], PDO::PARAM_BOOL);
        $stmt->bindParam(':created_at', $createdAt);
        $stmt->bindParam(':updated_at', $updatedAt);
        
        if ($stmt->execute()) {
            echo "تم إدراج السؤال: " . substr($faq['question'], 0, 50) . "...\n";
            $insertedCount++;
        } else {
            echo "خطأ في إدراج السؤال: " . substr($faq['question'], 0, 50) . "...\n";
        }
    }
    
    echo "\n=== ملخص العملية ===\n";
    echo "تم إدراج: $insertedCount سؤال\n";
    echo "تم تخطي: $skippedCount سؤال (موجود مسبقاً)\n";
    echo "إجمالي الأسئلة في الملف: " . count($data['faqs']) . "\n";
    
    // عرض إحصائيات التصنيفات
    echo "\n=== إحصائيات التصنيفات ===\n";
    $categorySql = "SELECT category, COUNT(*) as count FROM FAQs GROUP BY category ORDER BY count DESC";
    $categoryStmt = $pdo->query($categorySql);
    while ($row = $categoryStmt->fetch(PDO::FETCH_ASSOC)) {
        echo $row['category'] . ": " . $row['count'] . " سؤال\n";
    }
    
} catch (PDOException $e) {
    die("خطأ في قاعدة البيانات: " . $e->getMessage());
} catch (Exception $e) {
    die("خطأ عام: " . $e->getMessage());
}

echo "\nتمت العملية بنجاح!\n";
?>