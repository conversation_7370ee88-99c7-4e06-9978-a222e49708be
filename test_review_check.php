<?php
require_once 'config/config.php';
require_once 'includes/functions.php';

// تشغيل الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$product_id = $_GET['product_id'] ?? 11;

echo "<h1>اختبار فحص التقييمات السابقة</h1>";
echo "<h2>المنتج ID: $product_id</h2>";

// فحص حالة تسجيل الدخول
echo "<h3>حالة تسجيل الدخول:</h3>";
if (isLoggedIn()) {
    $current_user = getCurrentUser();
    echo "<p>✅ المستخدم مسجل دخول: " . htmlspecialchars($current_user['name']) . " (ID: " . $current_user['id'] . ")</p>";
    
    // فحص التقييم السابق
    echo "<h3>فحص التقييم السابق:</h3>";
    $user_has_reviewed = hasUserReviewed($product_id, $current_user['id']);
    echo "<p>هل قام المستخدم بتقييم المنتج؟ " . ($user_has_reviewed ? "✅ نعم" : "❌ لا") . "</p>";
    
    if ($user_has_reviewed) {
        $user_review = getUserReview($product_id, $current_user['id']);
        echo "<h4>تفاصيل التقييم السابق:</h4>";
        echo "<pre>" . print_r($user_review, true) . "</pre>";
    }
    
} else {
    echo "<p>❌ المستخدم غير مسجل دخول</p>";
    echo "<p><a href='" . SITE_URL . "/login'>تسجيل الدخول</a></p>";
}

// عرض جميع التقييمات للمنتج
echo "<h3>جميع التقييمات للمنتج:</h3>";
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $pdo->prepare("SELECT * FROM product_reviews WHERE product_id = ? ORDER BY created_at DESC");
    $stmt->execute([$product_id]);
    $reviews = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($reviews) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>المستخدم ID</th><th>الاسم</th><th>البريد</th><th>التقييم</th><th>العنوان</th><th>التعليق</th><th>التاريخ</th></tr>";
        foreach ($reviews as $review) {
            echo "<tr>";
            echo "<td>" . $review['id'] . "</td>";
            echo "<td>" . $review['user_id'] . "</td>";
            echo "<td>" . htmlspecialchars($review['name']) . "</td>";
            echo "<td>" . htmlspecialchars($review['email']) . "</td>";
            echo "<td>" . $review['rating'] . "</td>";
            echo "<td>" . htmlspecialchars($review['title']) . "</td>";
            echo "<td>" . htmlspecialchars(substr($review['comment'], 0, 50)) . "...</td>";
            echo "<td>" . $review['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>لا توجد تقييمات لهذا المنتج</p>";
    }
} catch (Exception $e) {
    echo "<p>خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

echo "<br><br>";
echo "<a href='" . SITE_URL . "/product_detail.php?id=$product_id&tab=reviews'>العودة لصفحة المنتج</a>";
?>