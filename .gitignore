# Green Line PHP - Git Ignore File
# ملف استبعاد Git للخط الأخضر

# Sensitive configuration files
config/config.php
.env
*.env

# Database files
*.sql
*.db
*.sqlite
*.sqlite3

# Log files
*.log
logs/
error.log
access.log

# Uploaded files
uploads/*
!uploads/.gitkeep
!uploads/README.md

# Cache and temporary files
cache/
tmp/
temp/
*.tmp
*.cache

# Session files
sessions/
*.sess

# Backup files
*.bak
*.backup
*.old
*.orig

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Composer dependencies
vendor/
composer.lock

# Node.js dependencies (if using)
node_modules/
npm-debug.log
yarn-error.log
package-lock.json
yarn.lock

# Build and distribution files
build/
dist/
*.min.js
*.min.css

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows image file caches
Thumbs.db
ehthumbs.db

# Folder config file
Desktop.ini

# Recycle Bin used on file shares
$RECYCLE.BIN/

# Windows Installer files
*.cab
*.msi
*.msm
*.msp

# Windows shortcuts
*.lnk

# Archive files
*.zip
*.rar
*.7z
*.tar
*.gz

# Image originals (keep optimized versions)
*.psd
*.ai
*.sketch

# Video files
*.mp4
*.avi
*.mov
*.wmv
*.flv

# Audio files
*.mp3
*.wav
*.ogg

# Documentation drafts
*.draft
*.todo

# Test files
test/
tests/coverage/
tests/test_report.html
*.test.php

# Development tools
.phpunit.result.cache
.php_cs.cache
.php-cs-fixer.cache
.phpstan.cache
psalm.xml
.psalm/
rector.php
infection.log

# Security files
*.key
*.pem
*.crt
*.csr

# Email templates backup
email_templates_backup/

# Database dumps
*.dump
*.sql.gz

# Error reporting files
error_reports/

# Maintenance mode
maintenance.flag

# Redis files
dump.rdb
*.rdb

# Storage directories
storage/app/*
!storage/app/.gitkeep
storage/framework/cache/*
!storage/framework/cache/.gitkeep
storage/framework/sessions/*
!storage/framework/sessions/.gitkeep
storage/logs/*
!storage/logs/.gitkeep

# Rate limiting logs
rate_limits/

# CSRF tokens cache
csrf_cache/

# Notification logs
notification_logs/

# SEO generated files
sitemap.xml
robots.txt

# Permission logs
permission_logs/

# Performance monitoring
performance_logs/

# API documentation
api_docs/
swagger.json
swagger.yaml

# Backup directories
backups/
*.backup/

# Local development overrides
config.local.php
settings.local.php
includes/config.local.php

# Custom exclusions
custom/
private/
secret/