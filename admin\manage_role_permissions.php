<?php
/**
 * إدارة صلاحيات الأدوار
 * Manage Role Permissions
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/PermissionManager.php';
require_once '../includes/middleware.php';

// التحقق من تسجيل الدخول وصلاحية الإدارة
requirePermission('manage-permissions');

$permissionManager = new PermissionManager($database);
$message = '';
$error = '';

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'update_role_permissions':
            $roleId = $_POST['role_id'] ?? null;
            $permissions = $_POST['permissions'] ?? [];
            
            if ($roleId) {
                $result = $permissionManager->updateRolePermissions($roleId, $permissions, getCurrentUserId());
                
                if ($result['success']) {
                    $message = 'تم تحديث صلاحيات الدور بنجاح';
                } else {
                    $error = $result['error'] ?? 'فشل في تحديث صلاحيات الدور';
                }
            } else {
                $error = 'معرف الدور مطلوب';
            }
            break;
            
        case 'create_role':
            $name = $_POST['name'] ?? '';
            $displayName = $_POST['display_name'] ?? '';
            $description = $_POST['description'] ?? '';
            $permissions = $_POST['permissions'] ?? [];
            
            if ($name && $displayName) {
                try {
                    $roleId = $permissionManager->createRole($name, $displayName, $description);
                    
                    if ($roleId) {
                        // إضافة الصلاحيات للدور الجديد
                        foreach ($permissions as $permissionId) {
                            $permissionManager->assignPermissionToRole($roleId, $permissionId);
                        }
                        
                        $message = 'تم إنشاء الدور بنجاح';
                    } else {
                        $error = 'فشل في إنشاء الدور';
                    }
                } catch (Exception $e) {
                    $error = 'خطأ في إنشاء الدور: ' . $e->getMessage();
                }
            } else {
                $error = 'يرجى ملء جميع الحقول المطلوبة';
            }
            break;
            
        case 'delete_role':
            $roleId = $_POST['role_id'] ?? null;
            
            if ($roleId) {
                try {
                    // التحقق من عدم وجود مستخدمين مرتبطين بهذا الدور
                    $stmt = $database->prepare("SELECT COUNT(*) FROM user_roles WHERE role_id = ?");
                    $stmt->execute([$roleId]);
                    $userCount = $stmt->fetchColumn();
                    
                    if ($userCount > 0) {
                        $error = 'لا يمكن حذف الدور لأنه مرتبط بـ ' . $userCount . ' مستخدم';
                    } else {
                        // حذف صلاحيات الدور
                        $stmt = $database->prepare("DELETE FROM role_permissions WHERE role_id = ?");
                        $stmt->execute([$roleId]);
                        
                        // حذف الدور
                        $stmt = $database->prepare("DELETE FROM roles WHERE id = ?");
                        $stmt->execute([$roleId]);
                        
                        $message = 'تم حذف الدور بنجاح';
                    }
                } catch (Exception $e) {
                    $error = 'خطأ في حذف الدور: ' . $e->getMessage();
                }
            }
            break;
    }
}

// جلب جميع الأدوار
$roles = $database->query("SELECT * FROM roles ORDER BY name")->fetchAll();

// جلب جميع الصلاحيات مجمعة حسب الفئة
$permissions = $database->query("
    SELECT * FROM permissions 
    ORDER BY category, name
")->fetchAll();

// تجميع الصلاحيات حسب الفئة
$permissionsByCategory = [];
foreach ($permissions as $permission) {
    $category = $permission['category'] ?? 'عام';
    $permissionsByCategory[$category][] = $permission;
}

// دالة للحصول على صلاحيات الدور
function getRolePermissions($roleId, $database) {
    $stmt = $database->prepare("
        SELECT p.id 
        FROM permissions p 
        JOIN role_permissions rp ON p.id = rp.permission_id 
        WHERE rp.role_id = ?
    ");
    $stmt->execute([$roleId]);
    return $stmt->fetchAll(PDO::FETCH_COLUMN);
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة صلاحيات الأدوار</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
        }
        .role-card {
            transition: transform 0.2s;
            cursor: pointer;
        }
        .role-card:hover {
            transform: translateY(-5px);
        }
        .permission-category {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .permission-item {
            background: white;
            border-radius: 8px;
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #e9ecef;
        }
        .permission-item:hover {
            background: #f8f9fa;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .btn-success {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border: none;
        }
        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            border: none;
        }
        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }
        .badge {
            font-size: 0.8em;
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <!-- رسائل النجاح والخطأ -->
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <div class="row">
            <!-- قائمة الأدوار -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-users-cog me-2"></i>
                            الأدوار المتاحة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2 mb-3">
                            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#createRoleModal">
                                <i class="fas fa-plus me-2"></i>
                                إنشاء دور جديد
                            </button>
                        </div>
                        
                        <div class="roles-list">
                            <?php foreach ($roles as $role): ?>
                                <div class="role-card card mb-2" onclick="selectRole(<?php echo $role['id']; ?>)" id="role-<?php echo $role['id']; ?>">
                                    <div class="card-body p-3">
                                        <h6 class="card-title mb-1">
                                            <?php echo htmlspecialchars($role['display_name']); ?>
                                            <span class="badge bg-secondary ms-2"><?php echo htmlspecialchars($role['name']); ?></span>
                                        </h6>
                                        <p class="card-text small text-muted mb-2">
                                            <?php echo htmlspecialchars($role['description'] ?? ''); ?>
                                        </p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                <?php 
                                                $rolePermissions = getRolePermissions($role['id'], $database);
                                                echo count($rolePermissions) . ' صلاحية';
                                                ?>
                                            </small>
                                            <div>
                                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="editRole(<?php echo $role['id']; ?>); event.stopPropagation();">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <?php if ($role['name'] !== 'super-admin'): ?>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteRole(<?php echo $role['id']; ?>); event.stopPropagation();">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- إدارة صلاحيات الدور المحدد -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-shield-alt me-2"></i>
                            إدارة صلاحيات الدور
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="role-permissions-content">
                            <div class="text-center text-muted py-5">
                                <i class="fas fa-hand-pointer fa-3x mb-3"></i>
                                <h5>اختر دوراً من القائمة لإدارة صلاحياته</h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- نافذة إنشاء دور جديد -->
    <div class="modal fade" id="createRoleModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i>
                        إنشاء دور جديد
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="create_role">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="role_name" class="form-label">اسم الدور (بالإنجليزية)</label>
                                    <input type="text" class="form-control" id="role_name" name="name" required>
                                    <div class="form-text">مثال: manager, employee</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="role_display_name" class="form-label">الاسم المعروض</label>
                                    <input type="text" class="form-control" id="role_display_name" name="display_name" required>
                                    <div class="form-text">مثال: مدير، موظف</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="role_description" class="form-label">الوصف</label>
                            <textarea class="form-control" id="role_description" name="description" rows="3"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">الصلاحيات</label>
                            <div class="permissions-container" style="max-height: 300px; overflow-y: auto;">
                                <?php foreach ($permissionsByCategory as $category => $categoryPermissions): ?>
                                    <div class="permission-category">
                                        <h6 class="mb-2">
                                            <i class="fas fa-folder me-2"></i>
                                            <?php echo htmlspecialchars($category); ?>
                                        </h6>
                                        <?php foreach ($categoryPermissions as $permission): ?>
                                            <div class="form-check permission-item">
                                                <input class="form-check-input" type="checkbox" 
                                                       name="permissions[]" 
                                                       value="<?php echo $permission['id']; ?>"
                                                       id="new_perm_<?php echo $permission['id']; ?>">
                                                <label class="form-check-label" for="new_perm_<?php echo $permission['id']; ?>">
                                                    <strong><?php echo htmlspecialchars($permission['display_name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($permission['name']); ?></small>
                                                </label>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-2"></i>
                            إنشاء الدور
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let selectedRoleId = null;
        
        function selectRole(roleId) {
            selectedRoleId = roleId;
            
            // إزالة التحديد من جميع الأدوار
            document.querySelectorAll('.role-card').forEach(card => {
                card.classList.remove('border-primary');
            });
            
            // تحديد الدور المختار
            document.getElementById('role-' + roleId).classList.add('border-primary');
            
            // تحميل صلاحيات الدور
            loadRolePermissions(roleId);
        }
        
        function loadRolePermissions(roleId) {
            fetch('get_role_permissions.php?role_id=' + roleId)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayRolePermissions(data.role, data.permissions, data.rolePermissions);
                    } else {
                        alert('خطأ في تحميل الصلاحيات: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('خطأ في الاتصال بالخادم');
                });
        }
        
        function displayRolePermissions(role, permissions, rolePermissions) {
            let html = `
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h5 class="mb-1">${role.display_name}</h5>
                        <p class="text-muted mb-0">${role.description || ''}</p>
                    </div>
                    <div>
                        <button type="button" class="btn btn-primary" onclick="saveRolePermissions()">
                            <i class="fas fa-save me-2"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </div>
                
                <form id="rolePermissionsForm">
                    <input type="hidden" name="action" value="update_role_permissions">
                    <input type="hidden" name="role_id" value="${role.id}">
            `;
            
            // تجميع الصلاحيات حسب الفئة
            let permissionsByCategory = {};
            permissions.forEach(permission => {
                let category = permission.category || 'عام';
                if (!permissionsByCategory[category]) {
                    permissionsByCategory[category] = [];
                }
                permissionsByCategory[category].push(permission);
            });
            
            // عرض الصلاحيات مجمعة
            Object.keys(permissionsByCategory).forEach(category => {
                html += `
                    <div class="permission-category">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0">
                                <i class="fas fa-folder me-2"></i>
                                ${category}
                            </h6>
                            <div>
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAllInCategory('${category}')">
                                    تحديد الكل
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="deselectAllInCategory('${category}')">
                                    إلغاء الكل
                                </button>
                            </div>
                        </div>
                `;
                
                permissionsByCategory[category].forEach(permission => {
                    let isChecked = rolePermissions.includes(permission.id) ? 'checked' : '';
                    html += `
                        <div class="form-check permission-item" data-category="${category}">
                            <input class="form-check-input" type="checkbox" 
                                   name="permissions[]" 
                                   value="${permission.id}"
                                   id="perm_${permission.id}" ${isChecked}>
                            <label class="form-check-label" for="perm_${permission.id}">
                                <strong>${permission.display_name}</strong>
                                <br>
                                <small class="text-muted">${permission.name}</small>
                            </label>
                        </div>
                    `;
                });
                
                html += '</div>';
            });
            
            html += '</form>';
            
            document.getElementById('role-permissions-content').innerHTML = html;
        }
        
        function selectAllInCategory(category) {
            document.querySelectorAll(`[data-category="${category}"] input[type="checkbox"]`).forEach(checkbox => {
                checkbox.checked = true;
            });
        }
        
        function deselectAllInCategory(category) {
            document.querySelectorAll(`[data-category="${category}"] input[type="checkbox"]`).forEach(checkbox => {
                checkbox.checked = false;
            });
        }
        
        function saveRolePermissions() {
            if (!selectedRoleId) {
                alert('يرجى اختيار دور أولاً');
                return;
            }
            
            let form = document.getElementById('rolePermissionsForm');
            let formData = new FormData(form);
            
            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(data => {
                location.reload();
            })
            .catch(error => {
                console.error('Error:', error);
                alert('خطأ في حفظ التغييرات');
            });
        }
        
        function editRole(roleId) {
            // يمكن إضافة نافذة تعديل الدور هنا
            alert('ميزة تعديل الدور ستكون متاحة قريباً');
        }
        
        function deleteRole(roleId) {
            if (confirm('هل أنت متأكد من حذف هذا الدور؟')) {
                let form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_role">
                    <input type="hidden" name="role_id" value="${roleId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>