<?php

// تضمين الكلاسات المطلوبة
require_once __DIR__ . '/ProductCache.php';
require_once __DIR__ . '/ProductValidator.php';

/**
 * مساعد المنتجات - يحتوي على دوال مساعدة لمعالجة وعرض بيانات المنتجات
 */
class ProductHelper {
    
    private static $cache;
    private static $validator;
    private static $config;
    
    /**
     * تهيئة الكلاس
     */
    public static function init() {
        if (!self::$cache) {
            self::$cache = new ProductCache();
        }
        
        if (!self::$validator) {
            self::$validator = new ProductValidator();
        }
        
        if (!self::$config) {
            self::$config = include __DIR__ . '/../config/products_config.php';
        }
    }
    
    /**
     * الحصول على بيانات المنتج مع التخزين المؤقت
     */
    public static function getProductWithCache($product_id) {
        self::init();
        
        $cache_key = "product_detail_{$product_id}";
        
        return self::$cache->remember($cache_key, function() use ($product_id) {
            // هنا يمكن إضافة استعلام قاعدة البيانات
            // return Database::getProduct($product_id);
            return null; // مؤقت
        });
    }
    
    /**
     * التحقق من صحة بيانات المنتج
     */
    public static function validateProductData($data) {
        self::init();
        return self::$validator->validateProduct($data);
    }
    
    /**
     * الحصول على أخطاء التحقق
     */
    public static function getValidationErrors() {
        self::init();
        return self::$validator->getErrors();
    }
    
    /**
     * مسح التخزين المؤقت للمنتج
     */
    public static function clearProductCache($product_id = null) {
        self::init();
        
        if ($product_id) {
            self::$cache->delete("product_detail_{$product_id}");
            self::$cache->delete("product_reviews_{$product_id}");
            self::$cache->delete("related_products_{$product_id}");
        } else {
            self::$cache->clear();
        }
    }
    
    /**
     * الحصول على إحصائيات التخزين المؤقت
     */
    public static function getCacheStats() {
        self::init();
        return self::$cache->getStats();
    }
    
    /**
     * تنظيف التخزين المؤقت المنتهي الصلاحية
     */
    public static function cleanupCache() {
        self::init();
        return self::$cache->cleanup();
    }
    
    /**
     * معالجة وتنسيق المميزات
     * @param mixed $features البيانات الخام للمميزات
     * @return array مصفوفة المميزات المنسقة
     */
    public static function processFeatures($features)
    {
        if (empty($features)) {
            return [];
        }
        
        // إذا كانت البيانات نص
        if (is_string($features)) {
            // محاولة تحويل JSON أولاً
            $decoded = json_decode($features, true);
            if (is_array($decoded)) {
                return $decoded;
            }
            
            // إذا لم يكن JSON، فهو نص مفصول بأسطر جديدة
            $lines = explode("\n", $features);
            $processedFeatures = [];
            
            foreach ($lines as $line) {
                $line = trim($line);
                if (!empty($line)) {
                    $processedFeatures[] = $line;
                }
            }
            
            return $processedFeatures;
        }
        
        return is_array($features) ? $features : [];
    }
    
    /**
     * معالجة وتنسيق المواصفات
     * @param mixed $specifications البيانات الخام للمواصفات
     * @return array مصفوفة المواصفات المنسقة
     */
    public static function processSpecifications($specifications)
    {
        if (empty($specifications)) {
            return [];
        }
        
        // إذا كانت البيانات نص
        if (is_string($specifications)) {
            // محاولة تحويل JSON أولاً
            $decoded = json_decode($specifications, true);
            if (is_array($decoded)) {
                return $decoded;
            }
            
            // إذا لم يكن JSON، فهو نص مفصول بأسطر جديدة
            $lines = explode("\n", $specifications);
            $processedSpecs = [];
            
            foreach ($lines as $line) {
                $line = trim($line);
                if (!empty($line)) {
                    $processedSpecs[] = $line;
                }
            }
            
            return $processedSpecs;
        }
        
        return is_array($specifications) ? $specifications : [];
    }
    
    /**
     * إنشاء HTML لعرض المميزات مع أيقونات
     * @param array $features مصفوفة المميزات
     * @param string $iconClass فئة CSS للأيقونة
     * @param string $textClass فئة CSS للنص
     * @return string HTML المنسق
     */
    public static function renderFeatures($features, $iconClass = 'w-5 h-5 text-green-500 ml-2 flex-shrink-0', $textClass = 'text-gray-700')
    {
        $processedFeatures = self::processFeatures($features);
        
        if (empty($processedFeatures)) {
            return '<p class="text-gray-500">لا توجد مميزات متاحة</p>';
        }
        
        $html = '<ul class="space-y-2">';
        foreach ($processedFeatures as $feature) {
            $html .= '<li class="flex items-center">';
            $html .= '<svg class="' . htmlspecialchars($iconClass) . '" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            $html .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>';
            $html .= '</svg>';
            $html .= '<span class="' . htmlspecialchars($textClass) . '">' . htmlspecialchars($feature) . '</span>';
            $html .= '</li>';
        }
        $html .= '</ul>';
        
        return $html;
    }
    
    /**
     * إنشاء HTML لعرض المواصفات في جدول
     * @param array $specifications مصفوفة المواصفات
     * @return string HTML المنسق
     */
    public static function renderSpecifications($specifications)
    {
        $processedSpecs = self::processSpecifications($specifications);
        
        if (empty($processedSpecs)) {
            return '<p class="text-gray-500">لا توجد مواصفات فنية متاحة لهذا المنتج.</p>';
        }
        
        $html = '<div class="bg-white rounded-lg p-6">';
        $html .= '<div class="grid grid-cols-1 md:grid-cols-2 gap-6">';
        
        foreach ($processedSpecs as $key => $value) {
            $html .= '<div class="flex justify-between py-3 border-b border-gray-200">';
            $html .= '<span class="font-medium text-gray-900">' . htmlspecialchars($key) . '</span>';
            $html .= '<span class="text-gray-600">' . htmlspecialchars($value) . '</span>';
            $html .= '</div>';
        }
        
        $html .= '</div></div>';
        
        return $html;
    }
    
    /**
     * تنسيق السعر
     * @param float $price السعر
     * @param string $currency العملة
     * @return string السعر المنسق
     */
    public static function formatPrice($price, $currency = 'ريال')
    {
        if (empty($price) || $price <= 0) {
            return 'السعر عند الطلب';
        }
        
        return number_format($price, 2) . ' ' . $currency;
    }
    
    /**
     * معالجة معرض الصور
     * @param array $images مصفوفة الصور
     * @param string $defaultImage الصورة الافتراضية
     * @return array مصفوفة الصور المعالجة
     */
    public static function processImageGallery($images, $defaultImage = 'assets/images/no-image.png')
    {
        if (empty($images) || !is_array($images)) {
            return [$defaultImage];
        }
        
        $processedImages = [];
        foreach ($images as $image) {
            if (!empty($image) && file_exists($image)) {
                $processedImages[] = $image;
            }
        }
        
        return empty($processedImages) ? [$defaultImage] : $processedImages;
    }
    
    /**
     * التحقق من صحة بيانات المنتج
     * @param array $productData بيانات المنتج
     * @return bool صحة البيانات
     */
    public static function validateProduct($productData)
    {
        $required_fields = ['name', 'description', 'price', 'category_id'];
        
        foreach ($required_fields as $field) {
            if (empty($productData[$field])) {
                return false;
            }
        }
        
        // التحقق من صحة السعر
        if (!is_numeric($productData['price']) || $productData['price'] < 0) {
            return false;
        }
        
        return true;
    }
    
    /**
     * إنشاء breadcrumb للمنتج
     * @param array $product بيانات المنتج
     * @return string HTML للـ breadcrumb
     */
    public static function generateBreadcrumb($product)
    {
        $html = '<nav class="flex mb-6" aria-label="Breadcrumb">';
        $html .= '<ol class="inline-flex items-center space-x-1 md:space-x-3">';
        
        // الرئيسية
        $html .= '<li class="inline-flex items-center">';
        $html .= '<a href="/" class="text-gray-700 hover:text-green-600">الرئيسية</a>';
        $html .= '</li>';
        
        // المنتجات
        $html .= '<li>';
        $html .= '<div class="flex items-center">';
        $html .= '<svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">';
        $html .= '<path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>';
        $html .= '</svg>';
        $html .= '<a href="/products" class="ml-1 text-gray-700 hover:text-green-600 md:ml-2">المنتجات</a>';
        $html .= '</div>';
        $html .= '</li>';
        
        // الفئة (إذا كانت متوفرة)
        if (!empty($product['category_name'])) {
            $html .= '<li>';
            $html .= '<div class="flex items-center">';
            $html .= '<svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">';
            $html .= '<path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>';
            $html .= '</svg>';
            $html .= '<span class="ml-1 text-gray-700 md:ml-2">' . htmlspecialchars($product['category_name']) . '</span>';
            $html .= '</div>';
            $html .= '</li>';
        }
        
        // المنتج الحالي
        $html .= '<li>';
        $html .= '<div class="flex items-center">';
        $html .= '<svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">';
        $html .= '<path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>';
        $html .= '</svg>';
        $html .= '<span class="ml-1 text-gray-500 md:ml-2">' . htmlspecialchars($product['name']) . '</span>';
        $html .= '</div>';
        $html .= '</li>';
        
        $html .= '</ol>';
        $html .= '</nav>';
        
        return $html;
    }
    
    /**
     * تنسيق التقييم بالنجوم
     * @param float $rating التقييم
     * @param int $maxRating أقصى تقييم
     * @return string HTML للنجوم
     */
    public static function renderStarRating($rating, $maxRating = 5)
    {
        $rating = max(0, min($maxRating, floatval($rating)));
        $fullStars = floor($rating);
        $halfStar = ($rating - $fullStars) >= 0.5;
        $emptyStars = $maxRating - $fullStars - ($halfStar ? 1 : 0);
        
        $html = '<div class="flex items-center">';
        
        // النجوم الممتلئة
        for ($i = 0; $i < $fullStars; $i++) {
            $html .= '<svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">';
            $html .= '<path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>';
            $html .= '</svg>';
        }
        
        // النجمة النصف ممتلئة
        if ($halfStar) {
            $html .= '<svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">';
            $html .= '<defs><linearGradient id="half"><stop offset="50%" stop-color="currentColor"/><stop offset="50%" stop-color="transparent"/></linearGradient></defs>';
            $html .= '<path fill="url(#half)" d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>';
            $html .= '</svg>';
        }
        
        // النجوم الفارغة
        for ($i = 0; $i < $emptyStars; $i++) {
            $html .= '<svg class="w-5 h-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">';
            $html .= '<path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>';
            $html .= '</svg>';
        }
        
        $html .= '<span class="ml-2 text-sm text-gray-600">' . number_format($rating, 1) . '</span>';
        $html .= '</div>';
        
        return $html;
    }
}