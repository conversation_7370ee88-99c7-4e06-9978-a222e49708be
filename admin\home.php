<?php
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !hasPermission('admin')) {
    redirect('/admin/login.php');
}

require_once '../config/database.php';

$database = new Database();
$message = '';
$error = '';
$pageTitle = 'إدارة الصفحة الرئيسية';
$currentPage = 'home';
$pageDescription = 'إدارة محتوى الصفحة الرئيسية والعناصر التفاعلية';
$breadcrumbs = [
    ['title' => 'إدارة المحتوى']
];

// تضمين التخطيط الموحد
require_once 'includes/layout.php';

// معالجة طلبات POST للصفحة الرئيسية
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_slide':
                $slideId = $_POST['slide_id'] ?? null;
                $title = $_POST['title'] ?? '';
                $subtitle = $_POST['subtitle'] ?? '';
                $description = $_POST['description'] ?? '';
                $buttonText = $_POST['button_text'] ?? '';
                $buttonLink = $_POST['button_link'] ?? '';
                $backgroundColor = $_POST['background_color'] ?? '#047857';
                $textColor = $_POST['text_color'] ?? '#FFFFFF';
                $sortOrder = !empty($_POST['sort_order']) ? (int)$_POST['sort_order'] : 0;
                $isActive = isset($_POST['is_active']) ? 1 : 0;
                
                $imagePath = null;
                if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                    $uploadDir = '../assets/images/';
                    $fileName = 'slide_' . time() . '_' . basename($_FILES['image']['name']);
                    $imagePath = '/assets/images/' . $fileName;
                    move_uploaded_file($_FILES['image']['tmp_name'], $uploadDir . $fileName);
                }
                
                if ($slideId) {
                    $query = "UPDATE hero_slides SET title = :title, subtitle = :subtitle, description = :description, button_text = :button_text, button_link = :button_link, sort_order = :sort_order, is_active = :is_active";
                    $params = [
                        'title' => $title,
                        'subtitle' => $subtitle,
                        'description' => $description,
                        'button_text' => $buttonText,
                        'button_link' => $buttonLink,
                        'sort_order' => $sortOrder,
                        'is_active' => $isActive,
                        'id' => $slideId
                    ];
                    
                    if ($imagePath) {
                        $query .= ", image = :image";
                        $params['image'] = $imagePath;
                    }
                    
                    $query .= " WHERE id = :id";
                    $database->query($query, $params);
                } else {
                    $database->query("
                        INSERT INTO hero_slides (title, subtitle, description, image, button_text, button_link, sort_order, is_active) 
                        VALUES (:title, :subtitle, :description, :image, :button_text, :button_link, :sort_order, :is_active)
                    ", [
                        'title' => $title,
                        'subtitle' => $subtitle,
                        'description' => $description,
                        'image' => $imagePath,
                        'button_text' => $buttonText,
                        'button_link' => $buttonLink,
                        'sort_order' => $sortOrder,
                        'is_active' => $isActive
                    ]);
                }
                $message = 'تم تحديث السلايدر بنجاح';
                break;
                
            case 'delete_slide':
                $slideId = $_POST['slide_id'] ?? null;
                if ($slideId) {
                    $database->query("DELETE FROM hero_slides WHERE id = :id", ['id' => $slideId]);
                    $message = 'تم حذف السلايدر بنجاح';
                }
                break;
                
            case 'update_testimonial':
                $testimonialId = $_POST['testimonial_id'] ?? null;
                $name = $_POST['name'] ?? '';
                $position = $_POST['position'] ?? '';
                $company = $_POST['company'] ?? '';
                $content = $_POST['content'] ?? '';
                $rating = $_POST['rating'] ?? 5;
                $isFeatured = isset($_POST['is_featured']) ? 1 : 0;
                $sortOrder = !empty($_POST['sort_order']) ? (int)$_POST['sort_order'] : 0;
                $isActive = isset($_POST['is_active']) ? 1 : 0;
                $isVisible = isset($_POST['is_visible']) ? 1 : 0;
                
                $imagePath = null;
                if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                    $uploadDir = '../assets/images/';
                    $fileName = 'testimonial_' . time() . '_' . basename($_FILES['image']['name']);
                    $imagePath = '/assets/images/' . $fileName;
                    move_uploaded_file($_FILES['image']['tmp_name'], $uploadDir . $fileName);
                }
                
                if ($testimonialId) {
                    $query = "UPDATE reviews SET name = :name, position = :position, company = :company, content = :content, rating = :rating, is_featured = :is_featured, sort_order = :sort_order, is_approved = :is_active, is_visible = :is_visible";
                    $params = [
                        'name' => $name,
                        'position' => $position,
                        'company' => $company,
                        'content' => $content,
                        'rating' => $rating,
                        'is_featured' => $isFeatured,
                        'sort_order' => $sortOrder,
                        'is_active' => $isActive,
                        'is_visible' => $isVisible,
                        'id' => $testimonialId
                    ];
                    
                    if ($imagePath) {
                        $query .= ", image = :image";
                        $params['image'] = $imagePath;
                    }
                    
                    $query .= " WHERE id = :id";
                    $database->query($query, $params);
                } else {
                    $database->query("
                        INSERT INTO reviews (name, position, company, content, rating, image, is_featured, sort_order, is_approved, is_visible) 
                        VALUES (:name, :position, :company, :content, :rating, :image, :is_featured, :sort_order, :is_active, :is_visible)
                    ", [
                        'name' => $name,
                        'position' => $position,
                        'company' => $company,
                        'content' => $content,
                        'rating' => $rating,
                        'image' => $imagePath,
                        'is_featured' => $isFeatured,
                        'sort_order' => $sortOrder,
                        'is_active' => $isActive,
                        'is_visible' => $isVisible
                    ]);
                }
                $message = 'تم تحديث المراجعة بنجاح';
                break;
                
            case 'delete_testimonial':
                $testimonialId = $_POST['testimonial_id'] ?? null;
                if ($testimonialId) {
                    $database->query("DELETE FROM reviews WHERE id = :id", ['id' => $testimonialId]);
                    $message = 'تم حذف المراجعة بنجاح';
                }
                break;
                
            case 'update_consultation':
                $title = $_POST['title'] ?? '';
                $subtitle = $_POST['subtitle'] ?? '';
                $description = $_POST['description'] ?? '';
                $buttonText = $_POST['button_text'] ?? '';
                $buttonLink = $_POST['button_link'] ?? '';
                $backgroundColor = $_POST['background_color'] ?? '#047857';
                $textColor = $_POST['text_color'] ?? '#FFFFFF';
                $isActive = isset($_POST['is_active']) ? 1 : 0;
                
                $database->query("
                    UPDATE consultation_section SET 
                    title = :title, subtitle = :subtitle, description = :description, 
                    button_text = :button_text, button_link = :button_link, 
                    background_color = :background_color, text_color = :text_color, is_active = :is_active 
                    WHERE id = 1
                ", [
                    'title' => $title,
                    'subtitle' => $subtitle,
                    'description' => $description,
                    'button_text' => $buttonText,
                    'button_link' => $buttonLink,
                    'background_color' => $backgroundColor,
                    'text_color' => $textColor,
                    'is_active' => $isActive
                ]);
                $message = 'تم تحديث قسم الاستشارة بنجاح';
                break;
                
            case 'update_why_choose_us_settings':
                $maxItems = $_POST['max_items'] ?? 6;
                $isVisible = isset($_POST['is_visible']) ? 1 : 0;
                $sectionTitle = $_POST['section_title'] ?? '';
                $sectionSubtitle = $_POST['section_subtitle'] ?? '';
                
                $database->query("
                        UPDATE home_why_choose_us_settings SET 
                    max_items = :max_items, is_visible = :is_visible, title = :title, subtitle = :subtitle 
                    WHERE id = 1
                ", [
                    'max_items' => $maxItems,
                    'is_visible' => $isVisible,
                    'title' => $sectionTitle,
                    'subtitle' => $sectionSubtitle
                ]);
                $message = 'تم تحديث إعدادات قسم "لماذا تختارنا؟" بنجاح';
                break;
                
            case 'update_category':
                $categoryId = $_POST['category_id'] ?? null;
                $name = $_POST['category_name'] ?? '';
                $description = $_POST['category_description'] ?? '';
                $sortOrder = !empty($_POST['category_sort_order']) ? (int)$_POST['category_sort_order'] : 0;
                $isActive = isset($_POST['category_is_active']) ? 1 : 0;
                $isVisible = isset($_POST['category_is_visible']) ? 1 : 0;
                
                $imagePath = null;
                if (isset($_FILES['category_image']) && $_FILES['category_image']['error'] === UPLOAD_ERR_OK) {
                    $uploadDir = '../assets/images/';
                    $fileName = 'category_' . time() . '_' . basename($_FILES['category_image']['name']);
                    $imagePath = '/assets/images/' . $fileName;
                    move_uploaded_file($_FILES['category_image']['tmp_name'], $uploadDir . $fileName);
                }
                
                if ($categoryId) {
                    $query = "UPDATE product_categories SET name = :name, description = :description, sort_order = :sort_order, is_active = :is_active, is_visible = :is_visible";
                    $params = [
                        'name' => $name,
                        'description' => $description,
                        'sort_order' => $sortOrder,
                        'is_active' => $isActive,
                        'is_visible' => $isVisible,
                        'id' => $categoryId
                    ];
                    
                    if ($imagePath) {
                        $query .= ", image = :image";
                        $params['image'] = $imagePath;
                    }
                    
                    $query .= " WHERE id = :id";
                    $database->query($query, $params);
                } else {
                    $database->query("
                        INSERT INTO product_categories (name, description, image, sort_order, is_active, is_visible) 
                        VALUES (:name, :description, :image, :sort_order, :is_active, :is_visible)
                    ", [
                        'name' => $name,
                        'description' => $description,
                        'image' => $imagePath,
                        'sort_order' => $sortOrder,
                        'is_active' => $isActive,
                        'is_visible' => $isVisible
                    ]);
                }
                $message = 'تم تحديث الفئة بنجاح';
                break;
                
            case 'delete_category':
                $categoryId = $_POST['category_id'] ?? null;
                if ($categoryId) {
                    $database->query("DELETE FROM product_categories WHERE id = :id", ['id' => $categoryId]);
                    $message = 'تم حذف الفئة بنجاح';
                }
                break;
                
            case 'update_featured_product':
                $productId = $_POST['product_id'] ?? null;
                $name = $_POST['product_name'] ?? '';
                $description = $_POST['product_description'] ?? '';
                $price = $_POST['product_price'] ?? 0;
                $originalPrice = $_POST['product_original_price'] ?? 0;
                if ($originalPrice === '' || $originalPrice === null) {
                    $originalPrice = 0;
                }
                $categoryId = $_POST['product_category_id'] ?? null;
                $rating = $_POST['product_rating'] ?? null;
                $sortOrder = !empty($_POST['product_sort_order']) ? (int)$_POST['product_sort_order'] : 0;
                $isFeatured = isset($_POST['product_is_featured']) ? 1 : 0;
                $isActive = isset($_POST['product_is_active']) ? 1 : 0;
                $isVisible = isset($_POST['product_is_visible']) ? 1 : 0;
                
                $imagePath = null;
                if (isset($_FILES['product_image']) && $_FILES['product_image']['error'] === UPLOAD_ERR_OK) {
                    $uploadDir = '../assets/images/';
                    $fileName = 'product_' . time() . '_' . basename($_FILES['product_image']['name']);
                    $imagePath = '/assets/images/' . $fileName;
                    move_uploaded_file($_FILES['product_image']['tmp_name'], $uploadDir . $fileName);
                }
                
                if ($productId) {
                    $query = "UPDATE products SET name = :name, description = :description, price = :price, original_price = :original_price, category_id = :category_id, rating = :rating, sort_order = :sort_order, is_featured = :is_featured, is_active = :is_active, is_visible = :is_visible";
                    $params = [
                        'name' => $name,
                        'description' => $description,
                        'price' => $price,
                        'original_price' => $originalPrice,
                        'category_id' => $categoryId,
                        'rating' => $rating,
                        'sort_order' => $sortOrder,
                        'is_featured' => $isFeatured,
                        'is_active' => $isActive,
                        'is_visible' => $isVisible,
                        'id' => $productId
                    ];
                    
                    if ($imagePath) {
                        $query .= ", image = :image";
                        $params['image'] = $imagePath;
                    }
                    
                    $query .= " WHERE id = :id";
                    $database->query($query, $params);
                } else {
                    $database->query("
                        INSERT INTO products (name, description, price, original_price, category_id, rating, image, sort_order, is_featured, is_active, is_visible) 
                        VALUES (:name, :description, :price, :original_price, :category_id, :rating, :image, :sort_order, :is_featured, :is_active, :is_visible)
                    ", [
                        'name' => $name,
                        'description' => $description,
                        'price' => $price,
                        'original_price' => $originalPrice,
                        'category_id' => $categoryId,
                        'rating' => $rating,
                        'image' => $imagePath,
                        'sort_order' => $sortOrder,
                        'is_featured' => $isFeatured,
                        'is_active' => $isActive,
                        'is_visible' => $isVisible
                    ]);
                }
                $message = 'تم تحديث المنتج بنجاح';
                break;
                
            case 'delete_product':
                $productId = $_POST['product_id'] ?? null;
                if ($productId) {
                    $database->query("DELETE FROM products WHERE id = :id", ['id' => $productId]);
                    $message = 'تم حذف المنتج بنجاح';
                }
                break;
                
            case 'update_testimonials_settings':
                $displayCount = $_POST['testimonials_count'] ?? 6;
                $showSection = isset($_POST['testimonials_show_section']) ? 1 : 0;
                
                // Update or insert testimonials display count setting
                $existingCount = $database->fetch("SELECT id FROM settings WHERE setting_key = 'testimonials_count'");
                
                if ($existingCount) {
                    $database->query("UPDATE settings SET setting_value = :value WHERE setting_key = 'testimonials_count'", [
                        'value' => $displayCount
                    ]);
                } else {
                    $database->query("INSERT INTO settings (setting_key, setting_value) VALUES ('testimonials_count', :value)", [
                        'value' => $displayCount
                    ]);
                }
                
                // Update or insert testimonials show section setting
                $existingShow = $database->fetch("SELECT id FROM settings WHERE setting_key = 'testimonials_show_section'");
                
                if ($existingShow) {
                    $database->query("UPDATE settings SET setting_value = :value WHERE setting_key = 'testimonials_show_section'", [
                        'value' => $showSection
                    ]);
                } else {
                    $database->query("INSERT INTO settings (setting_key, setting_value) VALUES ('testimonials_show_section', :value)", [
                        'value' => $showSection
                    ]);
                }
                
                $message = 'تم حفظ إعدادات التقييمات بنجاح';
                break;
                
            case 'update_categories_settings':
                $displayCount = $_POST['categories_count'] ?? 6;
                $showSection = isset($_POST['categories_show_section']) ? 1 : 0;
                
                // Update or insert categories display count setting
                $existingCount = $database->fetch("SELECT id FROM settings WHERE setting_key = 'categories_count'");
                
                if ($existingCount) {
                    $database->query("UPDATE settings SET setting_value = :value WHERE setting_key = 'categories_count'", [
                        'value' => $displayCount
                    ]);
                } else {
                    $database->query("INSERT INTO settings (setting_key, setting_value) VALUES ('categories_count', :value)", [
                        'value' => $displayCount
                    ]);
                }
                
                // Update or insert categories show section setting
                $existingShow = $database->fetch("SELECT id FROM settings WHERE setting_key = 'categories_show_section'");
                
                if ($existingShow) {
                    $database->query("UPDATE settings SET setting_value = :value WHERE setting_key = 'categories_show_section'", [
                        'value' => $showSection
                    ]);
                } else {
                    $database->query("INSERT INTO settings (setting_key, setting_value) VALUES ('categories_show_section', :value)", [
                        'value' => $showSection
                    ]);
                }
                
                $message = 'تم حفظ إعدادات فئات المنتجات بنجاح';
                break;
                
            case 'update_featured_products_settings':
                $displayCount = $_POST['featured_products_count'] ?? 6;
                $showSection = isset($_POST['featured_products_show_section']) ? 1 : 0;
                
                // Update or insert featured products display count setting
                $existingCount = $database->fetch("SELECT id FROM settings WHERE setting_key = 'featured_products_count'");
                
                if ($existingCount) {
                    $database->query("UPDATE settings SET setting_value = :value WHERE setting_key = 'featured_products_count'", [
                        'value' => $displayCount
                    ]);
                } else {
                    $database->query("INSERT INTO settings (setting_key, setting_value) VALUES ('featured_products_count', :value)", [
                        'value' => $displayCount
                    ]);
                }
                
                // Update or insert featured products show section setting
                $existingShow = $database->fetch("SELECT id FROM settings WHERE setting_key = 'featured_products_show_section'");
                
                if ($existingShow) {
                    $database->query("UPDATE settings SET setting_value = :value WHERE setting_key = 'featured_products_show_section'", [
                        'value' => $showSection
                    ]);
                } else {
                    $database->query("INSERT INTO settings (setting_key, setting_value) VALUES ('featured_products_show_section', :value)", [
                        'value' => $showSection
                    ]);
                }
                
                $message = 'تم حفظ إعدادات المنتجات المميزة بنجاح';
                break;
        }
    }
}

// إنشاء جداول إضافية للصفحة الرئيسية
// جدول السلايدرز
$database->query("
    CREATE TABLE IF NOT EXISTS hero_slides (
        id INT PRIMARY KEY AUTO_INCREMENT,
        title VARCHAR(255) NOT NULL,
        subtitle TEXT,
        description TEXT,
        image VARCHAR(255),
        button_text VARCHAR(100),
        button_link VARCHAR(255),
        sort_order INT DEFAULT 0,
        is_active BOOLEAN DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )
");

// إضافة عمود is_visible لجدول فئات المنتجات إذا لم يكن موجوداً
try {
    $database->query("ALTER TABLE product_categories ADD COLUMN is_visible BOOLEAN DEFAULT 1");
} catch (PDOException $e) {
    // العمود موجود بالفعل، تجاهل الخطأ
}

// إضافة عمود is_visible لجدول المنتجات إذا لم يكن موجوداً
try {
    $database->query("ALTER TABLE products ADD COLUMN is_visible BOOLEAN DEFAULT 1");
} catch (PDOException $e) {
    // العمود موجود بالفعل، تجاهل الخطأ
}

// جدول المراجعات/الشهادات - استخدام جدول reviews
// التأكد من وجود جدول reviews
$tableExists = $database->fetch("SHOW TABLES LIKE 'reviews'");
if (!$tableExists) {
    $database->query("
        CREATE TABLE IF NOT EXISTS reviews (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            position VARCHAR(100),
            company VARCHAR(100),
            content TEXT NOT NULL,
            rating INT DEFAULT 5,
            image VARCHAR(255),
            is_featured BOOLEAN DEFAULT 0,
            sort_order INT DEFAULT 0,
            is_approved BOOLEAN DEFAULT 1,
            is_rejected BOOLEAN DEFAULT 0,
            is_visible BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
}

// إضافة عمود is_visible لجدول المراجعات إذا لم يكن موجوداً
try {
    $database->query("ALTER TABLE reviews ADD COLUMN is_visible BOOLEAN DEFAULT 1");
} catch (PDOException $e) {
    // العمود موجود بالفعل، تجاهل الخطأ
}

// جدول الإعدادات العامة
$database->query("
    CREATE TABLE IF NOT EXISTS settings (
        id INT PRIMARY KEY AUTO_INCREMENT,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT,
        setting_type VARCHAR(20) DEFAULT 'string',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )
");

// التأكد من إنشاء الجدول قبل محاولة الوصول إليه
try {
    // إدراج إعداد افتراضي لعدد التقييمات
    $testimonialsCountExists = $database->fetch("SELECT id FROM settings WHERE setting_key = 'testimonials_count'");
    if (!$testimonialsCountExists) {
        $database->query("
            INSERT INTO settings (setting_key, setting_value, setting_type) 
            VALUES ('testimonials_count', '6', 'number')
        ");
    }
    
    // إدراج إعداد افتراضي لفلاتر التقييمات
    $testimonialsFiltersExists = $database->fetch("SELECT id FROM settings WHERE setting_key = 'testimonials_filters'");
    if (!$testimonialsFiltersExists) {
        $database->query("
            INSERT INTO settings (setting_key, setting_value, setting_type) 
            VALUES ('testimonials_filters', '[\"all\"]', 'json')
        ");
    }
    
    // إدراج إعداد افتراضي لعدد فئات المنتجات
    $categoriesCountExists = $database->fetch("SELECT id FROM settings WHERE setting_key = 'categories_count'");
    if (!$categoriesCountExists) {
        $database->query("
            INSERT INTO settings (setting_key, setting_value, setting_type) 
            VALUES ('categories_count', '6', 'number')
        ");
    }
    
    // إدراج إعداد افتراضي لإظهار قسم فئات المنتجات
    $categoriesShowExists = $database->fetch("SELECT id FROM settings WHERE setting_key = 'categories_show_section'");
    if (!$categoriesShowExists) {
        $database->query("
            INSERT INTO settings (setting_key, setting_value, setting_type) 
            VALUES ('categories_show_section', '1', 'boolean')
        ");
    }
    
    // إدراج إعداد افتراضي لعدد المنتجات المميزة
    $featuredProductsCountExists = $database->fetch("SELECT id FROM settings WHERE setting_key = 'featured_products_count'");
    if (!$featuredProductsCountExists) {
        $database->query("
            INSERT INTO settings (setting_key, setting_value, setting_type) 
            VALUES ('featured_products_count', '6', 'number')
        ");
    }
    
    // إدراج إعداد افتراضي لإظهار قسم المنتجات المميزة
    $featuredProductsShowExists = $database->fetch("SELECT id FROM settings WHERE setting_key = 'featured_products_show_section'");
    if (!$featuredProductsShowExists) {
        $database->query("
            INSERT INTO settings (setting_key, setting_value, setting_type) 
            VALUES ('featured_products_show_section', '1', 'boolean')
        ");
    }
} catch (PDOException $e) {
    // في حالة عدم وجود الجدول، إنشاؤه مرة أخرى
    $database->query("
        CREATE TABLE IF NOT EXISTS settings (
            id INT PRIMARY KEY AUTO_INCREMENT,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT,
            setting_type VARCHAR(20) DEFAULT 'string',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    
    // إدراج الإعداد الافتراضي
    $database->query("
        INSERT INTO settings (setting_key, setting_value, setting_type) 
        VALUES ('testimonials_count', '6', 'number')
    ");
    
    // إدراج إعدادات فئات المنتجات الافتراضية
    $database->query("
        INSERT INTO settings (setting_key, setting_value, setting_type) 
        VALUES ('categories_count', '6', 'number')
    ");
    
    $database->query("
        INSERT INTO settings (setting_key, setting_value, setting_type) 
        VALUES ('categories_show_section', '1', 'boolean')
    ");
    
    // إدراج إعدادات المنتجات المميزة الافتراضية
    $database->query("
        INSERT INTO settings (setting_key, setting_value, setting_type) 
        VALUES ('featured_products_count', '6', 'number')
    ");
    
    $database->query("
        INSERT INTO settings (setting_key, setting_value, setting_type) 
        VALUES ('featured_products_show_section', '1', 'boolean')
    ");
}

// جدول قسم الاستشارة المجانية
$database->query("
    CREATE TABLE IF NOT EXISTS consultation_section (
        id INT PRIMARY KEY AUTO_INCREMENT,
        title VARCHAR(255) DEFAULT 'هل تحتاج إلى استشارة مجانية؟',
        subtitle TEXT,
        description TEXT,
        button_text VARCHAR(100) DEFAULT 'احصل على استشارة مجانية',
        button_link VARCHAR(255) DEFAULT '/contact',
        background_color VARCHAR(7) DEFAULT '#047857',
        text_color VARCHAR(7) DEFAULT '#FFFFFF',
        is_active BOOLEAN DEFAULT 1,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )
");

// جدول إعدادات قسم "لماذا تختارنا؟" للصفحة الرئيسية
$database->query("
    CREATE TABLE IF NOT EXISTS home_why_choose_us_settings (
            id INT PRIMARY KEY AUTO_INCREMENT,
            max_items INT DEFAULT 6,
            is_visible BOOLEAN DEFAULT 1,
            title VARCHAR(255) DEFAULT 'لماذا تختارنا؟',
            subtitle TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
");

// إدراج البيانات الافتراضية
$slidesExists = $database->fetch("SELECT id FROM hero_slides LIMIT 1");
if (!$slidesExists) {
    $defaultSlides = [
        ['مرحباً بكم في الخطوط الخضراء', 'أفضل حلول التكييف والتبريد', 'نقدم أحدث تقنيات التبريد والتكييف مع خدمة عملاء متميزة', '/assets/images/hero-slide-1.jpg', 'تصفح منتجاتنا', '/products', 1],
        ['جودة عالية وخدمة ممتازة', 'نقدم أحدث تقنيات التبريد والتكييف', 'فريق متخصص وخبرة واسعة في مجال التكييف والتبريد', '/assets/images/hero-slide-2.jpg', 'اتصل بنا', '/contact', 2]
    ];
    
    foreach ($defaultSlides as $slide) {
        $database->query("
            INSERT INTO hero_slides (title, subtitle, description, image, button_text, button_link, sort_order) 
            VALUES (:title, :subtitle, :description, :image, :button_text, :button_link, :sort_order)
        ", [
            'title' => $slide[0],
            'subtitle' => $slide[1],
            'description' => $slide[2],
            'image' => $slide[3],
            'button_text' => $slide[4],
            'button_link' => $slide[5],
            'sort_order' => $slide[6]
        ]);
    }
}

$reviewsExists = $database->fetch("SELECT id FROM reviews LIMIT 1");
if (!$reviewsExists) {
    $defaultReviews = [
        ['أحمد محمد', 'مدير شركة', 'شركة النجاح التجارية', 'خدمة ممتازة وجودة عالية، أنصح بالتعامل معهم بشدة. فريق محترف ومتعاون.', 5, '/assets/images/testimonial-1.jpg', 1, 1],
        ['فاطمة أحمد', 'ربة منزل', '', 'تركيب سريع ومهني، والمكيف يعمل بكفاءة عالية. خدمة ما بعد البيع ممتازة.', 5, '/assets/images/testimonial-2.jpg', 1, 2],
        ['محمد علي', 'مهندس', 'شركة البناء الحديث', 'أسعار مناسبة وجودة ممتازة. تعامل راقي ومهني من الفريق.', 5, '/assets/images/testimonial-3.jpg', 1, 3]
    ];
    
    foreach ($defaultReviews as $review) {
        $database->query("
            INSERT INTO reviews (name, position, company, content, rating, image, is_featured, sort_order, is_approved) 
            VALUES (:name, :position, :company, :content, :rating, :image, :is_featured, :sort_order, 1)
        ", [
            'name' => $review[0],
            'position' => $review[1],
            'company' => $review[2],
            'content' => $review[3],
            'rating' => $review[4],
            'image' => $review[5],
            'is_featured' => $review[6],
            'sort_order' => $review[7]
        ]);
    }
}

$consultationExists = $database->fetch("SELECT id FROM consultation_section WHERE id = 1");
if (!$consultationExists) {
    $database->query("
        INSERT INTO consultation_section (id, title, subtitle, description, button_text, button_link, background_color, text_color) 
        VALUES (1, 'هل تحتاج إلى استشارة مجانية؟', 'فريقنا المتخصص جاهز لمساعدتك', 'احصل على استشارة مجانية من خبرائنا في مجال التكييف والتبريد. نحن هنا لمساعدتك في اختيار الحل الأمثل لاحتياجاتك.', 'احصل على استشارة مجانية', '/contact', '#047857', '#FFFFFF')
    ");
}

$whyChooseUsSettingsExists = $database->fetch("SELECT id FROM home_why_choose_us_settings WHERE id = 1");
if (!$whyChooseUsSettingsExists) {
    $database->query("
        INSERT INTO home_why_choose_us_settings (id, max_items, is_visible, title, subtitle) 
        VALUES (1, 6, 1, 'لماذا تختارنا؟', 'نحن نقدم أفضل الحلول والخدمات في مجال التكييف والتبريد')
    ");
}

// جلب البيانات للصفحة الرئيسية
$heroSlides = $database->fetchAll("SELECT * FROM hero_slides ORDER BY sort_order ASC, id ASC");

// جلب إعدادات عرض التقييمات من جدول settings
$displayCountSetting = $database->fetch("SELECT * FROM settings WHERE setting_key = 'testimonials_count'");
$testimonialsDisplayCount = $displayCountSetting ? (int)$displayCountSetting['setting_value'] : 6;

// جلب إعدادات عرض فئات المنتجات من جدول settings
$categoriesCountSetting = $database->fetch("SELECT * FROM settings WHERE setting_key = 'categories_count'");
$categoriesDisplayCount = $categoriesCountSetting ? (int)$categoriesCountSetting['setting_value'] : 6;

$categoriesShowSetting = $database->fetch("SELECT * FROM settings WHERE setting_key = 'categories_show_section'");
$categoriesShowSection = $categoriesShowSetting ? (bool)$categoriesShowSetting['setting_value'] : true;

// جلب إعدادات عرض المنتجات المميزة من جدول settings
$featuredProductsCountSetting = $database->fetch("SELECT * FROM settings WHERE setting_key = 'featured_products_count'");
$featuredProductsDisplayCount = $featuredProductsCountSetting ? (int)$featuredProductsCountSetting['setting_value'] : 6;

$featuredProductsShowSetting = $database->fetch("SELECT * FROM settings WHERE setting_key = 'featured_products_show_section'");
$featuredProductsShowSection = $featuredProductsShowSetting ? (bool)$featuredProductsShowSetting['setting_value'] : true;

// استخدام دالة getTestimonials للقراءة من جدول reviews مع تمرير العدد من الإعدادات
$testimonials = getTestimonials($testimonialsDisplayCount);
$consultationSection = $database->fetch("SELECT * FROM consultation_section WHERE id = 1");
$whyChooseUsSettings = $database->fetch("SELECT * FROM home_why_choose_us_settings WHERE id = 1");
$productCategories = getAllProductCategories();
$featuredProducts = $database->fetchAll("SELECT * FROM products WHERE is_featured = 1 AND is_active = 1 ORDER BY sort_order ASC, id ASC LIMIT " . $featuredProductsDisplayCount);
// بدء التخطيط الموحد
startLayout();
showPageHeader();
showMessages();

// إعداد التبويبات
$tabs = [
    'sliders' => [
        'title' => 'السلايدرز',
        'icon' => 'fas fa-images',
        'content' => getSlidersTabContent($heroSlides)
    ],
    'categories' => [
        'title' => 'فئات المنتجات',
        'icon' => 'fas fa-th-large',
        'content' => getProductCategoriesTabContent($productCategories, $categoriesDisplayCount, $categoriesShowSection)
    ],
    'featured-products' => [
        'title' => 'المنتجات المميزة',
        'icon' => 'fas fa-star',
        'content' => getFeaturedProductsTabContent($featuredProducts, $featuredProductsDisplayCount, $featuredProductsShowSection)
    ],
    'testimonials' => [
        'title' => 'المراجعات',
        'icon' => 'fas fa-quote-right',
        'content' => getTestimonialsTabContent($testimonials)
    ],
    'consultation' => [
        'title' => 'الاستشارة المجانية',
        'icon' => 'fas fa-handshake',
        'content' => getConsultationTabContent($consultationSection)
    ],
    'why-choose-us' => [
        'title' => 'إعدادات "لماذا تختارنا؟"',
        'icon' => 'fas fa-check-circle',
        'content' => getWhyChooseUsTabContent($whyChooseUsSettings)
    ]
];

createTabs($tabs, 'sliders');

// دوال محتوى التبويبات
function getSlidersTabContent($heroSlides) {
    ob_start();
    ?>
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <!-- Add/Edit Slider Form -->
                            <div class="bg-gray-50 p-6 rounded-lg">
                                <h3 class="text-xl font-semibold mb-4">إضافة/تعديل سلايدر</h3>
                                <form method="POST" enctype="multipart/form-data" class="space-y-4">
                                    <input type="hidden" name="action" value="update_slide">
                                    <input type="hidden" name="slide_id" id="slide_id">
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">العنوان الرئيسي</label>
                                        <input type="text" name="title" id="slide_title" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500" required>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">العنوان الفرعي</label>
                                        <input type="text" name="subtitle" id="slide_subtitle" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                                        <textarea name="description" id="slide_description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"></textarea>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">نص الزر</label>
                                        <input type="text" name="button_text" id="slide_button_text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">رابط الزر</label>
                                        <input type="text" name="button_link" id="slide_button_link" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">ترتيب العرض</label>
                                        <input type="number" name="sort_order" id="slide_sort_order" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500" min="0">
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">صورة السلايدر</label>
                                        <input type="file" name="image" id="slide_image" accept="image/*" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500" onchange="previewImage(this, 'slide_image_preview')">
                                        <div id="slide_image_preview" class="mt-2"></div>
                                    </div>
                                    
                                    <div class="flex items-center">
                                        <input type="checkbox" name="is_active" id="slide_is_active" class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                                        <label for="slide_is_active" class="mr-2 block text-sm text-gray-900">نشط</label>
                                    </div>
                                    
                                    <button type="submit" class="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                                        <i class="fas fa-save ml-2"></i>حفظ السلايدر
                                    </button>
                                </form>
                            </div>
                            
                            <!-- Existing Sliders -->
                            <div>
                                <h3 class="text-xl font-semibold mb-4">السلايدرز الحالية</h3>
                                <div class="space-y-4">
                                    <?php foreach ($heroSlides as $slide): ?>
                                        <div class="bg-white p-4 rounded-lg border border-gray-200">
                                            <div class="flex justify-between items-start">
                                                <div class="flex-1">
                                                    <h4 class="font-semibold text-lg"><?php echo htmlspecialchars($slide['title']); ?></h4>
                                                    <?php if ($slide['subtitle']): ?>
                                                        <p class="text-gray-600 text-sm"><?php echo htmlspecialchars($slide['subtitle']); ?></p>
                                                    <?php endif; ?>
                                                    <?php if ($slide['description']): ?>
                                                        <p class="text-gray-500 text-sm mt-1"><?php echo htmlspecialchars($slide['description']); ?></p>
                                                    <?php endif; ?>
                                                    <?php if ($slide['image']): ?>
                                                        <img src="<?php echo htmlspecialchars($slide['image']); ?>" alt="Slide Image" class="w-20 h-12 object-cover rounded mt-2">
                                                    <?php endif; ?>
                                                    <div class="flex items-center mt-2 space-x-4">
                                                        <span class="text-xs text-gray-500">ترتيب: <?php echo $slide['sort_order']; ?></span>
                                                        <span class="text-xs <?php echo $slide['is_active'] ? 'text-green-600' : 'text-red-600'; ?>">
                                                            <?php echo $slide['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="flex space-x-2">
                                                    <button onclick="editSlide(<?php echo htmlspecialchars(json_encode($slide)); ?>)" class="text-blue-600 hover:text-blue-800">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <form method="POST" class="inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا السلايدر؟')">
                                                        <input type="hidden" name="action" value="delete_slide">
                                                        <input type="hidden" name="slide_id" value="<?php echo $slide['id']; ?>">
                                                        <button type="submit" class="text-red-600 hover:text-red-800">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
    <?php
    return ob_get_clean();
}

function getConsultationTabContent($consultationSection) {
    ob_start();
    ?>
                    <div class="bg-gray-50 p-6 rounded-lg">
                        <h3 class="text-xl font-semibold mb-4">إعدادات الاستشارة المجانية</h3>
                        <form method="POST" class="space-y-4">
                            <input type="hidden" name="action" value="update_consultation">
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">العنوان الرئيسي</label>
                                <input type="text" name="title" value="<?php echo htmlspecialchars($consultationSection['title'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500" required>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">العنوان الفرعي</label>
                                <input type="text" name="subtitle" value="<?php echo htmlspecialchars($consultationSection['subtitle'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                                <textarea name="description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"><?php echo htmlspecialchars($consultationSection['description'] ?? ''); ?></textarea>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">نص الزر</label>
                                    <input type="text" name="button_text" value="<?php echo htmlspecialchars($consultationSection['button_text'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">رابط الزر</label>
                                    <input type="text" name="button_link" value="<?php echo htmlspecialchars($consultationSection['button_link'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">لون الخلفية</label>
                                    <input type="color" name="background_color" value="<?php echo htmlspecialchars($consultationSection['background_color'] ?? '#047857'); ?>" class="w-full h-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">لون النص</label>
                                    <input type="color" name="text_color" value="<?php echo htmlspecialchars($consultationSection['text_color'] ?? '#FFFFFF'); ?>" class="w-full h-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                                </div>
                            </div>
                            
                            <div class="flex items-center">
                                <input type="checkbox" name="is_active" <?php echo ($consultationSection['is_active'] ?? 1) ? 'checked' : ''; ?> class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                                <label class="mr-2 block text-sm text-gray-900">نشط</label>
                            </div>
                            
                            <button type="submit" class="bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                                <i class="fas fa-save ml-2"></i>حفظ الإعدادات
                            </button>
                        </form>
                    </div>
    <?php
    return ob_get_clean();
}

function getWhyChooseUsTabContent($whyChooseUsSettings) {
    ob_start();
    ?>
                    <div class="bg-gray-50 p-6 rounded-lg">
                        <h3 class="text-xl font-semibold mb-4">إعدادات قسم "لماذا تختارنا؟"</h3>
                        <p class="text-gray-600 mb-4">هذه الإعدادات تتحكم في عرض قسم "لماذا تختارنا؟" في الصفحة الرئيسية. المحتوى نفسه يُدار من <a href="about.php" class="text-blue-600 hover:underline">صفحة "من نحن"</a>.</p>
                        
                        <form method="POST" class="space-y-4">
                            <input type="hidden" name="action" value="update_why_choose_us_settings">
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">عدد العناصر المعروضة</label>
                                    <input type="number" name="max_items" value="<?php echo htmlspecialchars($whyChooseUsSettings['max_items'] ?? 6); ?>" min="1" max="12" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                                </div>
                                
                                <div class="flex items-center">
                                    <input type="checkbox" name="is_visible" <?php echo ($whyChooseUsSettings['is_visible'] ?? 1) ? 'checked' : ''; ?> class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                                    <label class="mr-2 block text-sm text-gray-900">عرض القسم في الصفحة الرئيسية</label>
                                </div>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">عنوان القسم</label>
                                <input type="text" name="section_title" value="<?php echo htmlspecialchars($whyChooseUsSettings['title'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">وصف القسم</label>
                                <textarea name="section_subtitle" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"><?php echo htmlspecialchars($whyChooseUsSettings['subtitle'] ?? ''); ?></textarea>
                            </div>
                            
                            <button type="submit" class="bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                                <i class="fas fa-save ml-2"></i>حفظ الإعدادات
                            </button>
                        </form>
                        
                        <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                            <h4 class="font-semibold text-blue-800 mb-2">ملاحظة مهمة:</h4>
                            <p class="text-blue-700 text-sm">لإدارة محتوى "لماذا تختارنا؟" (إضافة/تعديل/حذف الميزات)، يرجى الانتقال إلى <a href="about.php" class="underline font-semibold">صفحة "من نحن"</a>.</p>
                        </div>
                    </div>
    <?php
    return ob_get_clean();
}

function getProductCategoriesTabContent($productCategories, $currentDisplayCount = 6, $showSection = true) {
    global $database;
    
    // Get total count of active categories
    $totalCategoriesCount = $database->fetch("SELECT COUNT(*) as count FROM product_categories WHERE is_active = 1");
    $totalCategories = $totalCategoriesCount ? $totalCategoriesCount['count'] : 0;
    
    ob_start();
    ?>
                    <!-- Product Categories Tab -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Categories Display Settings -->
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <h3 class="text-xl font-semibold mb-4">إعدادات عرض فئات المنتجات</h3>
                            <form method="POST" class="space-y-4">
                                <input type="hidden" name="action" value="update_categories_settings">
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">عدد الفئات المعروضة في الصفحة الرئيسية</label>
                                    <select name="categories_count" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                                        <option value="3" <?php echo $currentDisplayCount == 3 ? 'selected' : ''; ?>>3 فئات</option>
                                        <option value="6" <?php echo $currentDisplayCount == 6 ? 'selected' : ''; ?>>6 فئات</option>
                                        <option value="9" <?php echo $currentDisplayCount == 9 ? 'selected' : ''; ?>>9 فئات</option>
                                        <option value="12" <?php echo $currentDisplayCount == 12 ? 'selected' : ''; ?>>12 فئة</option>
                                        <option value="15" <?php echo $currentDisplayCount == 15 ? 'selected' : ''; ?>>15 فئة</option>
                                    </select>
                                    <p class="text-sm text-gray-500 mt-1">اختر عدد فئات المنتجات التي تريد عرضها في الصفحة الرئيسية</p>
                                </div>
                                
                                <div class="flex items-center">
                                    <input type="checkbox" name="categories_show_section" id="categories_show_section" value="1" <?php echo $showSection ? 'checked' : ''; ?> class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                                    <label for="categories_show_section" class="mr-2 block text-sm text-gray-900">إظهار قسم "فئات المنتجات" في الصفحة الرئيسية</label>
                                </div>
                                
                                <button type="submit" class="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                                    <i class="fas fa-save ml-2"></i>حفظ الإعدادات
                                </button>
                            </form>
                        </div>
                        
                        <!-- Current Settings Info -->
                        <div>
                            <h3 class="text-xl font-semibold mb-4">الإعدادات الحالية</h3>
                            <div class="bg-white p-4 rounded-lg border border-gray-200">
                                <div class="space-y-3">
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">عدد الفئات المعروضة:</span>
                                        <span class="font-semibold text-green-600"><?php echo $currentDisplayCount; ?> فئة</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">حالة القسم:</span>
                                        <span class="font-semibold <?php echo $showSection ? 'text-green-600' : 'text-red-600'; ?>">
                                            <?php echo $showSection ? 'مفعل' : 'معطل'; ?>
                                        </span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">إجمالي الفئات في قاعدة البيانات:</span>
                                        <span class="font-semibold text-blue-600"><?php echo $totalCategories; ?> فئة</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                                <h4 class="font-semibold text-blue-800 mb-2">ملاحظة مهمة:</h4>
                                <p class="text-blue-700 text-sm">لإدارة فئات المنتجات (إضافة/تعديل/حذف)، يرجى الانتقال إلى صفحة إدارة المنتجات المخصصة.</p>
                                <p class="text-blue-700 text-sm mt-2">هذا القسم مخصص فقط للتحكم في عدد الفئات المعروضة في الصفحة الرئيسية.</p>
                            </div>
                        </div>
                    </div>
    
    <?php
    return ob_get_clean();
}

function getFeaturedProductsTabContent($featuredProducts, $currentDisplayCount, $showSection) {
    global $database;
    
    // Get total count of featured products from products table
    $totalFeaturedProductsCount = $database->fetch("SELECT COUNT(*) as count FROM products WHERE is_featured = 1 AND is_active = 1");
    $totalFeaturedProducts = $totalFeaturedProductsCount ? $totalFeaturedProductsCount['count'] : 0;
    
    ob_start();
    ?>
                    <!-- Featured Products Tab -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Featured Products Display Settings -->
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <h3 class="text-xl font-semibold mb-4">إعدادات عرض المنتجات المميزة</h3>
                            <form method="POST" class="space-y-4">
                                <input type="hidden" name="action" value="update_featured_products_settings">
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">عدد المنتجات المميزة المعروضة في الصفحة الرئيسية</label>
                                    <select name="featured_products_count" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                                        <option value="3" <?php echo $currentDisplayCount == 3 ? 'selected' : ''; ?>>3 منتجات</option>
                                        <option value="6" <?php echo $currentDisplayCount == 6 ? 'selected' : ''; ?>>6 منتجات</option>
                                        <option value="9" <?php echo $currentDisplayCount == 9 ? 'selected' : ''; ?>>9 منتجات</option>
                                        <option value="12" <?php echo $currentDisplayCount == 12 ? 'selected' : ''; ?>>12 منتج</option>
                                        <option value="15" <?php echo $currentDisplayCount == 15 ? 'selected' : ''; ?>>15 منتج</option>
                                    </select>
                                    <p class="text-sm text-gray-500 mt-1">اختر عدد المنتجات المميزة التي تريد عرضها في قسم "منتجاتنا المميزة" في الصفحة الرئيسية</p>
                                </div>
                                
                                <div class="flex items-center">
                                    <input type="checkbox" name="featured_products_show_section" id="featured_products_show_section" value="1" <?php echo $showSection ? 'checked' : ''; ?> class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                                    <label for="featured_products_show_section" class="mr-2 block text-sm text-gray-900">إظهار قسم "منتجاتنا المميزة" في الصفحة الرئيسية</label>
                                </div>
                                
                                <button type="submit" class="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                                    <i class="fas fa-save ml-2"></i>حفظ الإعدادات
                                </button>
                            </form>
                        </div>
                        
                        <!-- Current Settings Info -->
                        <div>
                            <h3 class="text-xl font-semibold mb-4">الإعدادات الحالية</h3>
                            <div class="bg-white p-4 rounded-lg border border-gray-200">
                                <div class="space-y-3">
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">عدد المنتجات المميزة المعروضة:</span>
                                        <span class="font-semibold text-green-600"><?php echo $currentDisplayCount; ?> منتج</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">حالة القسم:</span>
                                        <span class="font-semibold <?php echo $showSection ? 'text-green-600' : 'text-red-600'; ?>">
                                            <?php echo $showSection ? 'مفعل' : 'معطل'; ?>
                                        </span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">إجمالي المنتجات المميزة في قاعدة البيانات:</span>
                                        <span class="font-semibold text-blue-600"><?php echo $totalFeaturedProducts; ?> منتج</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                                <h4 class="font-semibold text-blue-800 mb-2">ملاحظة مهمة:</h4>
                                <p class="text-blue-700 text-sm">لإدارة المنتجات (إضافة/تعديل/حذف)، يرجى الانتقال إلى <a href="products.php" class="underline font-semibold">صفحة إدارة المنتجات</a>.</p>
                                <p class="text-blue-700 text-sm mt-2">هذا القسم مخصص فقط للتحكم في عدد المنتجات المميزة المعروضة في الصفحة الرئيسية.</p>
                            </div>
                        </div>
                    </div>
    <?php
    return ob_get_clean();
}

function getTestimonialsTabContent($testimonials) {
    global $database;
    
    // Get current testimonials display settings
    $displayCountSetting = $database->fetch("SELECT * FROM settings WHERE setting_key = 'testimonials_count'");
    $currentDisplayCount = $displayCountSetting ? (int)$displayCountSetting['setting_value'] : 6;
    
    $showSectionSetting = $database->fetch("SELECT * FROM settings WHERE setting_key = 'testimonials_show_section'");
    $showSection = $showSectionSetting ? $showSectionSetting['setting_value'] : 1;
    
    // Get total count of approved reviews from reviews table
    $totalReviewsCount = $database->fetch("SELECT COUNT(*) as count FROM reviews WHERE is_approved = 1 AND is_rejected = 0");
    $totalReviews = $totalReviewsCount ? $totalReviewsCount['count'] : 0;
    
    ob_start();
    ?>
                    <!-- Testimonials Tab -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Testimonials Display Settings -->
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <h3 class="text-xl font-semibold mb-4">إعدادات عرض التقييمات</h3>
                            <form method="POST" class="space-y-4">
                                <input type="hidden" name="action" value="update_testimonials_settings">
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">عدد التقييمات المعروضة في الصفحة الرئيسية</label>
                                    <select name="testimonials_count" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                                        <option value="3" <?php echo $currentDisplayCount == 3 ? 'selected' : ''; ?>>3 تقييمات</option>
                                        <option value="6" <?php echo $currentDisplayCount == 6 ? 'selected' : ''; ?>>6 تقييمات</option>
                                        <option value="9" <?php echo $currentDisplayCount == 9 ? 'selected' : ''; ?>>9 تقييمات</option>
                                        <option value="12" <?php echo $currentDisplayCount == 12 ? 'selected' : ''; ?>>12 تقييم</option>
                                        <option value="15" <?php echo $currentDisplayCount == 15 ? 'selected' : ''; ?>>15 تقييم</option>
                                    </select>
                                    <p class="text-sm text-gray-500 mt-1">اختر عدد التقييمات التي تريد عرضها في قسم "آراء عملائنا" في الصفحة الرئيسية</p>
                                </div>
                                
                                <div class="flex items-center">
                                    <input type="checkbox" name="testimonials_show_section" id="testimonials_show_section" value="1" <?php echo $showSection ? 'checked' : ''; ?> class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                                    <label for="testimonials_show_section" class="mr-2 block text-sm text-gray-900">إظهار قسم "آراء عملائنا" في الصفحة الرئيسية</label>
                                </div>
                                
                                <button type="submit" class="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                                    <i class="fas fa-save ml-2"></i>حفظ الإعدادات
                                </button>
                            </form>
                        </div>
                        
                        <!-- Current Settings Info -->
                        <div>
                            <h3 class="text-xl font-semibold mb-4">الإعدادات الحالية</h3>
                            <div class="bg-white p-4 rounded-lg border border-gray-200">
                                <div class="space-y-3">
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">عدد التقييمات المعروضة:</span>
                                        <span class="font-semibold text-green-600"><?php echo $currentDisplayCount; ?> تقييم</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">حالة القسم:</span>
                                        <span class="font-semibold <?php echo $showSection ? 'text-green-600' : 'text-red-600'; ?>">
                                            <?php echo $showSection ? 'مفعل' : 'معطل'; ?>
                                        </span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">إجمالي التقييمات في قاعدة البيانات:</span>
                                        <span class="font-semibold text-blue-600"><?php echo $totalReviews; ?> تقييم</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                                <h4 class="font-semibold text-blue-800 mb-2">ملاحظة مهمة:</h4>
                                <p class="text-blue-700 text-sm">لإدارة التقييمات (إضافة/تعديل/حذف)، يرجى الانتقال إلى <a href="reviews.php" class="underline font-semibold">صفحة إدارة المراجعات</a>.</p>
                                <p class="text-blue-700 text-sm mt-2">هذا القسم مخصص فقط للتحكم في عدد التقييمات المعروضة في الصفحة الرئيسية.</p>
                            </div>
                        </div>
                    </div>
    <?php
    return ob_get_clean();
}
?>
    
    <script>
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });
            
            // Remove active class from all tab buttons
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => {
                button.classList.remove('active');
            });
            
            // Show selected tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked tab button
            event.target.classList.add('active');
        }
        
        function editSlide(slide) {
            document.getElementById('slide_id').value = slide.id;
            document.getElementById('slide_title').value = slide.title;
            document.getElementById('slide_subtitle').value = slide.subtitle || '';
            document.getElementById('slide_description').value = slide.description || '';
            document.getElementById('slide_button_text').value = slide.button_text || '';
            document.getElementById('slide_button_link').value = slide.button_link || '';
            document.getElementById('slide_sort_order').value = slide.sort_order || 0;
            document.getElementById('slide_is_active').checked = slide.is_active == 1;
        }
        
        function editTestimonial(testimonial) {
            document.getElementById('testimonial_id').value = testimonial.id;
            document.getElementById('testimonial_name').value = testimonial.name;
            document.getElementById('testimonial_position').value = testimonial.position || '';
            document.getElementById('testimonial_company').value = testimonial.company || '';
            document.getElementById('testimonial_content').value = testimonial.content;
            document.getElementById('testimonial_rating').value = testimonial.rating;
            document.getElementById('testimonial_sort_order').value = testimonial.sort_order || 0;
            document.getElementById('testimonial_is_featured').checked = testimonial.is_featured == 1;
            document.getElementById('testimonial_is_active').checked = testimonial.is_active == 1;
            document.getElementById('testimonial_is_visible').checked = (testimonial.is_visible ?? 1) == 1;
        }
        
        function editCategory(category) {
            document.getElementById('category_id').value = category.id;
            document.getElementById('category_name').value = category.name;
            document.getElementById('category_description').value = category.description || '';
            document.getElementById('category_sort_order').value = category.sort_order || 0;
            document.getElementById('category_is_active').checked = category.is_active == 1;
            document.getElementById('category_is_visible').checked = (category.is_visible ?? 1) == 1;
        }
        
        function editProduct(product) {
            document.getElementById('featured_product_id').value = product.id;
            document.getElementById('featured_product_name').value = product.name;
            document.getElementById('featured_product_description').value = product.description || '';
            document.getElementById('featured_product_price').value = product.price;
            document.getElementById('featured_product_original_price').value = product.original_price || '';
            document.getElementById('featured_product_category_id').value = product.category_id || '';
            document.getElementById('featured_product_rating').value = product.rating || '';
            document.getElementById('featured_product_sort_order').value = product.sort_order || 0;
            document.getElementById('featured_product_is_featured').checked = product.is_featured == 1;
            document.getElementById('featured_product_is_active').checked = product.is_active == 1;
            document.getElementById('featured_product_is_visible').checked = (product.is_visible ?? 1) == 1;
            
            if (product.image) {
                document.getElementById('featured_product_image_preview').innerHTML = `<img src="../uploads/${product.image}" alt="Product Image" class="w-20 h-20 object-cover rounded">`;
            }
        }
        
        function previewImage(input, previewId) {
            const preview = document.getElementById(previewId);
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.innerHTML = `<img src="${e.target.result}" alt="Preview" class="w-20 h-20 object-cover rounded border">`;
                };
                reader.readAsDataURL(input.files[0]);
            } else {
                preview.innerHTML = '';
            }
        }
        
        // Store all testimonials for filtering
        let allTestimonials = <?php echo json_encode($testimonials); ?>;
        
        function updateTestimonialFilters() {
            const allCheckbox = document.getElementById('filter_all');
            const bestCheckbox = document.getElementById('filter_best');
            const randomCheckbox = document.getElementById('filter_random');
            const container = document.getElementById('testimonials_container');
            
            // Get selected filters
            const selectedFilters = [];
            if (allCheckbox.checked) selectedFilters.push('all');
            if (bestCheckbox.checked) selectedFilters.push('best');
            if (randomCheckbox.checked) selectedFilters.push('random');
            
            // If no filters selected, default to 'all'
            if (selectedFilters.length === 0) {
                allCheckbox.checked = true;
                selectedFilters.push('all');
            }
            
            // Save filters to database
            saveTestimonialFilters(selectedFilters);
            
            // Show loading state
            container.innerHTML = '<div class="text-center py-8"><i class="fas fa-spinner fa-spin text-2xl text-gray-400"></i><p class="text-gray-500 mt-2">جاري التحميل...</p></div>';
            
            // Fetch and combine results from multiple filters
            fetchMultipleFilteredTestimonials(selectedFilters);
        }
        
        function fetchMultipleFilteredTestimonials(filters) {
            const promises = [];
            const results = [];
            
            filters.forEach(filter => {
                if (filter === 'random') {
                    // For random, always fetch from API
                    promises.push(
                        fetch(`../api/testimonials.php?action=random&limit=3`)
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    return { filter, data: data.data };
                                } else {
                                    // Fallback to local filtering
                                    const shuffled = [...allTestimonials].sort(() => 0.5 - Math.random());
                                    return { filter, data: shuffled.slice(0, 3) };
                                }
                            })
                            .catch(error => {
                                console.error('Fetch error:', error);
                                // Fallback to local filtering
                                const shuffled = [...allTestimonials].sort(() => 0.5 - Math.random());
                                return { filter, data: shuffled.slice(0, 3) };
                            })
                    );
                } else {
                    // For other filters, use local data
                    let filteredTestimonials = [];
                    
                    switch(filter) {
                        case 'all':
                            filteredTestimonials = allTestimonials;
                            break;
                        case 'best':
                            filteredTestimonials = allTestimonials.filter(t => t.rating == 5);
                            break;
                    }
                    
                    promises.push(Promise.resolve({ filter, data: filteredTestimonials }));
                }
            });
            
            // Wait for all promises to resolve
            Promise.all(promises)
                .then(results => {
                    // Combine and deduplicate results
                    const combinedTestimonials = [];
                    const seenIds = new Set();
                    
                    results.forEach(result => {
                        result.data.forEach(testimonial => {
                            if (!seenIds.has(testimonial.id)) {
                                seenIds.add(testimonial.id);
                                combinedTestimonials.push(testimonial);
                            }
                        });
                    });
                    
                    // Limit total results
                    const maxResults = 20;
                    const finalResults = combinedTestimonials.slice(0, maxResults);
                    
                    displayTestimonials(finalResults);
                })
                .catch(error => {
                    console.error('Error combining results:', error);
                    // Fallback to showing all testimonials
                    displayTestimonials(allTestimonials);
                });
        }
        
        function refreshTestimonials() {
            // Refresh the page to get updated data from database
            window.location.reload();
        }
        
        function updateTestimonialsCount() {
            const count = document.getElementById('testimonials_count').value;
            if (count && count > 0 && count <= 20) {
                // Save the setting to localStorage for persistence
                localStorage.setItem('testimonials_count', count);
                
                // Send AJAX request to update the setting in database
                fetch('../api/update_setting.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        setting_name: 'testimonials_count',
                        setting_value: count
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show success message
                        showNotification('تم حفظ إعداد عدد التقييمات بنجاح', 'success');
                    } else {
                        console.error('Error updating setting:', data.message);
                        showNotification('حدث خطأ في حفظ الإعداد', 'error');
                    }
                })
                .catch(error => {
                    console.error('Fetch error:', error);
                    showNotification('حدث خطأ في الاتصال', 'error');
                });
            }
        }
        
        function showNotification(message, type = 'info') {
            // Simple notification function
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-md text-white ${
                type === 'success' ? 'bg-green-500' : 
                type === 'error' ? 'bg-red-500' : 'bg-blue-500'
            }`;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }
        
        function saveTestimonialFilters(filters) {
            // Send AJAX request to save filters in database
            fetch('../api/update_setting.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    setting_name: 'testimonials_filters',
                    setting_value: JSON.stringify(filters)
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('تم حفظ فلاتر التقييمات بنجاح');
                } else {
                    console.error('Error saving filters:', data.message);
                }
            })
            .catch(error => {
                console.error('Fetch error:', error);
            });
        }
        
        function loadSavedFilters() {
            // Load saved filters from database
            fetch('../api/get_setting.php?key=testimonials_filters')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.value) {
                    try {
                        const savedFilters = JSON.parse(data.value);
                        
                        // Reset all checkboxes
                        document.getElementById('filter_all').checked = false;
                        document.getElementById('filter_best').checked = false;
                        document.getElementById('filter_random').checked = false;
                        
                        // Apply saved filters
                        savedFilters.forEach(filter => {
                            const checkbox = document.getElementById('filter_' + filter);
                            if (checkbox) {
                                checkbox.checked = true;
                            }
                        });
                        
                        // Apply the filters
                        fetchMultipleFilteredTestimonials(savedFilters);
                    } catch (e) {
                        console.error('Error parsing saved filters:', e);
                        // Default to 'all' if parsing fails
                        document.getElementById('filter_all').checked = true;
                    }
                } else {
                    // Default to 'all' if no saved filters
                    document.getElementById('filter_all').checked = true;
                }
            })
            .catch(error => {
                console.error('Error loading filters:', error);
                // Default to 'all' if error
                document.getElementById('filter_all').checked = true;
            });
        }
        
        // Load saved testimonials count and filters on page load
        document.addEventListener('DOMContentLoaded', function() {
            const savedCount = localStorage.getItem('testimonials_count');
            if (savedCount) {
                document.getElementById('testimonials_count').value = savedCount;
            }
            
            // Load saved filters
            loadSavedFilters();
        });
        
        function displayTestimonials(testimonials) {
            const container = document.getElementById('testimonials_container');
            let html = '';
            
            testimonials.forEach(testimonial => {
                html += `
                    <div class="bg-white p-4 rounded-lg border border-gray-200">
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <div class="flex items-center mb-2">
                                    ${testimonial.image ? `<img src="${testimonial.image}" alt="Client Image" class="w-10 h-10 rounded-full ml-3">` : ''}
                                    <div>
                                        <h4 class="font-semibold">${testimonial.name}</h4>
                                        ${(testimonial.position || testimonial.company) ? `
                                            <p class="text-gray-600 text-sm">
                                                ${testimonial.position || ''}
                                                ${(testimonial.position && testimonial.company) ? ' - ' : ''}
                                                ${testimonial.company || ''}
                                            </p>
                                        ` : ''}
                                    </div>
                                </div>
                                <p class="text-gray-700 text-sm mb-2">${testimonial.content}</p>
                                <div class="flex items-center space-x-4">
                                    <div class="flex items-center">
                                        ${Array.from({length: 5}, (_, i) => 
                                            `<i class="fas fa-star text-xs ${i < testimonial.rating ? 'text-yellow-400' : 'text-gray-300'}"></i>`
                                        ).join('')}
                                    </div>
                                    <span class="text-xs text-gray-500">ترتيب: ${testimonial.sort_order}</span>
                                    ${testimonial.is_featured ? '<span class="text-xs text-blue-600">مميز</span>' : ''}
                                    <span class="text-xs ${testimonial.is_active ? 'text-green-600' : 'text-red-600'}">
                                        ${testimonial.is_active ? 'نشط' : 'غير نشط'}
                                    </span>
                                    <span class="text-xs ${(testimonial.is_visible ?? 1) ? 'text-blue-600' : 'text-orange-600'}">
                                        ${(testimonial.is_visible ?? 1) ? 'ظاهر' : 'مخفي'}
                                    </span>
                                </div>
                            </div>
                            <div class="flex space-x-2">
                                <button onclick="editTestimonial(${JSON.stringify(testimonial).replace(/"/g, '&quot;')})" class="text-blue-600 hover:text-blue-800">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <form method="POST" class="inline" onsubmit="return confirm('هل أنت متأكد من حذف هذه المراجعة؟')">
                                    <input type="hidden" name="action" value="delete_testimonial">
                                    <input type="hidden" name="testimonial_id" value="${testimonial.id}">
                                    <button type="submit" class="text-red-600 hover:text-red-800">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
    </script>

<?php endLayout(); ?>

                    