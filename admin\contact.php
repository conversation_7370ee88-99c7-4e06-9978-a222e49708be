<?php
/**
 * صفحة إدارة معلومات التواصل
 * Contact Information Management Page
 */

require_once 'includes/layout.php';

$currentPage = 'contact';
$pageTitle = 'إدارة معلومات التواصل';
$pageDescription = 'إدارة وتحديث معلومات التواصل والعنوان وأرقام الهاتف';
$additionalCSS = ['assets/css/contact-management.css'];
$breadcrumbs = [
    ['title' => 'المحتوى الإضافي'],
    ['title' => 'معلومات التواصل']
];
$message = '';
$messageType = '';

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $sectionKey = $_POST['section_key'] ?? '';
        $title = $_POST['title'] ?? '';
        $content = $_POST['content'] ?? '';
        $isActive = isset($_POST['is_active']) ? 1 : 0;
        
        // معالجة البيانات الإضافية (JSON)
        $data = [];
        if (isset($_POST['data']) && is_array($_POST['data'])) {
            $data = $_POST['data'];
        }
        
        // معالجة خاصة لكل قسم
        switch ($sectionKey) {
            case 'address':
                $data = [
                    'street' => $_POST['data']['street'] ?? '',
                    'district' => $_POST['data']['district'] ?? '',
                    'city' => $_POST['data']['city'] ?? '',
                    'postal_code' => $_POST['data']['postal_code'] ?? '',
                    'country' => $_POST['data']['country'] ?? '',
                    'coordinates' => [
                        'lat' => floatval($_POST['data']['lat'] ?? 0),
                        'lng' => floatval($_POST['data']['lng'] ?? 0)
                    ]
                ];
                break;
                
            case 'phone':
                $data = [
                    'primary' => $_POST['data']['primary'] ?? '',
                    'secondary' => $_POST['data']['secondary'] ?? '',
                    'mobile' => $_POST['data']['mobile'] ?? '',
                    'fax' => $_POST['data']['fax'] ?? ''
                ];
                break;
                
            case 'email':
                $data = [
                    'general' => $_POST['data']['general'] ?? '',
                    'sales' => $_POST['data']['sales'] ?? '',
                    'support' => $_POST['data']['support'] ?? '',
                    'hr' => $_POST['data']['hr'] ?? ''
                ];
                break;
                
            case 'working_hours':
                $data = [
                    'sunday_thursday' => [
                        'open' => $_POST['data']['sunday_thursday_open'] ?? '',
                        'close' => $_POST['data']['sunday_thursday_close'] ?? ''
                    ],
                    'friday' => [
                        'open' => $_POST['data']['friday_open'] ?? '',
                        'close' => $_POST['data']['friday_close'] ?? ''
                    ],
                    'saturday' => $_POST['data']['saturday'] ?? 'closed',
                    'timezone' => $_POST['data']['timezone'] ?? 'Asia/Riyadh'
                ];
                break;
                
            case 'whatsapp':
                $data = [
                    'number' => $_POST['data']['number'] ?? '',
                    'business_hours_only' => isset($_POST['data']['business_hours_only']),
                    'auto_reply' => $_POST['data']['auto_reply'] ?? ''
                ];
                break;
                
            case 'map_embed':
                $data = [
                    'embed_code' => $_POST['data']['embed_code'] ?? '',
                    'coordinates' => [
                        'lat' => floatval($_POST['data']['lat'] ?? 0),
                        'lng' => floatval($_POST['data']['lng'] ?? 0)
                    ],
                    'zoom_level' => intval($_POST['data']['zoom_level'] ?? 15)
                ];
                break;
                
            case 'social_media':
                // معالجة ديناميكية لجميع منصات التواصل الاجتماعي
                $data = [];
                if (isset($_POST['data']) && is_array($_POST['data'])) {
                    foreach ($_POST['data'] as $platform => $url) {
                        // تنظيف الرابط وحفظه فقط إذا لم يكن فارغاً
                        $cleanUrl = trim($url);
                        if (!empty($cleanUrl)) {
                            $data[$platform] = $cleanUrl;
                        }
                    }
                }
                break;
                
            case 'company_info':
                $data = [
                    'company_name' => $_POST['data']['company_name'] ?? '',
                    'commercial_register' => $_POST['data']['commercial_register'] ?? '',
                    'tax_number' => $_POST['data']['tax_number'] ?? '',
                    'established_year' => intval($_POST['data']['established_year'] ?? date('Y')),
                    'license_number' => $_POST['data']['license_number'] ?? ''
                ];
                break;
        }
        
        $dataJson = json_encode($data, JSON_UNESCAPED_UNICODE);
        
        // التحقق من وجود السجل
        $existing = $database->fetch("SELECT id FROM contact_info WHERE section_key = ?", [$sectionKey]);
        
        if ($existing) {
            // تحديث السجل الموجود
            $database->query(
                "UPDATE contact_info SET title = ?, content = ?, data = ?, is_active = ?, updated_at = NOW() WHERE section_key = ?",
                [$title, $content, $dataJson, $isActive, $sectionKey]
            );
        } else {
            // إنشاء سجل جديد
            $database->query(
                "INSERT INTO contact_info (section_key, title, content, data, is_active, created_at, updated_at) VALUES (?, ?, ?, ?, ?, NOW(), NOW())",
                [$sectionKey, $title, $content, $dataJson, $isActive]
            );
        }
        
        $message = 'تم حفظ معلومات التواصل بنجاح';
        $messageType = 'success';
        
    } catch (Exception $e) {
        $message = 'حدث خطأ أثناء حفظ البيانات: ' . $e->getMessage();
        $messageType = 'error';
    }
}

// الحصول على التبويب النشط
$activeTab = $_GET['tab'] ?? 'address';

// الحصول على جميع معلومات التواصل
$contactInfo = [];
try {
    $results = $database->fetchAll("SELECT * FROM contact_info ORDER BY section_key");
    foreach ($results as $row) {
        $contactInfo[$row['section_key']] = $row;
        if ($row['data']) {
            $contactInfo[$row['section_key']]['data_parsed'] = json_decode($row['data'], true);
        }
    }
} catch (Exception $e) {
    // في حالة عدم وجود الجدول أو خطأ
}

startLayout();
showPageHeader();
showMessages();

// عرض رسائل التنبيه المخصصة
if ($message): ?>
<div class="mb-6">
    <div class="<?php echo $messageType === 'success' ? 'bg-green-50 border-green-200 text-green-800' : 'bg-red-50 border-red-200 text-red-800'; ?> border rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <?php if ($messageType === 'success'): ?>
                <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                <?php else: ?>
                <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                </svg>
                <?php endif; ?>
            </div>
            <div class="mr-3">
                <p class="text-sm font-medium"><?php echo htmlspecialchars($message); ?></p>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- التبويبات -->
<div class="bg-white rounded-lg shadow">
    <div class="border-b border-gray-200">
        <nav class="-mb-px flex space-x-8 space-x-reverse px-6 overflow-x-auto" aria-label="Tabs">
            <a href="?tab=address" class="<?php echo $activeTab === 'address' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?> whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                العنوان
            </a>
            <a href="?tab=phone" class="<?php echo $activeTab === 'phone' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?> whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                الهاتف
            </a>
            <a href="?tab=email" class="<?php echo $activeTab === 'email' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?> whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                البريد الإلكتروني
            </a>
            <a href="?tab=working_hours" class="<?php echo $activeTab === 'working_hours' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?> whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                ساعات العمل
            </a>
            <a href="?tab=whatsapp" class="<?php echo $activeTab === 'whatsapp' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?> whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                واتساب
            </a>
            <a href="?tab=map_embed" class="<?php echo $activeTab === 'map_embed' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?> whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                الخريطة
            </a>
            <a href="?tab=social_media" class="<?php echo $activeTab === 'social_media' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?> whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                وسائل التواصل
            </a>
            <a href="?tab=company_info" class="<?php echo $activeTab === 'company_info' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?> whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                معلومات الشركة
            </a>
        </nav>
    </div>

    <div class="p-6">
        <?php if ($activeTab === 'address'): 
            $data = $contactInfo['address']['data_parsed'] ?? [];
        ?>
        <!-- معلومات العنوان -->
        <form method="POST" class="space-y-6">
            <input type="hidden" name="section_key" value="address">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">عنوان القسم</label>
                    <input type="text" id="title" name="title" value="<?php echo htmlspecialchars($contactInfo['address']['title'] ?? 'العنوان'); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                </div>
                
                <div>
                    <label for="content" class="block text-sm font-medium text-gray-700 mb-2">العنوان المختصر</label>
                    <input type="text" id="content" name="content" value="<?php echo htmlspecialchars($contactInfo['address']['content'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label for="street" class="block text-sm font-medium text-gray-700 mb-2">الشارع</label>
                    <input type="text" id="street" name="data[street]" value="<?php echo htmlspecialchars($data['street'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label for="district" class="block text-sm font-medium text-gray-700 mb-2">الحي</label>
                    <input type="text" id="district" name="data[district]" value="<?php echo htmlspecialchars($data['district'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label for="city" class="block text-sm font-medium text-gray-700 mb-2">المدينة</label>
                    <input type="text" id="city" name="data[city]" value="<?php echo htmlspecialchars($data['city'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label for="postal_code" class="block text-sm font-medium text-gray-700 mb-2">الرمز البريدي</label>
                    <input type="text" id="postal_code" name="data[postal_code]" value="<?php echo htmlspecialchars($data['postal_code'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label for="country" class="block text-sm font-medium text-gray-700 mb-2">الدولة</label>
                    <input type="text" id="country" name="data[country]" value="<?php echo htmlspecialchars($data['country'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label for="lat" class="block text-sm font-medium text-gray-700 mb-2">خط العرض (Latitude)</label>
                    <input type="number" step="any" id="lat" name="data[lat]" value="<?php echo $data['coordinates']['lat'] ?? ''; ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label for="lng" class="block text-sm font-medium text-gray-700 mb-2">خط الطول (Longitude)</label>
                    <input type="number" step="any" id="lng" name="data[lng]" value="<?php echo $data['coordinates']['lng'] ?? ''; ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
            
            <div class="flex items-center">
                <input type="checkbox" id="is_active" name="is_active" <?php echo ($contactInfo['address']['is_active'] ?? 1) ? 'checked' : ''; ?> class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <label for="is_active" class="mr-2 block text-sm text-gray-900">فعال</label>
            </div>
            
            <div class="flex justify-end">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                    حفظ التغييرات
                </button>
            </div>
        </form>
        
        <?php elseif ($activeTab === 'phone'): 
            $data = $contactInfo['phone']['data_parsed'] ?? [];
        ?>
        <!-- معلومات الهاتف -->
        <form method="POST" class="space-y-6">
            <input type="hidden" name="section_key" value="phone">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">عنوان القسم</label>
                    <input type="text" id="title" name="title" value="<?php echo htmlspecialchars($contactInfo['phone']['title'] ?? 'الهاتف'); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                </div>
                
                <div>
                    <label for="content" class="block text-sm font-medium text-gray-700 mb-2">الهاتف الرئيسي</label>
                    <input type="text" id="content" name="content" value="<?php echo htmlspecialchars($contactInfo['phone']['content'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label for="primary" class="block text-sm font-medium text-gray-700 mb-2">الهاتف الأساسي</label>
                    <input type="text" id="primary" name="data[primary]" value="<?php echo htmlspecialchars($data['primary'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label for="secondary" class="block text-sm font-medium text-gray-700 mb-2">الهاتف الثانوي</label>
                    <input type="text" id="secondary" name="data[secondary]" value="<?php echo htmlspecialchars($data['secondary'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label for="mobile" class="block text-sm font-medium text-gray-700 mb-2">الجوال</label>
                    <input type="text" id="mobile" name="data[mobile]" value="<?php echo htmlspecialchars($data['mobile'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label for="fax" class="block text-sm font-medium text-gray-700 mb-2">الفاكس</label>
                    <input type="text" id="fax" name="data[fax]" value="<?php echo htmlspecialchars($data['fax'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
            
            <div class="flex items-center">
                <input type="checkbox" id="is_active" name="is_active" <?php echo ($contactInfo['phone']['is_active'] ?? 1) ? 'checked' : ''; ?> class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <label for="is_active" class="mr-2 block text-sm text-gray-900">فعال</label>
            </div>
            
            <div class="flex justify-end">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                    حفظ التغييرات
                </button>
            </div>
        </form>
        
        <?php elseif ($activeTab === 'email'): 
            $data = $contactInfo['email']['data_parsed'] ?? [];
        ?>
        <!-- معلومات البريد الإلكتروني -->
        <form method="POST" class="space-y-6">
            <input type="hidden" name="section_key" value="email">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">عنوان القسم</label>
                    <input type="text" id="title" name="title" value="<?php echo htmlspecialchars($contactInfo['email']['title'] ?? 'البريد الإلكتروني'); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                </div>
                
                <div>
                    <label for="content" class="block text-sm font-medium text-gray-700 mb-2">البريد الرئيسي</label>
                    <input type="email" id="content" name="content" value="<?php echo htmlspecialchars($contactInfo['email']['content'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label for="general" class="block text-sm font-medium text-gray-700 mb-2">البريد العام</label>
                    <input type="email" id="general" name="data[general]" value="<?php echo htmlspecialchars($data['general'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label for="sales" class="block text-sm font-medium text-gray-700 mb-2">بريد المبيعات</label>
                    <input type="email" id="sales" name="data[sales]" value="<?php echo htmlspecialchars($data['sales'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label for="support" class="block text-sm font-medium text-gray-700 mb-2">بريد الدعم الفني</label>
                    <input type="email" id="support" name="data[support]" value="<?php echo htmlspecialchars($data['support'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label for="hr" class="block text-sm font-medium text-gray-700 mb-2">بريد الموارد البشرية</label>
                    <input type="email" id="hr" name="data[hr]" value="<?php echo htmlspecialchars($data['hr'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
            
            <div class="flex items-center">
                <input type="checkbox" id="is_active" name="is_active" <?php echo ($contactInfo['email']['is_active'] ?? 1) ? 'checked' : ''; ?> class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <label for="is_active" class="mr-2 block text-sm text-gray-900">فعال</label>
            </div>
            
            <div class="flex justify-end">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                    حفظ التغييرات
                </button>
            </div>
        </form>
        
        <?php elseif ($activeTab === 'working_hours'): 
            $data = $contactInfo['working_hours']['data_parsed'] ?? [];
        ?>
        <!-- ساعات العمل -->
        <form method="POST" class="space-y-6">
            <input type="hidden" name="section_key" value="working_hours">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">عنوان القسم</label>
                    <input type="text" id="title" name="title" value="<?php echo htmlspecialchars($contactInfo['working_hours']['title'] ?? 'ساعات العمل'); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                </div>
                
                <div>
                    <label for="timezone" class="block text-sm font-medium text-gray-700 mb-2">المنطقة الزمنية</label>
                    <select id="timezone" name="data[timezone]" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="Asia/Riyadh" <?php echo ($data['timezone'] ?? '') === 'Asia/Riyadh' ? 'selected' : ''; ?>>الرياض (Asia/Riyadh)</option>
                        <option value="Asia/Dubai" <?php echo ($data['timezone'] ?? '') === 'Asia/Dubai' ? 'selected' : ''; ?>>دبي (Asia/Dubai)</option>
                        <option value="Asia/Kuwait" <?php echo ($data['timezone'] ?? '') === 'Asia/Kuwait' ? 'selected' : ''; ?>>الكويت (Asia/Kuwait)</option>
                    </select>
                </div>
            </div>
            
            <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900">الأحد - الخميس</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="sunday_thursday_open" class="block text-sm font-medium text-gray-700 mb-2">وقت الفتح</label>
                        <input type="time" id="sunday_thursday_open" name="data[sunday_thursday_open]" value="<?php echo htmlspecialchars($data['sunday_thursday']['open'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label for="sunday_thursday_close" class="block text-sm font-medium text-gray-700 mb-2">وقت الإغلاق</label>
                        <input type="time" id="sunday_thursday_close" name="data[sunday_thursday_close]" value="<?php echo htmlspecialchars($data['sunday_thursday']['close'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
            </div>
            
            <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900">الجمعة</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="friday_open" class="block text-sm font-medium text-gray-700 mb-2">وقت الفتح</label>
                        <input type="time" id="friday_open" name="data[friday_open]" value="<?php echo htmlspecialchars($data['friday']['open'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label for="friday_close" class="block text-sm font-medium text-gray-700 mb-2">وقت الإغلاق</label>
                        <input type="time" id="friday_close" name="data[friday_close]" value="<?php echo htmlspecialchars($data['friday']['close'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
            </div>
            
            <div>
                <label for="saturday" class="block text-sm font-medium text-gray-700 mb-2">السبت</label>
                <select id="saturday" name="data[saturday]" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="closed" <?php echo ($data['saturday'] ?? '') === 'closed' ? 'selected' : ''; ?>>مغلق</option>
                    <option value="open" <?php echo ($data['saturday'] ?? '') === 'open' ? 'selected' : ''; ?>>مفتوح</option>
                </select>
            </div>
            
            <div>
                <label for="content" class="block text-sm font-medium text-gray-700 mb-2">وصف ساعات العمل</label>
                <textarea id="content" name="content" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo htmlspecialchars($contactInfo['working_hours']['content'] ?? ''); ?></textarea>
            </div>
            
            <div class="flex items-center">
                <input type="checkbox" id="is_active" name="is_active" <?php echo ($contactInfo['working_hours']['is_active'] ?? 1) ? 'checked' : ''; ?> class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <label for="is_active" class="mr-2 block text-sm text-gray-900">فعال</label>
            </div>
            
            <div class="flex justify-end">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                    حفظ التغييرات
                </button>
            </div>
        </form>
        
        <?php elseif ($activeTab === 'whatsapp'): 
            $data = $contactInfo['whatsapp']['data_parsed'] ?? [];
        ?>
        <!-- معلومات الواتساب -->
        <form method="POST" class="space-y-6">
            <input type="hidden" name="section_key" value="whatsapp">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">عنوان القسم</label>
                    <input type="text" id="title" name="title" value="<?php echo htmlspecialchars($contactInfo['whatsapp']['title'] ?? 'واتساب'); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                </div>
                
                <div>
                    <label for="content" class="block text-sm font-medium text-gray-700 mb-2">رقم الواتساب</label>
                    <input type="text" id="content" name="content" value="<?php echo htmlspecialchars($contactInfo['whatsapp']['content'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label for="number" class="block text-sm font-medium text-gray-700 mb-2">رقم الواتساب (مع رمز الدولة)</label>
                    <input type="text" id="number" name="data[number]" value="<?php echo htmlspecialchars($data['number'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="+966501234567">
                </div>
                
                <div>
                    <label for="auto_reply" class="block text-sm font-medium text-gray-700 mb-2">الرد التلقائي</label>
                    <textarea id="auto_reply" name="data[auto_reply]" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo htmlspecialchars($data['auto_reply'] ?? ''); ?></textarea>
                </div>
            </div>
            
            <div class="flex items-center">
                <input type="checkbox" id="business_hours_only" name="data[business_hours_only]" <?php echo ($data['business_hours_only'] ?? false) ? 'checked' : ''; ?> class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <label for="business_hours_only" class="mr-2 block text-sm text-gray-900">متاح فقط في ساعات العمل</label>
            </div>
            
            <div class="flex items-center">
                <input type="checkbox" id="is_active" name="is_active" <?php echo ($contactInfo['whatsapp']['is_active'] ?? 1) ? 'checked' : ''; ?> class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <label for="is_active" class="mr-2 block text-sm text-gray-900">فعال</label>
            </div>
            
            <div class="flex justify-end">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                    حفظ التغييرات
                </button>
            </div>
        </form>
        
        <?php elseif ($activeTab === 'map_embed'): 
            $data = $contactInfo['map_embed']['data_parsed'] ?? [];
        ?>
        <!-- خريطة جوجل -->
        <form method="POST" class="space-y-6">
            <input type="hidden" name="section_key" value="map_embed">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">عنوان القسم</label>
                    <input type="text" id="title" name="title" value="<?php echo htmlspecialchars($contactInfo['map_embed']['title'] ?? 'الخريطة'); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                </div>
                
                <div>
                    <label for="zoom_level" class="block text-sm font-medium text-gray-700 mb-2">مستوى التكبير</label>
                    <input type="number" id="zoom_level" name="data[zoom_level]" value="<?php echo $data['zoom_level'] ?? 15; ?>" min="1" max="20" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label for="lat" class="block text-sm font-medium text-gray-700 mb-2">خط العرض (Latitude)</label>
                    <input type="number" step="any" id="lat" name="data[lat]" value="<?php echo $data['coordinates']['lat'] ?? ''; ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label for="lng" class="block text-sm font-medium text-gray-700 mb-2">خط الطول (Longitude)</label>
                    <input type="number" step="any" id="lng" name="data[lng]" value="<?php echo $data['coordinates']['lng'] ?? ''; ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
            
            <div>
                <label for="embed_code" class="block text-sm font-medium text-gray-700 mb-2">كود تضمين الخريطة</label>
                <textarea id="embed_code" name="data[embed_code]" rows="5" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="<iframe src=...></iframe>"><?php echo htmlspecialchars($data['embed_code'] ?? ''); ?></textarea>
                <p class="mt-1 text-sm text-gray-500">انسخ كود التضمين من خرائط جوجل</p>
            </div>
            
            <!-- معاينة الخريطة -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">معاينة الخريطة</label>
                <div id="map_preview" class="border border-gray-300 rounded-md p-4" style="min-height: 200px; display: <?php echo !empty($data['embed_code']) ? 'block' : 'none'; ?>;">
                    <?php if (!empty($data['embed_code'])): ?>
                        <?php echo $data['embed_code']; ?>
                    <?php else: ?>
                        <div class="text-gray-500 text-center py-8">لا توجد خريطة للعرض</div>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="flex items-center">
                <input type="checkbox" id="is_active" name="is_active" <?php echo ($contactInfo['map_embed']['is_active'] ?? 1) ? 'checked' : ''; ?> class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <label for="is_active" class="mr-2 block text-sm text-gray-900">فعال</label>
            </div>
            
            <div class="flex justify-end">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                    حفظ التغييرات
                </button>
            </div>
        </form>
        
        <?php elseif ($activeTab === 'social_media'): 
            $data = $contactInfo['social_media']['data_parsed'] ?? [];
            
            // قائمة المنصات الافتراضية مع أيقوناتها
            $defaultPlatforms = [
                'facebook' => ['name' => 'فيسبوك', 'icon' => 'fab fa-facebook-f', 'color' => 'blue', 'placeholder' => 'https://facebook.com/yourpage'],
                'twitter' => ['name' => 'تويتر', 'icon' => 'fab fa-twitter', 'color' => 'sky', 'placeholder' => 'https://twitter.com/yourhandle'],
                'instagram' => ['name' => 'إنستغرام', 'icon' => 'fab fa-instagram', 'color' => 'pink', 'placeholder' => 'https://instagram.com/yourhandle'],
                'linkedin' => ['name' => 'لينكد إن', 'icon' => 'fab fa-linkedin-in', 'color' => 'blue', 'placeholder' => 'https://linkedin.com/company/yourcompany'],
                'youtube' => ['name' => 'يوتيوب', 'icon' => 'fab fa-youtube', 'color' => 'red', 'placeholder' => 'https://youtube.com/@yourchannel'],
                'tiktok' => ['name' => 'تيك توك', 'icon' => 'fab fa-tiktok', 'color' => 'gray', 'placeholder' => 'https://tiktok.com/@yourhandle'],
                'snapchat' => ['name' => 'سناب شات', 'icon' => 'fab fa-snapchat-ghost', 'color' => 'yellow', 'placeholder' => 'https://snapchat.com/add/yourhandle'],
                'telegram' => ['name' => 'تيليجرام', 'icon' => 'fab fa-telegram-plane', 'color' => 'blue', 'placeholder' => 'https://t.me/yourchannel'],
                'discord' => ['name' => 'ديسكورد', 'icon' => 'fab fa-discord', 'color' => 'indigo', 'placeholder' => 'https://discord.gg/yourserver'],
                'pinterest' => ['name' => 'بينتيريست', 'icon' => 'fab fa-pinterest', 'color' => 'red', 'placeholder' => 'https://pinterest.com/yourprofile'],
                'reddit' => ['name' => 'ريديت', 'icon' => 'fab fa-reddit', 'color' => 'orange', 'placeholder' => 'https://reddit.com/r/yoursubreddit'],
                'tumblr' => ['name' => 'تمبلر', 'icon' => 'fab fa-tumblr', 'color' => 'blue', 'placeholder' => 'https://yourhandle.tumblr.com'],
                'twitch' => ['name' => 'تويتش', 'icon' => 'fab fa-twitch', 'color' => 'purple', 'placeholder' => 'https://twitch.tv/yourchannel'],
                'github' => ['name' => 'جيت هاب', 'icon' => 'fab fa-github', 'color' => 'gray', 'placeholder' => 'https://github.com/yourusername'],
                'behance' => ['name' => 'بيهانس', 'icon' => 'fab fa-behance', 'color' => 'blue', 'placeholder' => 'https://behance.net/yourprofile'],
                'dribbble' => ['name' => 'دريبل', 'icon' => 'fab fa-dribbble', 'color' => 'pink', 'placeholder' => 'https://dribbble.com/yourprofile']
            ];
        ?>
        <!-- وسائل التواصل الاجتماعي -->
        <form method="POST" class="space-y-6" id="social-media-form">
            <input type="hidden" name="section_key" value="social_media">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="md:col-span-2">
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">عنوان القسم</label>
                    <input type="text" id="title" name="title" value="<?php echo htmlspecialchars($contactInfo['social_media']['title'] ?? 'وسائل التواصل الاجتماعي'); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                </div>
            </div>
            
            <!-- وسائل التواصل الحالية -->
            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-medium text-gray-900">وسائل التواصل المضافة</h3>
                    <button type="button" id="add-platform-btn" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                        <i class="fas fa-plus mr-2"></i>إضافة منصة جديدة
                    </button>
                </div>
                
                <div id="social-platforms-container" class="space-y-4">
                    <?php foreach ($data as $platform => $url): ?>
                        <?php if (!empty($url) && isset($defaultPlatforms[$platform])): ?>
                            <div class="social-platform-item border border-gray-200 rounded-lg p-4 bg-gray-50" data-platform="<?php echo $platform; ?>">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3 space-x-reverse">
                                        <i class="<?php echo $defaultPlatforms[$platform]['icon']; ?> text-xl text-<?php echo $defaultPlatforms[$platform]['color']; ?>-600"></i>
                                        <div class="flex-1">
                                            <label class="block text-sm font-medium text-gray-700 mb-1"><?php echo $defaultPlatforms[$platform]['name']; ?></label>
                                            <input type="url" name="data[<?php echo $platform; ?>]" value="<?php echo htmlspecialchars($url); ?>" 
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" 
                                                   placeholder="<?php echo $defaultPlatforms[$platform]['placeholder']; ?>">
                                        </div>
                                    </div>
                                    <button type="button" class="remove-platform-btn text-red-600 hover:text-red-800 ml-3" title="حذف المنصة">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; ?>
                    
                    <!-- إضافة المنصات الافتراضية غير المضافة -->
                    <?php foreach ($defaultPlatforms as $platform => $info): ?>
                        <?php if (empty($data[$platform])): ?>
                            <div class="social-platform-item border border-gray-200 rounded-lg p-4 bg-gray-50 hidden" data-platform="<?php echo $platform; ?>">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3 space-x-reverse">
                                        <i class="<?php echo $info['icon']; ?> text-xl text-<?php echo $info['color']; ?>-600"></i>
                                        <div class="flex-1">
                                            <label class="block text-sm font-medium text-gray-700 mb-1"><?php echo $info['name']; ?></label>
                                            <input type="url" name="data[<?php echo $platform; ?>]" value="" 
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" 
                                                   placeholder="<?php echo $info['placeholder']; ?>">
                                        </div>
                                    </div>
                                    <button type="button" class="remove-platform-btn text-red-600 hover:text-red-800 ml-3" title="حذف المنصة">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
                
                <!-- قائمة المنصات المتاحة للإضافة -->
                <div id="available-platforms" class="hidden">
                    <label class="block text-sm font-medium text-gray-700 mb-2">اختر منصة للإضافة:</label>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                        <?php foreach ($defaultPlatforms as $platform => $info): ?>
                            <button type="button" class="add-platform-option border border-gray-300 rounded-lg p-3 text-center hover:bg-gray-50 transition-colors duration-200" 
                                    data-platform="<?php echo $platform; ?>" data-name="<?php echo $info['name']; ?>" 
                                    data-icon="<?php echo $info['icon']; ?>" data-color="<?php echo $info['color']; ?>" 
                                    data-placeholder="<?php echo $info['placeholder']; ?>">
                                <i class="<?php echo $info['icon']; ?> text-xl text-<?php echo $info['color']; ?>-600 mb-1"></i>
                                <div class="text-xs text-gray-700"><?php echo $info['name']; ?></div>
                            </button>
                        <?php endforeach; ?>
                    </div>
                    <div class="mt-3 flex space-x-2 space-x-reverse">
                        <button type="button" id="cancel-add-platform" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                            إلغاء
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- معاينة وسائل التواصل الاجتماعي -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">معاينة الروابط</label>
                <div id="social_preview" class="border border-gray-300 rounded-md p-4 bg-gray-50">
                    <div class="social-links-preview flex flex-wrap gap-2">
                        <?php foreach ($data as $platform => $url): ?>
                            <?php if (!empty($url) && isset($defaultPlatforms[$platform])): ?>
                                <a href="<?php echo htmlspecialchars($url); ?>" target="_blank" 
                                   class="inline-flex items-center px-3 py-2 border border-<?php echo $defaultPlatforms[$platform]['color']; ?>-300 rounded-md text-sm font-medium text-<?php echo $defaultPlatforms[$platform]['color']; ?>-700 bg-<?php echo $defaultPlatforms[$platform]['color']; ?>-50 hover:bg-<?php echo $defaultPlatforms[$platform]['color']; ?>-100 transition-colors duration-200">
                                    <i class="<?php echo $defaultPlatforms[$platform]['icon']; ?> mr-2"></i> 
                                    <?php echo $defaultPlatforms[$platform]['name']; ?>
                                </a>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            
            <div class="flex items-center">
                <input type="checkbox" id="is_active" name="is_active" <?php echo ($contactInfo['social_media']['is_active'] ?? 1) ? 'checked' : ''; ?> class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <label for="is_active" class="mr-2 block text-sm text-gray-900">فعال</label>
            </div>
            
            <div class="flex justify-end">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                    حفظ التغييرات
                </button>
            </div>
        </form>
        
        <?php elseif ($activeTab === 'company_info'): 
            $data = $contactInfo['company_info']['data_parsed'] ?? [];
        ?>
        <!-- معلومات الشركة -->
        <form method="POST" class="space-y-6">
            <input type="hidden" name="section_key" value="company_info">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">عنوان القسم</label>
                    <input type="text" id="title" name="title" value="<?php echo htmlspecialchars($contactInfo['company_info']['title'] ?? 'معلومات الشركة'); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                </div>
                
                <div>
                    <label for="content" class="block text-sm font-medium text-gray-700 mb-2">اسم الشركة</label>
                    <input type="text" id="content" name="content" value="<?php echo htmlspecialchars($contactInfo['company_info']['content'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label for="company_name" class="block text-sm font-medium text-gray-700 mb-2">اسم الشركة الكامل</label>
                    <input type="text" id="company_name" name="data[company_name]" value="<?php echo htmlspecialchars($data['company_name'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label for="commercial_register" class="block text-sm font-medium text-gray-700 mb-2">السجل التجاري</label>
                    <input type="text" id="commercial_register" name="data[commercial_register]" value="<?php echo htmlspecialchars($data['commercial_register'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label for="tax_number" class="block text-sm font-medium text-gray-700 mb-2">الرقم الضريبي</label>
                    <input type="text" id="tax_number" name="data[tax_number]" value="<?php echo htmlspecialchars($data['tax_number'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label for="established_year" class="block text-sm font-medium text-gray-700 mb-2">سنة التأسيس</label>
                    <input type="number" id="established_year" name="data[established_year]" value="<?php echo $data['established_year'] ?? ''; ?>" min="1900" max="<?php echo date('Y'); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label for="license_number" class="block text-sm font-medium text-gray-700 mb-2">رقم الترخيص</label>
                    <input type="text" id="license_number" name="data[license_number]" value="<?php echo htmlspecialchars($data['license_number'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
            
            <div class="flex items-center">
                <input type="checkbox" id="is_active" name="is_active" <?php echo ($contactInfo['company_info']['is_active'] ?? 1) ? 'checked' : ''; ?> class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <label for="is_active" class="mr-2 block text-sm text-gray-900">فعال</label>
            </div>
            
            <div class="flex justify-end">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                    حفظ التغييرات
                </button>
            </div>
        </form>
        
        <?php endif; ?>
    </div>
</div>

<script>
// Contact Management JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Auto-save functionality
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('change', function() {
                // Add visual feedback for unsaved changes
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.classList.add('bg-orange-500', 'hover:bg-orange-600');
                    submitBtn.classList.remove('bg-blue-600', 'hover:bg-blue-700');
                    submitBtn.textContent = 'حفظ التغييرات *';
                }
            });
        });
    });

    // WhatsApp number formatting
    const whatsappInput = document.getElementById('number');
    if (whatsappInput) {
        whatsappInput.addEventListener('input', function() {
            let value = this.value.replace(/\D/g, '');
            if (value.startsWith('966')) {
                this.value = '+' + value;
            } else if (value.startsWith('0')) {
                this.value = '+966' + value.substring(1);
            }
        });
    }

    // Coordinates validation
    const latInputs = document.querySelectorAll('input[name*="lat"]');
    const lngInputs = document.querySelectorAll('input[name*="lng"]');
    
    latInputs.forEach(input => {
        input.addEventListener('blur', function() {
            const value = parseFloat(this.value);
            if (value < -90 || value > 90) {
                this.setCustomValidity('خط العرض يجب أن يكون بين -90 و 90');
            } else {
                this.setCustomValidity('');
            }
        });
    });
    
    lngInputs.forEach(input => {
        input.addEventListener('blur', function() {
            const value = parseFloat(this.value);
            if (value < -180 || value > 180) {
                this.setCustomValidity('خط الطول يجب أن يكون بين -180 و 180');
            } else {
                this.setCustomValidity('');
            }
        });
    });

    // Email validation
    const emailInputs = document.querySelectorAll('input[type="email"]');
    emailInputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.value && !this.value.includes('@')) {
                this.setCustomValidity('يرجى إدخال بريد إلكتروني صحيح');
            } else {
                this.setCustomValidity('');
            }
        });
    });

    // URL validation for social media
    const urlInputs = document.querySelectorAll('input[type="url"]');
    urlInputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.value && !this.value.startsWith('http')) {
                this.value = 'https://' + this.value;
            }
        });
    });

    // Working hours validation
    const openTimeInputs = document.querySelectorAll('input[name*="_open"]');
    const closeTimeInputs = document.querySelectorAll('input[name*="_close"]');
    
    function validateWorkingHours() {
        openTimeInputs.forEach((openInput, index) => {
            const closeInput = closeTimeInputs[index];
            if (openInput && closeInput && openInput.value && closeInput.value) {
                if (openInput.value >= closeInput.value) {
                    closeInput.setCustomValidity('وقت الإغلاق يجب أن يكون بعد وقت الفتح');
                } else {
                    closeInput.setCustomValidity('');
                }
            }
        });
    }
    
    openTimeInputs.forEach(input => {
        input.addEventListener('change', validateWorkingHours);
    });
    
    closeTimeInputs.forEach(input => {
        input.addEventListener('change', validateWorkingHours);
    });

    // Working Hours Auto-Update Description
    initializeWorkingHoursAutoUpdate();

    function initializeWorkingHoursAutoUpdate() {
        // التحقق من وجود عناصر ساعات العمل
        const sundayThursdayOpen = document.getElementById('sunday_thursday_open');
        const sundayThursdayClose = document.getElementById('sunday_thursday_close');
        const fridayOpen = document.getElementById('friday_open');
        const fridayClose = document.getElementById('friday_close');
        const saturdaySelect = document.getElementById('saturday');
        const contentTextarea = document.getElementById('content');
        
        // التحقق من وجود جميع العناصر المطلوبة
        if (!sundayThursdayOpen || !sundayThursdayClose || !fridayOpen || !fridayClose || !saturdaySelect || !contentTextarea) {
            return; // إذا لم تكن في صفحة ساعات العمل
        }
        
        // إضافة مستمعي الأحداث لجميع الحقول
        [sundayThursdayOpen, sundayThursdayClose, fridayOpen, fridayClose, saturdaySelect].forEach(element => {
            element.addEventListener('change', updateWorkingHoursDescription);
        });
        
        // تحديث أولي عند تحميل الصفحة
        updateWorkingHoursDescription();
        
        function updateWorkingHoursDescription() {
            let description = '';
            
            // ساعات العمل للأحد - الخميس
            const sundayThursdayOpenTime = sundayThursdayOpen.value;
            const sundayThursdayCloseTime = sundayThursdayClose.value;
            
            if (sundayThursdayOpenTime && sundayThursdayCloseTime) {
                const openTime = formatTime(sundayThursdayOpenTime);
                const closeTime = formatTime(sundayThursdayCloseTime);
                description += `الأحد - الخميس: ${openTime} - ${closeTime}`;
            } else if (sundayThursdayOpenTime || sundayThursdayCloseTime) {
                description += 'الأحد - الخميس: يرجى تحديد أوقات الفتح والإغلاق';
            } else {
                description += 'الأحد - الخميس: مغلق';
            }
            
            // ساعات العمل للجمعة
            const fridayOpenTime = fridayOpen.value;
            const fridayCloseTime = fridayClose.value;
            
            if (fridayOpenTime && fridayCloseTime) {
                const openTime = formatTime(fridayOpenTime);
                const closeTime = formatTime(fridayCloseTime);
                description += `\nالجمعة: ${openTime} - ${closeTime}`;
            } else if (fridayOpenTime || fridayCloseTime) {
                description += '\nالجمعة: يرجى تحديد أوقات الفتح والإغلاق';
            } else {
                description += '\nالجمعة: مغلق';
            }
            
            // حالة يوم السبت
            const saturdayStatus = saturdaySelect.value;
            if (saturdayStatus === 'open') {
                description += '\nالسبت: مفتوح';
            } else {
                description += '\nالسبت: مغلق';
            }
            
            // تحديث محتوى textarea
            contentTextarea.value = description;
            
            // إضافة تأثير بصري لإظهار التحديث
            contentTextarea.style.backgroundColor = '#f0f9ff';
            setTimeout(() => {
                contentTextarea.style.backgroundColor = '';
            }, 500);
        }
        
        function formatTime(timeString) {
            if (!timeString) return '';
            
            const [hours, minutes] = timeString.split(':');
            const hour = parseInt(hours);
            const minute = minutes;
            
            if (hour === 0) {
                return `12:${minute} ص`;
            } else if (hour < 12) {
                return `${hour}:${minute} ص`;
            } else if (hour === 12) {
                return `12:${minute} م`;
            } else {
                return `${hour - 12}:${minute} م`;
            }
        }
    }

    // Social Media Dynamic Management
    const socialMediaForm = document.getElementById('social-media-form');
    if (socialMediaForm) {
        const addPlatformBtn = document.getElementById('add-platform-btn');
        const availablePlatforms = document.getElementById('available-platforms');
        const cancelAddBtn = document.getElementById('cancel-add-platform');
        const platformsContainer = document.getElementById('social-platforms-container');

        // Show available platforms
        addPlatformBtn.addEventListener('click', function() {
            // Hide platforms that are already added
            const addedPlatforms = Array.from(platformsContainer.querySelectorAll('.social-platform-item:not(.hidden)')).map(item => item.dataset.platform);
            const platformOptions = availablePlatforms.querySelectorAll('.add-platform-option');
            
            platformOptions.forEach(option => {
                if (addedPlatforms.includes(option.dataset.platform)) {
                    option.style.display = 'none';
                } else {
                    option.style.display = 'block';
                }
            });

            availablePlatforms.classList.remove('hidden');
            addPlatformBtn.style.display = 'none';
        });

        // Cancel adding platform
        cancelAddBtn.addEventListener('click', function() {
            availablePlatforms.classList.add('hidden');
            addPlatformBtn.style.display = 'block';
        });

        // Add platform
        availablePlatforms.addEventListener('click', function(e) {
            if (e.target.closest('.add-platform-option')) {
                const option = e.target.closest('.add-platform-option');
                const platform = option.dataset.platform;
                
                // Find the hidden platform item and show it
                const platformItem = platformsContainer.querySelector(`[data-platform="${platform}"]`);
                if (platformItem) {
                    platformItem.classList.remove('hidden');
                    
                    // Focus on the input field
                    const input = platformItem.querySelector('input[type="url"]');
                    if (input) {
                        input.focus();
                    }
                }

                // Hide the available platforms section
                availablePlatforms.classList.add('hidden');
                addPlatformBtn.style.display = 'block';
            }
        });

        // Remove platform
        platformsContainer.addEventListener('click', function(e) {
            if (e.target.closest('.remove-platform-btn')) {
                const platformItem = e.target.closest('.social-platform-item');
                const input = platformItem.querySelector('input[type="url"]');
                
                // Clear the input value and hide the platform
                if (input) {
                    input.value = '';
                }
                platformItem.classList.add('hidden');
                
                // Update preview
                updateSocialPreview();
            }
        });

        // Update preview when input changes
        platformsContainer.addEventListener('input', function(e) {
            if (e.target.type === 'url') {
                updateSocialPreview();
            }
        });

        // Auto-hide empty platforms on form submit
        socialMediaForm.addEventListener('submit', function(e) {
            const platformItems = platformsContainer.querySelectorAll('.social-platform-item');
            platformItems.forEach(item => {
                const input = item.querySelector('input[type="url"]');
                if (input && !input.value.trim()) {
                    item.classList.add('hidden');
                }
            });
        });

        // Function to update social media preview
        function updateSocialPreview() {
            const socialPreview = document.getElementById('social_preview');
            if (!socialPreview) return;

            const previewContainer = socialPreview.querySelector('.social-links-preview');
            if (!previewContainer) return;

            // Clear current preview
            previewContainer.innerHTML = '';

            // Get all visible platform items with values
            const visiblePlatforms = platformsContainer.querySelectorAll('.social-platform-item:not(.hidden)');
            
            visiblePlatforms.forEach(item => {
                const input = item.querySelector('input[type="url"]');
                const platform = item.dataset.platform;
                
                if (input && input.value.trim()) {
                    const icon = item.querySelector('i').className;
                    const colorClass = icon.includes('facebook') ? 'blue' : 
                                     icon.includes('twitter') ? 'blue' : 
                                     icon.includes('instagram') ? 'pink' : 
                                     icon.includes('linkedin') ? 'blue' : 
                                     icon.includes('youtube') ? 'red' : 
                                     icon.includes('snapchat') ? 'yellow' : 
                                     icon.includes('tiktok') ? 'gray' : 
                                     icon.includes('pinterest') ? 'red' : 
                                     icon.includes('telegram') ? 'blue' : 
                                     icon.includes('whatsapp') ? 'green' : 
                                     icon.includes('discord') ? 'indigo' : 
                                     icon.includes('reddit') ? 'orange' : 
                                     icon.includes('tumblr') ? 'indigo' : 
                                     icon.includes('twitch') ? 'purple' : 
                                     icon.includes('behance') ? 'blue' : 
                                     icon.includes('dribbble') ? 'pink' : 'gray';
                    
                    const platformName = item.querySelector('label').textContent;
                    
                    const linkElement = document.createElement('a');
                    linkElement.href = input.value;
                    linkElement.target = '_blank';
                    linkElement.className = `inline-flex items-center px-3 py-2 border border-${colorClass}-300 rounded-md text-sm font-medium text-${colorClass}-700 bg-${colorClass}-50 hover:bg-${colorClass}-100 transition-colors duration-200`;
                    linkElement.innerHTML = `<i class="${icon} mr-2"></i> ${platformName}`;
                    
                    previewContainer.appendChild(linkElement);
                }
            });

            // Show message if no platforms are added
            if (previewContainer.children.length === 0) {
                previewContainer.innerHTML = '<p class="text-gray-500 text-sm">لم يتم إضافة أي منصات تواصل اجتماعي بعد</p>';
            }
        }

        // Initial preview update
        updateSocialPreview();
    }
});
</script>

<?php endLayout(); ?>