<?php
/**
 * نظام الصلاحيات المتقدم
 * Advanced Permissions System
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/functions.php';

class PermissionManager {
    private $database;
    private $cache = [];
    private $cacheExpiry = 3600; // ساعة واحدة
    
    public function __construct() {
        global $database;
        $this->database = $database;
    }
    
    /**
     * التحقق من صلاحية المستخدم
     */
    public function hasPermission($userId, $permission) {
        // التحقق من الكاش أولاً
        $cacheKey = "user_permissions_{$userId}";
        if (isset($this->cache[$cacheKey]) && 
            time() - $this->cache[$cacheKey]['timestamp'] < $this->cacheExpiry) {
            $permissions = $this->cache[$cacheKey]['data'];
        } else {
            $permissions = $this->getUserPermissions($userId);
            $this->cache[$cacheKey] = [
                'data' => $permissions,
                'timestamp' => time()
            ];
        }
        
        return in_array($permission, $permissions);
    }
    
    /**
     * الحصول على جميع صلاحيات المستخدم
     */
    public function getUserPermissions($userId) {
        $permissions = [];
        
        // الحصول على الصلاحيات من الأدوار
        $rolePermissions = $this->database->fetchAll("
            SELECT DISTINCT p.name
            FROM permissions p
            JOIN role_permissions rp ON p.id = rp.permission_id
            JOIN user_roles ur ON rp.role_id = ur.role_id
            WHERE ur.user_id = :user_id 
            AND ur.is_active = 1
            AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
            AND p.is_active = 1
        ", ['user_id' => $userId]);
        
        foreach ($rolePermissions as $perm) {
            $permissions[] = $perm['name'];
        }
        
        // الحصول على الصلاحيات المباشرة
        $directPermissions = $this->database->fetchAll("
            SELECT DISTINCT p.name
            FROM permissions p
            JOIN user_permissions up ON p.id = up.permission_id
            WHERE up.user_id = :user_id 
            AND up.is_active = 1
            AND (up.expires_at IS NULL OR up.expires_at > NOW())
            AND p.is_active = 1
        ", ['user_id' => $userId]);
        
        foreach ($directPermissions as $perm) {
            $permissions[] = $perm['name'];
        }
        
        return array_unique($permissions);
    }
    
    /**
     * الحصول على أدوار المستخدم
     */
    public function getUserRoles($userId) {
        return $this->database->fetchAll("
            SELECT r.name, r.display_name, r.description
            FROM roles r
            JOIN user_roles ur ON r.id = ur.role_id
            WHERE ur.user_id = :user_id 
            AND ur.is_active = 1
            AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
            AND r.is_active = 1
        ", ['user_id' => $userId]);
    }
    
    /**
     * إضافة دور للمستخدم
     */
    public function assignRole($userId, $roleName, $assignedBy = null, $expiresAt = null) {
        // الحصول على معرف الدور
        $role = $this->database->fetch(
            "SELECT id FROM roles WHERE name = :name AND is_active = 1",
            ['name' => $roleName]
        );
        
        if (!$role) {
            return ['success' => false, 'error' => 'الدور غير موجود'];
        }
        
        // التحقق من عدم وجود الدور مسبقاً
        $existing = $this->database->fetch("
            SELECT id FROM user_roles 
            WHERE user_id = :user_id AND role_id = :role_id AND is_active = 1
        ", [
            'user_id' => $userId,
            'role_id' => $role['id']
        ]);
        
        if ($existing) {
            return ['success' => false, 'error' => 'المستخدم لديه هذا الدور بالفعل'];
        }
        
        // إضافة الدور
        $result = $this->database->insert('user_roles', [
            'user_id' => $userId,
            'role_id' => $role['id'],
            'assigned_by' => $assignedBy,
            'expires_at' => $expiresAt
        ]);
        
        if ($result) {
            // تسجيل العملية
            $this->logPermissionChange($assignedBy, $userId, 'role_assigned', $role['id']);
            
            // مسح الكاش
            $this->clearUserCache($userId);
            
            return ['success' => true];
        }
        
        return ['success' => false, 'error' => 'فشل في إضافة الدور'];
    }
    
    /**
     * إزالة دور من المستخدم
     */
    public function removeRole($userId, $roleName, $removedBy = null) {
        // الحصول على معرف الدور
        $role = $this->database->fetch(
            "SELECT id FROM roles WHERE name = :name",
            ['name' => $roleName]
        );
        
        if (!$role) {
            return ['success' => false, 'error' => 'الدور غير موجود'];
        }
        
        // إزالة الدور
        $result = $this->database->delete(
            'user_roles',
            'user_id = :user_id AND role_id = :role_id',
            [
                'user_id' => $userId,
                'role_id' => $role['id']
            ]
        );
        
        if ($result) {
            // تسجيل العملية
            $this->logPermissionChange($removedBy, $userId, 'role_removed', $role['id']);
            
            // مسح الكاش
            $this->clearUserCache($userId);
            
            return ['success' => true];
        }
        
        return ['success' => false, 'error' => 'فشل في إزالة الدور'];
    }
    
    /**
     * منح صلاحية مباشرة للمستخدم
     */
    public function grantPermission($userId, $permissionName, $grantedBy = null, $expiresAt = null) {
        // الحصول على معرف الصلاحية
        $permission = $this->database->fetch(
            "SELECT id FROM permissions WHERE name = :name AND is_active = 1",
            ['name' => $permissionName]
        );
        
        if (!$permission) {
            return ['success' => false, 'error' => 'الصلاحية غير موجودة'];
        }
        
        // التحقق من عدم وجود الصلاحية مسبقاً
        $existing = $this->database->fetch("
            SELECT id FROM user_permissions 
            WHERE user_id = :user_id AND permission_id = :permission_id
        ", [
            'user_id' => $userId,
            'permission_id' => $permission['id']
        ]);
        
        if ($existing) {
            return ['success' => false, 'error' => 'المستخدم لديه هذه الصلاحية بالفعل'];
        }
        
        // منح الصلاحية
        $result = $this->database->insert('user_permissions', [
            'user_id' => $userId,
            'permission_id' => $permission['id'],
            'assigned_by' => $grantedBy,
            'expires_at' => $expiresAt
        ]);
        
        if ($result) {
            // تسجيل العملية
            $this->logPermissionChange($grantedBy, $userId, 'permission_granted', null, $permission['id']);
            
            // مسح الكاش
            $this->clearUserCache($userId);
            
            return ['success' => true];
        }
        
        return ['success' => false, 'error' => 'فشل في منح الصلاحية'];
    }
    
    /**
     * إلغاء صلاحية مباشرة من المستخدم
     */
    public function revokePermission($userId, $permissionName, $revokedBy = null) {
        // الحصول على معرف الصلاحية
        $permission = $this->database->fetch(
            "SELECT id FROM permissions WHERE name = :name",
            ['name' => $permissionName]
        );
        
        if (!$permission) {
            return ['success' => false, 'error' => 'الصلاحية غير موجودة'];
        }
        
        // إلغاء الصلاحية
        $result = $this->database->delete(
            'user_permissions',
            'user_id = :user_id AND permission_id = :permission_id',
            [
                'user_id' => $userId,
                'permission_id' => $permission['id']
            ]
        );
        
        if ($result) {
            // تسجيل العملية
            $this->logPermissionChange($revokedBy, $userId, 'permission_revoked', null, $permission['id']);
            
            // مسح الكاش
            $this->clearUserCache($userId);
            
            return ['success' => true];
        }
        
        return ['success' => false, 'error' => 'فشل في إلغاء الصلاحية'];
    }
    
    /**
     * الحصول على جميع الأدوار
     */
    public function getAllRoles() {
        return $this->database->fetchAll("
            SELECT * FROM roles WHERE is_active = 1 ORDER BY name
        ");
    }
    
    /**
     * الحصول على جميع الصلاحيات
     */
    public function getAllPermissions() {
        return $this->database->fetchAll("
            SELECT * FROM permissions WHERE is_active = 1 ORDER BY category, name
        ");
    }
    
    /**
     * الحصول على صلاحيات الدور
     */
    public function getRolePermissions($roleId) {
        return $this->database->fetchAll("
            SELECT p.*
            FROM permissions p
            JOIN role_permissions rp ON p.id = rp.permission_id
            WHERE rp.role_id = :role_id AND p.is_active = 1
            ORDER BY p.category, p.name
        ", ['role_id' => $roleId]);
    }
    
    /**
     * إنشاء دور جديد
     */
    public function createRole($name, $displayName, $description = null) {
        // التحقق من عدم وجود الدور مسبقاً
        $existing = $this->database->fetch(
            "SELECT id FROM roles WHERE name = :name",
            ['name' => $name]
        );
        
        if ($existing) {
            return false; // الدور موجود بالفعل
        }
        
        $result = $this->database->insert('roles', [
            'name' => $name,
            'display_name' => $displayName,
            'description' => $description,
            'is_active' => 1,
            'created_at' => date('Y-m-d H:i:s')
        ]);
        
        return $result ? $this->database->lastInsertId() : false;
    }
    
    /**
     * إضافة صلاحية لدور
     */
    public function assignPermissionToRole($roleId, $permissionId) {
        // التحقق من عدم وجود الصلاحية مسبقاً
        $existing = $this->database->fetch(
            "SELECT id FROM role_permissions WHERE role_id = :role_id AND permission_id = :permission_id",
            ['role_id' => $roleId, 'permission_id' => $permissionId]
        );
        
        if ($existing) {
            return ['success' => false, 'error' => 'الصلاحية موجودة بالفعل للدور'];
        }
        
        $result = $this->database->insert('role_permissions', [
            'role_id' => $roleId,
            'permission_id' => $permissionId
        ]);
        
        if ($result) {
            // مسح الكاش لجميع المستخدمين الذين لديهم هذا الدور
            $this->clearRoleUsersCache($roleId);
            return ['success' => true];
        }
        
        return ['success' => false, 'error' => 'فشل في إضافة الصلاحية للدور'];
    }
    
    /**
     * تحديث صلاحيات الدور
     */
    public function updateRolePermissions($roleId, $permissionIds, $updatedBy = null) {
        try {
            // بدء المعاملة
            $this->database->query('START TRANSACTION');
            
            // حذف الصلاحيات الحالية
            $this->database->delete(
                'role_permissions',
                'role_id = :role_id',
                ['role_id' => $roleId]
            );
            
            // إضافة الصلاحيات الجديدة
            foreach ($permissionIds as $permissionId) {
                $this->database->insert('role_permissions', [
                    'role_id' => $roleId,
                    'permission_id' => $permissionId
                ]);
            }
            
            // تأكيد المعاملة
            $this->database->query('COMMIT');
            
            // مسح الكاش لجميع المستخدمين الذين لديهم هذا الدور
            $this->clearRoleUsersCache($roleId);
            
            return ['success' => true];
            
        } catch (Exception $e) {
            // إلغاء المعاملة
            $this->database->query('ROLLBACK');
            return ['success' => false, 'error' => 'فشل في تحديث صلاحيات الدور: ' . $e->getMessage()];
        }
    }
    
    /**
     * تسجيل تغييرات الصلاحيات
     */
    private function logPermissionChange($userId, $targetUserId, $action, $roleId = null, $permissionId = null) {
        $this->database->insert('permission_logs', [
            'user_id' => $userId,
            'target_user_id' => $targetUserId,
            'action' => $action,
            'role_id' => $roleId,
            'permission_id' => $permissionId,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
    }
    
    /**
     * مسح كاش المستخدم
     */
    private function clearUserCache($userId) {
        unset($this->cache["user_permissions_{$userId}"]);
    }
    
    /**
     * مسح كاش جميع المستخدمين الذين لديهم دور معين
     */
    private function clearRoleUsersCache($roleId) {
        $users = $this->database->fetchAll(
            "SELECT user_id FROM user_roles WHERE role_id = :role_id",
            ['role_id' => $roleId]
        );
        
        foreach ($users as $user) {
            $this->clearUserCache($user['user_id']);
        }
    }
    
    /**
     * التحقق من صلاحية متعددة (AND)
     */
    public function hasAllPermissions($userId, $permissions) {
        foreach ($permissions as $permission) {
            if (!$this->hasPermission($userId, $permission)) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * التحقق من صلاحية متعددة (OR)
     */
    public function hasAnyPermission($userId, $permissions) {
        foreach ($permissions as $permission) {
            if ($this->hasPermission($userId, $permission)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * التحقق من الدور
     */
    public function hasRole($userId, $roleName) {
        $roles = $this->getUserRoles($userId);
        foreach ($roles as $role) {
            if ($role['name'] === $roleName) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * التحقق من أي دور من قائمة
     */
    public function hasAnyRole($userId, $roleNames) {
        foreach ($roleNames as $roleName) {
            if ($this->hasRole($userId, $roleName)) {
                return true;
            }
        }
        return false;
    }
}

// إنشاء مثيل عام
$permissionManager = new PermissionManager();

/**
 * دوال مساعدة للاستخدام السريع
 */

/**
 * التحقق من صلاحية المستخدم الحالي
 */
function can($permission) {
    global $permissionManager;
    $userId = getCurrentUserId();
    return $userId ? $permissionManager->hasPermission($userId, $permission) : false;
}

/**
 * التحقق من دور المستخدم الحالي
 */
function hasRole($roleName) {
    global $permissionManager;
    $userId = getCurrentUserId();
    return $userId ? $permissionManager->hasRole($userId, $roleName) : false;
}

/**
 * التحقق من صلاحيات متعددة (AND)
 */
function canAll($permissions) {
    global $permissionManager;
    $userId = getCurrentUserId();
    return $userId ? $permissionManager->hasAllPermissions($userId, $permissions) : false;
}

/**
 * التحقق من صلاحيات متعددة (OR)
 */
function canAny($permissions) {
    global $permissionManager;
    $userId = getCurrentUserId();
    return $userId ? $permissionManager->hasAnyPermission($userId, $permissions) : false;
}

/**
 * التحقق من الصلاحية أو إظهار خطأ 403
 */
function authorize($permission, $message = 'غير مصرح لك بالوصول إلى هذه الصفحة') {
    if (!can($permission)) {
        http_response_code(403);
        die($message);
    }
}

/**
 * التحقق من الدور أو إظهار خطأ 403
 */
function authorizeRole($roleName, $message = 'غير مصرح لك بالوصول إلى هذه الصفحة') {
    if (!hasRole($roleName)) {
        http_response_code(403);
        die($message);
    }
}

// تم نقل دوال requirePermission و requireRole إلى ملف functions.php لتجنب التضارب

?>