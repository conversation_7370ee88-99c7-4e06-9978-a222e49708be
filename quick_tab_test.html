<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سريع للتابات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .product-tab-pane {
            display: none !important;
        }
        
        .product-tab-pane.active {
            display: block !important;
        }
        
        .product-tab-button.active {
            color: #059669 !important;
            border-color: #059669 !important;
        }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-2xl font-bold mb-6">اختبار سريع للتابات</h1>
        
        <!-- التابات -->
        <div class="border-b border-gray-200 mb-8">
            <nav class="flex space-x-8 space-x-reverse">
                <button class="product-tab-button active px-4 py-2 text-sm font-medium border-b-2 border-primary text-primary" data-tab="description">
                    الوصف
                </button>
                <button class="product-tab-button px-4 py-2 text-sm font-medium border-b-2 border-transparent text-gray-500" data-tab="specifications">
                    المواصفات
                </button>
                <button class="product-tab-button px-4 py-2 text-sm font-medium border-b-2 border-transparent text-gray-500" data-tab="reviews">
                    التقييمات
                </button>
            </nav>
        </div>
        
        <!-- محتوى التابات -->
        <div>
            <div id="description-tab" class="product-tab-pane active">
                <div class="bg-blue-50 p-4 rounded">
                    <h3 class="font-bold">تاب الوصف</h3>
                    <p>هذا هو محتوى تاب الوصف - يجب أن يظهر بشكل افتراضي</p>
                </div>
            </div>
            
            <div id="specifications-tab" class="product-tab-pane">
                <div class="bg-green-50 p-4 rounded">
                    <h3 class="font-bold">تاب المواصفات</h3>
                    <p>هذا هو محتوى تاب المواصفات - يجب أن يظهر عند النقر على زر المواصفات</p>
                </div>
            </div>
            
            <div id="reviews-tab" class="product-tab-pane">
                <div class="bg-yellow-50 p-4 rounded">
                    <h3 class="font-bold">تاب التقييمات</h3>
                    <p>هذا هو محتوى تاب التقييمات - يجب أن يظهر عند النقر على زر التقييمات</p>
                </div>
            </div>
        </div>
        
        <div id="status" class="mt-4 p-2 bg-gray-100 rounded text-sm"></div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const tabButtons = document.querySelectorAll('.product-tab-button');
            const tabPanes = document.querySelectorAll('.product-tab-pane');
            const status = document.getElementById('status');
            
            status.innerHTML = `تم العثور على ${tabButtons.length} أزرار و ${tabPanes.length} ألواح`;
            
            tabButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    const tabId = this.dataset.tab;
                    status.innerHTML += `<br>تم النقر على: ${tabId}`;
                    
                    // إخفاء جميع التابات
                    tabButtons.forEach(btn => {
                        btn.classList.remove('active', 'border-primary', 'text-primary');
                        btn.classList.add('border-transparent', 'text-gray-500');
                    });
                    
                    tabPanes.forEach(pane => {
                        pane.classList.remove('active');
                    });
                    
                    // إظهار التاب المحدد
                    this.classList.add('active', 'border-primary', 'text-primary');
                    this.classList.remove('border-transparent', 'text-gray-500');
                    
                    const targetPane = document.getElementById(tabId + '-tab');
                    if (targetPane) {
                        targetPane.classList.add('active');
                        status.innerHTML += ` - تم التبديل بنجاح`;
                    } else {
                        status.innerHTML += ` - خطأ: لم يتم العثور على ${tabId}-tab`;
                    }
                });
            });
        });
    </script>
</body>
</html>