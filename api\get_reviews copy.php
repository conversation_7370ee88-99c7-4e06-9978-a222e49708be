<?php
// تعطيل عرض الأخطاء في HTML
ini_set('display_errors', 0);
error_reporting(0);

// تعيين headers قبل أي output
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// بدء output buffering لمنع أي output غير مرغوب فيه
ob_start();

try {
    // تضمين الملفات المطلوبة
    $config_path = __DIR__ . '/../config/config.php';
    $functions_path = __DIR__ . '/../includes/functions.php';
    $pagination_path = __DIR__ . '/../admin/includes/inc_Pagination.php';
    
    if (!file_exists($config_path)) {
        throw new Exception('ملف الإعدادات غير موجود');
    }
    
    if (!file_exists($functions_path)) {
        throw new Exception('ملف الدوال غير موجود');
    }
    
    if (!file_exists($pagination_path)) {
        throw new Exception('ملف الترقيم غير موجود');
    }
    
    require_once $config_path;
    require_once $functions_path;
    require_once $pagination_path;
    
    // التحقق من معاملات الطلب
    $product_id = isset($_GET['product_id']) ? (int)$_GET['product_id'] : 0;
    $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
    $reviews_per_page = 5;

    if ($product_id <= 0) {
        throw new Exception('معرف المنتج غير صحيح');
    }
    
    // التحقق من وجود الدوال المطلوبة
    if (!function_exists('getProductReviewsCount')) {
        throw new Exception('دالة getProductReviewsCount غير موجودة');
    }
    
    if (!function_exists('getProductReviews')) {
        throw new Exception('دالة getProductReviews غير موجودة');
    }
    
    if (!function_exists('calculatePagination')) {
        throw new Exception('دالة calculatePagination غير موجودة');
    }

    // حساب معلومات ترقيم الصفحات
    $total_reviews = getProductReviewsCount($product_id);
    $pagination = calculatePagination($total_reviews, $reviews_per_page, $page);

    // جلب المراجعات للصفحة المطلوبة
    $reviews = getProductReviews($product_id, $pagination['recordsPerPage'], $pagination['offset']);

    // تنسيق البيانات للإرسال
    $formatted_reviews = [];
    if (!empty($reviews) && is_array($reviews)) {
        foreach ($reviews as $review) {
            $formatted_reviews[] = [
                'id' => isset($review['id']) ? $review['id'] : 0,
                'name' => isset($review['name']) ? $review['name'] : 'غير محدد',
                'rating' => isset($review['rating']) ? (int)$review['rating'] : 0,
                'title' => isset($review['title']) ? $review['title'] : '',
                'comment' => isset($review['comment']) ? $review['comment'] : '',
                'created_at' => isset($review['created_at']) ? $review['created_at'] : '',
                'is_approved' => isset($review['is_approved']) ? (bool)$review['is_approved'] : false
            ];
        }
    }

    // تنظيف output buffer
    ob_clean();
    
    // إرسال الاستجابة
    echo json_encode([
        'success' => true,
        'reviews' => $formatted_reviews,
        'pagination' => [
            'currentPage' => $pagination['currentPage'],
            'totalPages' => $pagination['totalPages'],
            'totalRecords' => $pagination['totalRecords'],
            'recordsPerPage' => $pagination['recordsPerPage'],
            'hasNextPage' => $pagination['hasNextPage'],
            'hasPrevPage' => $pagination['hasPrevPage']
        ],
        'message' => 'تم جلب المراجعات بنجاح'
    ], JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    // تنظيف output buffer في حالة الخطأ
    ob_clean();
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'reviews' => [],
        'pagination' => null
    ], JSON_UNESCAPED_UNICODE);
} catch (Error $e) {
    // تنظيف output buffer في حالة الخطأ الفادح
    ob_clean();
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في النظام',
        'reviews' => [],
        'pagination' => null
    ], JSON_UNESCAPED_UNICODE);
}

// إنهاء output buffering
ob_end_flush();
?>