<?php
// Include required files
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../includes/functions.php';

// Get current page for active menu highlighting
$currentPage = $currentPage ?? basename($_SERVER['PHP_SELF'], '.php');

// Menu items
$menuItems = [
    [
        'id' => 'dashboard',
        'title' => 'لوحة التحكم',
        'url' => '/admin/',
        'icon' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path></svg>'
    ],
    [
        'id' => 'products',
        'title' => 'المنتجات',
        'url' => '/admin/products.php',
        'icon' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path></svg>',
        'submenu' => [
            ['title' => 'جميع المنتجات', 'url' => '/admin/products.php'],
            ['title' => 'إضافة منتج', 'url' => '/admin/products.php?action=add'],
            ['title' => 'الفئات', 'url' => '/admin/categories.php'],
        ]
    ],
    [
        'id' => 'users',
        'title' => 'المستخدمين',
        'url' => '/admin/users.php',
        'icon' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path></svg>',
        'submenu' => [
            ['title' => 'جميع المستخدمين', 'url' => '/admin/users.php'],
            ['title' => 'إضافة مستخدم', 'url' => '/admin/users.php?action=add'],
            ['title' => 'إدارة أدوار المستخدمين', 'url' => '/admin/manage_user_roles.php'],
            ['title' => 'الأدوار والصلاحيات', 'url' => '/admin/roles.php'],
            ['title' => 'إدارة الصلاحيات', 'url' => '/admin/permissions.php'],
            ['title' => 'تهيئة الصلاحيات', 'url' => '/admin/init_permissions.php'],
        ]
    ],
    [
        'id' => 'messages',
        'title' => 'الرسائل',
        'url' => '/admin/messages.php',
        'icon' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path></svg>'
    ],
    [
        'id' => 'contact_messages',
        'title' => 'تواصل معنا',
        'url' => '/admin/contact_messages.php',
        'icon' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path></svg>'
    ],

    [
        'id' => 'home',
        'title' => 'الصفحة الرئيسية',
        'url' => '/admin/home.php',
        'icon' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path></svg>'
    ],
    [
        'id' => 'about',
        'title' => 'من نحن',
        'url' => '/admin/about.php',
        'icon' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>'
    ],
    [
        'id' => 'content',
        'title' => 'المحتوى الإضافي',
        'url' => '#',
        'icon' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg>',
        'submenu' => [
            ['title' => 'الأسئلة الشائعة', 'url' => '/admin/faqs.php'],
            ['title' => 'الموزعين', 'url' => '/admin/distributors.php'],
            ['title' => 'التقييمات', 'url' => '/admin/reviews.php'],
            ['title' => 'خدمة ما بعد البيع', 'url' => '/admin/after-sales.php'],
            ['title' => 'معلومات التواصل', 'url' => '/admin/contact.php'],
            // ['title' => 'من نحن', 'url' => '/admin/content.php?page=about'],
        ]
    ],
    [
        'id' => 'legal',
        'title' => 'الصفحات القانونية',
        'url' => '#',
        'icon' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path></svg>',
        'submenu' => [
            ['title' => 'سياسة الخصوصية', 'url' => '/admin/legal_privacy.php'],
            ['title' => 'الشروط والأحكام', 'url' => '/admin/legal_terms.php'],
            ['title' => 'خريطة الموقع', 'url' => '/admin/legal_sitemap.php'],
        ]
    ],
    [
        'id' => 'media',
        'title' => 'الوسائط',
        'url' => '/admin/media.php',
        'icon' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg>'
    ],
    [
        'id' => 'settings',
        'title' => 'الإعدادات',
        'url' => '/admin/settings.php',
        'icon' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path></svg>',
        'submenu' => [
            ['title' => 'الإعدادات العامة', 'url' => '/admin/settings.php'],
            ['title' => 'إعدادات البريد', 'url' => '/admin/settings.php?tab=email'],
            ['title' => 'إعدادات الأمان', 'url' => '/admin/settings.php?tab=security'],
            ['title' => 'النسخ الاحتياطي', 'url' => '/admin/backup.php'],
        ]
    ],
    [
        'id' => 'reports',
        'title' => 'التقارير',
        'url' => '/admin/reports.php',
        'icon' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>'
    ]
];

// Get unread messages count
global $database;
try {
    $unreadMessages = $database->fetch("SELECT COUNT(*) as count FROM contact_messages WHERE status = 'unread'")['count'];
} catch (Exception $e) {
    $unreadMessages = 0;
}

// Get unread contact messages count
try {
    $unreadContactMessages = $database->fetch("SELECT COUNT(*) as count FROM contact_messages WHERE status = 'unread'")['count'];
} catch (Exception $e) {
    $unreadContactMessages = 0;
}

// Get pending reviews count
try {
    $pendingReviews = $database->fetch("SELECT COUNT(*) as count FROM reviews WHERE status = 'pending'")['count'];
} catch (Exception $e) {
    $pendingReviews = 0;
}
?>

<div class="bg-gray-900 text-white w-64 min-h-screen flex flex-col fixed top-0 right-0 z-50 overflow-y-auto">
    <!-- Logo -->
    <div class="flex items-center justify-center h-16 bg-gray-800">
        <div class="flex items-center">
            <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center ml-3">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
            </div>
            <span class="text-xl font-bold"><?php echo getSetting('site_name'); ?></span>
        </div>
    </div>
    
    <!-- Navigation -->
    <nav class="flex-1 px-4 py-6 space-y-2">
        <?php foreach ($menuItems as $item): ?>
            <?php 
            // Special handling for reviews page to be considered part of content section
            $isReviewsPage = $currentPage === 'reviews';
            $isActive = $currentPage === $item['id'] || 
                       (isset($item['submenu']) && in_array($currentPage, array_column($item['submenu'], 'id'))) ||
                       ($item['id'] === 'content' && $isReviewsPage);
            $hasSubmenu = isset($item['submenu']) && !empty($item['submenu']);
            ?>
            
            <div class="menu-item">
                <?php if ($hasSubmenu): ?>
                    <!-- Menu item with submenu -->
                    <button class="menu-toggle w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 <?php echo $isActive ? 'bg-blue-600 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white'; ?>" 
                            data-target="submenu-<?php echo $item['id']; ?>">
                        <div class="flex items-center">
                            <?php echo $item['icon']; ?>
                            <span class="mr-3"><?php echo $item['title']; ?></span>
                            
                            <!-- Badges -->
                            <?php if ($item['id'] === 'messages' && $unreadMessages > 0): ?>
                                <span class="bg-red-500 text-white text-xs rounded-full px-2 py-1 mr-2"><?php echo $unreadMessages; ?></span>
                            <?php elseif ($item['id'] === 'content' && $pendingReviews > 0): ?>
                                <span class="bg-yellow-500 text-white text-xs rounded-full px-2 py-1 mr-2"><?php echo $pendingReviews; ?></span>
                            <?php endif; ?>
                        </div>
                        
                        <svg class="w-4 h-4 transition-transform duration-200 <?php echo $isActive ? 'transform rotate-180' : ''; ?>" 
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    
                    <!-- Submenu -->
                    <div id="submenu-<?php echo $item['id']; ?>" class="submenu mt-2 space-y-1 <?php echo $isActive ? '' : 'hidden'; ?>">
                        <?php foreach ($item['submenu'] as $subitem): ?>
                            <?php 
                            $isSubitemActive = ($subitem['title'] === 'التقييمات' && $currentPage === 'reviews') ||
                                             (strpos($subitem['url'], $currentPage . '.php') !== false);
                            ?>
                            <a href="<?php echo $subitem['url']; ?>" 
                               class="flex items-center justify-between px-6 py-2 text-sm <?php echo $isSubitemActive ? 'text-blue-400 bg-gray-700' : 'text-gray-400'; ?> hover:text-white hover:bg-gray-700 rounded-lg transition-colors duration-200">
                                <span><?php echo $subitem['title']; ?></span>
                                <?php if ($subitem['title'] === 'التقييمات' && $pendingReviews > 0): ?>
                                    <span class="bg-yellow-500 text-white text-xs rounded-full px-2 py-1"><?php echo $pendingReviews; ?></span>
                                <?php endif; ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                    
                <?php else: ?>
                    <!-- Simple menu item -->
                    <a href="<?php echo $item['url']; ?>" 
                       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 <?php echo $isActive ? 'bg-blue-600 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white'; ?>">
                        <?php echo $item['icon']; ?>
                        <span class="mr-3"><?php echo $item['title']; ?></span>
                        
                        <!-- Badges -->
                        <?php if ($item['id'] === 'messages' && $unreadMessages > 0): ?>
                            <span class="bg-red-500 text-white text-xs rounded-full px-2 py-1 mr-auto"><?php echo $unreadMessages; ?></span>
                        <?php elseif ($item['id'] === 'contact_messages' && $unreadContactMessages > 0): ?>
                            <span class="bg-red-500 text-white text-xs rounded-full px-2 py-1 mr-auto"><?php echo $unreadContactMessages; ?></span>
                        <?php elseif ($item['id'] === 'content' && $pendingReviews > 0): ?>
                            <span class="bg-yellow-500 text-white text-xs rounded-full px-2 py-1 mr-auto"><?php echo $pendingReviews; ?></span>
                        <?php endif; ?>
                    </a>
                <?php endif; ?>
            </div>
        <?php endforeach; ?>
    </nav>
    
    <!-- User Info -->
    <div class="px-4 py-4 border-t border-gray-700">
        <div class="flex items-center">
            <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                <span class="text-sm font-medium text-white">
                    <?php echo strtoupper(substr($_SESSION['user_name'] ?? 'A', 0, 1)); ?>
                </span>
            </div>
            <div class="mr-3 flex-1">
                <p class="text-sm font-medium text-white"><?php echo htmlspecialchars($_SESSION['user_name'] ?? 'المدير'); ?></p>
                <p class="text-xs text-gray-400"><?php echo htmlspecialchars($_SESSION['user_email'] ?? ''); ?></p>
            </div>
        </div>
        
        <div class="mt-3 flex space-x-2 space-x-reverse">
            <a href="/admin/profile.php" 
               class="flex-1 bg-gray-700 hover:bg-gray-600 text-white text-xs font-medium py-2 px-3 rounded-lg text-center transition-colors duration-200">
                الملف الشخصي
            </a>
            <a href="/admin/logout.php" 
               class="flex-1 bg-red-600 hover:bg-red-700 text-white text-xs font-medium py-2 px-3 rounded-lg text-center transition-colors duration-200">
                تسجيل الخروج
            </a>
        </div>
    </div>
</div>

<script>
// Sidebar menu toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    const menuToggles = document.querySelectorAll('.menu-toggle');
    
    menuToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const submenu = document.getElementById(targetId);
            const arrow = this.querySelector('svg');
            
            if (submenu) {
                submenu.classList.toggle('hidden');
                arrow.classList.toggle('rotate-180');
            }
        });
    });
    
    // Auto-expand active menu
    const activeMenus = document.querySelectorAll('.menu-item');
    activeMenus.forEach(menu => {
        const toggle = menu.querySelector('.menu-toggle');
        if (toggle && toggle.classList.contains('bg-blue-600')) {
            const targetId = toggle.getAttribute('data-target');
            const submenu = document.getElementById(targetId);
            const arrow = toggle.querySelector('svg');
            
            if (submenu) {
                submenu.classList.remove('hidden');
                arrow.classList.add('rotate-180');
            }
        }
    });
});
</script>

<style>
.menu-toggle {
    transition: all 0.2s ease;
}

.submenu {
    transition: all 0.3s ease;
}

.submenu.hidden {
    max-height: 0;
    overflow: hidden;
}

.submenu:not(.hidden) {
    max-height: 500px;
}

/* Custom scrollbar for sidebar */
.sidebar::-webkit-scrollbar {
    width: 4px;
}

.sidebar::-webkit-scrollbar-track {
    background: #374151;
}

.sidebar::-webkit-scrollbar-thumb {
    background: #6b7280;
    border-radius: 2px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}
</style>