</main>
    
    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                
                <!-- Company Info -->
                <div class="col-span-1 lg:col-span-2">
                    <div class="flex items-center mb-4">
                        <?php 
                        $siteLogo = getSetting('site_logo', '/assets/images/logo.png');
                        $siteName = getSetting('site_name', 'Green Line');
                        ?>
                        <?php if (!empty($siteLogo) && $siteLogo !== '/assets/images/logo.png'): ?>
                            <img src="<?php echo SITE_URL . '/' . $siteLogo; ?>" alt="<?php echo $siteName; ?>" class="h-12 w-auto ml-3">
                        <?php endif; ?>
                        <h3 class="text-xl font-bold"><?php echo $siteName; ?></h3>
                    </div>
                    <p class="text-gray-300 mb-4 leading-relaxed">
                        <?php echo getSetting('site_description', 'شركة رائدة في مجال المكيفات وأنظمة التبريد، نقدم أفضل الحلول والخدمات لعملائنا الكرام.'); ?>
                    </p>
                    
                    <!-- Social Media Links - Dynamic -->
                    <?php
                    // جلب بيانات وسائل التواصل الاجتماعي الديناميكية
                    $social_media_data = [];
                    try {
                        $database = new Database();
                        $social_info = $database->fetch("SELECT data FROM contact_info WHERE section_key = 'social_media' AND is_active = 1");
                        if ($social_info && !empty($social_info['data'])) {
                            $social_media_data = json_decode($social_info['data'], true) ?: [];
                        }
                    } catch (Exception $e) {
                        // في حالة الخطأ، استخدم البيانات الثابتة كبديل
                        $social_media_data = [];
                    }
                    
                    // دمج البيانات الديناميكية مع البيانات الثابتة كبديل
                     $social_platforms = [
                         'facebook' => [
                             'url' => $social_media_data['facebook'] ?? getSetting('facebook_url'),
                             'color' => 'hover:text-blue-500',
                             'icon' => 'M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z'
                         ],
                         'twitter' => [
                             'url' => $social_media_data['twitter'] ?? getSetting('twitter_url'),
                             'color' => 'hover:text-blue-400',
                             'icon' => 'M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z'
                         ],
                         'instagram' => [
                             'url' => $social_media_data['instagram'] ?? getSetting('instagram_url'),
                             'color' => 'hover:text-pink-500',
                             'icon' => 'M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z'
                         ],
                         'linkedin' => [
                             'url' => $social_media_data['linkedin'] ?? getSetting('linkedin_url'),
                             'color' => 'hover:text-blue-600',
                             'icon' => 'M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z'
                         ],
                         'youtube' => [
                             'url' => $social_media_data['youtube'] ?? getSetting('youtube_url'),
                             'color' => 'hover:text-red-500',
                             'icon' => 'M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z'
                         ],
                         'snapchat' => [
                             'url' => $social_media_data['snapchat'] ?? getSetting('snapchat_url'),
                             'color' => 'hover:text-yellow-400',
                             'icon' => 'M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM15.232 18.668c-.755.002-1.351-.229-1.883-.652-.532-.424-.89-1.061-1.077-1.906-.187-.845-.187-1.816-.187-2.919 0-.366-.298-.664-.664-.664s-.664.298-.664.664c0 1.103 0 2.074-.187 2.919-.187.845-.545 1.482-1.077 1.906-.532.423-1.128.654-1.883.652-.755-.002-1.351-.232-1.883-.656-.532-.424-.89-1.061-1.077-1.906-.187-.845-.187-1.816-.187-2.919 0-.366-.298-.664-.664-.664s-.664.298-.664.664c0 1.103 0 2.074.187 2.919.187.845.545 1.482 1.077 1.906.532.424 1.128.654 1.883.656.755.002 1.351-.229 1.883-.652.532-.424.89-1.061 1.077-1.906.187-.845.187-1.816.187-2.919 0-.366.298-.664.664-.664s.664.298.664.664c0 1.103 0 2.074.187 2.919.187.845.545 1.482 1.077 1.906.532.423 1.128.654 1.883.652z'
                         ],
                         'tiktok' => [
                             'url' => $social_media_data['tiktok'] ?? getSetting('tiktok_url'),
                             'color' => 'hover:text-black',
                             'icon' => 'M19.59 6.69a4.83 4.83 0 01-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 01-5.2 1.74 2.89 2.89 0 012.31-4.64 2.93 2.93 0 01.88.13V9.4a6.84 6.84 0 00-.88-.05A6.33 6.33 0 005 20.1a6.34 6.34 0 0010.86-4.43v-7a8.16 8.16 0 004.77 1.52v-3.4a4.85 4.85 0 01-1-.1z'
                         ],
                         'telegram' => [
                             'url' => $social_media_data['telegram'] ?? getSetting('telegram_url'),
                             'color' => 'hover:text-blue-400',
                             'icon' => 'M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z'
                         ],
                         'whatsapp' => [
                             'url' => !empty($social_media_data['whatsapp']) ? 'https://wa.me/' . preg_replace('/[^0-9]/', '', $social_media_data['whatsapp']) : (getSetting('whatsapp_number') ? 'https://wa.me/' . getSetting('whatsapp_number') : ''),
                             'color' => 'hover:text-green-500',
                             'icon' => 'M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488'
                         ]
                     ];
                    
                    // إضافة المنصات الديناميكية الإضافية
                    foreach ($social_media_data as $platform => $url) {
                        if (!empty($url) && !isset($social_platforms[$platform])) {
                            $social_platforms[$platform] = [
                                'url' => $url,
                                'color' => 'hover:text-gray-300',
                                'icon' => 'M13.601 2.326A7.854 7.854 0 0 0 7.994 0C3.627 0 .068 3.558.064 7.926c0 1.399.366 2.76 1.057 3.965L0 16l4.204-1.102a7.933 7.933 0 0 0 3.79.965h.004c4.368 0 7.926-3.558 7.93-7.93A7.898 7.898 0 0 0 13.6 2.326zM7.994 14.521a6.573 6.573 0 0 1-3.356-.92l-.24-.144-2.494.654.666-2.433-.156-.251a6.56 6.56 0 0 1-1.007-3.505c0-3.626 2.957-6.584 6.591-6.584a6.56 6.56 0 0 1 4.66 1.931 6.557 6.557 0 0 1 1.928 4.66c-.004 3.639-2.961 6.592-6.592 6.592zm3.615-4.934c-.197-.099-1.17-.578-1.353-.644-.182-.066-.315-.099-.445.099-.133.197-.513.644-.627.775-.114.133-.232.148-.43.05-.197-.1-.836-.308-1.592-.985-.59-.525-.985-1.175-1.103-1.372-.114-.198-.011-.304.088-.402.087-.088.197-.232.296-.346.1-.114.133-.198.198-.33.066-.134.034-.248-.015-.347-.05-.099-.445-1.076-.612-1.47-.16-.389-.323-.335-.445-.34-.114-.007-.247-.007-.38-.007a.729.729 0 0 0-.529.247c-.182.198-.692.677-.692 1.654 0 .977.708 1.916.806 2.049.098.133 1.394 2.132 3.383 2.992.47.205.84.326 1.129.418.475.152.906.129 1.246.08.38-.058 1.171-.48 1.338-.943.164-.464.164-.86.114-.943-.049-.084-.182-.133-.38-.232z' // أيقونة افتراضية
                            ];
                        }
                    }
                    ?>
                    
                    <div class="flex space-x-4 space-x-reverse">
                        <?php foreach ($social_platforms as $platform => $data): ?>
                            <?php if (!empty($data['url'])): ?>
                                <a href="<?php echo htmlspecialchars($data['url']); ?>" 
                                   target="_blank" 
                                   class="text-gray-400 <?php echo $data['color']; ?> transition-colors transform hover:scale-110 duration-200"
                                   title="<?php echo ucfirst($platform); ?>">
                                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="<?php echo $data['icon']; ?>"/>
                                    </svg>
                                </a>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- Quick Links -->
                <div>
                    <h4 class="text-lg font-semibold mb-4">روابط سريعة</h4>
                    <ul class="space-y-2">
                        <li><a href="<?php echo SITE_URL; ?>" class="text-gray-300 hover:text-white transition-colors">الرئيسية</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/about" class="text-gray-300 hover:text-white transition-colors">عن الشركة</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/products" class="text-gray-300 hover:text-white transition-colors">المنتجات</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/distributors" class="text-gray-300 hover:text-white transition-colors">الموزعين</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/support" class="text-gray-300 hover:text-white transition-colors">خدمة ما بعد البيع</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/contact" class="text-gray-300 hover:text-white transition-colors">تواصل معنا</a></li>
                    </ul>
                </div>
                
                <!-- Contact Info -->
                <div>
                    <h4 class="text-lg font-semibold mb-4">معلومات التواصل</h4>
                    <?php
                    // جلب معلومات التواصل من جدول contact_info
                    $contact_info = getDetailedContactInfo();
                    ?>
                    <ul class="space-y-3">
                        <?php if (!empty($contact_info['address'])): ?>
                            <li class="flex items-start">
                                <svg class="w-5 h-5 text-green-500 mt-1 ml-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <span class="text-gray-300"><?php echo htmlspecialchars($contact_info['address']); ?></span>
                            </li>
                        <?php endif; ?>
                        
                        <?php if (!empty($contact_info['phone'])): ?>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 ml-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                </svg>
                                <a href="tel:<?php echo htmlspecialchars($contact_info['phone']); ?>" class="text-gray-300 hover:text-white transition-colors">
                                    <?php echo htmlspecialchars($contact_info['phone']); ?>
                                </a>
                            </li>
                        <?php endif; ?>
                        
                        <?php if (!empty($contact_info['email'])): ?>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 ml-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                                <a href="mailto:<?php echo htmlspecialchars($contact_info['email']); ?>" class="text-gray-300 hover:text-white transition-colors">
                                    <?php echo htmlspecialchars($contact_info['email']); ?>
                                </a>
                            </li>
                        <?php endif; ?>
                        
                        <?php 
                        // عرض بريد المبيعات إذا كان متوفراً في البيانات الإضافية
                        if (!empty($contact_info['email_data']['sales']) && $contact_info['email_data']['sales'] !== $contact_info['email']): 
                        ?>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 ml-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                                </svg>
                                <a href="mailto:<?php echo htmlspecialchars($contact_info['email_data']['sales']); ?>" class="text-gray-300 hover:text-white transition-colors">
                                    <?php echo htmlspecialchars($contact_info['email_data']['sales']); ?> <span class="text-xs text-gray-400">(المبيعات)</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        
                        <?php if (!empty($contact_info['working_hours'])): ?>
                            <li class="flex items-start">
                                <svg class="w-5 h-5 text-green-500 mt-1 ml-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span class="text-gray-300"><?php echo nl2br(htmlspecialchars($contact_info['working_hours'])); ?></span>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
            
            <!-- Bottom Footer -->
            <div class="border-t border-gray-700 mt-8 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="text-gray-400 text-sm mb-4 md:mb-0">
                        <p>&copy; <?php echo date('Y'); ?> <?php echo getSetting('site_name', 'Green Line'); ?>. جميع الحقوق محفوظة.</p>
                    </div>
                    
                    <div class="flex space-x-6 space-x-reverse text-sm">
                        <a href="<?php echo SITE_URL; ?>/privacy" class="text-gray-400 hover:text-white transition-colors">سياسة الخصوصية</a>
                        <a href="<?php echo SITE_URL; ?>/terms" class="text-gray-400 hover:text-white transition-colors">شروط الاستخدام</a>
                        <a href="<?php echo SITE_URL; ?>/sitemap" class="text-gray-400 hover:text-white transition-colors">خريطة الموقع</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- WhatsApp Float Button -->
    <?php if (getSetting('whatsapp_number') && getSetting('whatsapp_enabled', true)): ?>
        <div class="fixed bottom-6 left-6 z-50">
            <a href="https://wa.me/<?php echo getSetting('whatsapp_number'); ?>?text=<?php echo urlencode('مرحباً، أود الاستفسار عن خدماتكم'); ?>" 
               target="_blank" 
               class="bg-green-500 hover:bg-green-600 text-white p-4 rounded-full shadow-lg transition-all duration-300 hover:scale-110 flex items-center justify-center group">
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                </svg>
                <span class="absolute right-full ml-2 bg-gray-900 text-white px-2 py-1 rounded text-sm whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity">
                    تواصل معنا عبر واتساب
                </span>
            </a>
        </div>
    <?php endif; ?>
    
    <!-- Back to Top Button -->
    <button id="back-to-top" class="fixed bottom-6 right-6 bg-primary hover:bg-secondary text-white p-3 rounded-full shadow-lg transition-all duration-300 opacity-0 invisible hover:scale-110 z-40">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
        </svg>
    </button>
    
    <!-- Additional JavaScript -->
    <?php if (isset($additionalJS)): ?>
        <?php echo $additionalJS; ?>
    <?php endif; ?>
    
    <script>
        // Back to top functionality
        document.addEventListener('DOMContentLoaded', function() {
            const backToTopButton = document.getElementById('back-to-top');
            
            // Show/hide back to top button
            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    backToTopButton.classList.remove('opacity-0', 'invisible');
                    backToTopButton.classList.add('opacity-100', 'visible');
                } else {
                    backToTopButton.classList.add('opacity-0', 'invisible');
                    backToTopButton.classList.remove('opacity-100', 'visible');
                }
            });
            
            // Smooth scroll to top
            backToTopButton.addEventListener('click', function() {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
            
            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
            
            // Add loading animation to forms
            document.querySelectorAll('form').forEach(form => {
                form.addEventListener('submit', function() {
                    const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
                    if (submitButton) {
                        submitButton.disabled = true;
                        const originalText = submitButton.textContent || submitButton.value;
                        submitButton.textContent = 'جاري المعالجة...';
                        submitButton.innerHTML = '<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg> جاري المعالجة...';
                        
                        // Reset button after 10 seconds (fallback)
                        setTimeout(() => {
                            submitButton.disabled = false;
                            submitButton.textContent = originalText;
                        }, 10000);
                    }
                });
            });
            
            // Lazy loading for images
            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            img.src = img.dataset.src;
                            img.classList.remove('lazy');
                            imageObserver.unobserve(img);
                        }
                    });
                });
                
                document.querySelectorAll('img[data-src]').forEach(img => {
                    imageObserver.observe(img);
                });
            }
        });
        
        // Global error handler
        window.addEventListener('error', function(e) {
            console.error('JavaScript Error:', e.error);
        });
        
        // Service Worker registration (if available)
        if ('serviceWorker' in navigator && '<?php echo defined('ENVIRONMENT') ? ENVIRONMENT : 'development'; ?>' === 'production') {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                        console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    </script>
    
</body>
</html>