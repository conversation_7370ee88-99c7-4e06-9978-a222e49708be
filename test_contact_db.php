<?php
require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<h2>اختبار قاعدة البيانات - جدول contact_messages</h2>";

try {
    // التحقق من الاتصال بقاعدة البيانات
    echo "<h3>1. اختبار الاتصال بقاعدة البيانات:</h3>";
    global $database;
    $connection = $database->getConnection();
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح<br>";
    
    // التحقق من وجود جدول contact_messages
    echo "<h3>2. التحقق من وجود جدول contact_messages:</h3>";
    $tableExists = $database->fetch("SHOW TABLES LIKE 'contact_messages'");
    if ($tableExists) {
        echo "✅ جدول contact_messages موجود<br>";
        
        // عرض هيكل الجدول
        echo "<h3>3. هيكل جدول contact_messages:</h3>";
        $columns = $database->fetchAll("DESCRIBE contact_messages");
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "<td>{$column['Extra']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // عرض عدد الرسائل الموجودة
        echo "<h3>4. عدد الرسائل الموجودة:</h3>";
        $count = $database->fetch("SELECT COUNT(*) as count FROM contact_messages");
        echo "عدد الرسائل: " . $count['count'] . "<br>";
        
        // عرض آخر 5 رسائل
        echo "<h3>5. آخر 5 رسائل:</h3>";
        $messages = $database->fetchAll("SELECT * FROM contact_messages ORDER BY created_at DESC LIMIT 5");
        if (!empty($messages)) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>ID</th><th>الاسم</th><th>البريد</th><th>الموضوع</th><th>الحالة</th><th>التاريخ</th></tr>";
            foreach ($messages as $message) {
                echo "<tr>";
                echo "<td>{$message['id']}</td>";
                echo "<td>{$message['name']}</td>";
                echo "<td>{$message['email']}</td>";
                echo "<td>{$message['subject']}</td>";
                echo "<td>{$message['status']}</td>";
                echo "<td>{$message['created_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "لا توجد رسائل في الجدول<br>";
        }
        
        // اختبار إدراج رسالة تجريبية
        echo "<h3>6. اختبار إدراج رسالة تجريبية:</h3>";
        $testData = [
            'name' => 'اختبار النظام',
            'email' => '<EMAIL>',
            'phone' => '0501234567',
            'subject' => 'رسالة اختبار',
            'message' => 'هذه رسالة اختبار للتأكد من عمل النظام',
            'type' => 'general',
            'status' => 'new'
        ];
        
        $result = $database->insert('contact_messages', $testData);
        if ($result) {
            echo "✅ تم إدراج الرسالة التجريبية بنجاح - ID: {$result}<br>";
            
            // حذف الرسالة التجريبية
            $database->delete('contact_messages', 'id = ?', [$result]);
            echo "✅ تم حذف الرسالة التجريبية<br>";
        } else {
            echo "❌ فشل في إدراج الرسالة التجريبية<br>";
        }
        
    } else {
        echo "❌ جدول contact_messages غير موجود<br>";
        echo "<h3>إنشاء جدول contact_messages:</h3>";
        
        $createTableSQL = "
        CREATE TABLE contact_messages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255) NOT NULL,
            phone VARCHAR(20),
            subject VARCHAR(255),
            message TEXT NOT NULL,
            type VARCHAR(50) DEFAULT 'general',
            status ENUM('new', 'unread', 'read', 'replied') DEFAULT 'new',
            reply TEXT,
            replied_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $database->query($createTableSQL);
        echo "✅ تم إنشاء جدول contact_messages بنجاح<br>";
    }
    
    // اختبار دالة saveContactMessage
    echo "<h3>7. اختبار دالة saveContactMessage:</h3>";
    $testContactData = [
        'name' => 'اختبار الدالة',
        'email' => '<EMAIL>',
        'phone' => '0507654321',
        'subject' => 'اختبار دالة الحفظ',
        'message' => 'اختبار دالة saveContactMessage',
        'type' => 'general'
    ];
    
    $functionResult = saveContactMessage($testContactData);
    if ($functionResult) {
        echo "✅ دالة saveContactMessage تعمل بشكل صحيح - ID: {$functionResult}<br>";
        
        // حذف الرسالة التجريبية
        $database->delete('contact_messages', 'id = ?', [$functionResult]);
        echo "✅ تم حذف رسالة اختبار الدالة<br>";
    } else {
        echo "❌ دالة saveContactMessage لا تعمل بشكل صحيح<br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "<br>";
    echo "تفاصيل الخطأ: " . $e->getTraceAsString() . "<br>";
}

echo "<br><h3>الخلاصة:</h3>";
echo "إذا كانت جميع الاختبارات ناجحة، فإن المشكلة قد تكون في:<br>";
echo "1. مسار API غير صحيح في JavaScript<br>";
echo "2. مشكلة في إعدادات الخادم<br>";
echo "3. مشكلة في معالجة البيانات في ملف contact.php<br>";
echo "4. مشكلة في إعدادات CORS<br>";
?>