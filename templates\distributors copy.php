<?php
$pageTitle = 'الموزعون';
$page = 'distributors';

// جلب الموزعين
$region = $_GET['region'] ?? 'all';
$search = $_GET['search'] ?? '';

$distributors = getDistributors($region, $search);
$regions = getDistributorRegions();

include 'header.php';
?>

<!-- Leaflet CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>

<!-- Leaflet JavaScript -->
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>

<!-- Hero Section -->
<section class="bg-gradient-to-r from-primary to-secondary py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">
            شبكة الموزعين
        </h1>
        <p class="text-xl text-white opacity-90 max-w-2xl mx-auto">
            اعثر على أقرب موزع لمنتجاتنا في منطقتك
        </p>
    </div>
</section>

<!-- Search and Filter Section -->
<section class="py-8 bg-gray-50">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <!-- Search Bar -->
        <div class="mb-8">
            <form method="GET" class="max-w-2xl mx-auto">
                <input type="hidden" name="region" value="<?php echo htmlspecialchars($region); ?>">
                <div class="relative">
                    <input type="text" 
                           name="search" 
                           value="<?php echo htmlspecialchars($search); ?>"
                           placeholder="ابحث عن موزع أو منطقة..."
                           class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    <button type="submit" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-primary">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </button>
                </div>
            </form>
        </div>
        
        <!-- Region Filter -->
        <div class="flex flex-wrap gap-2 justify-center">
            <a href="<?php echo SITE_URL; ?>/distributors<?php echo $search ? '?search=' . urlencode($search) : ''; ?>" 
               class="px-4 py-2 rounded-full text-sm font-medium transition-colors duration-300 <?php echo $region === 'all' ? 'bg-primary text-white' : 'bg-white text-gray-700 hover:bg-primary hover:text-white'; ?>">
                جميع المناطق
            </a>
            
            <?php foreach ($regions as $reg): ?>
                <a href="<?php echo SITE_URL; ?>/distributors?region=<?php echo urlencode($reg['slug']); ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>" 
                   class="px-4 py-2 rounded-full text-sm font-medium transition-colors duration-300 <?php echo $region === $reg['slug'] ? 'bg-primary text-white' : 'bg-white text-gray-700 hover:bg-primary hover:text-white'; ?>">
                    <?php echo htmlspecialchars($reg['name']); ?>
                    <span class="mr-1 text-xs opacity-75">(<?php echo $reg['count']; ?>)</span>
                </a>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Map Section -->
<section class="py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-2xl font-bold text-gray-900 mb-2">
                    خريطة الموزعين
                </h2>
                <p class="text-gray-600">
                    انقر على العلامات لعرض تفاصيل الموزع
                </p>
            </div>
            
            <!-- Map Container -->
            <div id="distributors-map" class="h-96 bg-gray-100 relative">
                <!-- Loading indicator -->
                <div id="map-loading" class="absolute inset-0 flex items-center justify-center bg-gray-100 z-10">
                    <div class="text-center">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                        <p class="text-gray-500">جاري تحميل الخريطة...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Distributors List -->
<section class="py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <?php if (!empty($search)): ?>
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-2">
                    نتائج البحث عن: "<?php echo htmlspecialchars($search); ?>"
                </h2>
                <p class="text-gray-600">
                    تم العثور على <?php echo count($distributors); ?> موزع
                </p>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($distributors)): ?>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <?php foreach ($distributors as $distributor): ?>
                    <div class="bg-white rounded-lg shadow-md border-2 border-green-200 hover:shadow-lg transition-shadow duration-300 overflow-hidden">
                        
                        <!-- Distributor Header -->
                        <div class="p-6">
                            <div class="flex items-start justify-between mb-4">
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-1">
                                        <?php echo htmlspecialchars($distributor['name']); ?>
                                    </h3>
                                    
                                    <div class="flex flex-wrap gap-2 mb-3">
                                        <?php if (!empty($distributor['region'])): ?>
                                            <span class="inline-block bg-primary bg-opacity-10 text-white px-2 py-1 rounded-full text-xs font-medium">
                                                <?php echo htmlspecialchars($distributor['region']); ?>
                                            </span>
                                        <?php endif; ?>
                                        
                                        <?php if (!empty($distributor['type'])): ?>
                                            <span class="inline-block bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs font-medium">
                                                <?php echo htmlspecialchars($distributor['type']); ?>
                                            </span>
                                        <?php endif; ?>
                                        
                                        <!-- موزع معتمد -->
                                        <?php if (!empty($distributor['certified']) && $distributor['certified'] == 1): ?>
                                            <span class="inline-block bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                                                <i class="fas fa-certificate ml-1"></i>موزع معتمد
                                            </span>
                                        <?php endif; ?>
                                        
                                        <!-- موزع مميز -->
                                        <?php if (!empty($distributor['distributor_type']) && $distributor['distributor_type'] === 'مميز'): ?>
                                            <span class="inline-block bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">
                                                <i class="fas fa-star ml-1"></i>موزع مميز
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <?php if (!empty($distributor['logo'])): ?>
                                    <img src="<?php echo htmlspecialchars($distributor['logo']); ?>" 
                                         alt="<?php echo htmlspecialchars($distributor['name']); ?>"
                                         class="w-12 h-12 object-contain rounded-lg">
                                <?php endif; ?>
                            </div>
                            
                            <!-- المدير المسؤول وسنة التأسيس -->
                            <div class="flex flex-wrap gap-4 mb-4 text-sm text-gray-600">
                                <?php if (!empty($distributor['manager'])): ?>
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 text-gray-400 flex-shrink-0 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                        </svg>
                                        <span>المدير المسؤول: <?php echo htmlspecialchars($distributor['manager']); ?></span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if (!empty($distributor['established'])): ?>
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 text-gray-400 flex-shrink-0 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                        <span>سنة التأسيس: <?php echo htmlspecialchars($distributor['established']); ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <!-- التقييم وعدد التقييمات -->
                            <?php 
                            $rating = floatval($distributor['rating'] ?? 0);
                            $reviews = intval($distributor['reviews'] ?? 0);
                            if ($rating > 0 || $reviews > 0): 
                            ?>
                                <div class="flex items-center mb-4">
                                    <div class="flex items-center">
                                        <div class="flex text-yellow-400 ml-2">
                                            <?php for($i = 1; $i <= 5; $i++): ?>
                                                <?php if ($i <= floor($rating)): ?>
                                                    <svg class="w-4 h-4 fill-current" viewBox="0 0 20 20">
                                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                                    </svg>
                                                <?php elseif ($i <= ceil($rating) && $rating > floor($rating)): ?>
                                                    <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" fill-opacity="0.5"/>
                                                    </svg>
                                                <?php else: ?>
                                                    <svg class="w-4 h-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                                    </svg>
                                                <?php endif; ?>
                                            <?php endfor; ?>
                                        </div>
                                        <span class="text-sm text-gray-600">
                                            <?php if ($rating > 0): ?>
                                                <?php echo number_format($rating, 1); ?> التقييم
                                            <?php endif; ?>
                                            <?php if ($reviews > 0): ?>
                                                (<?php echo $reviews; ?> تقييم)
                                            <?php endif; ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <!-- الوصف -->
                            <?php if (!empty($distributor['description'])): ?>
                                <div class="mb-4">
                                    <h4 class="text-sm font-medium text-gray-700 mb-2">الوصف</h4>
                                    <p class="text-sm text-gray-600 leading-relaxed">
                                        <?php echo htmlspecialchars($distributor['description']); ?>
                                    </p>
                                </div>
                            <?php endif; ?>
                            
                            <!-- التخصصات -->
                            <?php 
                            $specialties = [];
                            if (!empty($distributor['specialties'])) {
                                $specialtiesData = json_decode($distributor['specialties'], true);
                                if (json_last_error() === JSON_ERROR_NONE && is_array($specialtiesData)) {
                                    $specialties = $specialtiesData;
                                } else {
                                    $specialties = array_map('trim', explode(',', $distributor['specialties']));
                                }
                            }
                            if (!empty($specialties)): 
                            ?>
                                <div class="mb-4">
                                    <h4 class="text-sm font-medium text-gray-700 mb-2">التخصصات</h4>
                                    <div class="flex flex-wrap gap-2">
                                        <?php foreach ($specialties as $specialty): ?>
                                            <?php if (!empty(trim($specialty))): ?>
                                                <span class="inline-block bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs font-medium">
                                                    <?php echo htmlspecialchars(trim($specialty)); ?>
                                                </span>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <!-- التخصصات الجديدة -->
                            <?php 
                            $newSpecialties = [];
                            if (!empty($distributor['new_specialties'])) {
                                $newSpecialtiesData = json_decode($distributor['new_specialties'], true);
                                if (json_last_error() === JSON_ERROR_NONE && is_array($newSpecialtiesData)) {
                                    $newSpecialties = $newSpecialtiesData;
                                } else {
                                    $newSpecialties = array_map('trim', explode(',', $distributor['new_specialties']));
                                }
                            }
                            if (!empty($newSpecialties)): 
                            ?>
                                <div class="mb-4">
                                    <h4 class="text-sm font-medium text-gray-700 mb-2">التخصصات الجديدة</h4>
                                    <div class="flex flex-wrap gap-2">
                                        <?php foreach ($newSpecialties as $newSpecialty): ?>
                                            <?php if (!empty(trim($newSpecialty))): ?>
                                                <span class="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                                                    <i class="fas fa-plus ml-1"></i><?php echo htmlspecialchars(trim($newSpecialty)); ?>
                                                </span>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <!-- مناطق التغطية -->
                            <?php 
                            $coverage = [];
                            if (!empty($distributor['coverage'])) {
                                $coverageData = json_decode($distributor['coverage'], true);
                                if (json_last_error() === JSON_ERROR_NONE && is_array($coverageData)) {
                                    $coverage = $coverageData;
                                } else {
                                    $coverage = array_map('trim', explode(',', $distributor['coverage']));
                                }
                            }
                            if (!empty($coverage)): 
                            ?>
                                <div class="mb-4">
                                    <h4 class="text-sm font-medium text-gray-700 mb-2">مناطق التغطية</h4>
                                    <div class="flex flex-wrap gap-2">
                                        <?php foreach ($coverage as $area): ?>
                                            <?php if (!empty(trim($area))): ?>
                                                <span class="inline-block bg-indigo-100 text-indigo-800 px-2 py-1 rounded-full text-xs font-medium">
                                                    <i class="fas fa-map-marker-alt ml-1"></i><?php echo htmlspecialchars(trim($area)); ?>
                                                </span>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <!-- Contact Information -->
                            <div class="space-y-3">
                                <?php if (!empty($distributor['address'])): ?>
                                    <div class="flex items-start">
                                        <svg class="w-4 h-4 text-gray-400 mt-1 flex-shrink-0 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        </svg>
                                        <span class="text-sm text-gray-600">
                                            <?php echo htmlspecialchars($distributor['address']); ?>
                                        </span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if (!empty($distributor['phone'])): ?>
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 text-gray-400 flex-shrink-0 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                        </svg>
                                        <a href="tel:<?php echo htmlspecialchars($distributor['phone']); ?>" 
                                           class="text-sm text-gray-600 hover:text-primary">
                                            <?php echo htmlspecialchars($distributor['phone']); ?>
                                        </a>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if (!empty($distributor['email'])): ?>
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 text-gray-400 flex-shrink-0 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                        </svg>
                                        <a href="mailto:<?php echo htmlspecialchars($distributor['email']); ?>" 
                                           class="text-sm text-gray-600 hover:text-primary">
                                            <?php echo htmlspecialchars($distributor['email']); ?>
                                        </a>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if (!empty($distributor['website'])): ?>
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 text-gray-400 flex-shrink-0 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9"></path>
                                        </svg>
                                        <a href="<?php echo htmlspecialchars($distributor['website']); ?>" 
                                           target="_blank" 
                                           class="text-sm text-gray-600 hover:text-primary">
                                            الموقع الإلكتروني
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <!-- Working Hours -->
                            <?php if (!empty($distributor['working_hours'])): ?>
                                <div class="mt-4 pt-4 border-t border-gray-200">
                                    <div class="flex items-center mb-2">
                                        <svg class="w-4 h-4 text-gray-400 flex-shrink-0 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <span class="text-sm font-medium text-gray-700">ساعات العمل</span>
                                    </div>
                                    <p class="text-sm text-gray-600 mr-6">
                                        <?php 
                                        if (is_array($distributor['working_hours'])) {
                                            // إذا كانت مصفوفة، قم بتحويلها إلى نص
                                            $hours_text = '';
                                            foreach ($distributor['working_hours'] as $day => $hours) {
                                                $hours_text .= $day . ': ' . $hours . ' | ';
                                            }
                                            echo htmlspecialchars(rtrim($hours_text, ' | '));
                                        } else {
                                            // إذا كانت نص عادي
                                            echo htmlspecialchars($distributor['working_hours'] ?? '');
                                        }
                                        ?>
                                    </p>
                                </div>
                            <?php endif; ?>
                            
                            <!-- Services -->
                            <?php if (!empty($distributor['services'])): ?>
                                <div class="mt-4 pt-4 border-t border-gray-200">
                                    <div class="flex items-center mb-2">
                                        <svg class="w-4 h-4 text-gray-400 flex-shrink-0 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <span class="text-sm font-medium text-gray-700">الخدمات</span>
                                    </div>
                                    <div class="flex flex-wrap gap-1 mr-6">
                                        <?php 
                                        // Handle services - check if it's already an array or needs to be decoded
                                        if (is_array($distributor['services'])) {
                                            $services = $distributor['services'];
                                        } else {
                                            $services = explode(',', $distributor['services']);
                                        }
                                        foreach ($services as $service):
                                        ?>
                                            <span class="inline-block bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">
                                                <?php echo htmlspecialchars(trim($service)); ?>
                                            </span>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
                            <div class="flex flex-wrap gap-2">
                                <?php if (!empty($distributor['phone'])): ?>
                                    <a href="tel:<?php echo htmlspecialchars($distributor['phone']); ?>" 
                                       class="flex-1 bg-primary text-white px-4 py-2 rounded-lg text-sm font-medium text-center hover:bg-primary-dark transition-colors duration-300">
                                        اتصل الآن
                                    </a>
                                <?php endif; ?>
                                
                                <?php if (!empty($distributor['whatsapp'])): ?>
                                    <a href="https://wa.me/<?php echo preg_replace('/[^0-9]/', '', $distributor['whatsapp']); ?>?text=مرحباً، أود الاستفسار عن منتجاتكم" 
                                       target="_blank" 
                                       class="flex-1 bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium text-center hover:bg-green-700 transition-colors duration-300">
                                        واتساب
                                    </a>
                                <?php endif; ?>
                                
                                <?php if (!empty($distributor['latitude']) && !empty($distributor['longitude'])): ?>
                                    <button onclick="showOnMap(<?php echo $distributor['latitude']; ?>, <?php echo $distributor['longitude']; ?>, '<?php echo htmlspecialchars($distributor['name']); ?>')" 
                                            class="flex-1 bg-gray-600 text-white px-4 py-2 rounded-lg text-sm font-medium text-center hover:bg-gray-700 transition-colors duration-300">
                                        عرض على الخريطة
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <!-- No Results -->
            <div class="text-center py-12">
                <svg class="mx-auto h-16 w-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                
                <?php if (!empty($search)): ?>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">لم نجد موزعين لبحثك</h3>
                    <p class="text-gray-600 mb-6">جرب استخدام كلمات مختلفة أو تصفح الموزعين حسب المنطقة</p>
                    <a href="<?php echo SITE_URL; ?>/distributors" class="inline-flex items-center text-primary hover:text-secondary font-medium">
                        عرض جميع الموزعين
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </a>
                <?php else: ?>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">لا يوجد موزعون في هذه المنطقة</h3>
                    <p class="text-gray-600 mb-6">جرب منطقة أخرى أو تصفح جميع الموزعين</p>
                    <a href="<?php echo SITE_URL; ?>/distributors" class="inline-flex items-center text-primary hover:text-secondary font-medium">
                        عرض جميع الموزعين
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Become a Distributor Section -->
<section class="py-16 bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="bg-gradient-to-r from-primary to-secondary rounded-lg p-8 text-white">
            <h2 class="text-2xl md:text-3xl font-bold mb-4">
                هل تريد أن تصبح موزعاً لمنتجاتنا؟
            </h2>
            <p class="text-lg opacity-90 mb-6">
                انضم إلى شبكة موزعينا واستفد من فرص النمو والربح
            </p>
            
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="<?php echo SITE_URL; ?>/contact?type=partnership" 
                   class="bg-white text-primary px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-300">
                    طلب شراكة
                </a>
                
                <a href="<?php echo SITE_URL; ?>/contact" 
                   class="border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary transition-colors duration-300">
                    تواصل معنا
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Statistics Section -->
<section class="py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">
                شبكة موزعين قوية
            </h2>
            <p class="text-gray-600">
                نفخر بشبكة موزعينا الواسعة التي تغطي جميع أنحاء المملكة
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div class="text-center">
                <div class="w-16 h-16 bg-primary bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </div>
                <div class="text-3xl font-bold text-gray-900 mb-2" data-counter="<?php echo count($distributors); ?>">
                    <?php echo count($distributors); ?>+
                </div>
                <p class="text-gray-600">موزع معتمد</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-primary bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                </div>
                <div class="text-3xl font-bold text-gray-900 mb-2" data-counter="<?php echo count($regions); ?>">
                    <?php echo count($regions); ?>+
                </div>
                <p class="text-gray-600">مدينة ومنطقة</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-primary bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="text-3xl font-bold text-gray-900 mb-2" data-counter="24">
                    24/7
                </div>
                <p class="text-gray-600">دعم متواصل</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-primary bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="text-3xl font-bold text-gray-900 mb-2" data-counter="100">
                    100%
                </div>
                <p class="text-gray-600">ضمان الجودة</p>
            </div>
        </div>
    </div>
</section>

<script>
// Map functionality will be handled by the main showOnMap function below

// Search functionality enhancement
const searchInput = document.querySelector('input[name="search"]');
if (searchInput) {
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            this.form.submit();
        }
    });
}

// Counter animation
document.addEventListener('DOMContentLoaded', function() {
    const counters = document.querySelectorAll('[data-counter]');
    
    const animateCounter = (counter) => {
        const target = parseInt(counter.getAttribute('data-counter'));
        const increment = target / 100;
        let current = 0;
        
        const updateCounter = () => {
            if (current < target) {
                current += increment;
                counter.textContent = Math.ceil(current) + (counter.textContent.includes('+') ? '+' : counter.textContent.includes('/') ? '/7' : '%');
                requestAnimationFrame(updateCounter);
            } else {
                counter.textContent = target + (counter.textContent.includes('+') ? '+' : counter.textContent.includes('/') ? '/7' : '%');
            }
        };
        
        updateCounter();
    };
    
    // Intersection Observer for counter animation
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const counter = entry.target;
                animateCounter(counter);
                observer.unobserve(counter);
            }
        });
    });
    
    counters.forEach(counter => {
        observer.observe(counter);
    });
});

// Initialize Leaflet map
let distributorsMap;
let mapMarkers = [];

document.addEventListener('DOMContentLoaded', function() {
    const mapContainer = document.getElementById('distributors-map');
    const loadingIndicator = document.getElementById('map-loading');
    
    if (mapContainer) {
        try {
            // Initialize the map centered on Saudi Arabia
            distributorsMap = L.map('distributors-map').setView([24.7136, 46.6753], 6);
            
            // Add OpenStreetMap tiles
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                maxZoom: 18
            }).addTo(distributorsMap);
            
            // Hide loading indicator
            if (loadingIndicator) {
                loadingIndicator.style.display = 'none';
            }
            
            // Add markers for distributors
            const distributors = <?php echo json_encode($distributors); ?>;
            
            distributors.forEach(distributor => {
                if (distributor.latitude && distributor.longitude) {
                    const lat = parseFloat(distributor.latitude);
                    const lng = parseFloat(distributor.longitude);
                    
                    if (!isNaN(lat) && !isNaN(lng)) {
                        // Create custom marker icon
                        const customIcon = L.divIcon({
                            html: `
                                <div style="
                                    width: 32px;
                                    height: 32px;
                                    background-color: #10B981;
                                    border: 2px solid white;
                                    border-radius: 50%;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
                                ">
                                    <div style="
                                        width: 12px;
                                        height: 12px;
                                        background-color: white;
                                        border-radius: 50%;
                                    "></div>
                                </div>
                            `,
                            className: 'custom-div-icon',
                            iconSize: [32, 32],
                            iconAnchor: [16, 16]
                        });
                        
                        // Create marker
                        const marker = L.marker([lat, lng], { icon: customIcon }).addTo(distributorsMap);
                        
                        // Create popup content
                        const popupContent = `
                            <div class="p-3 min-w-64">
                                <h3 class="font-bold text-lg mb-2 text-gray-900">${distributor.name}</h3>
                                ${distributor.address ? `<p class="text-sm text-gray-600 mb-2"><i class="fas fa-map-marker-alt ml-1"></i> ${distributor.address}</p>` : ''}
                                ${distributor.phone ? `<p class="text-sm text-gray-600 mb-2"><i class="fas fa-phone ml-1"></i> ${distributor.phone}</p>` : ''}
                                ${distributor.region ? `<span class="inline-block bg-primary bg-opacity-10 text-primary px-2 py-1 rounded-full text-xs font-medium">${distributor.region}</span>` : ''}
                                <div class="mt-3 flex gap-2">
                                    ${distributor.phone ? `<a href="tel:${distributor.phone}" class="bg-primary text-white px-3 py-1 rounded text-xs hover:bg-primary-dark">اتصل</a>` : ''}
                                    ${distributor.whatsapp ? `<a href="https://wa.me/${distributor.whatsapp.replace(/[^0-9]/g, '')}" target="_blank" class="bg-green-600 text-white px-3 py-1 rounded text-xs hover:bg-green-700">واتساب</a>` : ''}
                                </div>
                            </div>
                        `;
                        
                        marker.bindPopup(popupContent);
                        mapMarkers.push(marker);
                    }
                }
            });
            
            // Fit map to show all markers if there are any
            if (mapMarkers.length > 0) {
                const group = new L.featureGroup(mapMarkers);
                distributorsMap.fitBounds(group.getBounds().pad(0.1));
            }
            
        } catch (error) {
            console.error('Error initializing map:', error);
            if (loadingIndicator) {
                loadingIndicator.innerHTML = `
                    <div class="text-center">
                        <p class="text-red-500 mb-2">خطأ في تحميل الخريطة</p>
                        <p class="text-sm text-gray-400">يرجى إعادة تحميل الصفحة</p>
                    </div>
                `;
            }
        }
    }
});

// Function to show specific distributor on map
function showOnMap(lat, lng, name) {
    if (distributorsMap) {
        const latitude = parseFloat(lat);
        const longitude = parseFloat(lng);
        
        if (!isNaN(latitude) && !isNaN(longitude)) {
            // Center map on the distributor
            distributorsMap.setView([latitude, longitude], 15);
            
            // Find and open the corresponding marker popup
            mapMarkers.forEach(marker => {
                const markerLatLng = marker.getLatLng();
                if (Math.abs(markerLatLng.lat - latitude) < 0.001 && Math.abs(markerLatLng.lng - longitude) < 0.001) {
                    marker.openPopup();
                }
            });
            
            // Scroll to map section
            document.getElementById('distributors-map').scrollIntoView({ 
                behavior: 'smooth',
                block: 'center'
            });
        }
    }
}

// Fallback if Leaflet fails to load
window.addEventListener('load', function() {
    setTimeout(function() {
        const loadingIndicator = document.getElementById('map-loading');
        if (loadingIndicator && loadingIndicator.style.display !== 'none') {
            loadingIndicator.innerHTML = `
                <div class="text-center">
                    <p class="text-gray-500 mb-2">تعذر تحميل الخريطة</p>
                    <p class="text-sm text-gray-400">يرجى التحقق من اتصال الإنترنت أو المحاولة لاحقاً</p>
                </div>
            `;
        }
    }, 5000); // 5 seconds timeout
});
</script>

<?php include 'footer.php'; ?>