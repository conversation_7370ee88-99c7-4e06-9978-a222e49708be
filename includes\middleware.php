<?php
/**
 * Middleware للتحقق من الصلاحيات
 * Permission Middleware
 */

require_once 'PermissionManager.php';

class PermissionMiddleware {
    private $permissionManager;
    
    public function __construct($database) {
        $this->permissionManager = new PermissionManager($database);
    }
    
    /**
     * التحقق من صلاحية الوصول للوحة التحكم
     */
    public function requireAdminAccess() {
        if (!isLoggedIn()) {
            $this->redirectToLogin();
        }
        
        $userId = $_SESSION['user_id'];
        
        // التحقق من صلاحية الوصول للوحة التحكم
        if (!$this->permissionManager->hasAnyPermission($userId, [
            'admin-access',
            'manage-users',
            'manage-products',
            'manage-orders',
            'view-reports',
            'manage-content',
            'manage-settings'
        ])) {
            $this->accessDenied('ليس لديك صلاحية للوصول إلى لوحة التحكم');
        }
    }
    
    /**
     * التحقق من صلاحية إدارة المستخدمين
     */
    public function requireUserManagement() {
        $this->requireLogin();
        
        if (!$this->permissionManager->hasAnyPermission($_SESSION['user_id'], [
            'manage-users',
            'view-users',
            'create-users',
            'edit-users',
            'delete-users'
        ])) {
            $this->accessDenied('ليس لديك صلاحية لإدارة المستخدمين');
        }
    }
    
    /**
     * التحقق من صلاحية إدارة المنتجات
     */
    public function requireProductManagement() {
        $this->requireLogin();
        
        if (!$this->permissionManager->hasAnyPermission($_SESSION['user_id'], [
            'manage-products',
            'view-products',
            'create-products',
            'edit-products',
            'delete-products'
        ])) {
            $this->accessDenied('ليس لديك صلاحية لإدارة المنتجات');
        }
    }
    
    /**
     * التحقق من صلاحية إدارة الطلبات
     */
    public function requireOrderManagement() {
        $this->requireLogin();
        
        if (!$this->permissionManager->hasAnyPermission($_SESSION['user_id'], [
            'manage-orders',
            'view-orders',
            'edit-orders',
            'process-orders',
            'cancel-orders'
        ])) {
            $this->accessDenied('ليس لديك صلاحية لإدارة الطلبات');
        }
    }
    
    /**
     * التحقق من صلاحية عرض التقارير
     */
    public function requireReportsAccess() {
        $this->requireLogin();
        
        if (!$this->permissionManager->hasAnyPermission($_SESSION['user_id'], [
            'view-reports',
            'export-reports',
            'advanced-reports'
        ])) {
            $this->accessDenied('ليس لديك صلاحية لعرض التقارير');
        }
    }
    
    /**
     * التحقق من صلاحية إدارة المحتوى
     */
    public function requireContentManagement() {
        $this->requireLogin();
        
        if (!$this->permissionManager->hasAnyPermission($_SESSION['user_id'], [
            'manage-content',
            'create-content',
            'edit-content',
            'delete-content',
            'publish-content'
        ])) {
            $this->accessDenied('ليس لديك صلاحية لإدارة المحتوى');
        }
    }
    
    /**
     * التحقق من صلاحية إدارة الإعدادات
     */
    public function requireSettingsAccess() {
        $this->requireLogin();
        
        if (!$this->permissionManager->hasAnyPermission($_SESSION['user_id'], [
            'manage-settings',
            'view-settings',
            'edit-settings',
            'system-settings',
            'backup-system'
        ])) {
            $this->accessDenied('ليس لديك صلاحية لإدارة الإعدادات');
        }
    }
    
    /**
     * التحقق من صلاحية محددة
     */
    public function requirePermission($permission) {
        $this->requireLogin();
        
        if (!$this->permissionManager->hasPermission($_SESSION['user_id'], $permission)) {
            $this->accessDenied('ليس لديك الصلاحية المطلوبة: ' . $permission);
        }
    }
    
    /**
     * التحقق من أي من الصلاحيات المحددة
     */
    public function requireAnyPermission($permissions) {
        $this->requireLogin();
        
        if (!$this->permissionManager->hasAnyPermission($_SESSION['user_id'], $permissions)) {
            $this->accessDenied('ليس لديك أي من الصلاحيات المطلوبة');
        }
    }
    
    /**
     * التحقق من جميع الصلاحيات المحددة
     */
    public function requireAllPermissions($permissions) {
        $this->requireLogin();
        
        if (!$this->permissionManager->hasAllPermissions($_SESSION['user_id'], $permissions)) {
            $this->accessDenied('ليس لديك جميع الصلاحيات المطلوبة');
        }
    }
    
    /**
     * التحقق من دور محدد
     */
    public function requireRole($role) {
        $this->requireLogin();
        
        if (!$this->permissionManager->hasRole($_SESSION['user_id'], $role)) {
            $this->accessDenied('ليس لديك الدور المطلوب: ' . $role);
        }
    }
    
    /**
     * التحقق من أي من الأدوار المحددة
     */
    public function requireAnyRole($roles) {
        $this->requireLogin();
        
        $hasRole = false;
        foreach ($roles as $role) {
            if ($this->permissionManager->hasRole($_SESSION['user_id'], $role)) {
                $hasRole = true;
                break;
            }
        }
        
        if (!$hasRole) {
            $this->accessDenied('ليس لديك أي من الأدوار المطلوبة');
        }
    }
    
    /**
     * التحقق من تسجيل الدخول
     */
    private function requireLogin() {
        if (!isLoggedIn()) {
            $this->redirectToLogin();
        }
    }
    
    /**
     * إعادة التوجيه لصفحة تسجيل الدخول
     */
    private function redirectToLogin() {
        $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];
        $_SESSION['error'] = 'يجب تسجيل الدخول أولاً';
        header('Location: /admin/login.php');
        exit;
    }
    
    /**
     * رفض الوصول
     */
    private function accessDenied($message = 'ليس لديك صلاحية للوصول إلى هذه الصفحة') {
        $_SESSION['error'] = $message;
        
        // إعادة التوجيه حسب نوع المستخدم
        if ($this->permissionManager->hasPermission($_SESSION['user_id'], 'admin-access')) {
            header('Location: /admin/index.php');
        } else {
            header('Location: /index.php');
        }
        exit;
    }
    
    /**
     * التحقق من الصلاحية مع إرجاع نتيجة boolean
     */
    public function checkPermission($permission) {
        if (!isLoggedIn()) {
            return false;
        }
        
        return $this->permissionManager->hasPermission($_SESSION['user_id'], $permission);
    }
    
    /**
     * التحقق من الدور مع إرجاع نتيجة boolean
     */
    public function checkRole($role) {
        if (!isLoggedIn()) {
            return false;
        }
        
        return $this->permissionManager->hasRole($_SESSION['user_id'], $role);
    }
    
    /**
     * الحصول على جميع صلاحيات المستخدم الحالي
     */
    public function getCurrentUserPermissions() {
        if (!isLoggedIn()) {
            return [];
        }
        
        return $this->permissionManager->getUserPermissions($_SESSION['user_id']);
    }
    
    /**
     * الحصول على جميع أدوار المستخدم الحالي
     */
    public function getCurrentUserRoles() {
        if (!isLoggedIn()) {
            return [];
        }
        
        return $this->permissionManager->getUserRoles($_SESSION['user_id']);
    }
}

// إنشاء مثيل عام للاستخدام
if (isset($database)) {
    $middleware = new PermissionMiddleware($database);
}

// دوال مساعدة للاستخدام السريع
function requireAdminAccess() {
    global $middleware;
    $middleware->requireAdminAccess();
}

function requireUserManagement() {
    global $middleware;
    $middleware->requireUserManagement();
}

function requireProductManagement() {
    global $middleware;
    $middleware->requireProductManagement();
}

function requireOrderManagement() {
    global $middleware;
    $middleware->requireOrderManagement();
}

function requireReportsAccess() {
    global $middleware;
    $middleware->requireReportsAccess();
}

function requireContentManagement() {
    global $middleware;
    $middleware->requireContentManagement();
}

function requireSettingsAccess() {
    global $middleware;
    $middleware->requireSettingsAccess();
}

// تم نقل دوال requirePermission و requireRole إلى ملف functions.php لتجنب التضارب
// الدوال الأخرى متاحة عبر كلاس PermissionMiddleware

function requireAnyPermission($permissions) {
    global $middleware;
    $middleware->requireAnyPermission($permissions);
}

function requireAllPermissions($permissions) {
    global $middleware;
    $middleware->requireAllPermissions($permissions);
}

function requireAnyRole($roles) {
    global $middleware;
    $middleware->requireAnyRole($roles);
}

function checkPermission($permission) {
    global $middleware;
    return $middleware->checkPermission($permission);
}

function checkRole($role) {
    global $middleware;
    return $middleware->checkRole($role);
}

function getCurrentUserPermissions() {
    global $middleware;
    return $middleware->getCurrentUserPermissions();
}

function getCurrentUserRoles() {
    global $middleware;
    return $middleware->getCurrentUserRoles();
}
?>