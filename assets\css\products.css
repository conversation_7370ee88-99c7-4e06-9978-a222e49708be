/* ===== Product Detail Styles ===== */

/* Product Features */
.product-features {
    @apply space-y-2;
}

.product-feature-item {
    @apply flex items-center;
}

.product-feature-icon {
    @apply w-5 h-5 text-green-500 ml-2 flex-shrink-0;
}

.product-feature-text {
    @apply text-gray-700;
}

/* Product Specifications */
.product-specifications {
    @apply bg-white rounded-lg p-6;
}

.product-specifications-grid {
    @apply grid grid-cols-1 md:grid-cols-2 gap-6;
}

.product-specification-item {
    @apply flex justify-between py-3 border-b border-gray-200;
}

.product-specification-label {
    @apply font-medium text-gray-900;
}

.product-specification-value {
    @apply text-gray-600;
}

/* Product Gallery */
.product-gallery {
    @apply space-y-4;
}

.product-main-image {
    @apply w-full h-96 object-cover rounded-lg shadow-lg;
}

.product-thumbnail-grid {
    @apply grid grid-cols-4 gap-2;
}

.product-thumbnail {
    @apply w-full h-20 object-cover rounded cursor-pointer border-2 border-transparent hover:border-primary transition-colors;
}

.product-thumbnail.active {
    @apply border-primary;
}

/* Product Actions */
.product-actions {
    @apply space-y-4;
}

.product-action-buttons {
    @apply flex flex-col sm:flex-row gap-4;
}

.product-action-primary {
    @apply flex-1 bg-primary hover:bg-secondary text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105;
}

.product-action-secondary {
    @apply flex-1 bg-gray-600 hover:bg-gray-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300;
}

.product-meta-actions {
    @apply flex items-center justify-center space-x-4 space-x-reverse text-sm text-gray-600;
}

.product-meta-action {
    @apply flex items-center hover:text-primary transition-colors;
}

/* Product Tabs */
.product-tabs-nav {
    @apply -mb-px flex space-x-8 space-x-reverse;
}

.product-tab-button {
    @apply py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300 transition-colors;
}

.product-tab-button.active {
    @apply border-primary text-primary;
}

.product-tab-content {
    @apply mt-8;
}

.product-tab-pane {
    @apply hidden;
}

.product-tab-pane.active {
    @apply block;
}

/* Product Reviews */
.product-reviews {
    @apply space-y-6;
}

.product-review-item {
    @apply bg-white rounded-lg p-6 shadow-sm;
}

.product-review-header {
    @apply flex items-center justify-between mb-4;
}

.product-review-author {
    @apply flex items-center;
}

.product-review-avatar {
    @apply w-10 h-10 bg-primary bg-opacity-10 rounded-full flex items-center justify-center ml-3;
}

.product-review-rating {
    @apply flex items-center;
}

.product-review-star {
    @apply w-4 h-4;
}

.product-review-star.filled {
    @apply text-yellow-400;
}

.product-review-star.empty {
    @apply text-gray-300;
}

/* Product Price */
.product-price {
    @apply text-2xl font-bold text-primary;
}

.product-price-currency {
    @apply text-lg font-normal text-gray-600;
}

/* Product Status */
.product-status {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.product-status.available {
    @apply bg-green-100 text-green-800;
}

.product-status.unavailable {
    @apply bg-red-100 text-red-800;
}

.product-status.limited {
    @apply bg-yellow-100 text-yellow-800;
}

/* Responsive Design */
@media (max-width: 768px) {
    .product-main-image {
        @apply h-64;
    }
    
    .product-thumbnail-grid {
        @apply grid-cols-3;
    }
    
    .product-action-buttons {
        @apply flex-col;
    }
    
    .product-specifications-grid {
        @apply grid-cols-1;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading States */
.product-loading {
    @apply animate-pulse;
}

.product-skeleton {
    @apply bg-gray-200 rounded;
}

/* Print Styles */
@media print {
    .product-actions,
    .product-tabs-nav,
    .product-meta-actions {
        @apply hidden;
    }
    
    .product-tab-pane {
        @apply block !important;
    }
}