<?php
/**
 * الملف الرئيسي للموقع
 * Main Entry Point
 */

// تضمين ملف الإعدادات
require_once 'config/config.php';

// تضمين الملفات المطلوبة
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// الحصول على المسار المطلوب
$request_uri = $_SERVER['REQUEST_URI'];
$script_name = $_SERVER['SCRIPT_NAME'];
$base_path = dirname($script_name);

// إزالة المسار الأساسي والمعاملات
$path = str_replace($base_path, '', $request_uri);
$path = strtok($path, '?'); // إزالة المعاملات
$path = trim($path, '/');

// إذا كان المسار فارغ، اجعله الصفحة الرئيسية
if (empty($path)) {
    $path = 'home';
}

// تقسيم المسار
$segments = explode('/', $path);
$page = $segments[0];
$action = isset($segments[1]) ? $segments[1] : 'index';
$id = isset($segments[2]) ? $segments[2] : null;

// التحقق من طلبات API
if ($page === 'api') {
    handleApiRequest($segments);
    exit;
}

// التحقق من طلبات الإدارة
if ($page === 'admin') {
    handleAdminRequest($segments);
    exit;
}

// معالجة الصفحات العامة
handlePublicRequest($page, $action, $id);

/**
 * معالجة طلبات API
 */
function handleApiRequest($segments) {
    $endpoint = isset($segments[1]) ? $segments[1] : '';
    $method = $_SERVER['REQUEST_METHOD'];
    
    // تحديد ملف API المطلوب
    $apiFile = "api/{$endpoint}.php";
    
    if (file_exists($apiFile)) {
        require_once $apiFile;
    } else {
        jsonResponse(['error' => 'API endpoint not found'], 404);
    }
}

/**
 * معالجة طلبات الإدارة
 */
function handleAdminRequest($segments) {
    // التحقق من تسجيل الدخول للإدارة
    if (!isLoggedIn() || !hasPermission('admin')) {
        if (isset($segments[1]) && $segments[1] === 'login') {
            require_once 'admin/login.php';
            return;
        }
        redirect(SITE_URL . '/admin/login');
    }
    
    $adminPage = isset($segments[1]) ? $segments[1] : 'dashboard';
    $adminAction = isset($segments[2]) ? $segments[2] : 'index';
    
    // تحديد ملف الإدارة المطلوب
    $adminFile = "admin/{$adminPage}.php";
    
    if (file_exists($adminFile)) {
        require_once $adminFile;
    } else {
        require_once 'admin/404.php';
    }
}

/**
 * معالجة الصفحات العامة
 */
function handlePublicRequest($page, $action, $id) {
    // قائمة الصفحات المسموحة
    $allowedPages = [
        'home', 'about', 'products', 'device-features', 'contact', 'distributors',
        'testimonials', 'faq', 'support', 'auth', 'profile', 'privacy', 'terms', 'sitemap'
    ];
    
    // معالجة خاصة لصفحة تفاصيل المنتج
    if ($page === 'products' && $action && is_numeric($action)) {
        // إعداد متغير GET للمعرف
        $_GET['id'] = $action;
        require_once 'templates/product_detail.php';
        return;
    }
    
    if (!in_array($page, $allowedPages)) {
        $page = '404';
    }
    
    // تحديد ملف الصفحة المطلوبة
    $pageFile = "templates/{$page}.php";
    
    if (file_exists($pageFile)) {
        require_once $pageFile;
    } else {
        require_once 'templates/404.php';
    }
}

?>