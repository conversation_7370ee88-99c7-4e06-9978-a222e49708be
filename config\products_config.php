<?php

/**
 * إعدادات المنتجات
 * يحتوي على جميع الإعدادات المتعلقة بالمنتجات
 */

return [
    // إعدادات عامة
    'general' => [
        'products_per_page' => 12,
        'max_images_per_product' => 10,
        'default_image' => 'assets/images/no-image.png',
        'enable_reviews' => true,
        'enable_wishlist' => true,
        'enable_compare' => true,
    ],

    // إعدادات الصور
    'images' => [
        'upload_path' => 'uploads/products/',
        'allowed_types' => ['jpg', 'jpeg', 'png', 'webp'],
        'max_file_size' => 5 * 1024 * 1024, // 5MB
        'thumbnail_sizes' => [
            'small' => [150, 150],
            'medium' => [300, 300],
            'large' => [800, 600],
        ],
        'quality' => 85,
    ],

    // إعدادات المميزات
    'features' => [
        'max_features' => 20,
        'required_features' => false,
        'default_icon' => 'check',
        'icon_color' => 'green-500',
    ],

    // إعدادات المواصفات
    'specifications' => [
        'max_specifications' => 50,
        'required_specifications' => false,
        'predefined_specs' => [
            'الطاقة' => 'واط',
            'الجهد' => 'فولت',
            'التردد' => 'هرتز',
            'الوزن' => 'كيلوجرام',
            'الأبعاد' => 'سم',
            'اللون' => '',
            'المادة' => '',
            'بلد المنشأ' => '',
            'الضمان' => 'سنة',
        ],
    ],

    // إعدادات التقييمات
    'reviews' => [
        'enable_reviews' => true,
        'require_approval' => true,
        'max_rating' => 5,
        'allow_anonymous' => false,
        'rate_limit' => [
            'enabled' => true,
            'max_reviews_per_user_per_product' => 1,
            'cooldown_period' => 24 * 60 * 60, // 24 ساعة
        ],
    ],

    // إعدادات البحث والتصفية
    'search' => [
        'enable_search' => true,
        'search_fields' => ['name', 'description', 'features', 'specifications'],
        'enable_filters' => true,
        'available_filters' => [
            'category' => 'الفئة',
            'price_range' => 'نطاق السعر',
            'rating' => 'التقييم',
            'availability' => 'التوفر',
        ],
        'enable_sorting' => true,
        'sort_options' => [
            'name_asc' => 'الاسم (أ-ي)',
            'name_desc' => 'الاسم (ي-أ)',
            'price_asc' => 'السعر (الأقل أولاً)',
            'price_desc' => 'السعر (الأعلى أولاً)',
            'rating_desc' => 'التقييم (الأعلى أولاً)',
            'created_desc' => 'الأحدث أولاً',
        ],
    ],

    // إعدادات SEO
    'seo' => [
        'enable_seo' => true,
        'meta_title_template' => '{product_name} - {site_name}',
        'meta_description_template' => '{product_description}',
        'enable_structured_data' => true,
        'enable_breadcrumbs' => true,
        'enable_canonical_urls' => true,
    ],

    // إعدادات التخزين المؤقت
    'cache' => [
        'enable_cache' => true,
        'cache_duration' => 3600, // ساعة واحدة
        'cache_keys' => [
            'product_list' => 'products_list_{page}_{filters}',
            'product_detail' => 'product_detail_{id}',
            'product_reviews' => 'product_reviews_{id}',
            'related_products' => 'related_products_{id}',
        ],
    ],

    // إعدادات الأمان
    'security' => [
        'enable_csrf' => true,
        'enable_rate_limiting' => true,
        'max_requests_per_minute' => 60,
        'sanitize_input' => true,
        'validate_images' => true,
    ],

    // إعدادات الإشعارات
    'notifications' => [
        'enable_notifications' => true,
        'notify_on_new_review' => true,
        'notify_on_low_stock' => true,
        'notify_on_new_product' => true,
        'email_templates' => [
            'new_review' => 'emails/new_review.php',
            'low_stock' => 'emails/low_stock.php',
            'new_product' => 'emails/new_product.php',
        ],
    ],

    // إعدادات التصدير
    'export' => [
        'enable_export' => true,
        'formats' => ['csv', 'excel', 'pdf'],
        'max_export_records' => 1000,
        'export_fields' => [
            'id' => 'المعرف',
            'name' => 'الاسم',
            'description' => 'الوصف',
            'price' => 'السعر',
            'category' => 'الفئة',
            'status' => 'الحالة',
            'created_at' => 'تاريخ الإنشاء',
        ],
    ],

    // إعدادات API
    'api' => [
        'enable_api' => true,
        'version' => 'v1',
        'rate_limit' => 100, // طلب في الدقيقة
        'require_authentication' => true,
        'endpoints' => [
            'products' => '/api/v1/products',
            'categories' => '/api/v1/categories',
            'reviews' => '/api/v1/reviews',
        ],
    ],

    // رسائل النظام
    'messages' => [
        'success' => [
            'product_created' => 'تم إنشاء المنتج بنجاح',
            'product_updated' => 'تم تحديث المنتج بنجاح',
            'product_deleted' => 'تم حذف المنتج بنجاح',
            'review_added' => 'تم إضافة التقييم بنجاح',
            'wishlist_added' => 'تم إضافة المنتج للمفضلة',
            'wishlist_removed' => 'تم إزالة المنتج من المفضلة',
        ],
        'error' => [
            'product_not_found' => 'المنتج غير موجود',
            'invalid_data' => 'البيانات المدخلة غير صحيحة',
            'upload_failed' => 'فشل في رفع الملف',
            'permission_denied' => 'ليس لديك صلاحية للقيام بهذا الإجراء',
            'rate_limit_exceeded' => 'تم تجاوز الحد المسموح من الطلبات',
        ],
        'validation' => [
            'name_required' => 'اسم المنتج مطلوب',
            'description_required' => 'وصف المنتج مطلوب',
            'price_invalid' => 'السعر يجب أن يكون رقماً موجباً',
            'category_required' => 'فئة المنتج مطلوبة',
            'image_invalid' => 'نوع الصورة غير مدعوم',
            'file_too_large' => 'حجم الملف كبير جداً',
        ],
    ],

    // إعدادات التطوير
    'development' => [
        'debug_mode' => false,
        'log_queries' => false,
        'show_sql_errors' => false,
        'enable_profiling' => false,
    ],
];