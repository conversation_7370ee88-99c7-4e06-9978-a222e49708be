<?php
/**
 * API لجلب بيانات دور محدد
 * Get Role API
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/middleware.php';

// التحقق من تسجيل الدخول وصلاحية الإدارة
requirePermission('manage-roles');

header('Content-Type: application/json');

try {
    $roleId = $_GET['id'] ?? null;
    
    if (!$roleId) {
        throw new Exception('معرف الدور مطلوب');
    }
    
    // جلب بيانات الدور
    $stmt = $database->prepare("SELECT * FROM roles WHERE id = ?");
    $stmt->execute([$roleId]);
    $role = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$role) {
        throw new Exception('الدور غير موجود');
    }
    
    echo json_encode([
        'success' => true,
        'role' => $role
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>