<?php
require_once 'includes/layout.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !hasPermission('admin')) {
    redirect('/admin/login.php');
}

$pageTitle = 'إدارة الشروط والأحكام';
$currentPage = 'legal';
$pageDescription = 'إدارة محتوى صفحة الشروط والأحكام';
$breadcrumbs = [
    ['title' => 'الصفحات القانونية', 'url' => '#'],
    ['title' => 'الشروط والأحكام']
];

require_once '../config/database.php';
$database = new Database();
$pdo = $database->getConnection();

$message = '';
$error = '';

// إنشاء الجدول إذا لم يكن موجوداً
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS legal_pages (
        id INT PRIMARY KEY AUTO_INCREMENT,
        page_type ENUM('privacy', 'terms', 'sitemap') NOT NULL,
        title VARCHAR(255) NOT NULL,
        content LONGTEXT,
        meta_description TEXT,
        meta_keywords TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_page_type (page_type)
    )");

    // إدراج البيانات الافتراضية للشروط والأحكام
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM legal_pages WHERE page_type = 'terms'");
    $stmt->execute();
    if ($stmt->fetchColumn() == 0) {
        $default_content = '
        <div class="space-y-8">
            <section>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">مقدمة</h2>
                <p class="text-gray-600 leading-relaxed">
                    مرحباً بكم في موقع شركة الخط الأخضر. هذه الشروط والأحكام تحكم استخدامكم لموقعنا الإلكتروني وخدماتنا. باستخدام موقعنا، فإنكم توافقون على هذه الشروط والأحكام بالكامل.
                </p>
            </section>

            <section>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">التعريفات</h2>
                <div class="space-y-3">
                    <div>
                        <strong class="text-gray-700">الشركة:</strong>
                        <span class="text-gray-600">تشير إلى شركة الخط الأخضر</span>
                    </div>
                    <div>
                        <strong class="text-gray-700">الموقع:</strong>
                        <span class="text-gray-600">يشير إلى موقعنا الإلكتروني</span>
                    </div>
                    <div>
                        <strong class="text-gray-700">المستخدم:</strong>
                        <span class="text-gray-600">أي شخص يستخدم موقعنا أو خدماتنا</span>
                    </div>
                    <div>
                        <strong class="text-gray-700">الخدمات:</strong>
                        <span class="text-gray-600">جميع الخدمات المقدمة من خلال موقعنا</span>
                    </div>
                </div>
            </section>

            <section>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">شروط الاستخدام</h2>
                <div class="space-y-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-700 mb-2">الاستخدام المقبول</h3>
                        <ul class="list-disc list-inside text-gray-600 space-y-1">
                            <li>استخدام الموقع للأغراض القانونية فقط</li>
                            <li>عدم انتهاك حقوق الآخرين</li>
                            <li>احترام قوانين الملكية الفكرية</li>
                            <li>عدم نشر محتوى مسيء أو ضار</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-700 mb-2">الاستخدام المحظور</h3>
                        <ul class="list-disc list-inside text-gray-600 space-y-1">
                            <li>استخدام الموقع لأغراض غير قانونية</li>
                            <li>محاولة اختراق أو إتلاف النظام</li>
                            <li>نشر فيروسات أو برامج ضارة</li>
                            <li>انتحال شخصية الآخرين</li>
                            <li>جمع معلومات المستخدمين بطرق غير مشروعة</li>
                        </ul>
                    </div>
                </div>
            </section>

            <section>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">المنتجات والخدمات</h2>
                <div class="space-y-4">
                    <p class="text-gray-600 leading-relaxed">
                        نسعى لتقديم معلومات دقيقة حول منتجاتنا وخدماتنا، لكننا لا نضمن أن جميع المعلومات خالية من الأخطاء.
                    </p>
                    <ul class="list-disc list-inside text-gray-600 space-y-2">
                        <li>نحتفظ بالحق في تعديل أو إيقاف أي منتج أو خدمة</li>
                        <li>الأسعار قابلة للتغيير دون إشعار مسبق</li>
                        <li>التوفر غير مضمون لجميع المنتجات</li>
                        <li>قد تختلف المواصفات الفعلية عن المعروضة</li>
                    </ul>
                </div>
            </section>

            <section>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">الطلبات والمدفوعات</h2>
                <div class="space-y-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-700 mb-2">تأكيد الطلبات</h3>
                        <ul class="list-disc list-inside text-gray-600 space-y-1">
                            <li>جميع الطلبات تخضع لتأكيدنا</li>
                            <li>نحتفظ بالحق في رفض أي طلب</li>
                            <li>سيتم إرسال تأكيد الطلب عبر البريد الإلكتروني</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-700 mb-2">المدفوعات</h3>
                        <ul class="list-disc list-inside text-gray-600 space-y-1">
                            <li>نقبل طرق الدفع المعلنة على الموقع</li>
                            <li>جميع المدفوعات آمنة ومشفرة</li>
                            <li>يجب دفع كامل المبلغ قبل التسليم</li>
                        </ul>
                    </div>
                </div>
            </section>

            <section>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">الشحن والتسليم</h2>
                <ul class="list-disc list-inside text-gray-600 space-y-2">
                    <li>أوقات التسليم تقديرية وغير ملزمة</li>
                    <li>رسوم الشحن تحدد حسب الموقع والوزن</li>
                    <li>المخاطر تنتقل للعميل عند التسليم</li>
                    <li>يجب فحص الطلبية عند الاستلام</li>
                </ul>
            </section>

            <section>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">الإرجاع والاسترداد</h2>
                <div class="space-y-4">
                    <p class="text-gray-600 leading-relaxed">
                        نقبل إرجاع المنتجات وفقاً للشروط التالية:
                    </p>
                    <ul class="list-disc list-inside text-gray-600 space-y-2">
                        <li>الإرجاع خلال 14 يوم من تاريخ الاستلام</li>
                        <li>المنتج في حالته الأصلية وغير مستخدم</li>
                        <li>وجود فاتورة الشراء الأصلية</li>
                        <li>تكاليف الإرجاع على العميل ما لم يكن المنتج معيباً</li>
                        <li>الاسترداد خلال 7-14 يوم عمل</li>
                    </ul>
                </div>
            </section>

            <section>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">الضمان</h2>
                <div class="space-y-4">
                    <p class="text-gray-600 leading-relaxed">
                        نقدم ضمان محدود على منتجاتنا:
                    </p>
                    <ul class="list-disc list-inside text-gray-600 space-y-2">
                        <li>الضمان يغطي العيوب في التصنيع فقط</li>
                        <li>لا يشمل الضرر الناتج عن سوء الاستخدام</li>
                        <li>مدة الضمان تختلف حسب نوع المنتج</li>
                        <li>يجب الاحتفاظ بفاتورة الشراء</li>
                    </ul>
                </div>
            </section>

            <section>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">حدود المسؤولية</h2>
                <p class="text-gray-600 leading-relaxed mb-4">
                    مسؤوليتنا محدودة بالحد الأقصى المسموح به قانونياً:
                </p>
                <ul class="list-disc list-inside text-gray-600 space-y-2">
                    <li>لا نتحمل مسؤولية الأضرار غير المباشرة</li>
                    <li>مسؤوليتنا محدودة بقيمة المنتج المشترى</li>
                    <li>لا نضمن عدم انقطاع الخدمة</li>
                    <li>المستخدم مسؤول عن استخدامه للموقع</li>
                </ul>
            </section>

            <section>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">الملكية الفكرية</h2>
                <p class="text-gray-600 leading-relaxed mb-4">
                    جميع المحتويات على موقعنا محمية بحقوق الطبع والنشر:
                </p>
                <ul class="list-disc list-inside text-gray-600 space-y-2">
                    <li>النصوص والصور والتصاميم ملك للشركة</li>
                    <li>يُمنع النسخ أو التوزيع دون إذن</li>
                    <li>العلامات التجارية محفوظة للشركة</li>
                    <li>المحتوى المقدم من المستخدمين يخضع لشروط خاصة</li>
                </ul>
            </section>

            <section>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">تعديل الشروط</h2>
                <p class="text-gray-600 leading-relaxed">
                    نحتفظ بالحق في تعديل هذه الشروط والأحكام في أي وقت. التعديلات تصبح سارية فور نشرها على الموقع. استمرار استخدامكم للموقع يعني موافقتكم على الشروط المعدلة.
                </p>
            </section>

            <section>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">القانون الحاكم</h2>
                <p class="text-gray-600 leading-relaxed">
                    تخضع هذه الشروط والأحكام لقوانين المملكة العربية السعودية. أي نزاع ينشأ عن هذه الشروط يخضع لاختصاص المحاكم السعودية.
                </p>
            </section>

            <section>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">معلومات الاتصال</h2>
                <p class="text-gray-600 leading-relaxed mb-4">
                    لأي استفسارات حول هذه الشروط والأحكام، يرجى التواصل معنا:
                </p>
                <div class="p-4 bg-gray-50 rounded-lg">
                    <p class="text-gray-600"><strong>البريد الإلكتروني:</strong> <EMAIL></p>
                    <p class="text-gray-600"><strong>الهاتف:</strong> +966 11 123 4567</p>
                    <p class="text-gray-600"><strong>العنوان:</strong> الرياض، المملكة العربية السعودية</p>
                </div>
            </section>
        </div>';

        $stmt = $pdo->prepare("INSERT INTO legal_pages (page_type, title, content, meta_description, meta_keywords) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([
            'terms',
            'الشروط والأحكام',
            $default_content,
            'الشروط والأحكام لشركة الخط الأخضر - قواعد وشروط استخدام موقعنا وخدماتنا',
            'الشروط والأحكام, شروط الاستخدام, القوانين, الخدمات, المنتجات'
        ]);
    }

} catch (PDOException $e) {
    $error = "خطأ في قاعدة البيانات: " . $e->getMessage();
}

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_terms') {
        try {
            $title = trim($_POST['title'] ?? '');
            $content = $_POST['content'] ?? '';
            $meta_description = trim($_POST['meta_description'] ?? '');
            $meta_keywords = trim($_POST['meta_keywords'] ?? '');
            $is_active = isset($_POST['is_active']) ? 1 : 0;

            if (empty($title)) {
                throw new Exception('عنوان الصفحة مطلوب');
            }

            $stmt = $pdo->prepare("
                UPDATE legal_pages SET 
                    title = ?, 
                    content = ?, 
                    meta_description = ?, 
                    meta_keywords = ?, 
                    is_active = ?
                WHERE page_type = 'terms'
            ");
            
            if ($stmt->execute([$title, $content, $meta_description, $meta_keywords, $is_active])) {
                $message = 'تم تحديث الشروط والأحكام بنجاح';
            } else {
                throw new Exception('فشل في تحديث البيانات');
            }

        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}

// جلب البيانات الحالية
$terms_data = null;
try {
    $stmt = $pdo->prepare("SELECT * FROM legal_pages WHERE page_type = 'terms'");
    $stmt->execute();
    $terms_data = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = "خطأ في جلب البيانات: " . $e->getMessage();
}

startLayout($pageTitle, $currentPage, $pageDescription, $breadcrumbs);
showPageHeader();
?>

<div class="container mx-auto px-4 py-6">
    <!-- رسائل النجاح والخطأ -->
    <?php if ($message): ?>
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
            <?php echo htmlspecialchars($message); ?>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <?php echo htmlspecialchars($error); ?>
        </div>
    <?php endif; ?>

    <!-- نموذج تحرير الشروط والأحكام -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center justify-between mb-6">
            <h1 class="text-2xl font-bold text-gray-800">إدارة الشروط والأحكام</h1>
            <a href="/terms" target="_blank" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
                <i class="fas fa-external-link-alt ml-2"></i>
                معاينة الصفحة
            </a>
        </div>

        <form method="POST" class="space-y-6">
            <input type="hidden" name="action" value="update_terms">

            <!-- عنوان الصفحة -->
            <div>
                <label for="title" class="block text-sm font-medium text-gray-700 mb-2">عنوان الصفحة</label>
                <input type="text" id="title" name="title" 
                       value="<?php echo htmlspecialchars($terms_data['title'] ?? ''); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                       required>
            </div>

            <!-- محتوى الصفحة -->
            <div>
                <label for="content" class="block text-sm font-medium text-gray-700 mb-2">محتوى الصفحة</label>
                <textarea id="content" name="content" rows="20"
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="محتوى الشروط والأحكام..."><?php echo htmlspecialchars($terms_data['content'] ?? ''); ?></textarea>
            </div>

            <!-- وصف الميتا -->
            <div>
                <label for="meta_description" class="block text-sm font-medium text-gray-700 mb-2">وصف الميتا (SEO)</label>
                <textarea id="meta_description" name="meta_description" rows="3"
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="وصف مختصر للصفحة لمحركات البحث..."><?php echo htmlspecialchars($terms_data['meta_description'] ?? ''); ?></textarea>
            </div>

            <!-- كلمات مفتاحية -->
            <div>
                <label for="meta_keywords" class="block text-sm font-medium text-gray-700 mb-2">الكلمات المفتاحية (SEO)</label>
                <input type="text" id="meta_keywords" name="meta_keywords"
                       value="<?php echo htmlspecialchars($terms_data['meta_keywords'] ?? ''); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                       placeholder="كلمات مفتاحية مفصولة بفواصل...">
            </div>

            <!-- حالة النشر -->
            <div class="flex items-center">
                <input type="checkbox" id="is_active" name="is_active" 
                       <?php echo ($terms_data['is_active'] ?? 1) ? 'checked' : ''; ?>
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <label for="is_active" class="mr-2 block text-sm text-gray-700">
                    نشر الصفحة
                </label>
            </div>

            <!-- أزرار الحفظ -->
            <div class="flex justify-end space-x-4 space-x-reverse">
                <button type="button" onclick="history.back()" 
                        class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                    إلغاء
                </button>
                <button type="submit" 
                        class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    <i class="fas fa-save ml-2"></i>
                    حفظ التغييرات
                </button>
            </div>
        </form>
    </div>
</div>

<!-- محرر النصوص المتقدم -->
<script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    ClassicEditor
        .create(document.querySelector('#content'), {
            language: 'ar',
            toolbar: [
                'heading', '|',
                'bold', 'italic', 'underline', '|',
                'link', 'bulletedList', 'numberedList', '|',
                'outdent', 'indent', '|',
                'blockQuote', 'insertTable', '|',
                'undo', 'redo'
            ],
            heading: {
                options: [
                    { model: 'paragraph', title: 'فقرة', class: 'ck-heading_paragraph' },
                    { model: 'heading1', view: 'h1', title: 'عنوان 1', class: 'ck-heading_heading1' },
                    { model: 'heading2', view: 'h2', title: 'عنوان 2', class: 'ck-heading_heading2' },
                    { model: 'heading3', view: 'h3', title: 'عنوان 3', class: 'ck-heading_heading3' }
                ]
            }
        })
        .catch(error => {
            console.error(error);
        });
});
</script>

<?php endLayout(); ?>