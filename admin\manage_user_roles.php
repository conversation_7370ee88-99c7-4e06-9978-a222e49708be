<?php
/**
 * إدارة أدوار المستخدمين
 * Manage User Roles
 */

require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';
require_once 'includes/layout.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('manage-users');

// إعداد متغيرات التخطيط
$pageTitle = 'إدارة أدوار المستخدمين';
$pageDescription = 'تعيين وإدارة أدوار المستخدمين وصلاحياتهم';
$additionalCSS = [
    'https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css'
];
$additionalJS = [
    'https://code.jquery.com/jquery-3.6.0.min.js',
    'https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js',
    'https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js'
];
$breadcrumbs = [
    ['title' => 'المستخدمين', 'url' => 'users.php'],
    ['title' => 'إدارة أدوار المستخدمين']
];

// بدء التخطيط
startLayout();

$message = '';
$error = '';

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'assign_role':
            $userId = $_POST['user_id'] ?? null;
            $roleId = $_POST['role_id'] ?? null;
            
            if ($userId && $roleId) {
                try {
                    // التحقق من عدم وجود الربط مسبقاً
                    $existing = $database->fetch(
                        "SELECT COUNT(*) as count FROM user_roles WHERE user_id = :user_id AND role_id = :role_id",
                        ['user_id' => $userId, 'role_id' => $roleId]
                    );
                    
                    if ($existing['count'] > 0) {
                        $error = 'المستخدم لديه هذا الدور بالفعل';
                    } else {
                        $database->insert('user_roles', [
                            'user_id' => $userId,
                            'role_id' => $roleId,
                            'assigned_at' => date('Y-m-d H:i:s'),
                            'assigned_by' => $_SESSION['user_id']
                        ]);
                        
                        $message = 'تم تعيين الدور للمستخدم بنجاح';
                    }
                } catch (Exception $e) {
                    $error = 'خطأ في تعيين الدور: ' . $e->getMessage();
                }
            } else {
                $error = 'يرجى اختيار المستخدم والدور';
            }
            break;
            
        case 'remove_role':
            $userId = $_POST['user_id'] ?? null;
            $roleId = $_POST['role_id'] ?? null;
            
            if ($userId && $roleId) {
                try {
                    $database->delete(
                        'user_roles',
                        'user_id = :user_id AND role_id = :role_id',
                        ['user_id' => $userId, 'role_id' => $roleId]
                    );
                    
                    $message = 'تم إزالة الدور من المستخدم بنجاح';
                } catch (Exception $e) {
                    $error = 'خطأ في إزالة الدور: ' . $e->getMessage();
                }
            }
            break;
            
        case 'update_user_roles':
            $userId = $_POST['user_id'] ?? null;
            $roles = $_POST['roles'] ?? [];
            
            if ($userId) {
                try {
                    // حذف جميع أدوار المستخدم الحالية
                    $database->delete('user_roles', 'user_id = :user_id', ['user_id' => $userId]);
                    
                    // إضافة الأدوار الجديدة
                    foreach ($roles as $roleId) {
                        $database->insert('user_roles', [
                            'user_id' => $userId,
                            'role_id' => $roleId,
                            'assigned_at' => date('Y-m-d H:i:s'),
                            'assigned_by' => $_SESSION['user_id']
                        ]);
                    }
                    
                    $message = 'تم تحديث أدوار المستخدم بنجاح';
                } catch (Exception $e) {
                    $error = 'خطأ في تحديث الأدوار: ' . $e->getMessage();
                }
            }
            break;
    }
}

// جلب جميع المستخدمين مع أدوارهم
$users = $database->fetchAll("
    SELECT u.*, 
           GROUP_CONCAT(r.display_name SEPARATOR ', ') as roles_names,
           GROUP_CONCAT(r.id SEPARATOR ',') as role_ids
    FROM users u
    LEFT JOIN user_roles ur ON u.id = ur.user_id
    LEFT JOIN roles r ON ur.role_id = r.id
    GROUP BY u.id
    ORDER BY u.name
");

// جلب جميع الأدوار
$roles = $database->fetchAll("SELECT * FROM roles ORDER BY name");

// دالة للحصول على أدوار المستخدم
function getUserRoles($userId, $database) {
    return $database->fetchAll("
        SELECT r.* 
        FROM roles r 
        JOIN user_roles ur ON r.id = ur.role_id 
        WHERE ur.user_id = :user_id
        ORDER BY r.name
    ", ['user_id' => $userId]);
}

// دالة للحصول على صلاحيات المستخدم
function getUserPermissions($userId, $database) {
    return $database->fetchAll("
        SELECT DISTINCT p.* 
        FROM permissions p
        JOIN role_permissions rp ON p.id = rp.permission_id
        JOIN user_roles ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = :user_id
        ORDER BY p.category, p.name
    ", ['user_id' => $userId]);
}

// بدء التخطيط
// startLayout();

// عرض رأس الصفحة
showPageHeader();

// عرض الرسائل
showMessages();

?>

<style>
/* تحسين بطاقات الإحصائيات */
.stats-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    border: none;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    margin-bottom: 15px;
}

.stats-icon.users { background: linear-gradient(45deg, #4facfe, #00f2fe); }
.stats-icon.admins { background: linear-gradient(45deg, #fa709a, #fee140); }
.stats-icon.no-roles { background: linear-gradient(45deg, #ffecd2, #fcb69f); }
.stats-icon.roles { background: linear-gradient(45deg, #a8edea, #fed6e3); }

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
}

.stats-label {
    color: #7f8c8d;
    font-weight: 500;
    font-size: 0.95rem;
}

/* تحسين جدول المستخدمين */
.table-container {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    margin-top: 30px;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f1f3f4;
}

.table-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.user-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 16px;
    margin-right: 10px;
}

.user-info {
    display: flex;
    align-items: center;
}

.user-details h6 {
    margin: 0;
    font-weight: 600;
    color: #2c3e50;
}

.user-details small {
    color: #7f8c8d;
}

.role-badge {
    font-size: 0.75rem;
    margin: 2px;
    padding: 4px 8px;
    border-radius: 20px;
    font-weight: 500;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-active {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-inactive {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* تحسين الأزرار */
.action-buttons {
    display: flex;
    gap: 5px;
}

.btn-action {
    padding: 6px 12px;
    border-radius: 8px;
    font-size: 0.8rem;
    font-weight: 500;
    border: none;
    transition: all 0.3s ease;
}

.btn-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* تحسين النوافذ المنبثقة */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 20px 60px rgba(0,0,0,0.2);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
    padding: 20px 25px;
}

.modal-title {
    font-weight: 600;
    font-size: 1.2rem;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    padding: 20px 25px;
    border-top: 1px solid #f1f3f4;
}

/* تحسين قائمة الصلاحيات */
.permission-list {
    max-height: 350px;
    overflow-y: auto;
    padding-right: 10px;
}

.permission-category {
    margin-bottom: 25px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.permission-category h6 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 15px;
}

.permission-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.permission-item:last-child {
    border-bottom: none;
}

/* تحسين التجاوب */
@media (max-width: 768px) {
    .page-header h1 {
        font-size: 2rem;
    }
    
    .stats-card {
        margin-bottom: 20px;
    }
    
    .table-container {
        padding: 15px;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 8px;
    }
}
</style>
        
        <!-- إحصائيات سريعة -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-users text-white text-xl"></i>
                        </div>
                    </div>
                    <div class="mr-4">
                        <div class="text-2xl font-bold text-gray-900"><?php echo count($users); ?></div>
                        <div class="text-sm text-gray-600">إجمالي المستخدمين</div>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-pink-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-user-shield text-white text-xl"></i>
                        </div>
                    </div>
                    <div class="mr-4">
                        <div class="text-2xl font-bold text-gray-900">
                            <?php 
                            $adminCount = 0;
                            foreach ($users as $user) {
                                if (strpos($user['roles_names'] ?? '', 'مدير') !== false || 
                                    strpos($user['roles_names'] ?? '', 'admin') !== false) {
                                    $adminCount++;
                                }
                            }
                            echo $adminCount;
                            ?>
                        </div>
                        <div class="text-sm text-gray-600">المديرين</div>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-yellow-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-user-times text-white text-xl"></i>
                        </div>
                    </div>
                    <div class="mr-4">
                        <div class="text-2xl font-bold text-gray-900">
                            <?php 
                            $noRoleCount = 0;
                            foreach ($users as $user) {
                                if (empty($user['roles_names'])) {
                                    $noRoleCount++;
                                }
                            }
                            echo $noRoleCount;
                            ?>
                        </div>
                        <div class="text-sm text-gray-600">بدون أدوار</div>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-shield-alt text-white text-xl"></i>
                        </div>
                    </div>
                    <div class="mr-4">
                        <div class="text-2xl font-bold text-gray-900"><?php echo count($roles); ?></div>
                        <div class="text-sm text-gray-600">الأدوار المتاحة</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- جدول المستخدمين -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-table ml-2"></i>
                    قائمة المستخدمين
                </h3>
                <button type="button" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors" data-bs-toggle="modal" data-bs-target="#assignRoleModal">
                    <i class="fas fa-plus ml-2"></i>
                    تعيين دور جديد
                </button>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200" id="usersTable">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المستخدم</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الأدوار</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ التسجيل</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($users as $user): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10 ml-3">
                                                <div class="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center text-white font-bold">
                                                    <?php echo strtoupper(substr($user['name'], 0, 1)); ?>
                                                </div>
                                            </div>
                                            <div>
                                                <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($user['name']); ?></div>
                                                <div class="text-sm text-gray-500"><?php echo htmlspecialchars($user['email']); ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php if ($user['roles_names']): ?>
                                            <?php foreach (explode(', ', $user['roles_names']) as $roleName): ?>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 ml-1">
                                                    <?php echo htmlspecialchars($roleName); ?>
                                                </span>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">بدون دور</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center">
                                        <?php if ($user['is_active']): ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pink-100 text-pink-800">نشط</span>
                                        <?php else: ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">غير نشط</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                        <?php echo date('Y-m-d', strtotime($user['created_at'])); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button type="button" class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded-md text-sm transition-colors" 
                                                    onclick="manageUserRoles(<?php echo $user['id']; ?>)" 
                                                    title="إدارة الأدوار">
                                                <i class="fas fa-edit ml-1"></i>
                                                إدارة
                                            </button>
                                            <button type="button" class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded-md text-sm transition-colors" 
                                                    onclick="viewUserPermissions(<?php echo $user['id']; ?>)" 
                                                    title="عرض الصلاحيات">
                                                <i class="fas fa-eye ml-1"></i>
                                                عرض
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- نافذة تعيين دور جديد -->
    <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden" id="assignRoleModal">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-user-plus ml-2"></i>
                        تعيين دور جديد للمستخدم
                    </h3>
                    <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeModal('assignRoleModal')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form method="POST">
                    <input type="hidden" name="action" value="assign_role">
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                        <div>
                            <label for="assign_user_id" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-user ml-2"></i>
                                اختيار المستخدم
                            </label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" id="assign_user_id" name="user_id" required>
                                <option value="">-- اختر المستخدم --</option>
                                <?php foreach ($users as $user): ?>
                                    <option value="<?php echo $user['id']; ?>">
                                        <?php echo htmlspecialchars($user['name'] . ' (' . $user['email'] . ')'); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div>
                            <label for="assign_role_id" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-shield-alt ml-2"></i>
                                اختيار الدور
                            </label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" id="assign_role_id" name="role_id" required>
                                <option value="">-- اختر الدور --</option>
                                <?php foreach ($roles as $role): ?>
                                    <option value="<?php echo $role['id']; ?>">
                                        <?php echo htmlspecialchars($role['display_name'] . ' (' . $role['name'] . ')'); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
                        <div class="flex">
                            <i class="fas fa-info-circle text-blue-400 ml-2"></i>
                            <div class="text-sm text-blue-700">
                                <strong>ملاحظة:</strong> سيتم إضافة الدور المحدد للمستخدم المختار. يمكن للمستخدم الواحد أن يملك عدة أدوار.
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex justify-end space-x-3">
                        <button type="button" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors" onclick="closeModal('assignRoleModal')">
                            <i class="fas fa-times ml-2"></i>
                            إلغاء
                        </button>
                        <button type="submit" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md transition-colors">
                            <i class="fas fa-save ml-2"></i>
                            تعيين الدور
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- نافذة إدارة أدوار المستخدم -->
    <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden" id="manageUserRolesModal">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-user-cog ml-2"></i>
                        إدارة أدوار المستخدم
                    </h3>
                    <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeModal('manageUserRolesModal')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form method="POST" id="manageUserRolesForm">
                    <input type="hidden" name="action" value="update_user_roles">
                    <input type="hidden" name="user_id" id="manage_user_id">
                    
                    <div class="mb-6">
                        <h6 class="text-sm font-medium text-gray-700 mb-2">معلومات المستخدم:</h6>
                        <div id="user_info" class="bg-blue-50 border border-blue-200 rounded-md p-4"></div>
                    </div>
                    
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-3">الأدوار المتاحة:</label>
                        <div id="roles_checkboxes" class="space-y-3">
                            <?php foreach ($roles as $role): ?>
                                <div class="flex items-start">
                                    <input class="mt-1 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500" 
                                           type="checkbox" 
                                           name="roles[]" 
                                           value="<?php echo $role['id']; ?>"
                                           id="role_<?php echo $role['id']; ?>">
                                    <label class="mr-3 block text-sm" for="role_<?php echo $role['id']; ?>">
                                        <div class="font-medium text-gray-900"><?php echo htmlspecialchars($role['display_name']); ?></div>
                                        <div class="text-gray-500 text-xs"><?php echo htmlspecialchars($role['description'] ?? $role['name']); ?></div>
                                    </label>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                    <div class="flex justify-end space-x-3">
                        <button type="button" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors" onclick="closeModal('manageUserRolesModal')">إلغاء</button>
                        <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors">
                            <i class="fas fa-save ml-2"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- نافذة عرض صلاحيات المستخدم -->
    <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden" id="userPermissionsModal">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-shield-alt ml-2"></i>
                        صلاحيات المستخدم
                    </h3>
                    <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeModal('userPermissionsModal')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="max-h-96 overflow-y-auto">
                    <div id="user_permissions_content">
                        <div class="text-center py-8">
                            <i class="fas fa-spinner fa-spin fa-2x text-blue-500"></i>
                            <p class="mt-2 text-gray-600">جاري تحميل الصلاحيات...</p>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end mt-6">
                    <button type="button" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors" onclick="closeModal('userPermissionsModal')">إغلاق</button>
                </div>
            </div>
        </div>
    </div>
    
<script>
    // دوال إدارة النوافذ المنبثقة
    function openModal(modalId) {
        document.getElementById(modalId).classList.remove('hidden');
    }
    
    function closeModal(modalId) {
        document.getElementById(modalId).classList.add('hidden');
    }
    
    // تحديث زر تعيين دور جديد
    document.addEventListener('DOMContentLoaded', function() {
        const assignRoleBtn = document.querySelector('[data-bs-toggle="modal"][data-bs-target="#assignRoleModal"]');
        if (assignRoleBtn) {
            assignRoleBtn.removeAttribute('data-bs-toggle');
            assignRoleBtn.removeAttribute('data-bs-target');
            assignRoleBtn.onclick = function() {
                openModal('assignRoleModal');
            };
        }
        
        // تهيئة جدول البيانات
        if (typeof $ !== 'undefined' && $.fn.DataTable) {
            $('#usersTable').DataTable({
                language: {
                    "sProcessing": "جاري التحميل...",
                    "sLengthMenu": "أظهر _MENU_ مدخل",
                    "sZeroRecords": "لم يعثر على أية سجلات",
                    "sInfo": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل",
                    "sInfoEmpty": "يعرض 0 إلى 0 من أصل 0 سجل",
                    "sInfoFiltered": "(منتقاة من مجموع _MAX_ مُدخل)",
                    "sInfoPostFix": "",
                    "sSearch": "ابحث:",
                    "sUrl": "",
                    "oPaginate": {
                        "sFirst": "الأول",
                        "sPrevious": "السابق",
                        "sNext": "التالي",
                        "sLast": "الأخير"
                    }
                },
                pageLength: 10,
                order: [[0, 'asc']],
                responsive: true,
                dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                     '<"row"<"col-sm-12"tr>>' +
                     '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
                columnDefs: [
                    { targets: [4], orderable: false },
                    { targets: [2, 3], className: 'text-center' }
                ],
                drawCallback: function() {
                    // تحسين تنسيق ترقيم الصفحات
                    $('.dataTables_paginate .pagination').addClass('justify-content-center');
                    $('.dataTables_info').addClass('text-muted small');
                    $('.dataTables_length select').addClass('form-select form-select-sm d-inline w-auto');
                    $('.dataTables_filter input').addClass('form-control form-control-sm');
                }
            });
        }
    });
    
    function manageUserRoles(userId) {
        // إظهار مؤشر التحميل
        showLoadingSpinner('جاري تحميل بيانات المستخدم...');
        
        // جلب بيانات المستخدم وأدوارهم الحالية
        fetch('get_user_roles.php?user_id=' + userId)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                hideLoadingSpinner();
                
                if (data.success) {
                    const user = data.user;
                    const userRoles = data.roles;
                    
                    // تحديث معلومات المستخدم مع تحسين التصميم
                    document.getElementById('manage_user_id').value = userId;
                    document.getElementById('user_info').innerHTML = `
                        <div class="flex items-center">
                            <div class="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-content-center text-white font-bold ml-3">
                                ${user.name.charAt(0).toUpperCase()}
                            </div>
                            <div>
                                <div class="font-medium text-gray-900">${user.name}</div>
                                <div class="text-sm text-blue-600">${user.email}</div>
                                <div class="text-xs text-gray-500">تاريخ التسجيل: ${formatDate(user.created_at)}</div>
                            </div>
                        </div>
                    `;
                    
                    // إعادة تعيين جميع الخانات
                    document.querySelectorAll('input[name="roles[]"]').forEach(checkbox => {
                        checkbox.checked = false;
                        checkbox.closest('div').classList.remove('selected-role');
                    });
                    
                    // تحديد الأدوار الحالية مع تأثيرات بصرية
                    userRoles.forEach(role => {
                        const checkbox = document.getElementById('role_' + role.id);
                        if (checkbox) {
                            checkbox.checked = true;
                            checkbox.closest('div').classList.add('selected-role');
                        }
                    });
                    
                    // إظهار النافذة المنبثقة
                    openModal('manageUserRolesModal');
                } else {
                    showAlert('خطأ في جلب بيانات المستخدم: ' + data.message, 'error');
                }
            })
            .catch(error => {
                hideLoadingSpinner();
                console.error('Error:', error);
                showAlert('خطأ في الاتصال بالخادم. يرجى المحاولة مرة أخرى.', 'error');
            });
    }
    
    function viewUserPermissions(userId) {
        // عرض نافذة الصلاحيات مع مؤشر التحميل
        openModal('userPermissionsModal');
        
        // إظهار مؤشر التحميل داخل النافذة
        document.getElementById('user_permissions_content').innerHTML = `
            <div class="text-center py-8">
                <i class="fas fa-spinner fa-spin fa-2x text-blue-500"></i>
                <p class="mt-2 text-gray-600">جاري تحميل صلاحيات المستخدم...</p>
            </div>
        `;
        
        // جلب صلاحيات المستخدم
        fetch('get_user_permissions.php?user_id=' + userId, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin'
        })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    throw new Error('Response is not JSON');
                }
                
                return response.json();
            })
            .then(data => {
                console.log('Received data:', data);
                
                if (data && data.success) {
                    displayUserPermissions(data.user, data.permissions);
                } else {
                    document.getElementById('user_permissions_content').innerHTML = `
                        <div class="bg-red-50 border border-red-200 rounded-md p-4 text-center">
                            <i class="fas fa-exclamation-circle text-2xl mb-3 text-red-400"></i>
                            <h6 class="font-medium text-red-800">خطأ في تحميل البيانات</h6>
                            <p class="text-red-700">خطأ في جلب الصلاحيات: ${data.message || 'حدث خطأ غير معروف'}</p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Fetch error:', error);
                
                let errorMessage = 'تعذر الاتصال بالخادم. يرجى التحقق من الاتصال والمحاولة مرة أخرى.';
                let errorIcon = 'fas fa-wifi';
                
                if (error.message.includes('HTTP error')) {
                    errorMessage = 'خطأ في الخادم. يرجى التحقق من صحة الرابط.';
                    errorIcon = 'fas fa-server';
                } else if (error.message.includes('JSON')) {
                    errorMessage = 'خطأ في تنسيق البيانات المستلمة من الخادم.';
                    errorIcon = 'fas fa-file-code';
                } else if (error.message.includes('Failed to fetch')) {
                    errorMessage = 'فشل في الاتصال بالخادم. يرجى التحقق من الاتصال بالإنترنت.';
                    errorIcon = 'fas fa-wifi';
                }
                
                document.getElementById('user_permissions_content').innerHTML = `
                    <div class="bg-red-50 border border-red-200 rounded-md p-4 text-center">
                        <i class="${errorIcon} text-2xl mb-3 text-red-400"></i>
                        <h6 class="font-medium text-red-800">خطأ في الاتصال</h6>
                        <p class="text-red-700 mb-3">${errorMessage}</p>
                        <button class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded-md text-sm transition-colors" onclick="viewUserPermissions(${userId})">إعادة المحاولة</button>
                    </div>
                `;
            });
    }
    
    function displayUserPermissions(user, permissions) {
        let html = `
            <div class="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg mb-6 border-l-4 border-blue-500">
                <div class="flex items-center">
                    <div class="h-12 w-12 rounded-full bg-blue-500 flex items-center justify-center text-white font-bold text-lg ml-3">
                        ${user.name.charAt(0).toUpperCase()}
                    </div>
                    <div>
                        <h5 class="text-lg font-medium text-gray-900 mb-1">${user.name}</h5>
                        <p class="text-gray-600">${user.email}</p>
                    </div>
                </div>
            </div>
        `;
        
        if (permissions.length === 0) {
            html += `
                <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4 text-center">
                    <i class="fas fa-exclamation-triangle text-2xl mb-3 text-yellow-400"></i>
                    <h6 class="font-medium text-yellow-800">لا توجد صلاحيات</h6>
                    <p class="text-yellow-700">هذا المستخدم لا يملك أي صلاحيات حالياً</p>
                </div>
            `;
        } else {
            // تجميع الصلاحيات حسب الفئة
            let permissionsByCategory = {};
            permissions.forEach(permission => {
                let category = permission.category || 'صلاحيات عامة';
                if (!permissionsByCategory[category]) {
                    permissionsByCategory[category] = [];
                }
                permissionsByCategory[category].push(permission);
            });
            
            html += '<div class="space-y-4">';
            
            Object.keys(permissionsByCategory).forEach(category => {
                html += `
                    <div class="bg-white border border-gray-200 rounded-lg p-4">
                        <h6 class="text-md font-semibold text-gray-800 mb-3 flex items-center">
                            <i class="fas fa-folder-open ml-2 text-blue-500"></i>
                            ${category}
                        </h6>
                        <div class="space-y-2">
                `;
                
                permissionsByCategory[category].forEach(permission => {
                    html += `
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                            <div class="flex-1">
                                <div class="flex items-center">
                                    <i class="fas fa-check-circle text-green-500 ml-2"></i>
                                    <span class="font-medium text-gray-900">${permission.display_name}</span>
                                </div>
                                <div class="text-sm text-gray-500 mt-1">
                                    ${permission.name}
                                </div>
                            </div>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">مفعل</span>
                        </div>
                    `;
                });
                
                html += '</div></div>';
            });
            
            html += '</div>';
            
            // إضافة إحصائيات
            html += `
                <div class="mt-6">
                    <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                        <div class="flex items-center">
                            <i class="fas fa-info-circle text-blue-400 ml-2"></i>
                            <div class="text-sm text-blue-700">
                                <strong>الإجمالي:</strong> ${permissions.length} صلاحية موزعة على ${Object.keys(permissionsByCategory).length} فئة
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        document.getElementById('user_permissions_content').innerHTML = html;
    }
    
    // دوال مساعدة لتحسين تجربة المستخدم
    function showLoadingSpinner(message = 'جاري التحميل...') {
        const spinner = document.createElement('div');
        spinner.id = 'loadingSpinner';
        spinner.className = 'position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center';
        spinner.style.backgroundColor = 'rgba(0,0,0,0.5)';
        spinner.style.zIndex = '9999';
        spinner.innerHTML = `
            <div class="bg-white p-4 rounded-3 text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mb-0">${message}</p>
            </div>
        `;
        document.body.appendChild(spinner);
    }
    
    function hideLoadingSpinner() {
        const spinner = document.getElementById('loadingSpinner');
        if (spinner) {
            spinner.remove();
        }
    }
    
    function showAlert(message, type = 'info') {
        const alertClass = type === 'error' ? 'alert-danger' : 'alert-info';
        const icon = type === 'error' ? 'fas fa-exclamation-circle' : 'fas fa-info-circle';
        
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        alertDiv.style.top = '20px';
        alertDiv.style.right = '20px';
        alertDiv.style.zIndex = '9999';
        alertDiv.style.minWidth = '300px';
        alertDiv.innerHTML = `
            <i class="${icon} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        // إزالة التنبيه تلقائياً بعد 5 ثوان
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
    
    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }
    
    // إضافة تأثيرات للأدوار المحددة
    document.addEventListener('change', function(e) {
        if (e.target.name === 'roles[]') {
            const formCheck = e.target.closest('.form-check');
            if (e.target.checked) {
                formCheck.classList.add('selected-role');
            } else {
                formCheck.classList.remove('selected-role');
            }
        }
    });
</script>

<!-- إضافة أنماط CSS للتحسينات الجديدة -->
<style>
.selected-role {
    background: linear-gradient(45deg, #e3f2fd, #f3e5f5);
    border-radius: 8px;
    padding: 8px;
    border: 2px solid #2196f3;
    transition: all 0.3s ease;
}

.permissions-grid {
    display: grid;
    gap: 10px;
}

.permission-details {
    flex-grow: 1;
}

.permission-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
}

.permission-description {
    color: #7f8c8d;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.user-info-header {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

/* تحسين تنسيق DataTables */
.dataTables_wrapper {
    padding: 20px;
}

/* تنسيق الصف العلوي - البحث والقائمة المنسدلة */
.dataTables_wrapper .row:first-child {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: nowrap;
}

.dataTables_length,
.dataTables_filter {
    margin-bottom: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.dataTables_length label,
.dataTables_filter label {
    font-weight: 500;
    color: #374151;
    margin-bottom: 0;
    display: flex;
    align-items: center;
    gap: 8px;
    white-space: nowrap;
}

.dataTables_length select {
    margin: 0;
    padding: 6px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background-color: white;
    min-width: 80px;
}

.dataTables_filter input {
    margin: 0;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    width: 250px;
    min-width: 200px;
}

.dataTables_info {
    color: #6b7280;
    font-size: 14px;
    margin-top: 15px;
}

/* تنسيق الصف السفلي - المعلومات وترقيم الصفحات */
.dataTables_wrapper .row:last-child {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    flex-wrap: nowrap;
}

.dataTables_paginate {
    margin: 0;
    display: flex;
    justify-content: center;
    flex: 1;
}

.dataTables_paginate .pagination {
    margin: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: nowrap;
    gap: 2px;
}

.dataTables_paginate .page-item {
    display: inline-block;
}

.dataTables_paginate .page-link {
    color: #374151;
    border: 1px solid #d1d5db;
    padding: 8px 12px;
    border-radius: 6px;
    text-decoration: none;
    transition: all 0.2s;
    display: inline-block;
    min-width: 40px;
    text-align: center;
    white-space: nowrap;
}

.dataTables_paginate .page-link:hover {
    background-color: #f3f4f6;
    border-color: #9ca3af;
}

.dataTables_paginate .page-item.active .page-link {
    background-color: #3b82f6;
    border-color: #3b82f6;
    color: white;
}

.dataTables_paginate .page-item.disabled .page-link {
    color: #9ca3af;
    background-color: #f9fafb;
    border-color: #e5e7eb;
}

/* تحسين عرض المعلومات */
.dataTables_info {
    color: #6b7280;
    font-size: 14px;
    margin: 0;
    white-space: nowrap;
    flex-shrink: 0;
}

/* تحسين الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .dataTables_wrapper .row:first-child,
    .dataTables_wrapper .row:last-child {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .dataTables_filter input {
        width: 100%;
        min-width: auto;
    }
    
    .dataTables_paginate {
        justify-content: center;
    }
    
    .dataTables_info {
        text-align: center;
    }
}

/* تحسين الجدول */
#usersTable {
    border-collapse: separate;
    border-spacing: 0;
}

#usersTable thead th {
    background-color: #f8fafc;
    border-bottom: 2px solid #e2e8f0;
    font-weight: 600;
    color: #374151;
    padding: 12px 16px;
}

#usersTable tbody td {
    padding: 12px 16px;
    border-bottom: 1px solid #e5e7eb;
}

#usersTable tbody tr:hover {
    background-color: #f8fafc;
}
</style>

<?php endLayout(); ?>