<?php
/**
 * نظام التخزين المؤقت باستخدام Redis
 * Redis Caching System
 */

class RedisCache {
    private $redis;
    private $connected = false;
    private $prefix = 'greenline:';
    private $defaultTTL = 3600; // ساعة واحدة
    
    public function __construct($host = '127.0.0.1', $port = 6379, $password = null) {
        if (!extension_loaded('redis')) {
            error_log('Redis extension is not loaded');
            return;
        }
        
        try {
            $this->redis = new Redis();
            $this->redis->connect($host, $port);
            
            if ($password) {
                $this->redis->auth($password);
            }
            
            $this->connected = true;
            $this->redis->setOption(Redis::OPT_SERIALIZER, Redis::SERIALIZER_JSON);
            
        } catch (Exception $e) {
            error_log('Redis connection failed: ' . $e->getMessage());
            $this->connected = false;
        }
    }
    
    /**
     * التحقق من الاتصال
     */
    public function isConnected() {
        return $this->connected;
    }
    
    /**
     * تخزين قيمة
     */
    public function set($key, $value, $ttl = null) {
        if (!$this->connected) {
            return false;
        }
        
        $ttl = $ttl ?: $this->defaultTTL;
        $fullKey = $this->prefix . $key;
        
        try {
            return $this->redis->setex($fullKey, $ttl, $value);
        } catch (Exception $e) {
            error_log('Redis set error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على قيمة
     */
    public function get($key) {
        if (!$this->connected) {
            return null;
        }
        
        $fullKey = $this->prefix . $key;
        
        try {
            $value = $this->redis->get($fullKey);
            return $value === false ? null : $value;
        } catch (Exception $e) {
            error_log('Redis get error: ' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * حذف قيمة
     */
    public function delete($key) {
        if (!$this->connected) {
            return false;
        }
        
        $fullKey = $this->prefix . $key;
        
        try {
            return $this->redis->del($fullKey) > 0;
        } catch (Exception $e) {
            error_log('Redis delete error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * التحقق من وجود مفتاح
     */
    public function exists($key) {
        if (!$this->connected) {
            return false;
        }
        
        $fullKey = $this->prefix . $key;
        
        try {
            return $this->redis->exists($fullKey);
        } catch (Exception $e) {
            error_log('Redis exists error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * زيادة قيمة رقمية
     */
    public function increment($key, $value = 1) {
        if (!$this->connected) {
            return false;
        }
        
        $fullKey = $this->prefix . $key;
        
        try {
            return $this->redis->incrBy($fullKey, $value);
        } catch (Exception $e) {
            error_log('Redis increment error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تقليل قيمة رقمية
     */
    public function decrement($key, $value = 1) {
        if (!$this->connected) {
            return false;
        }
        
        $fullKey = $this->prefix . $key;
        
        try {
            return $this->redis->decrBy($fullKey, $value);
        } catch (Exception $e) {
            error_log('Redis decrement error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تخزين مؤقت مع callback
     */
    public function remember($key, $callback, $ttl = null) {
        $value = $this->get($key);
        
        if ($value !== null) {
            return $value;
        }
        
        $value = $callback();
        $this->set($key, $value, $ttl);
        
        return $value;
    }
    
    /**
     * حذف عدة مفاتيح بنمط معين
     */
    public function deletePattern($pattern) {
        if (!$this->connected) {
            return false;
        }
        
        try {
            $keys = $this->redis->keys($this->prefix . $pattern);
            if (!empty($keys)) {
                return $this->redis->del($keys);
            }
            return 0;
        } catch (Exception $e) {
            error_log('Redis delete pattern error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * مسح جميع البيانات
     */
    public function flush() {
        if (!$this->connected) {
            return false;
        }
        
        try {
            return $this->redis->flushDB();
        } catch (Exception $e) {
            error_log('Redis flush error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على معلومات الذاكرة
     */
    public function getMemoryInfo() {
        if (!$this->connected) {
            return null;
        }
        
        try {
            $info = $this->redis->info('memory');
            return [
                'used_memory' => $info['used_memory'] ?? 0,
                'used_memory_human' => $info['used_memory_human'] ?? '0B',
                'used_memory_peak' => $info['used_memory_peak'] ?? 0,
                'used_memory_peak_human' => $info['used_memory_peak_human'] ?? '0B'
            ];
        } catch (Exception $e) {
            error_log('Redis memory info error: ' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * تخزين قائمة
     */
    public function setList($key, $values, $ttl = null) {
        if (!$this->connected) {
            return false;
        }
        
        $fullKey = $this->prefix . $key;
        $ttl = $ttl ?: $this->defaultTTL;
        
        try {
            $this->redis->del($fullKey);
            foreach ($values as $value) {
                $this->redis->rPush($fullKey, $value);
            }
            $this->redis->expire($fullKey, $ttl);
            return true;
        } catch (Exception $e) {
            error_log('Redis set list error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على قائمة
     */
    public function getList($key) {
        if (!$this->connected) {
            return [];
        }
        
        $fullKey = $this->prefix . $key;
        
        try {
            return $this->redis->lRange($fullKey, 0, -1);
        } catch (Exception $e) {
            error_log('Redis get list error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * تخزين hash
     */
    public function setHash($key, $hash, $ttl = null) {
        if (!$this->connected) {
            return false;
        }
        
        $fullKey = $this->prefix . $key;
        $ttl = $ttl ?: $this->defaultTTL;
        
        try {
            $this->redis->del($fullKey);
            $this->redis->hMSet($fullKey, $hash);
            $this->redis->expire($fullKey, $ttl);
            return true;
        } catch (Exception $e) {
            error_log('Redis set hash error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على hash
     */
    public function getHash($key) {
        if (!$this->connected) {
            return [];
        }
        
        $fullKey = $this->prefix . $key;
        
        try {
            return $this->redis->hGetAll($fullKey);
        } catch (Exception $e) {
            error_log('Redis get hash error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * إغلاق الاتصال
     */
    public function close() {
        if ($this->connected && $this->redis) {
            $this->redis->close();
            $this->connected = false;
        }
    }
    
    public function __destruct() {
        $this->close();
    }
}

/**
 * فئة للتخزين المؤقت للاستعلامات
 */
class QueryCache {
    private $cache;
    private $defaultTTL = 1800; // 30 دقيقة
    
    public function __construct($cache) {
        $this->cache = $cache;
    }
    
    /**
     * تخزين نتيجة استعلام
     */
    public function cacheQuery($sql, $params, $result, $ttl = null) {
        $key = $this->generateQueryKey($sql, $params);
        $ttl = $ttl ?: $this->defaultTTL;
        
        return $this->cache->set('query:' . $key, $result, $ttl);
    }
    
    /**
     * الحصول على نتيجة استعلام محفوظة
     */
    public function getCachedQuery($sql, $params) {
        $key = $this->generateQueryKey($sql, $params);
        return $this->cache->get('query:' . $key);
    }
    
    /**
     * حذف cache للجداول المحددة
     */
    public function invalidateTable($tableName) {
        return $this->cache->deletePattern('query:*' . $tableName . '*');
    }
    
    /**
     * توليد مفتاح للاستعلام
     */
    private function generateQueryKey($sql, $params) {
        $normalized = preg_replace('/\s+/', ' ', trim($sql));
        $paramString = serialize($params);
        return md5($normalized . $paramString);
    }
}

// إنشاء مثيل عام
$redisCache = new RedisCache();
$queryCache = new QueryCache($redisCache);

/**
 * دوال مساعدة للاستخدام السريع
 */

/**
 * تخزين قيمة في الكاش
 */
function cache_set($key, $value, $ttl = null) {
    global $redisCache;
    return $redisCache->set($key, $value, $ttl);
}

/**
 * الحصول على قيمة من الكاش
 */
function cache_get($key) {
    global $redisCache;
    return $redisCache->get($key);
}

/**
 * حذف قيمة من الكاش
 */
function cache_delete($key) {
    global $redisCache;
    return $redisCache->delete($key);
}

/**
 * تخزين مؤقت مع callback
 */
function cache_remember($key, $callback, $ttl = null) {
    global $redisCache;
    return $redisCache->remember($key, $callback, $ttl);
}

/**
 * تخزين نتائج المنتجات
 */
function cache_products($category = null, $limit = null) {
    $key = 'products';
    if ($category) $key .= ':cat:' . $category;
    if ($limit) $key .= ':limit:' . $limit;
    
    return cache_remember($key, function() use ($category, $limit) {
        global $database;
        
        $sql = "SELECT p.*, pc.name as category_name FROM products p 
                LEFT JOIN product_categories pc ON p.category_id = pc.id 
                WHERE p.status = 'active'";
        $params = [];
        
        if ($category) {
            $sql .= " AND p.category_id = :category";
            $params['category'] = $category;
        }
        
        $sql .= " ORDER BY p.created_at DESC";
        
        if ($limit) {
            $sql .= " LIMIT :limit";
            $params['limit'] = $limit;
        }
        
        return $database->fetchAll($sql, $params);
    }, 1800); // 30 دقيقة
}

/**
 * تخزين إعدادات الموقع
 */
function cache_settings() {
    return cache_remember('site_settings', function() {
        global $database;
        $settings = $database->fetchAll("SELECT setting_key, setting_value FROM settings");
        $result = [];
        foreach ($settings as $setting) {
            $result[$setting['setting_key']] = $setting['setting_value'];
        }
        return $result;
    }, 3600); // ساعة واحدة
}

/**
 * مسح كاش المنتجات
 */
function clear_products_cache() {
    global $redisCache;
    return $redisCache->deletePattern('products*');
}

/**
 * مسح كاش الإعدادات
 */
function clear_settings_cache() {
    return cache_delete('site_settings');
}

/**
 * تخزين إحصائيات الموقع
 */
function cache_stats() {
    return cache_remember('site_stats', function() {
        global $database;
        
        $stats = [];
        $stats['total_products'] = $database->fetchColumn("SELECT COUNT(*) FROM products WHERE status = 'active'");
        $stats['total_categories'] = $database->fetchColumn("SELECT COUNT(*) FROM product_categories WHERE status = 'active'");
        $stats['total_reviews'] = $database->fetchColumn("SELECT COUNT(*) FROM reviews WHERE status = 'approved'");
        $stats['total_users'] = $database->fetchColumn("SELECT COUNT(*) FROM users WHERE status = 'active'");
        
        return $stats;
    }, 1800); // 30 دقيقة
}

?>