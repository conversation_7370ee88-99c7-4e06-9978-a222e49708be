<?php
/**
 * ملف تهيئة الصلاحيات الافتراضية
 * Initialize Default Permissions
 */

require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../includes/permissions.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();

// التحقق من الدور - يجب أن يكون المستخدم super-admin أو admin
global $permissionManager;
$userId = getCurrentUserId();
if (!$userId || (!$permissionManager->hasRole($userId, 'super-admin') && !$permissionManager->hasRole($userId, 'admin'))) {
    die('ليس لديك صلاحية للوصول إلى هذه الصفحة. يجب أن تكون مدير عام أو مدير.');
}

$message = '';
$error = '';

// معالجة طلب التهيئة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['initialize_permissions'])) {
    try {
        // إنشاء مدير الصلاحيات
        $permissionManager = new PermissionManager($database);
        
        // قراءة ملف SQL للتهيئة
        $sqlFile = '../setup/permissions_setup.sql';
        if (!file_exists($sqlFile)) {
            throw new Exception('ملف إعداد قاعدة البيانات غير موجود');
        }
        
        $sql = file_get_contents($sqlFile);
        if ($sql === false) {
            throw new Exception('فشل في قراءة ملف إعداد قاعدة البيانات');
        }
        
        // تقسيم الاستعلامات
        $queries = array_filter(array_map('trim', explode(';', $sql)));
        
        $successCount = 0;
        $errorCount = 0;
        $errors = [];
        
        foreach ($queries as $query) {
            if (empty($query) || strpos($query, '--') === 0 || strpos($query, 'DELIMITER') === 0) {
                continue;
            }
            
            try {
                $database->query($query);
                $successCount++;
            } catch (Exception $e) {
                $errorCount++;
                $errors[] = $e->getMessage();
            }
        }
        
        if ($errorCount === 0) {
            // تعيين دور المدير العام للمستخدم الحالي إذا لم يكن لديه أدوار
            $currentUserId = getCurrentUserId();
            $userRoles = $permissionManager->getUserRoles($currentUserId);
            
            if (empty($userRoles)) {
                $assignResult = $permissionManager->assignRole($currentUserId, 'super-admin', $currentUserId);
                if ($assignResult['success']) {
                    $message = "تم تهيئة الصلاحيات والأدوار الافتراضية بنجاح! تم تنفيذ {$successCount} استعلام.<br>تم تعيين دور المدير العام لحسابك";
                } else {
                    $message = "تم تهيئة الصلاحيات والأدوار الافتراضية بنجاح! تم تنفيذ {$successCount} استعلام.";
                }
            } else {
                $message = "تم تهيئة الصلاحيات والأدوار الافتراضية بنجاح! تم تنفيذ {$successCount} استعلام.";
            }
        } else {
            $error = "تم إعداد النظام مع بعض الأخطاء. نجح: {$successCount}, فشل: {$errorCount}";
            if (!empty($errors)) {
                $error .= "<br>الأخطاء: " . implode('<br>', array_slice($errors, 0, 5));
            }
        }
        
    } catch (Exception $e) {
        $error = 'خطأ في تهيئة الصلاحيات: ' . $e->getMessage();
    }
}

// إعداد الصفحة
$pageTitle = 'تهيئة الصلاحيات';
$currentPage = 'permissions';

// إعداد مسار التنقل
$breadcrumbs = [
    ['title' => 'لوحة التحكم', 'url' => '/admin/'],
    ['title' => 'النظام', 'url' => '#'],
    ['title' => 'تهيئة الصلاحيات', 'url' => '']
];

require_once 'includes/layout.php';
startLayout();
showPageHeader();
showMessages();
?>

<div class="max-w-4xl mx-auto">
    <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">تهيئة نظام الصلاحيات</h2>
        
        <div class="mb-6">
            <p class="text-gray-600 mb-4">
                هذه الصفحة تقوم بتهيئة نظام الصلاحيات والأدوار الافتراضية في النظام. 
                يتم إنشاء الجداول المطلوبة والصلاحيات الأساسية والأدوار الافتراضية.
            </p>
            
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="mr-3">
                        <h3 class="text-sm font-medium text-yellow-800">تنبيه</h3>
                        <div class="mt-2 text-sm text-yellow-700">
                            <p>تأكد من عمل نسخة احتياطية من قاعدة البيانات قبل تشغيل هذه العملية.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <form method="POST" onsubmit="return confirm('هل أنت متأكد من تهيئة نظام الصلاحيات؟')">
            <button type="submit" name="initialize_permissions" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                <i class="fas fa-cogs ml-2"></i>
                تهيئة نظام الصلاحيات
            </button>
        </form>
        
        <div class="mt-8">
            <h3 class="text-lg font-medium text-gray-900 mb-4">ما سيتم إنشاؤه:</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="border border-gray-200 rounded-lg p-4">
                    <h4 class="font-medium text-gray-900 mb-2">الجداول</h4>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• permissions - جدول الصلاحيات</li>
                        <li>• roles - جدول الأدوار</li>
                        <li>• role_permissions - ربط الأدوار بالصلاحيات</li>
                        <li>• user_roles - ربط المستخدمين بالأدوار</li>
                        <li>• user_permissions - صلاحيات مباشرة للمستخدمين</li>
                        <li>• permission_logs - سجل تغييرات الصلاحيات</li>
                    </ul>
                </div>
                
                <div class="border border-gray-200 rounded-lg p-4">
                    <h4 class="font-medium text-gray-900 mb-2">الأدوار الافتراضية</h4>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• super-admin - المدير العام</li>
                        <li>• admin - مدير</li>
                        <li>• editor - محرر</li>
                        <li>• moderator - مشرف</li>
                        <li>• user - مستخدم عادي</li>
                    </ul>
                </div>
            </div>
            
            <div class="mt-6 border border-gray-200 rounded-lg p-4">
                <h4 class="font-medium text-gray-900 mb-2">الصلاحيات الافتراضية</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                    <div>
                        <strong>إدارة النظام:</strong>
                        <ul class="mt-1 space-y-1">
                            <li>• admin - الوصول للوحة التحكم</li>
                            <li>• manage-permissions - إدارة الصلاحيات</li>
                            <li>• manage-settings - إدارة الإعدادات</li>
                            <li>• view-logs - عرض السجلات</li>
                        </ul>
                    </div>
                    
                    <div>
                        <strong>إدارة المستخدمين:</strong>
                        <ul class="mt-1 space-y-1">
                            <li>• view-users - عرض المستخدمين</li>
                            <li>• create-users - إنشاء مستخدمين</li>
                            <li>• edit-users - تعديل المستخدمين</li>
                            <li>• delete-users - حذف المستخدمين</li>
                            <li>• manage-users - إدارة شاملة للمستخدمين</li>
                        </ul>
                    </div>
                    
                    <div>
                        <strong>إدارة المحتوى:</strong>
                        <ul class="mt-1 space-y-1">
                            <li>• view-products - عرض المنتجات</li>
                            <li>• create-products - إنشاء منتجات</li>
                            <li>• edit-products - تعديل المنتجات</li>
                            <li>• delete-products - حذف المنتجات</li>
                            <li>• manage-categories - إدارة الفئات</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-6 flex space-x-4 space-x-reverse">
            <a href="/admin/permissions.php" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700">
                <i class="fas fa-shield-alt ml-2"></i>
                إدارة الصلاحيات
            </a>
            <a href="/admin/" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                <i class="fas fa-home ml-2"></i>
                العودة للوحة التحكم
            </a>
        </div>
    </div>
</div>

<?php
endLayout();
?>