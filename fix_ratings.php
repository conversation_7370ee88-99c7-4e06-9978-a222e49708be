<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<h1>فحص وإصلاح بيانات التقييمات</h1>";

// جلب جميع المنتجات
$products = $database->fetchAll("SELECT id, name, rating, average_rating, reviews_count FROM products ORDER BY id");

echo "<h2>حالة المنتجات الحالية:</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>ID</th><th>اسم المنتج</th><th>التقييم القديم</th><th>متوسط التقييم</th><th>عدد المراجعات</th><th>التقييمات الفعلية</th><th>المتوسط المحسوب</th></tr>";

foreach ($products as $product) {
    // جلب التقييمات الفعلية من جدول reviews
    $reviews = $database->fetchAll(
        "SELECT rating FROM reviews WHERE product_id = ? AND is_approved = 1", 
        [$product['id']]
    );
    
    $actual_count = count($reviews);
    $actual_average = 0;
    $ratings_list = [];
    
    if ($actual_count > 0) {
        $total_rating = 0;
        foreach ($reviews as $review) {
            $total_rating += $review['rating'];
            $ratings_list[] = $review['rating'];
        }
        $actual_average = round($total_rating / $actual_count, 1);
    }
    
    echo "<tr>";
    echo "<td>{$product['id']}</td>";
    echo "<td>" . htmlspecialchars($product['name']) . "</td>";
    echo "<td>{$product['rating']}</td>";
    echo "<td>{$product['average_rating']}</td>";
    echo "<td>{$product['reviews_count']}</td>";
    echo "<td>" . implode(', ', $ratings_list) . " (العدد: $actual_count)</td>";
    echo "<td>$actual_average</td>";
    echo "</tr>";
}

echo "</table>";

// إصلاح البيانات
echo "<h2>إصلاح البيانات:</h2>";

foreach ($products as $product) {
    // حساب التقييم الصحيح
    $stats = $database->fetch(
        "SELECT AVG(rating) as avg_rating, COUNT(*) as review_count 
         FROM reviews 
         WHERE product_id = ? AND is_approved = 1",
        [$product['id']]
    );
    
    if ($stats && $stats['review_count'] > 0) {
        $new_average = round($stats['avg_rating'], 1);
        $new_count = $stats['review_count'];
        
        // تحديث المنتج
        $database->update('products', [
            'average_rating' => $new_average,
            'reviews_count' => $new_count,
            'rating' => $new_average  // تحديث العمود القديم أيضاً
        ], 'id = ?', [$product['id']]);
        
        echo "<p>تم تحديث المنتج {$product['id']}: متوسط التقييم = $new_average، عدد المراجعات = $new_count</p>";
    } else {
        // لا توجد مراجعات معتمدة
        $database->update('products', [
            'average_rating' => 0,
            'reviews_count' => 0,
            'rating' => 0
        ], 'id = ?', [$product['id']]);
        
        echo "<p>تم تصفير تقييم المنتج {$product['id']} (لا توجد مراجعات معتمدة)</p>";
    }
}

echo "<h2>التحقق من جدول reviews:</h2>";
$all_reviews = $database->fetchAll(
    "SELECT r.*, p.name as product_name 
     FROM reviews r 
     LEFT JOIN products p ON r.product_id = p.id 
     ORDER BY r.product_id, r.created_at DESC"
);

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>ID</th><th>المنتج</th><th>التقييم</th><th>التعليق</th><th>معتمد</th><th>مرفوض</th><th>تاريخ الإنشاء</th></tr>";

foreach ($all_reviews as $review) {
    $approved = $review['is_approved'] ? 'نعم' : 'لا';
    $rejected = $review['is_rejected'] ? 'نعم' : 'لا';
    
    echo "<tr>";
    echo "<td>{$review['id']}</td>";
    echo "<td>" . htmlspecialchars($review['product_name']) . "</td>";
    echo "<td>{$review['rating']}</td>";
    echo "<td>" . htmlspecialchars(substr($review['comment'], 0, 50)) . "...</td>";
    echo "<td>$approved</td>";
    echo "<td>$rejected</td>";
    echo "<td>{$review['created_at']}</td>";
    echo "</tr>";
}

echo "</table>";

echo "<p><strong>تم الانتهاء من فحص وإصلاح البيانات!</strong></p>";
echo "<p><a href='products/6'>العودة إلى صفحة المنتج</a></p>";
?>