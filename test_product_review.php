<?php
require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<h2>اختبار إرسال تقييم من صفحة المنتج</h2>";

// محاكاة إرسال تقييم
if ($_POST && isset($_POST['test_review'])) {
    echo "<h3>نتيجة الاختبار:</h3>";
    
    // محاكاة بيانات المستخدم المسجل
    $_SESSION['user_id'] = 1; // افتراض وجود مستخدم برقم 1
    
    $result = saveProductReview(8, 5, 'تقييم تجريبي', 'هذا تقييم تجريبي من صفحة الاختبار');
    
    if ($result === true) {
        echo "<p style='color: green;'>تم حفظ التقييم بنجاح!</p>";
    } else {
        echo "<p style='color: red;'>خطأ: " . $result . "</p>";
    }
}

// عرض نموذج الاختبار
?>
<form method="POST">
    <h3>اختبار إرسال تقييم:</h3>
    <p>سيتم إرسال تقييم تجريبي للمنتج رقم 8</p>
    <button type="submit" name="test_review" style="background: #059669; color: white; padding: 10px 20px; border: none; border-radius: 5px;">
        إرسال تقييم تجريبي
    </button>
</form>

<br><br>
<a href="check_reviews.php">عرض جميع التقييمات</a><br>
<a href="products/8">عرض صفحة المنتج</a><br>
<a href="test_api_review.php">اختبار API التقييمات</a>