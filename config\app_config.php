<?php
/**
 * Application Configuration
 * Central configuration file for all application settings
 */

return [
    'app' => [
        'name' => 'Greenline Admin Panel',
        'version' => '1.0.0',
        'debug' => true, // Set to false in production
        'timezone' => 'UTC',
        'url' => 'http://localhost/greenline_php'
    ],
    
    'database' => [
        'host' => 'localhost',
        'port' => 3306,
        'name' => 'greenline_db',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8mb4',
        'options' => [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    ],
    
    'session' => [
        'name' => 'greenline_session',
        'lifetime' => 7200, // 2 hours
        'path' => '/',
        'domain' => '',
        'secure' => false, // Set to true for HTTPS
        'httponly' => true,
        'samesite' => 'Lax'
    ],
    
    'security' => [
        'password_min_length' => 8,
        'max_login_attempts' => 5,
        'lockout_duration' => 900, // 15 minutes
        'csrf_token_name' => '_token',
        'allowed_file_types' => ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'txt'],
        'max_file_size' => 5242880 // 5MB
    ],
    
    'upload' => [
        'path' => __DIR__ . '/../uploads',
        'url' => '/greenline_php/uploads',
        'temp_path' => __DIR__ . '/../temp',
        'max_size' => 5242880, // 5MB
        'allowed_types' => [
            'image' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
            'document' => ['pdf', 'doc', 'docx', 'txt', 'rtf'],
            'archive' => ['zip', 'rar', '7z']
        ]
    ],
    
    'mail' => [
        'driver' => 'smtp', // smtp, mail, sendmail
        'host' => 'smtp.gmail.com',
        'port' => 587,
        'username' => '',
        'password' => '',
        'encryption' => 'tls', // tls, ssl
        'from' => [
            'address' => '<EMAIL>',
            'name' => 'Greenline System'
        ]
    ],
    
    'logging' => [
        'enabled' => true,
        'level' => 'debug', // debug, info, warning, error
        'path' => __DIR__ . '/../logs',
        'max_files' => 30,
        'max_size' => 10485760 // 10MB
    ],
    
    'cache' => [
        'enabled' => true,
        'driver' => 'file', // file, redis, memcached
        'path' => __DIR__ . '/../cache',
        'ttl' => 3600 // 1 hour
    ],
    
    'pagination' => [
        'per_page' => 20,
        'max_per_page' => 100
    ],
    
    'features' => [
        'maintenance_mode' => false,
        'user_registration' => false,
        'email_verification' => false,
        'two_factor_auth' => false
    ]
];
?>