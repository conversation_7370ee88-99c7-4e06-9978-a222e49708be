# دليل استخدام دوال POST المساعدة
## POST Helper Functions Usage Guide

تم إنشاء ملف `includes/post_helper.php` لحل مشكلة "Undefined array key" التي تظهر عند التعامل مع بيانات `$_POST` في PHP.

## المشكلة الأصلية

```php
// هذا الكود يسبب خطأ "Undefined array key" إذا لم يكن المفتاح موجوداً
$name = $_POST['name'];
$email = $_POST['email'];
```

## الحلول المتاحة

### 1. الحل التقليدي (Null Coalescing Operator)

```php
$name = $_POST['name'] ?? '';
$email = $_POST['email'] ?? '';
```

### 2. الحل المحسن (استخدام الدوال المساعدة)

```php
// تضمين ملف الدوال المساعدة
require_once 'includes/post_helper.php';

// استخدام الدوال المساعدة
$name = getPost('name');
$email = getPost('email');
```

## الدوال المتاحة

### 1. `getPost($key, $default = '')`
الحصول على قيمة من $_POST مع قيمة افتراضية

```php
$name = getPost('name', 'غير محدد');
$age = getPost('age', 0);
```

### 2. `getPostSanitized($key, $default = '')`
الحصول على قيمة من $_POST وتنظيفها من HTML

```php
$comment = getPostSanitized('comment');
$title = getPostSanitized('title', 'عنوان افتراضي');
```

### 3. `getPostNumeric($key, $default = 0, $type = 'int')`
الحصول على قيمة رقمية من $_POST

```php
$price = getPostNumeric('price', 0, 'float');
$quantity = getPostNumeric('quantity', 1, 'int');
```

### 4. `getPostCheckbox($key)`
التحقق من وجود checkbox

```php
$isActive = getPostCheckbox('is_active');
$isFeatured = getPostCheckbox('is_featured');
```

### 5. `getPostArray($key, $default = [])`
الحصول على مصفوفة من $_POST

```php
$categories = getPostArray('categories', []);
$tags = getPostArray('tags');
```

### 6. `getPostNested($parentKey, $childKey, $default = '')`
الحصول على قيمة من مصفوفة متداخلة

```php
$street = getPostNested('address', 'street');
$city = getPostNested('address', 'city', 'الرياض');
```

### 7. `getPostEmail($key)`
التحقق من صحة البريد الإلكتروني

```php
$email = getPostEmail('email');
if ($email === false) {
    // البريد الإلكتروني غير صحيح
}
```

### 8. `getPostUrl($key)`
التحقق من صحة URL

```php
$website = getPostUrl('website');
if ($website === false) {
    // الرابط غير صحيح
}
```

### 9. `getPostWithLimit($key, $maxLength, $default = '')`
الحصول على قيمة مع تحديد الحد الأقصى للطول

```php
$title = getPostWithLimit('title', 100);
$description = getPostWithLimit('description', 500, 'وصف افتراضي');
```

### 10. `hasRequiredPostData($requiredKeys)`
التحقق من وجود جميع المفاتيح المطلوبة

```php
$required = ['name', 'email', 'phone'];
if (!hasRequiredPostData($required)) {
    // بعض البيانات المطلوبة مفقودة
}
```

### 11. `getAllPostDataSanitized($excludeKeys = [])`
الحصول على جميع بيانات $_POST مع تنظيفها

```php
$data = getAllPostDataSanitized(['password', 'confirm_password']);
```

## أمثلة عملية

### مثال 1: نموذج تسجيل المستخدم

```php
require_once 'includes/post_helper.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من وجود البيانات المطلوبة
    $required = ['name', 'email', 'password'];
    if (!hasRequiredPostData($required)) {
        $error = 'جميع الحقول مطلوبة';
    } else {
        // الحصول على البيانات بشكل آمن
        $name = getPostSanitized('name');
        $email = getPostEmail('email');
        $password = getPost('password');
        $phone = getPostSanitized('phone');
        $isActive = getPostCheckbox('is_active');
        
        // التحقق من صحة البريد الإلكتروني
        if ($email === false) {
            $error = 'البريد الإلكتروني غير صحيح';
        } else {
            // حفظ البيانات
            // ...
        }
    }
}
```

### مثال 2: نموذج معلومات التواصل

```php
require_once 'includes/post_helper.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $sectionKey = getPost('section_key');
    $title = getPostSanitized('title');
    $content = getPostSanitized('content');
    $isActive = getPostCheckbox('is_active');
    
    // معالجة البيانات المتداخلة
    $data = [];
    switch ($sectionKey) {
        case 'address':
            $data = [
                'street' => getPostNested('data', 'street'),
                'city' => getPostNested('data', 'city'),
                'country' => getPostNested('data', 'country', 'السعودية'),
                'lat' => getPostNumeric('data[lat]', 0, 'float'),
                'lng' => getPostNumeric('data[lng]', 0, 'float')
            ];
            break;
            
        case 'social_media':
            $data = [
                'facebook' => getPostUrl('data[facebook]') ?: '',
                'twitter' => getPostUrl('data[twitter]') ?: '',
                'instagram' => getPostUrl('data[instagram]') ?: ''
            ];
            break;
    }
}
```

## التطبيق على الملفات الموجودة

لتطبيق هذه الدوال على الملفات الموجودة:

1. أضف `require_once 'includes/post_helper.php';` في بداية الملف
2. استبدل `$_POST['key']` بـ `getPost('key')`
3. استخدم الدوال المناسبة حسب نوع البيانات

## فوائد استخدام هذه الدوال

1. **منع أخطاء "Undefined array key"**
2. **تنظيف البيانات تلقائياً**
3. **التحقق من صحة البيانات**
4. **كود أكثر قابلية للقراءة**
5. **أمان أفضل**
6. **سهولة الصيانة**

## ملاحظات مهمة

- تأكد من تضمين ملف `post_helper.php` في جميع الملفات التي تتعامل مع `$_POST`
- استخدم الدالة المناسبة حسب نوع البيانات المتوقعة
- لا تنس التحقق من صحة البيانات المهمة مثل البريد الإلكتروني والروابط