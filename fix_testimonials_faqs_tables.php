<?php
/**
 * Fix testimonials and faqs table structures
 */

require_once __DIR__ . '/config/database.php';

try {
    $database = new Database();
    $pdo = $database->getConnection();
    
    echo "Checking and fixing table structures...\n";
    
    // Check testimonials table
    echo "\n=== Checking testimonials table ===\n";
    $stmt = $pdo->query("SHOW TABLES LIKE 'testimonials'");
    if ($stmt->rowCount() == 0) {
        echo "Creating testimonials table...\n";
        $sql = "CREATE TABLE testimonials (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255),
            company VARCHAR(255),
            message TEXT NOT NULL,
            rating INT DEFAULT 5,
            status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
            image VARCHAR(500),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        $pdo->exec($sql);
        echo "✅ testimonials table created successfully!\n";
    } else {
        echo "testimonials table exists. Checking columns...\n";
        
        // Check columns
        $stmt = $pdo->query("DESCRIBE testimonials");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $hasStatus = false;
        $hasCreatedAt = false;
        
        foreach ($columns as $column) {
            echo "- " . $column['Field'] . " (" . $column['Type'] . ")\n";
            if ($column['Field'] === 'status') {
                $hasStatus = true;
            }
            if ($column['Field'] === 'created_at') {
                $hasCreatedAt = true;
            }
        }
        
        // Add missing columns
        if (!$hasStatus) {
            echo "Adding status column to testimonials...\n";
            $pdo->exec("ALTER TABLE testimonials ADD COLUMN status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending'");
            echo "✅ status column added to testimonials!\n";
        }
        
        if (!$hasCreatedAt) {
            echo "Adding created_at column to testimonials...\n";
            $pdo->exec("ALTER TABLE testimonials ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP");
            echo "✅ created_at column added to testimonials!\n";
        }
    }
    
    // Check faqs table
    echo "\n=== Checking faqs table ===\n";
    $stmt = $pdo->query("SHOW TABLES LIKE 'faqs'");
    if ($stmt->rowCount() == 0) {
        echo "Creating faqs table...\n";
        $sql = "CREATE TABLE faqs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            question TEXT NOT NULL,
            answer TEXT NOT NULL,
            category VARCHAR(255),
            status ENUM('active', 'inactive') DEFAULT 'active',
            sort_order INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        $pdo->exec($sql);
        echo "✅ faqs table created successfully!\n";
    } else {
        echo "faqs table exists. Checking columns...\n";
        
        // Check columns
        $stmt = $pdo->query("DESCRIBE faqs");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $hasStatus = false;
        $hasSortOrder = false;
        
        foreach ($columns as $column) {
            echo "- " . $column['Field'] . " (" . $column['Type'] . ")\n";
            if ($column['Field'] === 'status') {
                $hasStatus = true;
            }
            if ($column['Field'] === 'sort_order') {
                $hasSortOrder = true;
            }
        }
        
        // Add missing columns
        if (!$hasStatus) {
            echo "Adding status column to faqs...\n";
            $pdo->exec("ALTER TABLE faqs ADD COLUMN status ENUM('active', 'inactive') DEFAULT 'active'");
            echo "✅ status column added to faqs!\n";
        }
        
        if (!$hasSortOrder) {
            echo "Adding sort_order column to faqs...\n";
            $pdo->exec("ALTER TABLE faqs ADD COLUMN sort_order INT DEFAULT 0");
            echo "✅ sort_order column added to faqs!\n";
        }
    }
    
    echo "\n=== Final table structures ===\n";
    
    echo "\nTestimonials table:\n";
    $stmt = $pdo->query("DESCRIBE testimonials");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($columns as $column) {
        echo "- " . $column['Field'] . " (" . $column['Type'] . ")\n";
    }
    
    echo "\nFAQs table:\n";
    $stmt = $pdo->query("DESCRIBE faqs");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($columns as $column) {
        echo "- " . $column['Field'] . " (" . $column['Type'] . ")\n";
    }
    
    echo "\n✅ All tables are now properly structured!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>