<?php
/**
 * اختبار حفظ user_id في المراجعات
 * Test saving user_id in reviews
 */

require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/auth.php';

// بدء الجلسة
session_start();

echo "<h1>اختبار حفظ user_id في المراجعات</h1>";
echo "<hr>";

// 1. فحص المستخدم الحالي
echo "<h2>1. معلومات المستخدم الحالي:</h2>";
if (isLoggedIn()) {
    $user = getCurrentUser();
    echo "✅ المستخدم مسجل الدخول<br>";
    echo "- معرف المستخدم: " . $user['id'] . "<br>";
    echo "- الاسم: " . $user['name'] . "<br>";
    echo "- البريد الإلكتروني: " . $user['email'] . "<br>";
    echo "- الدور: " . $user['role'] . "<br>";
} else {
    echo "❌ المستخدم غير مسجل الدخول<br>";
    echo "<p><a href='admin/login.php'>تسجيل الدخول</a></p>";
    exit;
}
echo "<hr>";

// 2. فحص المنتجات المتاحة
echo "<h2>2. فحص المنتجات المتاحة:</h2>";
$database = new Database();
$products = $database->fetchAll("SELECT id, name FROM products WHERE is_active = 1 AND is_visible = 1 LIMIT 5");

if (!empty($products)) {
    echo "✅ المنتجات المتاحة:<br>";
    foreach ($products as $product) {
        echo "- المنتج #{$product['id']}: {$product['name']}<br>";
    }
    $test_product_id = $products[0]['id'];
    echo "<br><strong>سيتم استخدام المنتج #{$test_product_id} للاختبار</strong><br>";
} else {
    echo "❌ لا توجد منتجات متاحة<br>";
    exit;
}
echo "<hr>";

// 3. حذف أي مراجعة سابقة للاختبار
echo "<h2>3. حذف المراجعات السابقة للاختبار:</h2>";
try {
    $deleted = $database->query(
        "DELETE FROM reviews WHERE product_id = ? AND user_id = ?",
        [$test_product_id, $user['id']]
    );
    echo "✅ تم حذف " . $deleted->rowCount() . " مراجعة سابقة<br>";
} catch (Exception $e) {
    echo "❌ خطأ في حذف المراجعات السابقة: " . $e->getMessage() . "<br>";
}
echo "<hr>";

// 4. اختبار حفظ مراجعة جديدة
echo "<h2>4. اختبار حفظ مراجعة جديدة:</h2>";
$test_data = [
    'product_id' => $test_product_id,
    'user_id' => $user['id'],
    'customer_name' => $user['name'],
    'customer_email' => $user['email'],
    'rating' => 5,
    'review_text' => 'هذا اختبار لحفظ المراجعة مع user_id',
    'review_title' => 'اختبار المراجعة'
];

echo "<strong>البيانات المرسلة:</strong><br>";
foreach ($test_data as $key => $value) {
    echo "- $key: $value<br>";
}

try {
    $result = saveUserReview(
        $test_data['product_id'],
        $test_data['user_id'],
        $test_data['customer_name'],
        $test_data['customer_email'],
        $test_data['rating'],
        $test_data['review_text'],
        $test_data['review_title']
    );
    
    if ($result === true || is_numeric($result)) {
        echo "<br>✅ تم حفظ المراجعة بنجاح!<br>";
        if (is_numeric($result)) {
            echo "- معرف المراجعة: $result<br>";
        }
    } else {
        echo "<br>❌ فشل في حفظ المراجعة: $result<br>";
    }
} catch (Exception $e) {
    echo "<br>❌ خطأ في حفظ المراجعة: " . $e->getMessage() . "<br>";
}
echo "<hr>";

// 5. التحقق من حفظ البيانات
echo "<h2>5. التحقق من حفظ البيانات:</h2>";
try {
    $saved_review = $database->fetch(
        "SELECT * FROM reviews WHERE product_id = ? AND user_id = ? ORDER BY created_at DESC LIMIT 1",
        [$test_product_id, $user['id']]
    );
    
    if ($saved_review) {
        echo "✅ تم العثور على المراجعة المحفوظة:<br>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>الحقل</th><th>القيمة</th></tr>";
        foreach ($saved_review as $field => $value) {
            echo "<tr><td>$field</td><td>" . ($value ?: 'NULL') . "</td></tr>";
        }
        echo "</table>";
        
        // التحقق من user_id بشكل خاص
        if ($saved_review['user_id'] == $user['id']) {
            echo "<br>✅ <strong>user_id تم حفظه بشكل صحيح: {$saved_review['user_id']}</strong><br>";
        } else {
            echo "<br>❌ <strong>user_id لم يتم حفظه بشكل صحيح</strong><br>";
            echo "- المتوقع: {$user['id']}<br>";
            echo "- المحفوظ: " . ($saved_review['user_id'] ?: 'NULL') . "<br>";
        }
    } else {
        echo "❌ لم يتم العثور على المراجعة المحفوظة<br>";
    }
} catch (Exception $e) {
    echo "❌ خطأ في التحقق من البيانات: " . $e->getMessage() . "<br>";
}
echo "<hr>";

// 6. عرض آخر 5 مراجعات
echo "<h2>6. آخر 5 مراجعات في قاعدة البيانات:</h2>";
try {
    $recent_reviews = $database->fetchAll(
        "SELECT id, product_id, user_id, name, email, rating, title, comment, created_at 
         FROM reviews 
         ORDER BY created_at DESC 
         LIMIT 5"
    );
    
    if (!empty($recent_reviews)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Product ID</th><th>User ID</th><th>Name</th><th>Email</th><th>Rating</th><th>Title</th><th>Comment</th><th>Created</th></tr>";
        foreach ($recent_reviews as $review) {
            echo "<tr>";
            echo "<td>" . $review['id'] . "</td>";
            echo "<td>" . $review['product_id'] . "</td>";
            echo "<td>" . ($review['user_id'] ?: 'NULL') . "</td>";
            echo "<td>" . $review['name'] . "</td>";
            echo "<td>" . $review['email'] . "</td>";
            echo "<td>" . $review['rating'] . "</td>";
            echo "<td>" . substr($review['title'], 0, 30) . "...</td>";
            echo "<td>" . substr($review['comment'], 0, 50) . "...</td>";
            echo "<td>" . $review['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "لا توجد مراجعات في قاعدة البيانات<br>";
    }
} catch (Exception $e) {
    echo "خطأ في عرض المراجعات: " . $e->getMessage() . "<br>";
}

echo "<hr>";
echo "<h2>✅ انتهى الاختبار</h2>";
echo "<p><a href='debug_api_review.php'>العودة إلى صفحة التشخيص الرئيسية</a></p>";
?>