<?php
require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<h2>فحص التقييمات في قاعدة البيانات</h2>";

// جلب جميع التقييمات
$reviews = $database->fetchAll("SELECT * FROM reviews ORDER BY created_at DESC LIMIT 20");

echo "<h3>عدد التقييمات الإجمالي: " . count($reviews) . "</h3>";

if (!empty($reviews)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr>
            <th>ID</th>
            <th>Product ID</th>
            <th>User ID</th>
            <th>Name</th>
            <th>Email</th>
            <th>Rating</th>
            <th>Comment</th>
            <th>Is Approved</th>
            <th>Created At</th>
          </tr>";
    
    foreach ($reviews as $review) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($review['id']) . "</td>";
        echo "<td>" . htmlspecialchars($review['product_id']) . "</td>";
        echo "<td>" . htmlspecialchars($review['user_id'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($review['name']) . "</td>";
        echo "<td>" . htmlspecialchars($review['email']) . "</td>";
        echo "<td>" . htmlspecialchars($review['rating']) . "</td>";
        echo "<td>" . htmlspecialchars(substr($review['comment'], 0, 50)) . "...</td>";
        echo "<td>" . ($review['is_approved'] ? 'نعم' : 'لا') . "</td>";
        echo "<td>" . htmlspecialchars($review['created_at']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>لا توجد تقييمات في قاعدة البيانات</p>";
}

// فحص التقييمات المعتمدة للمنتج رقم 8
echo "<h3>التقييمات المعتمدة للمنتج رقم 8:</h3>";
$product8_reviews = getProductReviews(8);
echo "<p>عدد التقييمات المعتمدة: " . count($product8_reviews) . "</p>";

if (!empty($product8_reviews)) {
    foreach ($product8_reviews as $review) {
        echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
        echo "<strong>الاسم:</strong> " . htmlspecialchars($review['name']) . "<br>";
        echo "<strong>التقييم:</strong> " . $review['rating'] . "/5<br>";
        echo "<strong>التعليق:</strong> " . htmlspecialchars($review['comment']) . "<br>";
        echo "<strong>التاريخ:</strong> " . $review['created_at'] . "<br>";
        echo "</div>";
    }
}

// فحص جميع التقييمات للمنتج رقم 8 (بما في ذلك غير المعتمدة)
echo "<h3>جميع التقييمات للمنتج رقم 8 (معتمدة وغير معتمدة):</h3>";
$all_product8_reviews = getAllProductReviews(8);
echo "<p>عدد جميع التقييمات: " . count($all_product8_reviews) . "</p>";

if (!empty($all_product8_reviews)) {
    foreach ($all_product8_reviews as $review) {
        echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0; background: " . 
             ($review['is_approved'] ? '#e8f5e8' : '#fff3cd') . ";'>";
        echo "<strong>الاسم:</strong> " . htmlspecialchars($review['name']) . "<br>";
        echo "<strong>التقييم:</strong> " . $review['rating'] . "/5<br>";
        echo "<strong>التعليق:</strong> " . htmlspecialchars($review['comment']) . "<br>";
        echo "<strong>الحالة:</strong> " . $review['status_text'] . "<br>";
        echo "<strong>التاريخ:</strong> " . $review['created_at'] . "<br>";
        echo "</div>";
    }
}
?>