<?php
/**
 * نظام تحسين محركات البحث (SEO)
 * SEO Management System
 */

class SEOManager {
    private $database;
    private $defaultMeta;
    private $siteName;
    private $siteUrl;
    
    public function __construct() {
        global $database;
        $this->database = $database;
        $this->siteName = 'Green Line';
        $this->siteUrl = 'https://greenline.com'; // يجب تعديله حسب النطاق الفعلي
        
        $this->defaultMeta = [
            'title' => 'Green Line - منتجات طبيعية وصحية',
            'description' => 'اكتشف مجموعة واسعة من المنتجات الطبيعية والصحية في Green Line. جودة عالية وأسعار منافسة.',
            'keywords' => 'منتجات طبيعية, صحة, جمال, مكملات غذائية, عضوي',
            'image' => '/assets/images/logo.png',
            'type' => 'website'
        ];
        
        $this->createSEOTables();
    }
    
    /**
     * إنشاء جداول SEO
     */
    private function createSEOTables() {
        // جدول Meta Tags للصفحات
        $this->database->query("
            CREATE TABLE IF NOT EXISTS seo_meta (
                id INT PRIMARY KEY AUTO_INCREMENT,
                page_type VARCHAR(50) NOT NULL,
                page_id INT,
                title VARCHAR(255),
                description TEXT,
                keywords TEXT,
                canonical_url VARCHAR(255),
                og_title VARCHAR(255),
                og_description TEXT,
                og_image VARCHAR(255),
                og_type VARCHAR(50),
                twitter_title VARCHAR(255),
                twitter_description TEXT,
                twitter_image VARCHAR(255),
                robots VARCHAR(100) DEFAULT 'index,follow',
                schema_markup JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_page (page_type, page_id)
            )
        ");
        
        // جدول URLs للـ Sitemap
        $this->database->query("
            CREATE TABLE IF NOT EXISTS sitemap_urls (
                id INT PRIMARY KEY AUTO_INCREMENT,
                url VARCHAR(255) NOT NULL UNIQUE,
                priority DECIMAL(2,1) DEFAULT 0.5,
                changefreq ENUM('always', 'hourly', 'daily', 'weekly', 'monthly', 'yearly', 'never') DEFAULT 'weekly',
                lastmod TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                status ENUM('active', 'inactive') DEFAULT 'active',
                page_type VARCHAR(50),
                page_id INT,
                INDEX idx_status (status),
                INDEX idx_lastmod (lastmod)
            )
        ");
        
        $this->insertDefaultSEOData();
    }
    
    /**
     * إدراج بيانات SEO الافتراضية
     */
    private function insertDefaultSEOData() {
        $defaultPages = [
            [
                'page_type' => 'home',
                'page_id' => 0,
                'title' => 'Green Line - منتجات طبيعية وصحية',
                'description' => 'اكتشف مجموعة واسعة من المنتجات الطبيعية والصحية في Green Line. جودة عالية وأسعار منافسة.',
                'keywords' => 'منتجات طبيعية, صحة, جمال, مكملات غذائية, عضوي',
                'og_title' => 'Green Line - منتجات طبيعية وصحية',
                'og_description' => 'اكتشف مجموعة واسعة من المنتجات الطبيعية والصحية في Green Line.',
                'og_type' => 'website'
            ],
            [
                'page_type' => 'products',
                'page_id' => 0,
                'title' => 'منتجاتنا - Green Line',
                'description' => 'تصفح جميع منتجاتنا الطبيعية والصحية. مجموعة متنوعة من المنتجات عالية الجودة.',
                'keywords' => 'منتجات, كتالوج, طبيعي, صحي',
                'og_title' => 'منتجاتنا - Green Line',
                'og_description' => 'تصفح جميع منتجاتنا الطبيعية والصحية.',
                'og_type' => 'website'
            ],
            [
                'page_type' => 'about',
                'page_id' => 0,
                'title' => 'من نحن - Green Line',
                'description' => 'تعرف على Green Line وقصتنا في تقديم أفضل المنتجات الطبيعية والصحية.',
                'keywords' => 'من نحن, قصتنا, رؤيتنا, رسالتنا',
                'og_title' => 'من نحن - Green Line',
                'og_description' => 'تعرف على Green Line وقصتنا في تقديم أفضل المنتجات الطبيعية.',
                'og_type' => 'website'
            ],
            [
                'page_type' => 'contact',
                'page_id' => 0,
                'title' => 'اتصل بنا - Green Line',
                'description' => 'تواصل معنا للاستفسارات والدعم. نحن هنا لمساعدتك.',
                'keywords' => 'اتصل بنا, تواصل, دعم, استفسارات',
                'og_title' => 'اتصل بنا - Green Line',
                'og_description' => 'تواصل معنا للاستفسارات والدعم.',
                'og_type' => 'website'
            ]
        ];
        
        foreach ($defaultPages as $page) {
            $existing = $this->database->fetch(
                "SELECT id FROM seo_meta WHERE page_type = :type AND page_id = :id",
                ['type' => $page['page_type'], 'id' => $page['page_id']]
            );
            
            if (!$existing) {
                $this->database->insert('seo_meta', $page);
            }
        }
        
        // إضافة URLs للـ Sitemap
        $this->updateSitemapUrls();
    }
    
    /**
     * الحصول على Meta Tags للصفحة
     */
    public function getMetaTags($pageType, $pageId = 0, $customData = []) {
        $meta = $this->database->fetch(
            "SELECT * FROM seo_meta WHERE page_type = :type AND page_id = :id",
            ['type' => $pageType, 'id' => $pageId]
        );
        
        if (!$meta) {
            $meta = $this->defaultMeta;
        }
        
        // دمج البيانات المخصصة
        $meta = array_merge($meta, $customData);
        
        // معالجة المتغيرات الديناميكية
        $meta = $this->processDynamicVariables($meta, $pageType, $pageId);
        
        return $meta;
    }
    
    /**
     * معالجة المتغيرات الديناميكية
     */
    private function processDynamicVariables($meta, $pageType, $pageId) {
        switch ($pageType) {
            case 'product':
                $product = $this->getProductData($pageId);
                if ($product) {
                    $meta['title'] = $product['name'] . ' - Green Line';
                    $meta['description'] = substr(strip_tags($product['description']), 0, 160);
                    $meta['keywords'] = $product['name'] . ', ' . $product['category_name'] . ', منتجات طبيعية';
                    $meta['og_title'] = $product['name'];
                    $meta['og_description'] = substr(strip_tags($product['description']), 0, 200);
                    $meta['og_image'] = $product['main_image'] ?? '/assets/images/default-product.svg';
                    $meta['og_type'] = 'product';
                    $meta['canonical_url'] = $this->siteUrl . '/product.php?id=' . $pageId;
                }
                break;
                
            case 'category':
                $category = $this->getCategoryData($pageId);
                if ($category) {
                    $meta['title'] = $category['name'] . ' - Green Line';
                    $meta['description'] = $category['description'] ?: 'تصفح منتجات ' . $category['name'] . ' في Green Line';
                    $meta['keywords'] = $category['name'] . ', منتجات, طبيعية';
                    $meta['og_title'] = $category['name'];
                    $meta['og_description'] = $category['description'] ?: 'تصفح منتجات ' . $category['name'];
                    $meta['canonical_url'] = $this->siteUrl . '/products.php?category=' . $pageId;
                }
                break;
        }
        
        return $meta;
    }
    
    /**
     * الحصول على بيانات المنتج
     */
    private function getProductData($productId) {
        return $this->database->fetch("
            SELECT p.*, pc.name as category_name,
                   (SELECT image_url FROM product_images WHERE product_id = p.id ORDER BY sort_order LIMIT 1) as main_image
            FROM products p
            LEFT JOIN product_categories pc ON p.category_id = pc.id
            WHERE p.id = :id AND p.status = 'active'
        ", ['id' => $productId]);
    }
    
    /**
     * الحصول على بيانات الفئة
     */
    private function getCategoryData($categoryId) {
        return $this->database->fetch(
            "SELECT * FROM product_categories WHERE id = :id AND status = 'active'",
            ['id' => $categoryId]
        );
    }
    
    /**
     * إنشاء HTML للـ Meta Tags
     */
    public function renderMetaTags($pageType, $pageId = 0, $customData = []) {
        $meta = $this->getMetaTags($pageType, $pageId, $customData);
        
        $html = "\n";
        
        // Basic Meta Tags
        $html .= "<title>" . htmlspecialchars($meta['title']) . "</title>\n";
        $html .= "<meta name='description' content='" . htmlspecialchars($meta['description']) . "'>\n";
        $html .= "<meta name='keywords' content='" . htmlspecialchars($meta['keywords']) . "'>\n";
        
        // Robots
        $robots = $meta['robots'] ?? 'index,follow';
        $html .= "<meta name='robots' content='{$robots}'>\n";
        
        // Canonical URL
        if (!empty($meta['canonical_url'])) {
            $html .= "<link rel='canonical' href='" . htmlspecialchars($meta['canonical_url']) . "'>\n";
        }
        
        // Open Graph Tags
        $html .= "<meta property='og:title' content='" . htmlspecialchars($meta['og_title'] ?? $meta['title']) . "'>\n";
        $html .= "<meta property='og:description' content='" . htmlspecialchars($meta['og_description'] ?? $meta['description']) . "'>\n";
        $html .= "<meta property='og:type' content='" . ($meta['og_type'] ?? 'website') . "'>\n";
        $html .= "<meta property='og:site_name' content='{$this->siteName}'>\n";
        
        if (!empty($meta['og_image'])) {
            $imageUrl = strpos($meta['og_image'], 'http') === 0 ? $meta['og_image'] : $this->siteUrl . $meta['og_image'];
            $html .= "<meta property='og:image' content='{$imageUrl}'>\n";
        }
        
        // Twitter Cards
        $html .= "<meta name='twitter:card' content='summary_large_image'>\n";
        $html .= "<meta name='twitter:title' content='" . htmlspecialchars($meta['twitter_title'] ?? $meta['title']) . "'>\n";
        $html .= "<meta name='twitter:description' content='" . htmlspecialchars($meta['twitter_description'] ?? $meta['description']) . "'>\n";
        
        if (!empty($meta['twitter_image'])) {
            $imageUrl = strpos($meta['twitter_image'], 'http') === 0 ? $meta['twitter_image'] : $this->siteUrl . $meta['twitter_image'];
            $html .= "<meta name='twitter:image' content='{$imageUrl}'>\n";
        }
        
        // Schema Markup
        if (!empty($meta['schema_markup'])) {
            $schema = is_string($meta['schema_markup']) ? $meta['schema_markup'] : json_encode($meta['schema_markup']);
            $html .= "<script type='application/ld+json'>{$schema}</script>\n";
        }
        
        return $html;
    }
    
    /**
     * تحديث Meta Tags للصفحة
     */
    public function updateMetaTags($pageType, $pageId, $metaData) {
        $existing = $this->database->fetch(
            "SELECT id FROM seo_meta WHERE page_type = :type AND page_id = :id",
            ['type' => $pageType, 'id' => $pageId]
        );
        
        if ($existing) {
            return $this->database->update(
                'seo_meta',
                $metaData,
                'page_type = :type AND page_id = :id',
                ['type' => $pageType, 'id' => $pageId]
            );
        } else {
            $metaData['page_type'] = $pageType;
            $metaData['page_id'] = $pageId;
            return $this->database->insert('seo_meta', $metaData);
        }
    }
    
    /**
     * تحديث URLs في الـ Sitemap
     */
    public function updateSitemapUrls() {
        // مسح URLs القديمة
        $this->database->delete('sitemap_urls', '1=1');
        
        $urls = [];
        
        // الصفحات الرئيسية
        $urls[] = ['url' => '/', 'priority' => 1.0, 'changefreq' => 'daily', 'page_type' => 'home'];
        $urls[] = ['url' => '/products.php', 'priority' => 0.9, 'changefreq' => 'daily', 'page_type' => 'products'];
        $urls[] = ['url' => '/about.php', 'priority' => 0.7, 'changefreq' => 'monthly', 'page_type' => 'about'];
        $urls[] = ['url' => '/contact.php', 'priority' => 0.6, 'changefreq' => 'monthly', 'page_type' => 'contact'];
        
        // صفحات المنتجات
        $products = $this->database->fetchAll(
            "SELECT id, updated_at FROM products WHERE status = 'active'"
        );
        
        foreach ($products as $product) {
            $urls[] = [
                'url' => '/product.php?id=' . $product['id'],
                'priority' => 0.8,
                'changefreq' => 'weekly',
                'page_type' => 'product',
                'page_id' => $product['id'],
                'lastmod' => $product['updated_at']
            ];
        }
        
        // صفحات الفئات
        $categories = $this->database->fetchAll(
            "SELECT id, updated_at FROM product_categories WHERE status = 'active'"
        );
        
        foreach ($categories as $category) {
            $urls[] = [
                'url' => '/products.php?category=' . $category['id'],
                'priority' => 0.7,
                'changefreq' => 'weekly',
                'page_type' => 'category',
                'page_id' => $category['id'],
                'lastmod' => $category['updated_at']
            ];
        }
        
        // إدراج URLs
        foreach ($urls as $url) {
            $this->database->insert('sitemap_urls', $url);
        }
        
        return count($urls);
    }
    
    /**
     * إنشاء ملف Sitemap XML
     */
    public function generateSitemap() {
        $urls = $this->database->fetchAll(
            "SELECT * FROM sitemap_urls WHERE status = 'active' ORDER BY priority DESC, lastmod DESC"
        );
        
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
        
        foreach ($urls as $url) {
            $xml .= '  <url>' . "\n";
            $xml .= '    <loc>' . htmlspecialchars($this->siteUrl . $url['url']) . '</loc>' . "\n";
            $xml .= '    <lastmod>' . date('Y-m-d\TH:i:s+00:00', strtotime($url['lastmod'])) . '</lastmod>' . "\n";
            $xml .= '    <changefreq>' . $url['changefreq'] . '</changefreq>' . "\n";
            $xml .= '    <priority>' . $url['priority'] . '</priority>' . "\n";
            $xml .= '  </url>' . "\n";
        }
        
        $xml .= '</urlset>';
        
        // حفظ الملف
        $sitemapPath = $_SERVER['DOCUMENT_ROOT'] . '/sitemap.xml';
        file_put_contents($sitemapPath, $xml);
        
        return $xml;
    }
    
    /**
     * إنشاء Schema Markup للمنتج
     */
    public function generateProductSchema($productId) {
        $product = $this->getProductData($productId);
        if (!$product) return null;
        
        $schema = [
            '@context' => 'https://schema.org/',
            '@type' => 'Product',
            'name' => $product['name'],
            'description' => strip_tags($product['description']),
            'brand' => [
                '@type' => 'Brand',
                'name' => 'Green Line'
            ],
            'category' => $product['category_name']
        ];
        
        if ($product['price']) {
            $schema['offers'] = [
                '@type' => 'Offer',
                'price' => $product['price'],
                'priceCurrency' => 'SAR',
                'availability' => $product['stock_quantity'] > 0 ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock'
            ];
        }
        
        if ($product['main_image']) {
            $schema['image'] = $this->siteUrl . $product['main_image'];
        }
        
        // إضافة التقييمات إذا وجدت
        $reviews = $this->database->fetch(
            "SELECT AVG(rating) as avg_rating, COUNT(*) as review_count FROM reviews WHERE product_id = :id AND status = 'approved'",
            ['id' => $productId]
        );
        
        if ($reviews && $reviews['review_count'] > 0) {
            $schema['aggregateRating'] = [
                '@type' => 'AggregateRating',
                'ratingValue' => round($reviews['avg_rating'], 1),
                'reviewCount' => $reviews['review_count']
            ];
        }
        
        return json_encode($schema, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }
    
    /**
     * إنشاء Schema Markup للموقع
     */
    public function generateOrganizationSchema() {
        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'Organization',
            'name' => 'Green Line',
            'url' => $this->siteUrl,
            'logo' => $this->siteUrl . '/assets/images/logo.png',
            'description' => 'متجر متخصص في المنتجات الطبيعية والصحية',
            'contactPoint' => [
                '@type' => 'ContactPoint',
                'telephone' => '+966-XX-XXX-XXXX', // يجب تعديله
                'contactType' => 'customer service',
                'availableLanguage' => ['Arabic', 'English']
            ],
            'sameAs' => [
                // يمكن إضافة روابط وسائل التواصل الاجتماعي
            ]
        ];
        
        return json_encode($schema, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }
}

// إنشاء مثيل عام
$seoManager = new SEOManager();

/**
 * دوال مساعدة للاستخدام السريع
 */

/**
 * عرض Meta Tags للصفحة
 */
function renderSEOTags($pageType, $pageId = 0, $customData = []) {
    global $seoManager;
    echo $seoManager->renderMetaTags($pageType, $pageId, $customData);
}

/**
 * تحديث Sitemap
 */
function updateSitemap() {
    global $seoManager;
    return $seoManager->updateSitemapUrls();
}

/**
 * إنشاء ملف Sitemap
 */
function generateSitemapFile() {
    global $seoManager;
    return $seoManager->generateSitemap();
}

/**
 * إضافة Schema Markup للمنتج
 */
function addProductSchema($productId) {
    global $seoManager;
    $schema = $seoManager->generateProductSchema($productId);
    if ($schema) {
        echo "<script type='application/ld+json'>{$schema}</script>\n";
    }
}

/**
 * إضافة Schema Markup للمؤسسة
 */
function addOrganizationSchema() {
    global $seoManager;
    $schema = $seoManager->generateOrganizationSchema();
    echo "<script type='application/ld+json'>{$schema}</script>\n";
}

/**
 * تحسين عنوان URL
 */
function slugify($text) {
    // تحويل النص العربي والإنجليزي إلى slug
    $text = trim($text);
    $text = preg_replace('/[^\p{L}\p{N}\s-]/u', '', $text);
    $text = preg_replace('/[\s-]+/', '-', $text);
    $text = trim($text, '-');
    return strtolower($text);
}

/**
 * إنشاء URL صديق لمحركات البحث
 */
function createSEOUrl($type, $id, $title = '') {
    $slug = $title ? slugify($title) : '';
    
    switch ($type) {
        case 'product':
            return '/product/' . $id . ($slug ? '/' . $slug : '');
        case 'category':
            return '/category/' . $id . ($slug ? '/' . $slug : '');
        default:
            return '/' . $type . '.php';
    }
}

?>