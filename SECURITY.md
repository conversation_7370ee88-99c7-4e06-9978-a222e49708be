# 🔒 سياسة الأمان - Green Line E-commerce

نحن في Green Line نأخذ الأمان على محمل الجد. هذا المستند يوضح سياساتنا الأمنية وكيفية الإبلاغ عن الثغرات الأمنية.

## 📋 جدول المحتويات

- [الإصدارات المدعومة](#الإصدارات-المدعومة)
- [الإبلاغ عن الثغرات الأمنية](#الإبلاغ-عن-الثغرات-الأمنية)
- [عملية التعامل مع التقارير](#عملية-التعامل-مع-التقارير)
- [الميزات الأمنية المطبقة](#الميزات-الأمنية-المطبقة)
- [أفضل الممارسات الأمنية](#أفضل-الممارسات-الأمنية)
- [إعدادات الأمان الموصى بها](#إعدادات-الأمان-الموصى-بها)
- [مراجعة الأمان](#مراجعة-الأمان)

## 🛡️ الإصدارات المدعومة

نحن ندعم الإصدارات التالية بتحديثات أمنية:

| الإصدار | مدعوم ✅ | ملاحظات |
| ------- | -------- | ------- |
| 2.0.x   | ✅       | الإصدار الحالي - دعم كامل |
| 1.5.x   | ✅       | دعم أمني فقط حتى 2024-06-30 |
| 1.4.x   | ❌       | انتهى الدعم |
| < 1.4   | ❌       | انتهى الدعم |

### جدولة التحديثات

- **تحديثات أمنية حرجة**: خلال 24-48 ساعة
- **تحديثات أمنية عادية**: خلال 7-14 يوم
- **تحديثات الصيانة**: شهرياً

## 🚨 الإبلاغ عن الثغرات الأمنية

### الإبلاغ المسؤول

إذا اكتشفت ثغرة أمنية، يرجى **عدم** الإبلاغ عنها علناً. بدلاً من ذلك:

#### 1. الإبلاغ الخاص

📧 **البريد الإلكتروني**: <EMAIL>

**يرجى تضمين المعلومات التالية:**

```
الموضوع: [SECURITY] وصف مختصر للثغرة

1. وصف الثغرة:
   - نوع الثغرة (SQL Injection, XSS, إلخ)
   - الملفات أو الصفحات المتأثرة
   - مستوى الخطورة المقدر

2. خطوات إعادة الإنتاج:
   - خطوات تفصيلية لاستغلال الثغرة
   - أي أدوات أو سكريبتات مستخدمة
   - لقطات شاشة إذا كان مناسباً

3. التأثير المحتمل:
   - ما يمكن للمهاجم فعله
   - البيانات التي قد تتأثر
   - المستخدمين المتأثرين

4. معلومات البيئة:
   - إصدار المنصة
   - إصدار PHP
   - نظام التشغيل
   - المتصفح (إذا كان مناسباً)

5. معلومات الاتصال:
   - اسمك (اختياري)
   - طريقة التواصل المفضلة
   - هل تريد الإشارة إليك في الشكر؟
```

#### 2. التشفير (اختياري)

للمعلومات الحساسة جداً، يمكنك استخدام مفتاح PGP العام:

```
-----BEGIN PGP PUBLIC KEY BLOCK-----
[سيتم إضافة المفتاح العام هنا]
-----END PGP PUBLIC KEY BLOCK-----
```

### ما لا يُعتبر ثغرة أمنية

- مشاكل في الإصدارات غير المدعومة
- مشاكل تتطلب وصول فيزيائي للخادم
- هجمات الهندسة الاجتماعية
- مشاكل في التبعيات الخارجية (يرجى الإبلاغ للمطورين الأصليين)
- مشاكل تتطلب تفاعل مستخدم غير واقعي

## 🔄 عملية التعامل مع التقارير

### 1. الاستلام والتأكيد (24 ساعة)

- تأكيد استلام التقرير
- تقييم أولي للخطورة
- إسناد رقم تتبع (SEC-YYYY-NNNN)

### 2. التحقق والتحليل (2-5 أيام)

- إعادة إنتاج الثغرة
- تحليل التأثير والمخاطر
- تحديد الأولوية

### 3. التطوير والاختبار (3-14 يوم)

- تطوير الإصلاح
- اختبار شامل
- مراجعة الكود

### 4. النشر والإشعار (1-3 أيام)

- نشر الإصلاح
- إشعار المستخدمين
- تحديث التوثيق

### 5. الكشف العلني (30 يوم)

- نشر تفاصيل الثغرة بعد الإصلاح
- شكر المُبلغ (إذا رغب)
- إضافة للسجل الأمني

## 🛡️ الميزات الأمنية المطبقة

### 1. حماية المصادقة

```php
// تشفير كلمات المرور
password_hash($password, PASSWORD_ARGON2ID);

// حماية من Brute Force
$rateLimiter->checkLimit('login', $ip, 5, 900); // 5 محاولات كل 15 دقيقة

// جلسات آمنة
session_set_cookie_params([
    'lifetime' => 3600,
    'path' => '/',
    'domain' => '',
    'secure' => true,
    'httponly' => true,
    'samesite' => 'Strict'
]);
```

### 2. حماية CSRF

```php
// إنشاء رمز CSRF
$token = CSRFProtection::generateToken();

// التحقق من الرمز
CSRFProtection::validateToken($_POST['csrf_token']);

// في النماذج
echo csrf_field();
```

### 3. حماية من SQL Injection

```php
// استخدام Prepared Statements
$stmt = $pdo->prepare("SELECT * FROM users WHERE email = ? AND status = ?");
$stmt->execute([$email, 'active']);

// تنظيف المدخلات
$email = filter_var($email, FILTER_VALIDATE_EMAIL);
$name = htmlspecialchars($name, ENT_QUOTES, 'UTF-8');
```

### 4. حماية من XSS

```php
// تنظيف المخرجات
function safe_output($text) {
    return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
}

// Content Security Policy
header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'");
```

### 5. تحديد معدل الطلبات

```php
// حماية API
$rateLimiter->checkLimit('api', $userId, 100, 3600); // 100 طلب كل ساعة

// حماية تسجيل الدخول
$rateLimiter->checkLimit('login', $ip, 5, 900); // 5 محاولات كل 15 دقيقة
```

### 6. تشفير البيانات الحساسة

```php
// تشفير البيانات
$encrypted = openssl_encrypt($data, 'AES-256-GCM', $key, 0, $iv, $tag);

// فك التشفير
$decrypted = openssl_decrypt($encrypted, 'AES-256-GCM', $key, 0, $iv, $tag);
```

## 🔧 أفضل الممارسات الأمنية

### للمطورين

#### 1. التحقق من المدخلات

```php
// ✅ جيد
function validateUserInput($data) {
    $errors = [];
    
    // التحقق من البريد الإلكتروني
    if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'البريد الإلكتروني غير صحيح';
    }
    
    // التحقق من كلمة المرور
    if (strlen($data['password']) < 8) {
        $errors[] = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
    }
    
    // التحقق من الأحرف الخاصة
    if (!preg_match('/^[a-zA-Z0-9\s\u0600-\u06FF]+$/', $data['name'])) {
        $errors[] = 'الاسم يحتوي على أحرف غير مسموحة';
    }
    
    return $errors;
}

// ❌ سيء
$name = $_POST['name']; // بدون تحقق
$sql = "SELECT * FROM users WHERE name = '$name'"; // SQL Injection
```

#### 2. إدارة الأخطاء الآمنة

```php
// ✅ جيد
try {
    $user = $userManager->getUser($id);
} catch (Exception $e) {
    // تسجيل الخطأ التفصيلي
    error_log('خطأ في جلب المستخدم: ' . $e->getMessage());
    
    // رسالة عامة للمستخدم
    throw new UserException('حدث خطأ في النظام');
}

// ❌ سيء
try {
    $user = $userManager->getUser($id);
} catch (Exception $e) {
    // كشف معلومات حساسة
    echo 'خطأ: ' . $e->getMessage();
}
```

#### 3. إدارة الجلسات الآمنة

```php
// ✅ جيد
class SecureSession {
    public static function start() {
        // إعدادات آمنة للجلسة
        ini_set('session.cookie_httponly', 1);
        ini_set('session.cookie_secure', 1);
        ini_set('session.use_strict_mode', 1);
        
        session_start();
        
        // تجديد معرف الجلسة
        if (!isset($_SESSION['initiated'])) {
            session_regenerate_id(true);
            $_SESSION['initiated'] = true;
        }
    }
    
    public static function destroy() {
        session_unset();
        session_destroy();
        session_write_close();
        setcookie(session_name(), '', 0, '/');
    }
}
```

### للمديرين

#### 1. إعدادات الخادم

```apache
# Apache .htaccess
# منع الوصول للملفات الحساسة
<Files ".env">
    Order allow,deny
    Deny from all
</Files>

<Files "composer.json">
    Order allow,deny
    Deny from all
</Files>

# إعدادات الأمان
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"
```

```nginx
# Nginx
# منع الوصول للملفات الحساسة
location ~ /\.(env|git) {
    deny all;
    return 404;
}

# إعدادات الأمان
add_header X-Content-Type-Options nosniff;
add_header X-Frame-Options DENY;
add_header X-XSS-Protection "1; mode=block";
add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
```

#### 2. إعدادات PHP

```ini
; php.ini
; إخفاء معلومات PHP
expose_php = Off

; تعطيل الدوال الخطيرة
disable_functions = exec,passthru,shell_exec,system,proc_open,popen

; حدود الذاكرة والوقت
memory_limit = 128M
max_execution_time = 30
max_input_time = 30

; حدود الملفات المرفوعة
file_uploads = On
upload_max_filesize = 10M
post_max_size = 10M
max_file_uploads = 5

; إعدادات الجلسات
session.cookie_httponly = 1
session.cookie_secure = 1
session.use_strict_mode = 1
session.cookie_samesite = Strict
```

#### 3. إعدادات قاعدة البيانات

```sql
-- إنشاء مستخدم محدود الصلاحيات
CREATE USER 'greenline_app'@'localhost' IDENTIFIED BY 'strong_password_here';

-- منح صلاحيات محددة فقط
GRANT SELECT, INSERT, UPDATE, DELETE ON greenline_db.* TO 'greenline_app'@'localhost';

-- منع صلاحيات خطيرة
-- لا تمنح: CREATE, DROP, ALTER, INDEX, REFERENCES

FLUSH PRIVILEGES;
```

## ⚙️ إعدادات الأمان الموصى بها

### 1. متغيرات البيئة (.env)

```env
# إعدادات الأمان
APP_ENV=production
APP_DEBUG=false
APP_KEY=your-32-character-secret-key-here

# إعدادات قاعدة البيانات
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=greenline_db
DB_USERNAME=greenline_app
DB_PASSWORD=your-strong-database-password

# إعدادات الجلسات
SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_ENCRYPT=true

# إعدادات Redis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=your-redis-password
REDIS_PORT=6379

# إعدادات البريد الإلكتروني
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls

# إعدادات الأمان المتقدمة
CSRF_TOKEN_LIFETIME=3600
RATE_LIMIT_ENABLED=true
ENCRYPTION_KEY=your-encryption-key-here
JWT_SECRET=your-jwt-secret-key

# إعدادات التخزين المؤقت
CACHE_DRIVER=redis
CACHE_PREFIX=greenline_
CACHE_DEFAULT_TTL=3600

# إعدادات الملفات المرفوعة
UPLOAD_MAX_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx
UPLOAD_PATH=/var/www/uploads

# إعدادات النسخ الاحتياطي
BACKUP_ENABLED=true
BACKUP_SCHEDULE=daily
BACKUP_RETENTION_DAYS=30
```

### 2. إعدادات Firewall

```bash
#!/bin/bash
# إعدادات UFW للخادم

# تعطيل جميع الاتصالات الواردة افتراضياً
ufw default deny incoming
ufw default allow outgoing

# السماح بـ SSH (غير المنفذ الافتراضي)
ufw allow 2222/tcp

# السماح بـ HTTP و HTTPS
ufw allow 80/tcp
ufw allow 443/tcp

# السماح بـ MySQL من الخادم المحلي فقط
ufw allow from 127.0.0.1 to any port 3306

# السماح بـ Redis من الخادم المحلي فقط
ufw allow from 127.0.0.1 to any port 6379

# تفعيل Firewall
ufw enable

# عرض الحالة
ufw status verbose
```

### 3. إعدادات SSL/TLS

```nginx
# إعدادات SSL قوية لـ Nginx
server {
    listen 443 ssl http2;
    server_name greenline.com www.greenline.com;
    
    # شهادات SSL
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # إعدادات SSL قوية
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # HSTS
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
    
    # باقي الإعدادات...
}

# إعادة توجيه HTTP إلى HTTPS
server {
    listen 80;
    server_name greenline.com www.greenline.com;
    return 301 https://$server_name$request_uri;
}
```

## 🔍 مراجعة الأمان

### 1. قائمة التحقق الأمنية

#### الخادم والبنية التحتية
- [ ] **نظام التشغيل محدث** - آخر تحديثات الأمان
- [ ] **Firewall مُفعل** - قواعد صارمة للوصول
- [ ] **SSH آمن** - مفاتيح بدلاً من كلمات المرور
- [ ] **SSL/TLS مُفعل** - شهادات صالحة ومحدثة
- [ ] **النسخ الاحتياطي** - نسخ منتظمة ومشفرة

#### التطبيق
- [ ] **المدخلات محققة** - جميع بيانات المستخدم
- [ ] **المخرجات منظفة** - حماية من XSS
- [ ] **قاعدة البيانات آمنة** - Prepared Statements
- [ ] **الجلسات آمنة** - إعدادات صحيحة
- [ ] **CSRF محمي** - جميع النماذج
- [ ] **Rate Limiting مُفعل** - حماية من الإفراط
- [ ] **التسجيل مُفعل** - مراقبة الأنشطة المشبوهة

#### البيانات
- [ ] **كلمات المرور مشفرة** - خوارزميات قوية
- [ ] **البيانات الحساسة مشفرة** - في قاعدة البيانات
- [ ] **الصلاحيات محددة** - مبدأ الحد الأدنى
- [ ] **الوصول مُسجل** - تتبع العمليات الحساسة

### 2. أدوات الفحص الأمني

#### فحص الكود
```bash
# PHPStan - تحليل ثابت للكود
composer require --dev phpstan/phpstan
vendor/bin/phpstan analyse src

# Psalm - فحص أمني متقدم
composer require --dev vimeo/psalm
vendor/bin/psalm --show-info=true

# PHP_CodeSniffer - معايير الكود
composer require --dev squizlabs/php_codesniffer
vendor/bin/phpcs --standard=PSR12 src/
```

#### فحص التبعيات
```bash
# Composer Security Checker
composer audit

# فحص الثغرات المعروفة
composer require --dev roave/security-advisories:dev-latest
```

#### فحص الخادم
```bash
# Nmap - فحص المنافذ
nmap -sS -O target_ip

# Nikto - فحص الثغرات
nikto -h https://greenline.com

# SSL Labs - فحص SSL
# https://www.ssllabs.com/ssltest/
```

### 3. مراقبة الأمان

#### تسجيل الأحداث الأمنية
```php
class SecurityLogger {
    public static function logSecurityEvent($event, $details = []) {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'event' => $event,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'user_id' => $_SESSION['user_id'] ?? null,
            'details' => $details
        ];
        
        error_log('SECURITY: ' . json_encode($logData));
        
        // إرسال تنبيه للأحداث الحرجة
        if (in_array($event, ['login_bruteforce', 'sql_injection_attempt', 'xss_attempt'])) {
            self::sendSecurityAlert($logData);
        }
    }
    
    private static function sendSecurityAlert($data) {
        // إرسال تنبيه فوري للمديرين
        mail('<EMAIL>', 'تنبيه أمني', json_encode($data));
    }
}

// أمثلة على الاستخدام
SecurityLogger::logSecurityEvent('login_attempt', ['email' => $email]);
SecurityLogger::logSecurityEvent('permission_denied', ['resource' => $resource]);
SecurityLogger::logSecurityEvent('suspicious_activity', ['reason' => 'multiple_failed_logins']);
```

#### مراقبة الملفات الحساسة
```bash
#!/bin/bash
# سكريبت مراقبة تغييرات الملفات الحساسة

# قائمة الملفات المراقبة
FILES_TO_MONITOR=(
    "/var/www/greenline/.env"
    "/var/www/greenline/includes/config.php"
    "/var/www/greenline/includes/database.php"
    "/etc/nginx/sites-available/greenline"
    "/etc/php/8.1/fpm/php.ini"
)

# حساب checksums
for file in "${FILES_TO_MONITOR[@]}"; do
    if [ -f "$file" ]; then
        checksum=$(sha256sum "$file" | cut -d' ' -f1)
        echo "$file:$checksum" >> /var/log/file_checksums.log
    fi
done

# مقارنة مع النسخة السابقة وإرسال تنبيه عند التغيير
```

## 📊 تقارير الأمان

### تقرير أمني شهري

```php
class SecurityReport {
    public function generateMonthlyReport() {
        $report = [
            'period' => date('Y-m'),
            'login_attempts' => $this->getLoginAttempts(),
            'blocked_ips' => $this->getBlockedIPs(),
            'security_events' => $this->getSecurityEvents(),
            'vulnerabilities' => $this->getVulnerabilities(),
            'updates_applied' => $this->getUpdatesApplied()
        ];
        
        return $report;
    }
    
    private function getLoginAttempts() {
        // إحصائيات محاولات تسجيل الدخول
        return [
            'successful' => 1250,
            'failed' => 89,
            'blocked' => 23
        ];
    }
    
    private function getSecurityEvents() {
        // الأحداث الأمنية المسجلة
        return [
            'csrf_attempts' => 5,
            'sql_injection_attempts' => 2,
            'xss_attempts' => 8,
            'rate_limit_exceeded' => 45
        ];
    }
}
```

## 🏆 برنامج المكافآت الأمنية

### مستويات المكافآت

| مستوى الخطورة | الوصف | المكافأة |
|---------------|--------|----------|
| **حرج** | تنفيذ كود عن بُعد، SQL Injection، تسريب بيانات شامل | $500 - $1000 |
| **عالي** | XSS مخزن، CSRF، تصعيد صلاحيات | $200 - $500 |
| **متوسط** | XSS منعكس، تسريب معلومات محدود | $50 - $200 |
| **منخفض** | مشاكل إعدادات، تسريب معلومات غير حساسة | $25 - $50 |

### شروط المكافأة

- ✅ **الإبلاغ المسؤول** - عدم الكشف العلني قبل الإصلاح
- ✅ **تقرير مفصل** - خطوات واضحة لإعادة الإنتاج
- ✅ **عدم الإضرار** - عدم الوصول لبيانات حقيقية
- ✅ **النطاق المحدد** - الثغرات في النطاقات المعتمدة فقط
- ✅ **الأصالة** - أول من يبلغ عن الثغرة

### النطاقات المشمولة

- ✅ greenline.com
- ✅ api.greenline.com
- ✅ admin.greenline.com
- ❌ test.greenline.com (بيئة اختبار)
- ❌ النطاقات الفرعية الأخرى

## 📞 جهات الاتصال الأمنية

### فريق الأمان

- **منسق الأمان الرئيسي**: <EMAIL>
- **مطور الأمان**: <EMAIL>
- **مدير النظام**: <EMAIL>

### أوقات الاستجابة

- **الثغرات الحرجة**: 2-4 ساعات
- **الثغرات العالية**: 24-48 ساعة
- **الثغرات المتوسطة**: 3-7 أيام
- **الثغرات المنخفضة**: 1-2 أسبوع

### قنوات التواصل الطارئة

- 📧 **البريد الإلكتروني**: <EMAIL>
- 📱 **الهاتف الطارئ**: +966-XX-XXX-XXXX (للثغرات الحرجة فقط)
- 💬 **Signal**: @greenline-security (مشفر)

---

## 📝 سجل التحديثات الأمنية

### 2024-01-15 - الإصدار 2.0.0
- ✅ إضافة نظام صلاحيات متقدم
- ✅ تطبيق حماية CSRF شاملة
- ✅ إضافة نظام Rate Limiting
- ✅ تحسين تشفير كلمات المرور
- ✅ إضافة تسجيل الأحداث الأمنية

### 2023-12-01 - الإصدار 1.5.1
- 🔒 إصلاح ثغرة XSS في نموذج التعليقات
- 🔒 تحسين التحقق من صحة المدخلات
- 🔒 تحديث إعدادات الجلسات

---

**الأمان مسؤولية الجميع. شكراً لمساعدتكم في حماية منصة Green Line! 🛡️**