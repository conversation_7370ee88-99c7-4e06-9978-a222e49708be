<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// بدء الجلسة
session_start();

// محاكاة تسجيل دخول المستخدم
if (!isLoggedIn()) {
    $_SESSION['user_id'] = 1;
    $_SESSION['user_name'] = 'Test User';
    $_SESSION['user_email'] = '<EMAIL>';
}

$pageTitle = 'فحص حالة التفعيل التلقائي للتقييمات';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .status-box {
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            border: 2px solid;
        }
        .enabled { border-color: #10b981; background-color: #d1fae5; }
        .disabled { border-color: #ef4444; background-color: #fee2e2; }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-3xl font-bold text-center mb-8"><?php echo $pageTitle; ?></h1>
            
            <!-- فحص حالة الإعداد -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">حالة إعداد التفعيل التلقائي</h2>
                
                <?php
                $auto_approval = getSetting('reviews_auto_approval', false);
                $auto_approval_value = $auto_approval ? 'مفعل' : 'معطل';
                $status_class = $auto_approval ? 'enabled' : 'disabled';
                ?>
                
                <div class="status-box <?php echo $status_class; ?>">
                    <h3 class="text-lg font-medium">التفعيل التلقائي للتقييمات: <?php echo $auto_approval_value; ?></h3>
                    <p class="mt-2">القيمة في قاعدة البيانات: <?php echo var_export($auto_approval, true); ?></p>
                </div>
                
                <!-- أزرار التحكم -->
                <div class="mt-4 space-x-4 space-x-reverse">
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="enable_auto_approval">
                        <button type="submit" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">
                            تفعيل التلقائي
                        </button>
                    </form>
                    
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="disable_auto_approval">
                        <button type="submit" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded">
                            إلغاء التفعيل التلقائي
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- اختبار حفظ التقييم -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">اختبار حفظ تقييم جديد</h2>
                
                <form method="POST" class="space-y-4">
                    <input type="hidden" name="action" value="test_review">
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">معرف المنتج</label>
                        <input type="number" name="product_id" value="8" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">التقييم (1-5)</label>
                        <select name="rating" required class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                            <option value="5">5 نجوم</option>
                            <option value="4">4 نجوم</option>
                            <option value="3">3 نجوم</option>
                            <option value="2">2 نجوم</option>
                            <option value="1">1 نجمة</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">عنوان التقييم</label>
                        <input type="text" name="title" value="تقييم تجريبي" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">التعليق</label>
                        <textarea name="comment" rows="3" required 
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg">هذا تقييم تجريبي لاختبار النظام - <?php echo date('Y-m-d H:i:s'); ?></textarea>
                    </div>
                    
                    <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg">
                        إرسال التقييم التجريبي
                    </button>
                </form>
            </div>
            
            <!-- عرض آخر التقييمات -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">آخر التقييمات</h2>
                
                <?php
                $recent_reviews = getProductReviews(8, 5); // آخر 5 تقييمات للمنتج 8
                if (!empty($recent_reviews)):
                ?>
                    <div class="space-y-4">
                        <?php foreach ($recent_reviews as $review): ?>
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex justify-between items-start mb-2">
                                    <div>
                                        <strong><?php echo htmlspecialchars($review['name']); ?></strong>
                                        <span class="text-gray-500 text-sm">- <?php echo $review['created_at']; ?></span>
                                    </div>
                                    <div class="flex items-center">
                                        <span class="text-yellow-500">★</span>
                                        <span class="ml-1"><?php echo $review['rating']; ?>/5</span>
                                    </div>
                                </div>
                                <p class="text-gray-700"><?php echo htmlspecialchars($review['comment']); ?></p>
                                <div class="mt-2">
                                    <span class="text-xs px-2 py-1 rounded <?php echo $review['is_approved'] ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'; ?>">
                                        <?php echo $review['is_approved'] ? 'مُعتمد' : 'في انتظار الموافقة'; ?>
                                    </span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <p class="text-gray-500">لا توجد تقييمات للمنتج بعد.</p>
                <?php endif; ?>
            </div>
            
            <!-- روابط مفيدة -->
            <div class="mt-6 text-center space-x-4 space-x-reverse">
                <a href="<?php echo SITE_URL; ?>/admin/reviews.php" 
                   class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded inline-block">
                    إدارة التقييمات
                </a>
                <a href="<?php echo SITE_URL; ?>/products/8" 
                   class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded inline-block">
                    صفحة المنتج
                </a>
                <a href="<?php echo SITE_URL; ?>/test_api_review.php" 
                   class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded inline-block">
                    اختبار API
                </a>
            </div>
        </div>
    </div>
</body>
</html>

<?php
// معالجة الطلبات
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'enable_auto_approval':
            setSetting('reviews_auto_approval', true);
            echo "<script>alert('تم تفعيل التلقائي للتقييمات'); window.location.reload();</script>";
            break;
            
        case 'disable_auto_approval':
            setSetting('reviews_auto_approval', false);
            echo "<script>alert('تم إلغاء التفعيل التلقائي للتقييمات'); window.location.reload();</script>";
            break;
            
        case 'test_review':
            $product_id = intval($_POST['product_id']);
            $rating = intval($_POST['rating']);
            $title = sanitizeInput($_POST['title']);
            $comment = sanitizeInput($_POST['comment']);
            
            $result = saveProductReview($product_id, $rating, $title, $comment);
            
            if ($result === true) {
                echo "<script>alert('تم حفظ التقييم بنجاح!'); window.location.reload();</script>";
            } else {
                $error_msg = is_string($result) ? $result : 'حدث خطأ أثناء حفظ التقييم';
                echo "<script>alert('خطأ: " . addslashes($error_msg) . "'); window.location.reload();</script>";
            }
            break;
    }
}
?>