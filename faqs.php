<?php
require_once 'config/database.php';

try {
    $database = new Database();
    $pdo = $database->getConnection();
    
    // جلب جميع الأسئلة مرتبة حسب التصنيف وترتيب العرض
    $sql = "SELECT * FROM FAQs WHERE is_active = 1 ORDER BY category, sort_order";
    $stmt = $pdo->query($sql);
    $faqs = $stmt->fetchAll();
    
    // تجميع الأسئلة حسب التصنيف
    $faqsByCategory = [];
    foreach ($faqs as $faq) {
        $faqsByCategory[$faq['category']][] = $faq;
    }
    
} catch (Exception $e) {
    die("خطأ في قاعدة البيانات: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الأسئلة الشائعة - Green Line</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .faq-header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 60px 0;
            text-align: center;
        }
        .faq-category {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }
        .category-header {
            background: #28a745;
            color: white;
            padding: 20px;
            font-size: 1.3rem;
            font-weight: bold;
        }
        .faq-item {
            border-bottom: 1px solid #eee;
        }
        .faq-item:last-child {
            border-bottom: none;
        }
        .faq-question {
            background: #f8f9fa;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            width: 100%;
            text-align: right;
            font-size: 1.1rem;
            font-weight: 500;
        }
        .faq-question:hover {
            background: #e9ecef;
        }
        .faq-answer {
            padding: 20px;
            background: white;
            line-height: 1.8;
            color: #555;
        }
        .collapse-icon {
            transition: transform 0.3s ease;
        }
        .collapsed .collapse-icon {
            transform: rotate(180deg);
        }
        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: #28a745;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="faq-header">
        <div class="container">
            <h1><i class="fas fa-question-circle me-3"></i>الأسئلة الشائعة</h1>
            <p class="lead">إجابات شاملة على جميع استفساراتكم حول منتجاتنا وخدماتنا</p>
        </div>
    </div>

    <div class="container my-5">
        <!-- إحصائيات -->
        <div class="row mb-5">
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="stats-number"><?= count($faqs) ?></div>
                    <div>إجمالي الأسئلة</div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="stats-number"><?= count($faqsByCategory) ?></div>
                    <div>التصنيفات</div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="stats-number"><?= count(array_filter($faqs, function($faq) { return $faq['is_active']; })) ?></div>
                    <div>أسئلة نشطة</div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="stats-number"><?= date('Y-m-d') ?></div>
                    <div>آخر تحديث</div>
                </div>
            </div>
        </div>

        <!-- الأسئلة الشائعة -->
        <?php foreach ($faqsByCategory as $category => $categoryFaqs): ?>
        <div class="faq-category">
            <div class="category-header">
                <i class="fas fa-folder me-2"></i>
                <?= htmlspecialchars($category) ?>
                <span class="badge bg-light text-dark ms-2"><?= count($categoryFaqs) ?> سؤال</span>
            </div>
            
            <div class="accordion" id="accordion<?= md5($category) ?>">
                <?php foreach ($categoryFaqs as $index => $faq): ?>
                <div class="faq-item">
                    <button class="faq-question collapsed" type="button" 
                            data-bs-toggle="collapse" 
                            data-bs-target="#faq<?= $faq['id'] ?>" 
                            aria-expanded="false">
                        <div class="d-flex justify-content-between align-items-center">
                            <span><?= htmlspecialchars($faq['question']) ?></span>
                            <i class="fas fa-chevron-down collapse-icon"></i>
                        </div>
                    </button>
                    
                    <div id="faq<?= $faq['id'] ?>" class="collapse" 
                         data-bs-parent="#accordion<?= md5($category) ?>">
                        <div class="faq-answer">
                            <?= nl2br(htmlspecialchars($faq['answer'])) ?>
                            <div class="mt-3 text-muted small">
                                <i class="fas fa-clock me-1"></i>
                                تم الإنشاء: <?= date('d/m/Y', strtotime($faq['created_at'])) ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endforeach; ?>

        <!-- معلومات الاتصال -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <h4>لم تجد إجابة لسؤالك؟</h4>
                        <p class="text-muted">فريق خدمة العملاء متاح لمساعدتك على مدار الساعة</p>
                        <div class="row">
                            <div class="col-md-4">
                                <i class="fas fa-phone fa-2x text-success mb-2"></i>
                                <h6>اتصل بنا</h6>
                                <p>920001234</p>
                            </div>
                            <div class="col-md-4">
                                <i class="fab fa-whatsapp fa-2x text-success mb-2"></i>
                                <h6>واتساب</h6>
                                <p>966501234567+</p>
                            </div>
                            <div class="col-md-4">
                                <i class="fas fa-envelope fa-2x text-success mb-2"></i>
                                <h6>البريد الإلكتروني</h6>
                                <p><EMAIL></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // إضافة تأثير للأيقونة عند فتح/إغلاق السؤال
        document.addEventListener('DOMContentLoaded', function() {
            const collapseElements = document.querySelectorAll('.collapse');
            
            collapseElements.forEach(function(element) {
                element.addEventListener('show.bs.collapse', function() {
                    const button = document.querySelector(`[data-bs-target="#${element.id}"]`);
                    button.classList.remove('collapsed');
                });
                
                element.addEventListener('hide.bs.collapse', function() {
                    const button = document.querySelector(`[data-bs-target="#${element.id}"]`);
                    button.classList.add('collapsed');
                });
            });
        });
    </script>
</body>
</html>