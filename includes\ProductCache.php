<?php

/**
 * كلاس إدارة التخزين المؤقت للمنتجات
 * يوفر وظائف التخزين المؤقت لتحسين الأداء
 */
class ProductCache {
    
    private $cache_dir;
    private $cache_duration;
    private $enabled;
    
    public function __construct() {
        $config = include __DIR__ . '/../config/products_config.php';
        $this->cache_dir = __DIR__ . '/../cache/products/';
        $this->cache_duration = $config['cache']['cache_duration'];
        $this->enabled = $config['cache']['enable_cache'];
        
        // إنشاء مجلد التخزين المؤقت إذا لم يكن موجوداً
        if (!is_dir($this->cache_dir)) {
            mkdir($this->cache_dir, 0755, true);
        }
    }
    
    /**
     * حفظ البيانات في التخزين المؤقت
     */
    public function set($key, $data, $duration = null) {
        if (!$this->enabled) {
            return false;
        }
        
        $duration = $duration ?: $this->cache_duration;
        $cache_file = $this->getCacheFile($key);
        
        $cache_data = [
            'data' => $data,
            'expires' => time() + $duration,
            'created' => time()
        ];
        
        return file_put_contents($cache_file, serialize($cache_data)) !== false;
    }
    
    /**
     * استرجاع البيانات من التخزين المؤقت
     */
    public function get($key) {
        if (!$this->enabled) {
            return null;
        }
        
        $cache_file = $this->getCacheFile($key);
        
        if (!file_exists($cache_file)) {
            return null;
        }
        
        $cache_data = unserialize(file_get_contents($cache_file));
        
        if (!$cache_data || $cache_data['expires'] < time()) {
            $this->delete($key);
            return null;
        }
        
        return $cache_data['data'];
    }
    
    /**
     * حذف عنصر من التخزين المؤقت
     */
    public function delete($key) {
        $cache_file = $this->getCacheFile($key);
        
        if (file_exists($cache_file)) {
            return unlink($cache_file);
        }
        
        return true;
    }
    
    /**
     * مسح جميع ملفات التخزين المؤقت
     */
    public function clear() {
        $files = glob($this->cache_dir . '*.cache');
        
        foreach ($files as $file) {
            unlink($file);
        }
        
        return true;
    }
    
    /**
     * مسح ملفات التخزين المؤقت المنتهية الصلاحية
     */
    public function cleanup() {
        $files = glob($this->cache_dir . '*.cache');
        $cleaned = 0;
        
        foreach ($files as $file) {
            $cache_data = unserialize(file_get_contents($file));
            
            if (!$cache_data || $cache_data['expires'] < time()) {
                unlink($file);
                $cleaned++;
            }
        }
        
        return $cleaned;
    }
    
    /**
     * التحقق من وجود عنصر في التخزين المؤقت
     */
    public function exists($key) {
        return $this->get($key) !== null;
    }
    
    /**
     * الحصول على معلومات التخزين المؤقت
     */
    public function getStats() {
        $files = glob($this->cache_dir . '*.cache');
        $total_size = 0;
        $expired = 0;
        $valid = 0;
        
        foreach ($files as $file) {
            $total_size += filesize($file);
            $cache_data = unserialize(file_get_contents($file));
            
            if (!$cache_data || $cache_data['expires'] < time()) {
                $expired++;
            } else {
                $valid++;
            }
        }
        
        return [
            'total_files' => count($files),
            'valid_files' => $valid,
            'expired_files' => $expired,
            'total_size' => $total_size,
            'total_size_mb' => round($total_size / 1024 / 1024, 2),
            'cache_dir' => $this->cache_dir,
            'enabled' => $this->enabled
        ];
    }
    
    /**
     * تذكر البيانات (استرجاع من التخزين المؤقت أو تنفيذ الدالة)
     */
    public function remember($key, $callback, $duration = null) {
        $data = $this->get($key);
        
        if ($data === null) {
            $data = $callback();
            $this->set($key, $data, $duration);
        }
        
        return $data;
    }
    
    /**
     * تذكر إلى الأبد (لا ينتهي إلا بالحذف اليدوي)
     */
    public function rememberForever($key, $callback) {
        return $this->remember($key, $callback, 365 * 24 * 60 * 60); // سنة واحدة
    }
    
    /**
     * إنشاء مفتاح تخزين مؤقت من المعاملات
     */
    public function generateKey($prefix, $params = []) {
        $key_parts = [$prefix];
        
        foreach ($params as $param) {
            $key_parts[] = is_array($param) ? md5(serialize($param)) : $param;
        }
        
        return implode('_', $key_parts);
    }
    
    /**
     * تحديث وقت انتهاء الصلاحية
     */
    public function touch($key, $duration = null) {
        $data = $this->get($key);
        
        if ($data !== null) {
            return $this->set($key, $data, $duration);
        }
        
        return false;
    }
    
    /**
     * الحصول على مسار ملف التخزين المؤقت
     */
    private function getCacheFile($key) {
        $safe_key = preg_replace('/[^a-zA-Z0-9_-]/', '_', $key);
        return $this->cache_dir . $safe_key . '.cache';
    }
    
    /**
     * تمكين أو تعطيل التخزين المؤقت
     */
    public function setEnabled($enabled) {
        $this->enabled = $enabled;
    }
    
    /**
     * التحقق من حالة التخزين المؤقت
     */
    public function isEnabled() {
        return $this->enabled;
    }
    
    /**
     * تعيين مدة التخزين المؤقت الافتراضية
     */
    public function setCacheDuration($duration) {
        $this->cache_duration = $duration;
    }
    
    /**
     * الحصول على مدة التخزين المؤقت الافتراضية
     */
    public function getCacheDuration() {
        return $this->cache_duration;
    }
    
    /**
     * حفظ البيانات مع ضغط
     */
    public function setCompressed($key, $data, $duration = null) {
        if (!$this->enabled) {
            return false;
        }
        
        $compressed_data = gzcompress(serialize($data));
        
        $cache_data = [
            'data' => $compressed_data,
            'compressed' => true,
            'expires' => time() + ($duration ?: $this->cache_duration),
            'created' => time()
        ];
        
        $cache_file = $this->getCacheFile($key);
        return file_put_contents($cache_file, serialize($cache_data)) !== false;
    }
    
    /**
     * استرجاع البيانات المضغوطة
     */
    public function getCompressed($key) {
        if (!$this->enabled) {
            return null;
        }
        
        $cache_file = $this->getCacheFile($key);
        
        if (!file_exists($cache_file)) {
            return null;
        }
        
        $cache_data = unserialize(file_get_contents($cache_file));
        
        if (!$cache_data || $cache_data['expires'] < time()) {
            $this->delete($key);
            return null;
        }
        
        if (isset($cache_data['compressed']) && $cache_data['compressed']) {
            return unserialize(gzuncompress($cache_data['data']));
        }
        
        return $cache_data['data'];
    }
}