<?php
/**
 * لوحة التحكم الرئيسية للإدارة
 * Admin Dashboard
 */

// تعريف متغيرات الصفحة
$pageTitle = 'لوحة التحكم';
$currentPage = 'dashboard';
$pageDescription = 'نظرة عامة على إحصائيات النظام';
$breadcrumbs = [];

// تضمين التخطيط
require_once 'includes/layout.php';

// بدء التخطيط
startLayout();

// جلب الإحصائيات العامة
$stats = [];

// إحصائيات المستخدمين
$stmt = $db->prepare("SELECT COUNT(*) FROM users");
$stmt->execute();
$stats['total_users'] = $stmt->fetchColumn();

$stmt = $db->prepare("SELECT COUNT(*) FROM users WHERE status = 'active'");
$stmt->execute();
$stats['active_users'] = $stmt->fetchColumn();

$stmt = $db->prepare("SELECT COUNT(*) FROM users WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)");
$stmt->execute();
$stats['new_users_month'] = $stmt->fetchColumn();

// إحصائيات الأدوار والصلاحيات
$stmt = $db->prepare("SELECT COUNT(*) FROM roles");
$stmt->execute();
$stats['total_roles'] = $stmt->fetchColumn();

$stmt = $db->prepare("SELECT COUNT(*) FROM permissions");
$stmt->execute();
$stats['total_permissions'] = $stmt->fetchColumn();

$stmt = $db->prepare("SELECT COUNT(DISTINCT category) FROM permissions");
$stmt->execute();
$stats['permission_categories'] = $stmt->fetchColumn();

// إحصائيات الطلبات (إذا كانت موجودة)
$stmt = $db->prepare("SHOW TABLES LIKE 'orders'");
$stmt->execute();
if ($stmt->fetch()) {
    $stmt = $db->prepare("SELECT COUNT(*) FROM orders");
    $stmt->execute();
    $stats['total_orders'] = $stmt->fetchColumn();
    
    $stmt = $db->prepare("SELECT COUNT(*) FROM orders WHERE status = 'pending'");
    $stmt->execute();
    $stats['pending_orders'] = $stmt->fetchColumn();
} else {
    $stats['total_orders'] = 0;
    $stats['pending_orders'] = 0;
}

// إحصائيات المنتجات (إذا كانت موجودة)
$stmt = $db->prepare("SHOW TABLES LIKE 'products'");
$stmt->execute();
if ($stmt->fetch()) {
    $stmt = $db->prepare("SELECT COUNT(*) FROM products");
    $stmt->execute();
    $stats['total_products'] = $stmt->fetchColumn();
    
    $stmt = $db->prepare("SELECT COUNT(*) FROM products WHERE status = 'active'");
    $stmt->execute();
    $stats['active_products'] = $stmt->fetchColumn();
} else {
    $stats['total_products'] = 0;
    $stats['active_products'] = 0;
}

// جلب آخر المستخدمين المسجلين
$stmt = $db->prepare("
    SELECT u.*, 
           GROUP_CONCAT(r.name SEPARATOR ', ') as roles
    FROM users u
    LEFT JOIN user_roles ur ON u.id = ur.user_id
    LEFT JOIN roles r ON ur.role_id = r.id
    GROUP BY u.id
    ORDER BY u.created_at DESC
    LIMIT 5
");
$stmt->execute();
$recent_users = $stmt->fetchAll(PDO::FETCH_ASSOC);

// جلب الأدوار الأكثر استخداماً
$stmt = $db->prepare("
    SELECT r.name, r.description, COUNT(ur.user_id) as user_count
    FROM roles r
    LEFT JOIN user_roles ur ON r.id = ur.role_id
    GROUP BY r.id
    ORDER BY user_count DESC
    LIMIT 5
");
$stmt->execute();
$popular_roles = $stmt->fetchAll(PDO::FETCH_ASSOC);

// جلب الصلاحيات حسب الفئة
$stmt = $db->prepare("
    SELECT category, COUNT(*) as permission_count
    FROM permissions
    GROUP BY category
    ORDER BY permission_count DESC
");
$stmt->execute();
$permission_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);

// عرض رأس الصفحة والرسائل
showPageHeader();
showMessages();
?>

<!-- ترحيب بالمستخدم -->
<div class="bg-white rounded-lg shadow-sm p-6 mb-6">
    <div class="flex items-center justify-between">
        <div>
            <h2 class="text-2xl font-bold text-gray-900">مرحباً، <?php echo htmlspecialchars($_SESSION['user_name']); ?></h2>
            <p class="text-gray-600 mt-1">إليك نظرة عامة على أداء النظام اليوم</p>
        </div>
        <div class="text-sm text-gray-500">
            <?php echo date('Y-m-d H:i'); ?>
        </div>
    </div>
</div>

<!-- الإحصائيات الرئيسية -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- إجمالي المستخدمين -->
    <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">إجمالي المستخدمين</p>
                <p class="text-3xl font-bold text-gray-900"><?php echo number_format($stats['total_users']); ?></p>
                <p class="text-sm text-green-600 mt-1">
                    <i class="fas fa-arrow-up"></i> <?php echo $stats['active_users']; ?> نشط
                </p>
            </div>
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-users text-blue-600 text-xl"></i>
            </div>
        </div>
    </div>

    <!-- الأدوار والصلاحيات -->
    <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">الأدوار</p>
                <p class="text-3xl font-bold text-gray-900"><?php echo number_format($stats['total_roles']); ?></p>
                <p class="text-sm text-blue-600 mt-1">
                    <i class="fas fa-shield-alt"></i> <?php echo $stats['total_permissions']; ?> صلاحية
                </p>
            </div>
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-user-shield text-green-600 text-xl"></i>
            </div>
        </div>
    </div>

    <!-- الطلبات -->
    <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">الطلبات</p>
                <p class="text-3xl font-bold text-gray-900"><?php echo number_format($stats['total_orders']); ?></p>
                <p class="text-sm text-yellow-600 mt-1">
                    <i class="fas fa-clock"></i> <?php echo $stats['pending_orders']; ?> في الانتظار
                </p>
            </div>
            <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-shopping-cart text-yellow-600 text-xl"></i>
            </div>
        </div>
    </div>

    <!-- المنتجات -->
    <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">المنتجات</p>
                <p class="text-3xl font-bold text-gray-900"><?php echo number_format($stats['total_products']); ?></p>
                <p class="text-sm text-green-600 mt-1">
                    <i class="fas fa-check-circle"></i> <?php echo $stats['active_products']; ?> نشط
                </p>
            </div>
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-box text-purple-600 text-xl"></i>
            </div>
        </div>
    </div>
</div>

<!-- الجداول والمعلومات التفصيلية -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- آخر المستخدمين -->
    <div class="bg-white rounded-lg shadow-sm">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">آخر المستخدمين المسجلين</h3>
        </div>
        <div class="p-6">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المستخدم</th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الأدوار</th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التاريخ</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($recent_users as $user): ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div>
                                    <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($user['name']); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo htmlspecialchars($user['email']); ?></div>
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <?php if ($user['roles']): ?>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                        <?php echo htmlspecialchars($user['roles']); ?>
                                    </span>
                                <?php else: ?>
                                    <span class="text-gray-400 text-sm">لا يوجد أدوار</span>
                                <?php endif; ?>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <?php if ($user['status'] === 'active'): ?>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">نشط</span>
                                <?php else: ?>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">غير نشط</span>
                                <?php endif; ?>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo date('Y-m-d', strtotime($user['created_at'])); ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <div class="mt-4 text-center">
                <a href="users.php" class="inline-flex items-center px-4 py-2 border border-blue-300 text-sm font-medium rounded-md text-blue-700 bg-white hover:bg-blue-50">
                    عرض جميع المستخدمين
                </a>
            </div>
        </div>
    </div>

    <!-- الأدوار الأكثر استخداماً -->
    <div class="bg-white rounded-lg shadow-sm">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">الأدوار الأكثر استخداماً</h3>
        </div>
        <div class="p-6">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الدور</th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الوصف</th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المستخدمين</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($popular_roles as $role): ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($role['name']); ?></div>
                            </td>
                            <td class="px-4 py-4">
                                <div class="text-sm text-gray-500"><?php echo htmlspecialchars($role['description'] ?? 'لا يوجد وصف'); ?></div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
                                    <?php echo $role['user_count']; ?>
                                </span>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <div class="mt-4 text-center">
                <a href="roles.php" class="inline-flex items-center px-4 py-2 border border-blue-300 text-sm font-medium rounded-md text-blue-700 bg-white hover:bg-blue-50">
                    إدارة الأدوار
                </a>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات الصلاحيات -->
<div class="bg-white rounded-lg shadow-sm mb-8">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">توزيع الصلاحيات حسب الفئة</h3>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <?php foreach ($permission_stats as $stat): ?>
            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <h4 class="text-base font-medium text-gray-900 mb-2"><?php echo htmlspecialchars($stat['category']); ?></h4>
                <p class="text-gray-600">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                        <?php echo $stat['permission_count']; ?>
                    </span> صلاحية
                </p>
            </div>
            <?php endforeach; ?>
        </div>
        <div class="mt-6 text-center">
            <a href="permissions.php" class="inline-flex items-center px-4 py-2 border border-blue-300 text-sm font-medium rounded-md text-blue-700 bg-white hover:bg-blue-50">
                إدارة الصلاحيات
            </a>
        </div>
    </div>
</div>

<!-- روابط سريعة -->
<div class="bg-white rounded-lg shadow-sm">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">روابط سريعة</h3>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <a href="users.php" class="flex items-center justify-center px-4 py-3 border border-blue-300 rounded-lg text-blue-700 hover:bg-blue-50 transition-colors">
                <i class="fas fa-users ml-2"></i>
                إدارة المستخدمين
            </a>
            <a href="roles.php" class="flex items-center justify-center px-4 py-3 border border-green-300 rounded-lg text-green-700 hover:bg-green-50 transition-colors">
                <i class="fas fa-user-shield ml-2"></i>
                إدارة الأدوار
            </a>
            <a href="permissions.php" class="flex items-center justify-center px-4 py-3 border border-purple-300 rounded-lg text-purple-700 hover:bg-purple-50 transition-colors">
                <i class="fas fa-shield-alt ml-2"></i>
                إدارة الصلاحيات
            </a>
            <a href="manage_user_roles.php" class="flex items-center justify-center px-4 py-3 border border-yellow-300 rounded-lg text-yellow-700 hover:bg-yellow-50 transition-colors">
                <i class="fas fa-user-cog ml-2"></i>
                أدوار المستخدمين
            </a>
        </div>
    </div>
</div>

<?php
// إنهاء التخطيط
endLayout();
?>