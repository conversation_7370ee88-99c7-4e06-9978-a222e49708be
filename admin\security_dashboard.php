<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم الأمنية - Green Line</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .security-card { border-left: 4px solid #007bff; }
        .alert-critical { border-left-color: #dc3545 !important; }
        .alert-high { border-left-color: #fd7e14 !important; }
        .alert-medium { border-left-color: #ffc107 !important; }
        .alert-low { border-left-color: #28a745 !important; }
        .stats-card { transition: transform 0.2s; }
        .stats-card:hover { transform: translateY(-2px); }
        .chart-container { height: 300px; }
    </style>
</head>
<body class="bg-light">
    <?php
    require_once '../config/database.php';
    require_once '../includes/security_monitor.php';
    require_once '../config/config.php';
    
    // التحقق من صلاحيات المدير
    if (!isLoggedIn() || !hasPermission('admin')) {
        header('Location: login.php');
        exit;
    }
    
    $monitor = new SecurityMonitor();
    $alerts = $monitor->getActiveAlerts(20);
    $stats = $monitor->getSecurityStats(30);
    ?>
    
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-shield-alt me-2"></i>
                لوحة التحكم الأمنية
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../admin/">العودة للوحة الرئيسية</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card text-white bg-primary">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4><?= number_format($stats['total_events']) ?></h4>
                                <p class="mb-0">إجمالي الأحداث (30 يوم)</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-exclamation-triangle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card text-white bg-danger">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4><?= count(array_filter($alerts, fn($a) => $a['severity'] === 'critical')) ?></h4>
                                <p class="mb-0">تنبيهات حرجة</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-fire fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card text-white bg-warning">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4><?= count(array_filter($alerts, fn($a) => $a['severity'] === 'high')) ?></h4>
                                <p class="mb-0">تنبيهات عالية</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-exclamation fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card text-white bg-success">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4><?= count(array_filter($alerts, fn($a) => $a['status'] === 'resolved')) ?></h4>
                                <p class="mb-0">تنبيهات محلولة</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-check fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- التنبيهات النشطة -->
            <div class="col-lg-8">
                <div class="card security-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-bell text-warning me-2"></i>
                            التنبيهات النشطة
                        </h5>
                        <button class="btn btn-sm btn-outline-primary" onclick="refreshAlerts()">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>النوع</th>
                                        <th>العنوان</th>
                                        <th>المستوى</th>
                                        <th>عدد الأحداث</th>
                                        <th>آخر حدوث</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="alertsTable">
                                    <?php foreach ($alerts as $alert): ?>
                                    <tr class="alert-<?= $alert['severity'] ?>">
                                        <td>
                                            <span class="badge bg-secondary"><?= htmlspecialchars($alert['alert_type']) ?></span>
                                        </td>
                                        <td><?= htmlspecialchars($alert['title']) ?></td>
                                        <td>
                                            <span class="badge bg-<?= getSeverityColor($alert['severity']) ?>">
                                                <?= getSeverityText($alert['severity']) ?>
                                            </span>
                                        </td>
                                        <td><?= $alert['event_count'] ?></td>
                                        <td><?= date('Y-m-d H:i', strtotime($alert['last_occurrence'])) ?></td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-info" onclick="viewAlert(<?= $alert['id'] ?>)">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-success" onclick="resolveAlert(<?= $alert['id'] ?>)">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إحصائيات الأحداث -->
            <div class="col-lg-4">
                <div class="card security-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-pie text-info me-2"></i>
                            الأحداث حسب النوع
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="eventsChart" class="chart-container"></canvas>
                    </div>
                </div>

                <div class="card security-card mt-3">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-globe text-success me-2"></i>
                            أكثر عناوين IP نشاطاً
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group list-group-flush">
                            <?php foreach (array_slice($stats['top_ips'], 0, 5) as $ip): ?>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <span class="font-monospace"><?= htmlspecialchars($ip['ip_address']) ?></span>
                                <span class="badge bg-primary rounded-pill"><?= $ip['count'] ?></span>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الأحداث حسب المستوى -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card security-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar text-warning me-2"></i>
                            توزيع الأحداث حسب المستوى (آخر 30 يوم)
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="severityChart" height="100"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal لعرض تفاصيل التنبيه -->
    <div class="modal fade" id="alertModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تفاصيل التنبيه</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="alertDetails">
                    <!-- سيتم تحميل التفاصيل هنا -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-success" onclick="resolveCurrentAlert()">حل التنبيه</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        let currentAlertId = null;

        // رسم بياني للأحداث حسب النوع
        const eventsData = <?= json_encode($stats['events_by_type']) ?>;
        const eventsChart = new Chart(document.getElementById('eventsChart'), {
            type: 'doughnut',
            data: {
                labels: eventsData.map(item => item.event_type),
                datasets: [{
                    data: eventsData.map(item => item.count),
                    backgroundColor: [
                        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });

        // رسم بياني للأحداث حسب المستوى
        const severityData = <?= json_encode($stats['events_by_severity']) ?>;
        const severityChart = new Chart(document.getElementById('severityChart'), {
            type: 'bar',
            data: {
                labels: severityData.map(item => getSeverityTextAr(item.severity)),
                datasets: [{
                    label: 'عدد الأحداث',
                    data: severityData.map(item => item.count),
                    backgroundColor: severityData.map(item => getSeverityChartColor(item.severity))
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        function getSeverityTextAr(severity) {
            const map = {
                'critical': 'حرج',
                'high': 'عالي',
                'medium': 'متوسط',
                'low': 'منخفض'
            };
            return map[severity] || severity;
        }

        function getSeverityChartColor(severity) {
            const map = {
                'critical': '#dc3545',
                'high': '#fd7e14',
                'medium': '#ffc107',
                'low': '#28a745'
            };
            return map[severity] || '#6c757d';
        }

        function viewAlert(alertId) {
            currentAlertId = alertId;
            fetch(`security_api.php?action=get_alert&id=${alertId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('alertDetails').innerHTML = formatAlertDetails(data.alert);
                        new bootstrap.Modal(document.getElementById('alertModal')).show();
                    }
                });
        }

        function formatAlertDetails(alert) {
            return `
                <div class="row">
                    <div class="col-md-6">
                        <strong>النوع:</strong> ${alert.alert_type}<br>
                        <strong>المستوى:</strong> <span class="badge bg-${getSeverityBootstrapColor(alert.severity)}">${getSeverityTextAr(alert.severity)}</span><br>
                        <strong>الحالة:</strong> ${alert.status}<br>
                        <strong>عدد الأحداث:</strong> ${alert.event_count}
                    </div>
                    <div class="col-md-6">
                        <strong>أول حدوث:</strong> ${alert.first_occurrence}<br>
                        <strong>آخر حدوث:</strong> ${alert.last_occurrence}<br>
                    </div>
                </div>
                <hr>
                <div>
                    <strong>الوصف:</strong><br>
                    <p class="text-muted">${alert.description}</p>
                </div>
                <div>
                    <strong>بيانات إضافية:</strong><br>
                    <pre class="bg-light p-2 rounded">${JSON.stringify(JSON.parse(alert.event_data || '{}'), null, 2)}</pre>
                </div>
            `;
        }

        function getSeverityBootstrapColor(severity) {
            const map = {
                'critical': 'danger',
                'high': 'warning',
                'medium': 'info',
                'low': 'success'
            };
            return map[severity] || 'secondary';
        }

        function resolveAlert(alertId) {
            if (confirm('هل أنت متأكد من حل هذا التنبيه؟')) {
                fetch('security_api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'resolve_alert',
                        alert_id: alertId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('حدث خطأ: ' + data.message);
                    }
                });
            }
        }

        function resolveCurrentAlert() {
            if (currentAlertId) {
                resolveAlert(currentAlertId);
            }
        }

        function refreshAlerts() {
            location.reload();
        }

        // تحديث تلقائي كل 30 ثانية
        setInterval(refreshAlerts, 30000);
    </script>

    <?php
    function getSeverityColor($severity) {
        $colors = [
            'critical' => 'danger',
            'high' => 'warning',
            'medium' => 'info',
            'low' => 'success'
        ];
        return $colors[$severity] ?? 'secondary';
    }

    function getSeverityText($severity) {
        $texts = [
            'critical' => 'حرج',
            'high' => 'عالي',
            'medium' => 'متوسط',
            'low' => 'منخفض'
        ];
        return $texts[$severity] ?? $severity;
    }
    ?>
</body>
</html>