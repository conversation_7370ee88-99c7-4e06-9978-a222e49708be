<?php
$pageTitle = 'المنتجات';
$page = 'products';

// معالجة المعاملات
$category = $_GET['category'] ?? '';
$search = $_GET['search'] ?? '';
$sort = $_GET['sort'] ?? 'name';
$page_num = max(1, intval($_GET['page'] ?? 1));
$per_page = 12;

// جلب البيانات
$categories = getProductCategories();
$filters = [
    'category' => $category,
    'search' => $search,
    'sort' => $sort,
    'page' => $page_num,
    'per_page' => $per_page
];

$products_data = getProducts($filters);
$products = $products_data['products'];
$total_products = $products_data['total'];
$total_pages = ceil($total_products / $per_page);

// معلومات الفئة المحددة
$current_category = null;
if ($category) {
    foreach ($categories as $cat) {
        if ($cat['id'] == $category) {
            $current_category = $cat;
            break;
        }
    }
}

include 'header.php';
?>

<!-- Page Header -->
<section class="bg-gradient-to-r from-primary to-secondary text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-bold mb-4">
                <?php if ($current_category): ?>
                    <?php echo htmlspecialchars($current_category['name']); ?>
                <?php elseif ($search): ?>
                    نتائج البحث عن: "<?php echo htmlspecialchars($search); ?>"
                <?php else: ?>
                    جميع المنتجات
                <?php endif; ?>
            </h1>
            <p class="text-xl opacity-90">
                <?php if ($current_category): ?>
                    <?php echo htmlspecialchars($current_category['description']); ?>
                <?php else: ?>
                    اكتشف مجموعتنا الواسعة من المنتجات عالية الجودة
                <?php endif; ?>
            </p>
            
            <!-- Breadcrumb -->
            <nav class="mt-6">
                <ol class="flex items-center justify-center space-x-2 space-x-reverse text-sm">
                    <li><a href="<?php echo SITE_URL; ?>" class="hover:text-gray-200">الرئيسية</a></li>
                    <li class="mx-2">/</li>
                    <li><a href="<?php echo SITE_URL; ?>/products" class="hover:text-gray-200">المنتجات</a></li>
                    <?php if ($current_category): ?>
                        <li class="mx-2">/</li>
                        <li class="text-gray-200"><?php echo htmlspecialchars($current_category['name']); ?></li>
                    <?php endif; ?>
                </ol>
            </nav>
        </div>
    </div>
</section>

<!-- Products Section -->
<section class="py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <!-- Filters and Search -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <form method="GET" action="<?php echo SITE_URL; ?>/products" class="space-y-4 md:space-y-0 md:flex md:items-center md:space-x-4 md:space-x-reverse">
                
                <!-- Search -->
                <div class="flex-1">
                    <div class="relative">
                        <input type="text" 
                               name="search" 
                               value="<?php echo htmlspecialchars($search); ?>"
                               placeholder="ابحث عن المنتجات..."
                               class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
                
                <!-- Category Filter -->
                <div class="md:w-48">
                    <select name="category" class="w-full py-2 px-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="">جميع الفئات</option>
                        <?php foreach ($categories as $cat): ?>
                            <option value="<?php echo $cat['id']; ?>" <?php echo $category == $cat['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($cat['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <!-- Sort -->
                <div class="md:w-48">
                    <select name="sort" class="w-full py-2 px-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="name" <?php echo $sort === 'name' ? 'selected' : ''; ?>>الاسم (أ-ي)</option>
                        <option value="name_desc" <?php echo $sort === 'name_desc' ? 'selected' : ''; ?>>الاسم (ي-أ)</option>
                        <option value="newest" <?php echo $sort === 'newest' ? 'selected' : ''; ?>>الأحدث</option>
                        <option value="oldest" <?php echo $sort === 'oldest' ? 'selected' : ''; ?>>الأقدم</option>
                        <option value="rating" <?php echo $sort === 'rating' ? 'selected' : ''; ?>>الأعلى تقييماً</option>
                    </select>
                </div>
                
                <!-- Submit Button -->
                <div>
                    <button type="submit" class="w-full md:w-auto bg-primary hover:bg-secondary text-white font-medium py-2 px-6 rounded-lg transition-colors duration-300">
                        بحث
                    </button>
                </div>
                
                <!-- Clear Filters -->
                <?php if ($search || $category || $sort !== 'name'): ?>
                    <div>
                        <a href="<?php echo SITE_URL; ?>/products" class="w-full md:w-auto inline-block text-center bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-6 rounded-lg transition-colors duration-300">
                            مسح الفلاتر
                        </a>
                    </div>
                <?php endif; ?>
            </form>
        </div>
        
        <!-- Results Info -->
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
            <div class="text-gray-600 mb-4 md:mb-0">
                عرض <?php echo count($products); ?> من أصل <?php echo $total_products; ?> منتج
                <?php if ($search): ?>
                    للبحث عن "<?php echo htmlspecialchars($search); ?>"
                <?php endif; ?>
            </div>
            
            <!-- View Toggle -->
            <div class="flex items-center space-x-2 space-x-reverse">
                <span class="text-sm text-gray-600 ml-2">عرض:</span>
                <button id="grid-view" class="p-2 rounded-lg bg-primary text-white">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                    </svg>
                </button>
                <button id="list-view" class="p-2 rounded-lg bg-gray-200 text-gray-600">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
        </div>
        
        <!-- Products Grid -->
        <?php if (!empty($products)): ?>
            <div id="products-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                <?php foreach ($products as $product): ?>
                    <div class="product-card bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                        <div class="relative">
                            <a href="<?php echo SITE_URL; ?>/products/<?php echo $product['id']; ?>">
                                <img src="<?php echo $product['image'] ?: '/assets/images/default-product.svg'; ?>" 
                                     alt="<?php echo htmlspecialchars($product['name']); ?>" 
                                     class="w-full h-48 object-cover hover:scale-105 transition-transform duration-300">
                            </a>
                            
                            <!-- Badges -->
                            <div class="absolute top-2 right-2 space-y-1">
                                <?php if (isset($product['featured']) && $product['featured']): ?>
                                    <span class="bg-primary text-white px-2 py-1 rounded-full text-xs font-semibold block">
                                        مميز
                                    </span>
                                <?php endif; ?>
                                <?php if (isset($product['new']) && $product['new']): ?>
                                    <span class="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-semibold block">
                                        جديد
                                    </span>
                                <?php endif; ?>
                                <?php if (isset($product['badge']) && !empty($product['badge'])): ?>
                                    <span class="bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-semibold block">
                                        <?php echo htmlspecialchars($product['badge']); ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                            
                            <!-- Quick Actions -->
                            <div class="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                <button class="bg-white bg-opacity-80 hover:bg-opacity-100 p-2 rounded-full shadow-md transition-all duration-300 mb-2 block" 
                                        onclick="toggleWishlist(<?php echo $product['id']; ?>)" 
                                        title="إضافة للمفضلة">
                                    <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        
                        <div class="p-4">
                            <div class="mb-2">
                                <span class="text-xs text-primary font-medium">
                                    <?php echo htmlspecialchars($product['category_name'] ?? 'غير محدد'); ?>
                                </span>
                            </div>
                            
                            <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                                <a href="<?php echo SITE_URL; ?>/products/<?php echo $product['id']; ?>" class="hover:text-primary transition-colors">
                                    <?php echo htmlspecialchars($product['name']); ?>
                                </a>
                            </h3>
                            
                            <p class="text-gray-600 text-sm mb-3 line-clamp-2">
                                <?php echo htmlspecialchars(substr($product['description'], 0, 80)) . '...'; ?>
                            </p>
                            
                            <!-- Rating -->
                            <?php if ($product['rating'] > 0): ?>
                                <div class="flex items-center mb-3">
                                    <div class="flex items-center ml-2">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <svg class="w-4 h-4 <?php echo $i <= $product['rating'] ? 'text-yellow-400' : 'text-gray-300'; ?>" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                            </svg>
                                        <?php endfor; ?>
                                    </div>
                                    <span class="text-sm text-gray-500">(<?php echo $product['rating']; ?>)</span>
                                </div>
                            <?php endif; ?>
                            
                            <!-- Actions -->
                            <div class="flex items-center justify-between">
                                <a href="<?php echo SITE_URL; ?>/products/<?php echo $product['id']; ?>" 
                                   class="bg-primary hover:bg-secondary text-white px-4 py-2 rounded-lg transition-colors duration-300 text-sm font-medium">
                                    عرض التفاصيل
                                </a>
                                
                                <button onclick="requestQuote(<?php echo $product['id']; ?>)" 
                                        class="text-primary hover:text-secondary font-medium text-sm transition-colors duration-300">
                                    طلب عرض سعر
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div class="mt-12 flex justify-center">
                    <nav class="flex items-center space-x-1 space-x-reverse">
                        <!-- Previous Page -->
                        <?php if ($page_num > 1): ?>
                            <a href="<?php echo SITE_URL; ?>/products?<?php echo http_build_query(array_merge($_GET, ['page' => $page_num - 1])); ?>" 
                               class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50">
                                السابق
                            </a>
                        <?php endif; ?>
                        
                        <!-- Page Numbers -->
                        <?php
                        $start_page = max(1, $page_num - 2);
                        $end_page = min($total_pages, $page_num + 2);
                        
                        for ($i = $start_page; $i <= $end_page; $i++):
                        ?>
                            <a href="<?php echo SITE_URL; ?>/products?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>" 
                               class="px-3 py-2 text-sm font-medium <?php echo $i === $page_num ? 'text-white bg-primary border-primary' : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50'; ?> border">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>
                        
                        <!-- Next Page -->
                        <?php if ($page_num < $total_pages): ?>
                            <a href="<?php echo SITE_URL; ?>/products?<?php echo http_build_query(array_merge($_GET, ['page' => $page_num + 1])); ?>" 
                               class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50">
                                التالي
                            </a>
                        <?php endif; ?>
                    </nav>
                </div>
            <?php endif; ?>
            
        <?php else: ?>
            <!-- No Products Found -->
            <div class="text-center py-12">
                <svg class="mx-auto h-24 w-24 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
                </svg>
                <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد منتجات</h3>
                <p class="text-gray-500 mb-6">
                    <?php if ($search): ?>
                        لم نجد أي منتجات تطابق بحثك "<?php echo htmlspecialchars($search); ?>"
                    <?php elseif ($category): ?>
                        لا توجد منتجات في هذه الفئة حالياً
                    <?php else: ?>
                        لا توجد منتجات متاحة حالياً
                    <?php endif; ?>
                </p>
                <div class="space-x-4 space-x-reverse">
                    <a href="<?php echo SITE_URL; ?>/products" class="inline-block bg-primary hover:bg-secondary text-white font-medium py-2 px-6 rounded-lg transition-colors duration-300">
                        عرض جميع المنتجات
                    </a>
                    <a href="<?php echo SITE_URL; ?>/contact" class="inline-block bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-6 rounded-lg transition-colors duration-300">
                        تواصل معنا
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>

<script>
// View toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    const gridViewBtn = document.getElementById('grid-view');
    const listViewBtn = document.getElementById('list-view');
    const productsContainer = document.getElementById('products-container');
    
    gridViewBtn.addEventListener('click', function() {
        productsContainer.className = 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6';
        gridViewBtn.classList.add('bg-primary', 'text-white');
        gridViewBtn.classList.remove('bg-gray-200', 'text-gray-600');
        listViewBtn.classList.add('bg-gray-200', 'text-gray-600');
        listViewBtn.classList.remove('bg-primary', 'text-white');
    });
    
    listViewBtn.addEventListener('click', function() {
        productsContainer.className = 'space-y-6';
        listViewBtn.classList.add('bg-primary', 'text-white');
        listViewBtn.classList.remove('bg-gray-200', 'text-gray-600');
        gridViewBtn.classList.add('bg-gray-200', 'text-gray-600');
        gridViewBtn.classList.remove('bg-primary', 'text-white');
        
        // Update product cards for list view
        const productCards = document.querySelectorAll('.product-card');
        productCards.forEach(card => {
            card.className = 'product-card bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-all duration-300 flex';
        });
    });
});

// Wishlist functionality
function toggleWishlist(productId) {
    // Implementation for wishlist toggle
    console.log('Toggle wishlist for product:', productId);
    // You can implement AJAX call here
}

// Request quote functionality
function requestQuote(productId) {
    // Implementation for quote request
    window.location.href = `<?php echo SITE_URL; ?>/contact?product=${productId}`;
}
</script>

<?php include __DIR__ . '/footer.php'; ?>