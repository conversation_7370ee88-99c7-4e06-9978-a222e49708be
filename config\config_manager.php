<?php
/**
 * Configuration Management System
 * Centralizes all application settings and environment variables
 */

class Config {
    private static $config = [];
    private static $loaded = false;
    
    public static function load($configFile = null) {
        if (self::$loaded) {
            return;
        }
        
        $configFile = $configFile ?: __DIR__ . '/app_config.php';
        
        if (file_exists($configFile)) {
            self::$config = require $configFile;
        }
        
        // Load environment-specific config
        $envFile = __DIR__ . '/.env';
        if (file_exists($envFile)) {
            self::loadEnvFile($envFile);
        }
        
        self::$loaded = true;
    }
    
    private static function loadEnvFile($envFile) {
        $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            if (strpos($line, '#') === 0) continue; // Skip comments
            
            list($key, $value) = explode('=', $line, 2);
            $key = trim($key);
            $value = trim($value, '"\'');
            
            $_ENV[$key] = $value;
            putenv("$key=$value");
        }
    }
    
    public static function get($key, $default = null) {
        self::load();
        
        // Check environment variables first
        if (isset($_ENV[$key])) {
            return $_ENV[$key];
        }
        
        // Then check config array
        $keys = explode('.', $key);
        $value = self::$config;
        
        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                return $default;
            }
            $value = $value[$k];
        }
        
        return $value;
    }
    
    public static function set($key, $value) {
        self::load();
        
        $keys = explode('.', $key);
        $config = &self::$config;
        
        foreach ($keys as $k) {
            if (!isset($config[$k]) || !is_array($config[$k])) {
                $config[$k] = [];
            }
            $config = &$config[$k];
        }
        
        $config = $value;
    }
    
    public static function all() {
        self::load();
        return self::$config;
    }
    
    public static function has($key) {
        return self::get($key) !== null;
    }
}

// Initialize configuration
Config::load();
?>