<?php
session_start();
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// محاكاة تسجيل دخول المستخدم
if (!isLoggedIn()) {
    $_SESSION['user_id'] = 3;
    $_SESSION['user_name'] = 'المدير العام';
    $_SESSION['user_email'] = '<EMAIL>';
    $_SESSION['user_role'] = 'super-admin';
}

echo "<h1>🔍 تشخيص إرسال النموذج من صفحة المنتج</h1>";

// محاكاة البيانات التي ترسل من النموذج
$product_id = 3;
$current_user = getCurrentUser();

echo "<h2>1. حالة المستخدم:</h2>";
echo "<div style='background: #e7f3ff; padding: 10px; border-radius: 5px;'>";
echo "<strong>isLoggedIn():</strong> " . (isLoggedIn() ? 'true' : 'false') . "<br>";
echo "<strong>getCurrentUser():</strong><br>";
echo "<pre>" . print_r($current_user, true) . "</pre>";
echo "</div>";

echo "<h2>2. محاكاة النموذج من صفحة المنتج:</h2>";

// محاكاة ما سيرسله النموذج
$form_data = [];
$form_data['product_id'] = $product_id;

if (isLoggedIn() && $current_user) {
    $form_data['user_id'] = $current_user['id'];
    $form_data['customer_name'] = $current_user['name'];
    $form_data['customer_email'] = $current_user['email'];
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
    echo "✅ المستخدم مسجل الدخول - سيتم إرسال user_id";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
    echo "❌ المستخدم غير مسجل الدخول - لن يتم إرسال user_id";
    echo "</div>";
}

$form_data['rating'] = 4;
$form_data['review_title'] = 'اختبار تشخيصي';
$form_data['review_text'] = 'هذا اختبار لتشخيص مشكلة user_id';

echo "<h3>بيانات النموذج المحاكاة:</h3>";
echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
foreach ($form_data as $key => $value) {
    $highlight = ($key == 'user_id') ? 'background: yellow; font-weight: bold;' : '';
    echo "• <span style='$highlight'>$key:</span> " . ($value ?? 'NULL') . "<br>";
}
echo "</div>";

echo "<h2>3. اختبار إرسال البيانات إلى API:</h2>";

// محاكاة POST request
$_POST = $form_data;

echo "<h3>أ. فحص البيانات المستلمة في API:</h3>";
echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px;'>";
echo "<strong>البيانات المستلمة في \$_POST:</strong><br>";
foreach ($_POST as $key => $value) {
    $highlight = ($key == 'user_id') ? 'background: yellow; font-weight: bold;' : '';
    echo "• <span style='$highlight'>$key:</span> " . ($value ?? 'NULL') . "<br>";
}
echo "</div>";

echo "<h3>ب. معالجة البيانات في API:</h3>";

try {
    // نسخ منطق API
    $isLoggedIn = isLoggedIn();
    $currentUser = $isLoggedIn ? getCurrentUser() : null;
    
    echo "<div style='background: #e7f3ff; padding: 10px; border-radius: 5px;'>";
    echo "<strong>حالة المستخدم في API:</strong><br>";
    echo "• isLoggedIn: " . ($isLoggedIn ? 'true' : 'false') . "<br>";
    echo "• currentUser: " . ($currentUser ? 'موجود' : 'NULL') . "<br>";
    if ($currentUser) {
        echo "• currentUser['id']: " . $currentUser['id'] . "<br>";
    }
    echo "</div>";
    
    // Sanitize input
    $product_id = (int)$_POST['product_id'];
    $rating = (int)$_POST['rating'];
    $review_text = sanitizeInput($_POST['review_text']);
    $review_title = sanitizeInput($_POST['review_title'] ?? '');
    
    // تحديد بيانات المستخدم
    if ($isLoggedIn) {
        $user_id = $currentUser['id'];
        $customer_name = $currentUser['name'];
        $customer_email = $currentUser['email'];
        
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
        echo "<strong>✅ سيتم استخدام بيانات المستخدم المسجل:</strong><br>";
        echo "• user_id: $user_id<br>";
        echo "• customer_name: $customer_name<br>";
        echo "• customer_email: $customer_email<br>";
        echo "</div>";
    } else {
        $user_id = null;
        $customer_name = sanitizeInput($_POST['customer_name'] ?? '');
        $customer_email = sanitizeInput($_POST['customer_email'] ?? '');
        
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
        echo "<strong>❌ سيتم استخدام بيانات الزائر:</strong><br>";
        echo "• user_id: NULL<br>";
        echo "• customer_name: $customer_name<br>";
        echo "• customer_email: $customer_email<br>";
        echo "</div>";
    }
    
    echo "<h3>ج. اختبار حفظ التقييم:</h3>";
    
    // حذف أي تقييم سابق
    global $database;
    if ($user_id) {
        $database->query("DELETE FROM reviews WHERE product_id = ? AND user_id = ?", [$product_id, $user_id]);
    }
    
    // Save review
    if ($isLoggedIn) {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px;'>";
        echo "<strong>استدعاء saveUserReview مع:</strong><br>";
        echo "• product_id: $product_id<br>";
        echo "• user_id: $user_id<br>";
        echo "• customer_name: $customer_name<br>";
        echo "• customer_email: $customer_email<br>";
        echo "• rating: $rating<br>";
        echo "</div>";
        
        $review_id = saveUserReview($product_id, $user_id, $customer_name, $customer_email, $rating, $review_text, $review_title);
    } else {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px;'>";
        echo "<strong>استدعاء saveGuestReview مع:</strong><br>";
        echo "• product_id: $product_id<br>";
        echo "• customer_name: $customer_name<br>";
        echo "• customer_email: $customer_email<br>";
        echo "• rating: $rating<br>";
        echo "</div>";
        
        $review_id = saveGuestReview($product_id, $customer_name, $customer_email, $rating, $review_text, $review_title);
    }
    
    if ($review_id && is_numeric($review_id)) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
        echo "✅ <strong>نجح حفظ التقييم!</strong> معرف التقييم: $review_id";
        echo "</div>";
        
        // فحص البيانات المحفوظة
        $saved_review = $database->fetch("SELECT * FROM reviews WHERE id = ?", [$review_id]);
        if ($saved_review) {
            echo "<h3>د. البيانات المحفوظة في قاعدة البيانات:</h3>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>العمود</th><th>القيمة</th><th>الحالة</th></tr>";
            
            $important_fields = ['id', 'product_id', 'user_id', 'name', 'email', 'rating', 'title', 'comment', 'is_approved'];
            foreach ($important_fields as $field) {
                $value = $saved_review[$field] ?? 'غير موجود';
                $status = '';
                $color = 'black';
                
                if ($field == 'user_id') {
                    if ($value && $value == $user_id) {
                        $status = '✅ صحيح';
                        $color = 'green';
                    } elseif ($value === null || $value === '') {
                        $status = '❌ NULL';
                        $color = 'red';
                    } else {
                        $status = '⚠️ قيمة خاطئة';
                        $color = 'orange';
                    }
                }
                
                echo "<tr>";
                echo "<td><strong>$field</strong></td>";
                echo "<td style='color: $color;'>" . ($value ?? 'NULL') . "</td>";
                echo "<td>$status</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            if ($saved_review['user_id'] == $user_id) {
                echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724; border: 2px solid #28a745;'>";
                echo "🎉 <strong>نجح الإصلاح!</strong> user_id محفوظ بشكل صحيح: " . $saved_review['user_id'];
                echo "</div>";
            } else {
                echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24; border: 2px solid #dc3545;'>";
                echo "❌ <strong>المشكلة ما زالت موجودة!</strong><br>";
                echo "• المتوقع: $user_id<br>";
                echo "• المحفوظ: " . ($saved_review['user_id'] ?? 'NULL') . "<br>";
                echo "</div>";
            }
        }
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
        echo "❌ فشل في حفظ التقييم: " . (is_string($review_id) ? $review_id : 'خطأ غير محدد');
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
    echo "❌ خطأ: " . $e->getMessage();
    echo "</div>";
}

echo "<h2>4. التوصيات:</h2>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px;'>";
echo "<p>إذا كانت المشكلة ما زالت موجودة، فقد تكون في:</p>";
echo "<ul>";
echo "<li>دالة saveUserReview نفسها</li>";
echo "<li>دالة database->insert</li>";
echo "<li>هيكل جدول reviews</li>";
echo "<li>قيود قاعدة البيانات</li>";
echo "</ul>";
echo "</div>";
?>
