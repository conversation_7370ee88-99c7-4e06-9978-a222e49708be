<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نموذج التواصل النهائي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .error {
            border-color: #ef4444 !important;
        }
        .error-message {
            color: #ef4444;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            padding: 16px 24px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 400px;
        }
        .notification.show {
            transform: translateX(0);
        }
        .notification-success {
            background-color: #10b981;
        }
        .notification-error {
            background-color: #ef4444;
        }
        .notification-info {
            background-color: #3b82f6;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-8">
            <h1 class="text-3xl font-bold text-center mb-8 text-gray-800">اختبار نموذج التواصل النهائي</h1>
            
            <form id="contact-form" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل *</label>
                        <input type="text" id="name" name="name" required 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                               placeholder="أدخل اسمك الكامل">
                    </div>
                    
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني *</label>
                        <input type="email" id="email" name="email" required 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                               placeholder="أدخل بريدك الإلكتروني">
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف *</label>
                        <input type="tel" id="phone" name="phone" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                               placeholder="أدخل رقم هاتفك">
                    </div>
                    
                    <div>
                        <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">الموضوع *</label>
                        <select id="subject" name="subject" required 
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300">
                            <option value="">اختر الموضوع</option>
                            <option value="استفسار عام">استفسار عام</option>
                            <option value="طلب عرض سعر">طلب عرض سعر</option>
                            <option value="الدعم الفني">الدعم الفني</option>
                            <option value="شراكة">شراكة</option>
                            <option value="شكوى">شكوى</option>
                            <option value="اقتراح">اقتراح</option>
                        </select>
                    </div>
                </div>
                
                <div>
                    <label for="message" class="block text-sm font-medium text-gray-700 mb-2">الرسالة *</label>
                    <textarea id="message" name="message" rows="6" required 
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                              placeholder="اكتب رسالتك هنا..."></textarea>
                </div>
                
                <button type="submit" 
                        class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105">
                    إرسال الرسالة
                </button>
            </form>
            
            <div class="mt-8 p-4 bg-gray-50 rounded-lg">
                <h3 class="font-bold mb-2">معلومات الاختبار:</h3>
                <p class="text-sm text-gray-600">• تم إصلاح النموذج ليرسل البيانات إلى API بدلاً من POST التقليدي</p>
                <p class="text-sm text-gray-600">• تم جعل رقم الهاتف مطلوباً</p>
                <p class="text-sm text-gray-600">• تم إضافة التحقق من صحة البيانات</p>
                <p class="text-sm text-gray-600">• تم إضافة الإشعارات للنجاح والخطأ</p>
            </div>
        </div>
    </div>

    <script>
        // Contact Form
        function initContactForm() {
            const contactForm = document.getElementById('contact-form');
            
            if (contactForm) {
                contactForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    if (validateForm(this)) {
                        submitContactForm(this);
                    }
                });
            }
        }

        function validateForm(form) {
            const name = form.querySelector('[name="name"]').value.trim();
            const email = form.querySelector('[name="email"]').value.trim();
            const phone = form.querySelector('[name="phone"]').value.trim();
            const subject = form.querySelector('[name="subject"]').value.trim();
            const message = form.querySelector('[name="message"]').value.trim();
            
            // Clear previous errors
            form.querySelectorAll('.error-message').forEach(error => error.remove());
            form.querySelectorAll('.error').forEach(field => field.classList.remove('error'));
            
            let isValid = true;
            
            // Validate name
            if (!name) {
                showFieldError(form.querySelector('[name="name"]'), 'الاسم مطلوب');
                isValid = false;
            }
            
            // Validate email
            if (!email) {
                showFieldError(form.querySelector('[name="email"]'), 'البريد الإلكتروني مطلوب');
                isValid = false;
            } else if (!isValidEmail(email)) {
                showFieldError(form.querySelector('[name="email"]'), 'البريد الإلكتروني غير صحيح');
                isValid = false;
            }
            
            // Validate phone
            if (!phone) {
                showFieldError(form.querySelector('[name="phone"]'), 'رقم الهاتف مطلوب');
                isValid = false;
            }
            
            // Validate subject
            if (!subject) {
                showFieldError(form.querySelector('[name="subject"]'), 'الموضوع مطلوب');
                isValid = false;
            }
            
            // Validate message
            if (!message) {
                showFieldError(form.querySelector('[name="message"]'), 'الرسالة مطلوبة');
                isValid = false;
            }
            
            return isValid;
        }

        function showFieldError(field, message) {
            field.classList.add('error');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.textContent = message;
            field.parentNode.appendChild(errorDiv);
        }

        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        function submitContactForm(form) {
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            
            // Show loading state
            submitBtn.textContent = 'جاري الإرسال...';
            submitBtn.disabled = true;
            
            const formData = new FormData(form);
            
            console.log('إرسال البيانات إلى:', '/api/contact.php');
            console.log('البيانات:', Object.fromEntries(formData));
            
            fetch('/api/contact.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('استجابة الخادم:', response);
                return response.json();
            })
            .then(data => {
                console.log('البيانات المستلمة:', data);
                if (data.success) {
                    showNotification('تم إرسال رسالتك بنجاح! رقم المرجع: ' + (data.reference_id || ''), 'success');
                    form.reset();
                } else {
                    showNotification(data.message || 'حدث خطأ أثناء الإرسال', 'error');
                }
            })
            .catch(error => {
                console.error('خطأ:', error);
                showNotification('حدث خطأ أثناء الإرسال', 'error');
            })
            .finally(() => {
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            });
        }

        // Notifications
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            // Show notification
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);
            
            // Hide notification
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 5000);
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initContactForm();
        });
    </script>
</body>
</html>