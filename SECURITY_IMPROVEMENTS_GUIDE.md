# دليل تحسينات الأمان والجودة - Green Line PHP

## نظرة عامة
تم تطوير مجموعة شاملة من التحسينات الأمنية وتحسينات جودة الكود لمشروع Green Line PHP، بما في ذلك أنظمة مراقبة متقدمة وحماية شاملة.

## 🔒 التحسينات الأمنية المطبقة

### 1. نظام مراقبة الأمان المتقدم
- **الملف**: `includes/security_monitor.php`
- **الوظائف**:
  - تسجيل الأحداث الأمنية في قاعدة البيانات وملفات السجل
  - إنشاء تنبيهات تلقائية عند اكتشاف أنشطة مشبوهة
  - مراقبة محاولات تسجيل الدخول الفاشلة
  - ر<PERSON>د تجاوز حدود معدل الطلبات
  - تتبع الوصول الإداري المكثف
  - إحصائيات شاملة للأحداث الأمنية

### 2. API التواصل المحسن أمنياً
- **الملف**: `api/contact_secure.php`
- **التحسينات**:
  - تطبيق حماية CSRF tokens
  - تحديد معدل الطلبات (10 رسائل/ساعة)
  - فلترة محتوى الرسائل الاقتحامية
  - تحسين التحقق من صحة المدخلات
  - تسجيل مفصل للأنشطة

### 3. إعدادات الأمان الشاملة
- **الملف**: `config/security_config.php`
- **الإعدادات**:
  - إعدادات CSRF وانتهاء الصلاحية
  - حدود معدل الطلبات لكل نوع عملية
  - سياسات كلمات المرور القوية
  - إعدادات الجلسات الآمنة
  - قيود تحميل الملفات
  - إعدادات مكافحة البريد المزعج
  - إعدادات التشفير والشبكة

### 4. لوحة التحكم الأمنية
- **الملف**: `admin/security_dashboard.php`
- **المميزات**:
  - عرض التنبيهات النشطة في الوقت الفعلي
  - إحصائيات مرئية للأحداث الأمنية
  - رسوم بيانية تفاعلية
  - إدارة التنبيهات (عرض/حل)
  - قائمة أكثر عناوين IP نشاطاً
  - تحديث تلقائي كل 30 ثانية

### 5. API إدارة الأمان
- **الملف**: `admin/security_api.php`
- **الوظائف**:
  - إدارة التنبيهات الأمنية
  - حظر/إلغاء حظر عناوين IP
  - تصدير الأحداث (CSV/JSON)
  - تنظيف السجلات القديمة
  - إحصائيات مفصلة

## 📊 أنواع الأحداث المراقبة

### الأحداث الحرجة (Critical)
- اكتشاف برمجيات ضارة
- محاولات اختراق مباشرة
- تلاعب في ملفات النظام

### الأحداث عالية المستوى (High)
- محاولات تسجيل دخول فاشلة متعددة (5+ في 15 دقيقة)
- تجاوز حدود معدل الطلبات المتكرر
- محاولات الوصول لملفات محظورة

### الأحداث متوسطة المستوى (Medium)
- أنشطة مشبوهة عامة
- محاولات إرسال رسائل اقتحامية
- وصول إداري مكثف (10+ في ساعة)

### الأحداث منخفضة المستوى (Low)
- تسجيل دخول ناجح
- عمليات عادية مع تنبيهات بسيطة

## 🛡️ آليات الحماية

### 1. حماية CSRF
```php
// التحقق من CSRF token
if (!validateCSRFToken($_POST['csrf_token'])) {
    throw new Exception('رمز الأمان غير صحيح');
}
```

### 2. تحديد معدل الطلبات
```php
// فحص حدود معدل الطلبات
if (!checkRateLimit('contact', 10, 3600)) {
    logSecurityEvent('rate_limit_exceeded', 'high');
    throw new Exception('تم تجاوز حد الطلبات المسموح');
}
```

### 3. فلترة المحتوى الاقتحامي
```php
// فحص أنماط الرسائل الاقتحامية
$spamPatterns = [
    '/\b(viagra|casino|lottery|winner)\b/i',
    '/\b(click here|free money|guaranteed)\b/i',
    '/http[s]?:\/\/[^\s]{10,}/i'
];
```

## 📈 الإحصائيات والتقارير

### لوحة المعلومات
- إجمالي الأحداث (آخر 30 يوم)
- عدد التنبيهات حسب المستوى
- أكثر عناوين IP نشاطاً
- توزيع الأحداث حسب النوع

### التصدير والأرشفة
- تصدير الأحداث بصيغة CSV أو JSON
- أرشفة تلقائية للسجلات القديمة
- تنظيف دوري للبيانات (90 يوم افتراضياً)

## 🔧 التكوين والإعداد

### متطلبات النظام
- PHP 7.4+
- MySQL 5.7+
- مساحة تخزين للسجلات
- صلاحيات كتابة في مجلد logs/

### إعداد قاعدة البيانات
```sql
-- جداول المراقبة الأمنية
CREATE TABLE security_events (
    id INT PRIMARY KEY AUTO_INCREMENT,
    event_type VARCHAR(50) NOT NULL,
    severity ENUM('low', 'medium', 'high', 'critical'),
    ip_address VARCHAR(45),
    user_id INT NULL,
    user_agent TEXT,
    event_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE security_alerts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    alert_type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    severity ENUM('low', 'medium', 'high', 'critical'),
    status ENUM('new', 'investigating', 'resolved', 'false_positive'),
    event_count INT DEFAULT 1,
    first_occurrence TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_occurrence TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### إعداد مجلد السجلات
```bash
mkdir logs
chmod 755 logs
```

## 🚀 الاستخدام

### تسجيل حدث أمني
```php
// استخدام الدالة العامة
logSecurityEvent('failed_login', 'medium', [
    'username' => $username,
    'attempt_count' => $attempts
]);

// استخدام الكلاس مباشرة
$monitor = new SecurityMonitor();
$monitor->logSecurityEvent('suspicious_activity', 'high', $eventData);
```

### الوصول للوحة التحكم
1. تسجيل الدخول كمدير
2. الانتقال إلى `/admin/security_dashboard.php`
3. مراجعة التنبيهات والإحصائيات
4. اتخاذ الإجراءات المناسبة

## 📋 قائمة المراجعة الأمنية

### ✅ مطبق
- [x] نظام مراقبة الأحداث الأمنية
- [x] تنبيهات تلقائية للأنشطة المشبوهة
- [x] حماية CSRF للنماذج
- [x] تحديد معدل الطلبات
- [x] فلترة المحتوى الاقتحامي
- [x] لوحة تحكم أمنية شاملة
- [x] تسجيل مفصل للأنشطة
- [x] إدارة عناوين IP المحظورة

### 🔄 قيد التطوير
- [ ] تشفير البيانات الحساسة
- [ ] مصادقة ثنائية العامل
- [ ] فحص الملفات المرفوعة للفيروسات
- [ ] تحليل سلوك المستخدمين بالذكاء الاصطناعي

### 📝 اقتراحات إضافية
- [ ] تكامل مع خدمات مراقبة خارجية
- [ ] إشعارات فورية عبر البريد الإلكتروني/SMS
- [ ] نسخ احتياطية مشفرة تلقائية
- [ ] فحص دوري للثغرات الأمنية

## 🔗 الملفات ذات الصلة

### الملفات الأساسية
- `includes/security_monitor.php` - نظام المراقبة الأساسي
- `config/security_config.php` - إعدادات الأمان
- `admin/security_dashboard.php` - لوحة التحكم
- `admin/security_api.php` - API الإدارة
- `api/contact_secure.php` - API محسن أمنياً

### ملفات الدعم
- `includes/csrf.php` - حماية CSRF (موجود مسبقاً)
- `includes/rate_limiter.php` - تحديد معدل الطلبات (موجود مسبقاً)
- `logs/security_*.log` - ملفات السجل اليومية

## 📞 الدعم والصيانة

### المراقبة اليومية
- مراجعة التنبيهات النشطة
- فحص الإحصائيات غير الطبيعية
- تحديث قوائم الحظر حسب الحاجة

### الصيانة الأسبوعية
- تنظيف السجلات القديمة
- مراجعة إعدادات الأمان
- تحديث قواعد الفلترة

### الصيانة الشهرية
- تحليل اتجاهات الأمان
- تحديث سياسات الحماية
- مراجعة صلاحيات المستخدمين

---

**تاريخ آخر تحديث**: <?= date('Y-m-d H:i:s') ?>  
**الإصدار**: 2.0  
**المطور**: فريق Green Line PHP  

> **ملاحظة**: هذا النظام يوفر حماية شاملة ولكن يجب مراجعته وتحديثه بانتظام لضمان فعاليته ضد التهديدات الجديدة.