<?php
require_once 'config/config.php';
require_once 'includes/functions.php';

$product_id = $_GET['product_id'] ?? 11;
$user_id = $_GET['user_id'] ?? null;

echo "<h1>اختبار دالة hasUserReviewed</h1>";

echo "<h2>معلومات الجلسة:</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h2>معلومات المستخدم الحالي:</h2>";
if (isLoggedIn()) {
    $current_user = getCurrentUser();
    echo "<pre>";
    print_r($current_user);
    echo "</pre>";
    
    echo "<h2>فحص التقييم للمستخدم الحالي:</h2>";
    $has_reviewed = hasUserReviewed($product_id, $current_user['id']);
    echo "المستخدم " . $current_user['id'] . " قيّم المنتج " . $product_id . ": " . ($has_reviewed ? "نعم" : "لا") . "<br>";
    
    if ($has_reviewed) {
        $user_review = getUserReview($product_id, $current_user['id']);
        echo "<h3>تفاصيل التقييم:</h3>";
        echo "<pre>";
        print_r($user_review);
        echo "</pre>";
    }
} else {
    echo "المستخدم غير مسجل دخول";
}

echo "<h2>جميع التقييمات للمنتج " . $product_id . ":</h2>";
global $database;
$reviews = $database->fetchAll("SELECT * FROM reviews WHERE product_id = :product_id", ['product_id' => $product_id]);
echo "<pre>";
print_r($reviews);
echo "</pre>";

if ($user_id) {
    echo "<h2>فحص التقييم للمستخدم " . $user_id . ":</h2>";
    $has_reviewed_specific = hasUserReviewed($product_id, $user_id);
    echo "المستخدم " . $user_id . " قيّم المنتج " . $product_id . ": " . ($has_reviewed_specific ? "نعم" : "لا") . "<br>";
    
    if ($has_reviewed_specific) {
        $user_review_specific = getUserReview($product_id, $user_id);
        echo "<h3>تفاصيل التقييم:</h3>";
        echo "<pre>";
        print_r($user_review_specific);
        echo "</pre>";
    }
}
?>