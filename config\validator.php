<?php
/**
 * Input Validation and Sanitization Helper
 * Provides secure input handling and validation methods
 */

class Validator {
    private $errors = [];
    private $data = [];
    
    public function __construct($data = []) {
        $this->data = $data;
    }
    
    public static function make($data) {
        return new self($data);
    }
    
    public function validate($rules) {
        foreach ($rules as $field => $ruleSet) {
            $rules = is_string($ruleSet) ? explode('|', $ruleSet) : $ruleSet;
            $value = $this->data[$field] ?? null;
            
            foreach ($rules as $rule) {
                $this->applyRule($field, $value, $rule);
            }
        }
        
        return empty($this->errors);
    }
    
    private function applyRule($field, $value, $rule) {
        $params = [];
        if (strpos($rule, ':') !== false) {
            list($rule, $paramString) = explode(':', $rule, 2);
            $params = explode(',', $paramString);
        }
        
        switch ($rule) {
            case 'required':
                if (empty($value) && $value !== '0') {
                    $this->addError($field, "The $field field is required.");
                }
                break;
                
            case 'email':
                if (!empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    $this->addError($field, "The $field must be a valid email address.");
                }
                break;
                
            case 'min':
                $min = (int)$params[0];
                if (!empty($value) && strlen($value) < $min) {
                    $this->addError($field, "The $field must be at least $min characters.");
                }
                break;
                
            case 'max':
                $max = (int)$params[0];
                if (!empty($value) && strlen($value) > $max) {
                    $this->addError($field, "The $field may not be greater than $max characters.");
                }
                break;
                
            case 'numeric':
                if (!empty($value) && !is_numeric($value)) {
                    $this->addError($field, "The $field must be a number.");
                }
                break;
                
            case 'integer':
                if (!empty($value) && !filter_var($value, FILTER_VALIDATE_INT)) {
                    $this->addError($field, "The $field must be an integer.");
                }
                break;
                
            case 'url':
                if (!empty($value) && !filter_var($value, FILTER_VALIDATE_URL)) {
                    $this->addError($field, "The $field must be a valid URL.");
                }
                break;
                
            case 'in':
                if (!empty($value) && !in_array($value, $params)) {
                    $allowed = implode(', ', $params);
                    $this->addError($field, "The $field must be one of: $allowed.");
                }
                break;
                
            case 'file':
                if (isset($_FILES[$field]) && $_FILES[$field]['error'] !== UPLOAD_ERR_NO_FILE) {
                    $this->validateFile($field, $_FILES[$field]);
                }
                break;
        }
    }
    
    private function validateFile($field, $file) {
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $this->addError($field, "File upload failed for $field.");
            return;
        }
        
        $allowedTypes = Config::get('upload.allowed_types.image', []);
        $allowedTypes = array_merge($allowedTypes, Config::get('upload.allowed_types.document', []));
        
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, $allowedTypes)) {
            $this->addError($field, "The $field must be a file of type: " . implode(', ', $allowedTypes));
        }
        
        $maxSize = Config::get('upload.max_size', 5242880);
        if ($file['size'] > $maxSize) {
            $maxSizeMB = round($maxSize / 1024 / 1024, 2);
            $this->addError($field, "The $field may not be greater than {$maxSizeMB}MB.");
        }
    }
    
    private function addError($field, $message) {
        if (!isset($this->errors[$field])) {
            $this->errors[$field] = [];
        }
        $this->errors[$field][] = $message;
    }
    
    public function errors() {
        return $this->errors;
    }
    
    public function firstError($field = null) {
        if ($field) {
            return isset($this->errors[$field]) ? $this->errors[$field][0] : null;
        }
        
        foreach ($this->errors as $fieldErrors) {
            return $fieldErrors[0];
        }
        
        return null;
    }
    
    public function hasErrors() {
        return !empty($this->errors);
    }
    
    public function validated() {
        return $this->data;
    }
}

class Sanitizer {
    public static function string($value, $allowHtml = false) {
        if ($allowHtml) {
            return htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
        }
        return strip_tags(trim($value));
    }
    
    public static function email($value) {
        return filter_var(trim($value), FILTER_SANITIZE_EMAIL);
    }
    
    public static function url($value) {
        return filter_var(trim($value), FILTER_SANITIZE_URL);
    }
    
    public static function integer($value) {
        return filter_var($value, FILTER_SANITIZE_NUMBER_INT);
    }
    
    public static function float($value) {
        return filter_var($value, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
    }
    
    public static function filename($value) {
        // Remove any path traversal attempts and dangerous characters
        $value = basename($value);
        $value = preg_replace('/[^a-zA-Z0-9._-]/', '', $value);
        return $value;
    }
    
    public static function array($array, $callback = null) {
        if (!is_array($array)) {
            return [];
        }
        
        if ($callback && is_callable($callback)) {
            return array_map($callback, $array);
        }
        
        return array_map([self::class, 'string'], $array);
    }
    
    public static function sql($value) {
        // This should be used in conjunction with prepared statements
        return addslashes($value);
    }
}

// Usage examples:
/*
// Validation
$validator = Validator::make($_POST);
$isValid = $validator->validate([
    'title' => 'required|max:255',
    'email' => 'required|email',
    'description' => 'required|min:10',
    'status' => 'required|in:active,inactive',
    'file' => 'file'
]);

if (!$isValid) {
    $errors = $validator->errors();
    // Handle errors
}

// Sanitization
$title = Sanitizer::string($_POST['title']);
$email = Sanitizer::email($_POST['email']);
$description = Sanitizer::string($_POST['description'], true); // Allow HTML
*/
?>