<?php
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/LegalPageManager.php';

// إنشاء مدير الصفحات القانونية
$legalManager = new LegalPageManager($database);

// جلب بيانات صفحة سياسة الخصوصية
$pageData = $legalManager->getPage('privacy');

$pageTitle = $pageData['title'];
$metaDescription = $pageData['meta_description'];
$metaKeywords = $pageData['meta_keywords'];

include 'includes/header.php';
?>

<div class="min-h-screen bg-gray-50">
    <!-- Header Section -->
    <div class="bg-white shadow-sm">
        <div class="container mx-auto px-4 py-8">
            <div class="max-w-4xl mx-auto">
                <nav class="text-sm text-gray-500 mb-4">
                    <a href="/" class="hover:text-green-600">الرئيسية</a>
                    <span class="mx-2">/</span>
                    <span class="text-gray-900"><?php echo htmlspecialchars($pageData['title']); ?></span>
                </nav>
                <h1 class="text-3xl font-bold text-gray-900"><?php echo htmlspecialchars($pageData['title']); ?></h1>
            </div>
        </div>
    </div>

    <!-- Content Section -->
    <div class="container mx-auto px-4 py-12">
        <div class="max-w-4xl mx-auto">
            <div class="bg-white rounded-lg shadow-sm p-8">
                <div class="prose prose-lg max-w-none">
                    <?php echo $pageData['content']; ?>
                </div>
                
                <!-- تاريخ آخر تحديث -->
                <?php if (isset($pageData['updated_at'])): ?>
                <div class="mt-8 pt-6 border-t border-gray-200">
                    <p class="text-sm text-gray-500">
                        آخر تحديث: <?php echo date('d/m/Y', strtotime($pageData['updated_at'])); ?>
                    </p>
                </div>
                <?php endif; ?>
            </div>
            
            <!-- روابط ذات صلة -->
            <div class="mt-8 bg-white rounded-lg shadow-sm p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">صفحات ذات صلة</h3>
                <div class="grid md:grid-cols-2 gap-4">
                    <a href="/terms" class="block p-4 border border-gray-200 rounded-lg hover:border-green-500 hover:bg-green-50 transition-colors">
                        <h4 class="font-medium text-gray-900">الشروط والأحكام</h4>
                        <p class="text-sm text-gray-600 mt-1">اطلع على شروط وأحكام استخدام الموقع</p>
                    </a>
                    <a href="/sitemap" class="block p-4 border border-gray-200 rounded-lg hover:border-green-500 hover:bg-green-50 transition-colors">
                        <h4 class="font-medium text-gray-900">خريطة الموقع</h4>
                        <p class="text-sm text-gray-600 mt-1">تصفح جميع صفحات الموقع</p>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.prose {
    color: #374151;
    line-height: 1.75;
}

.prose h2 {
    color: #111827;
    font-weight: 700;
    font-size: 1.5rem;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.prose h3 {
    color: #111827;
    font-weight: 600;
    font-size: 1.25rem;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
}

.prose p {
    margin-bottom: 1rem;
}

.prose ul {
    margin-bottom: 1rem;
    padding-right: 1.5rem;
}

.prose li {
    margin-bottom: 0.5rem;
}

.prose a {
    color: #059669;
    text-decoration: underline;
}

.prose a:hover {
    color: #047857;
}

.prose strong {
    font-weight: 600;
    color: #111827;
}
</style>

<?php include 'includes/footer.php'; ?>