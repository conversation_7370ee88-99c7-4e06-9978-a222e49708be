<?php
require_once 'config/config.php';
require_once 'includes/functions.php';

$pageTitle = 'اختبار نموذج التواصل العام';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .notification {
            transition: all 0.3s ease;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-2xl mx-auto">
            <div class="bg-white rounded-lg shadow-lg p-8">
                <h1 class="text-3xl font-bold text-center mb-8 text-gray-800">
                    اختبار نموذج التواصل العام
                </h1>
                
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <h2 class="text-lg font-semibold text-blue-800 mb-2">معلومات الاختبار:</h2>
                    <ul class="text-blue-700 space-y-1">
                        <li>• هذا الاختبار يحاكي نموذج التواصل في الصفحة العامة</li>
                        <li>• يستخدم نفس JavaScript و API المستخدم في الصفحة الأصلية</li>
                        <li>• يجب أن تظهر رسالة نجاح ويتم حفظ البيانات في قاعدة البيانات</li>
                    </ul>
                </div>
                
                <form id="contact-form" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل *</label>
                            <input type="text" id="name" name="name" required 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   placeholder="أدخل اسمك الكامل" value="أحمد محمد">
                        </div>
                        
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني *</label>
                            <input type="email" id="email" name="email" required 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   placeholder="أدخل بريدك الإلكتروني" value="<EMAIL>">
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف *</label>
                            <input type="tel" id="phone" name="phone" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   placeholder="أدخل رقم هاتفك" value="0501234567">
                        </div>
                        
                        <div>
                            <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">الموضوع *</label>
                            <select id="subject" name="subject" required 
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="">اختر الموضوع</option>
                                <option value="استفسار عام">استفسار عام</option>
                                <option value="طلب عرض سعر" selected>طلب عرض سعر</option>
                                <option value="الدعم الفني">الدعم الفني</option>
                                <option value="شراكة">شراكة</option>
                                <option value="شكوى">شكوى</option>
                                <option value="اقتراح">اقتراح</option>
                            </select>
                        </div>
                    </div>
                    
                    <div>
                        <label for="message" class="block text-sm font-medium text-gray-700 mb-2">الرسالة *</label>
                        <textarea id="message" name="message" rows="6" required 
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                  placeholder="اكتب رسالتك هنا...">أريد الاستفسار عن أسعار المكيفات المتاحة لديكم.</textarea>
                    </div>
                    
                    <button type="submit" 
                            class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300">
                        إرسال الرسالة
                    </button>
                </form>
                
                <div class="mt-8 text-center">
                    <div class="space-y-2">
                        <a href="<?php echo SITE_URL; ?>/contact" 
                           class="inline-block bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-colors">
                            الصفحة الأصلية للتواصل
                        </a>
                        <br>
                        <a href="<?php echo SITE_URL; ?>/test_final_contact.php" 
                           class="inline-block bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg transition-colors">
                            صفحة الاختبار الشاملة
                        </a>
                        <br>
                        <a href="<?php echo SITE_URL; ?>/test_database_fix.php" 
                           class="inline-block bg-orange-600 hover:bg-orange-700 text-white px-6 py-2 rounded-lg transition-colors">
                            اختبار قاعدة البيانات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    // Contact Form Handler - نفس الكود المستخدم في الصفحة الأصلية
    document.addEventListener('DOMContentLoaded', function() {
        const contactForm = document.getElementById('contact-form');
        if (contactForm) {
            contactForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                // جمع بيانات النموذج
                const formData = new FormData(contactForm);
                const data = {
                    name: formData.get('name'),
                    email: formData.get('email'),
                    phone: formData.get('phone'),
                    subject: formData.get('subject'),
                    message: formData.get('message'),
                    product_id: formData.get('product_id') || null
                };
                
                // التحقق من صحة البيانات
                if (!data.name || !data.email || !data.phone || !data.subject || !data.message) {
                    showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
                    return;
                }
                
                // التحقق من صحة البريد الإلكتروني
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(data.email)) {
                    showNotification('يرجى إدخال بريد إلكتروني صحيح', 'error');
                    return;
                }
                
                // التحقق من رقم الهاتف
                const phoneRegex = /^[0-9+\-\s()]{10,}$/;
                if (!phoneRegex.test(data.phone)) {
                    showNotification('يرجى إدخال رقم هاتف صحيح', 'error');
                    return;
                }
                
                // تعطيل زر الإرسال
                const submitBtn = contactForm.querySelector('button[type="submit"]');
                const originalText = submitBtn.textContent;
                submitBtn.disabled = true;
                submitBtn.textContent = 'جاري الإرسال...';
                
                try {
                    // إرسال البيانات إلى API
                    const response = await fetch('<?php echo SITE_URL; ?>/api/contact.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(data)
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        showNotification('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً', 'success');
                        contactForm.reset();
                    } else {
                        showNotification(result.message || 'حدث خطأ أثناء إرسال الرسالة', 'error');
                    }
                } catch (error) {
                    console.error('Error:', error);
                    showNotification('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى', 'error');
                } finally {
                    // إعادة تفعيل زر الإرسال
                    submitBtn.disabled = false;
                    submitBtn.textContent = originalText;
                }
            });
        }
    });

    // دالة عرض الإشعارات
    function showNotification(message, type = 'info') {
        // إزالة الإشعارات السابقة
        const existingNotifications = document.querySelectorAll('.notification');
        existingNotifications.forEach(notification => notification.remove());
        
        // إنشاء الإشعار الجديد
        const notification = document.createElement('div');
        notification.className = `notification fixed top-4 right-4 z-50 max-w-sm p-4 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full`;
        
        // تحديد لون الإشعار حسب النوع
        if (type === 'success') {
            notification.classList.add('bg-green-500', 'text-white');
        } else if (type === 'error') {
            notification.classList.add('bg-red-500', 'text-white');
        } else {
            notification.classList.add('bg-blue-500', 'text-white');
        }
        
        notification.innerHTML = `
            <div class="flex items-center">
                <div class="flex-1">
                    <p class="text-sm font-medium">${message}</p>
                </div>
                <button onclick="this.parentElement.parentElement.remove()" class="mr-2 text-white hover:text-gray-200">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // عرض الإشعار
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);
        
        // إخفاء الإشعار تلقائياً بعد 5 ثوان
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 5000);
    }
    </script>
</body>
</html>