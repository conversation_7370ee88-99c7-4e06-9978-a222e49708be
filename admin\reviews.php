<?php
require_once '../config/database.php';
require_once '../includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

// إعداد الصفحة
$pageTitle = 'إدارة المراجعات والتقييمات';
$pageDescription = 'إدارة مراجعات العملاء وتقييمات الخدمات';
$currentPage = 'reviews';
$breadcrumbs = [
    ['title' => 'المحتوى الإضافي', 'url' => '#'],
    ['title' => 'المراجعات والتقييمات']
];

// إنشاء جدول المراجعات إذا لم يكن موجوداً (للمراجعات المرتبطة بالمنتجات)
try {
    $db->exec("CREATE TABLE IF NOT EXISTS reviews (
        id INT AUTO_INCREMENT PRIMARY KEY,
        product_id INT,
        user_id INT,
        name VARCHAR(255),
        email VARCHAR(255),
        rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
        title VARCHAR(255),
        comment TEXT,
        is_approved BOOLEAN DEFAULT FALSE,
        is_rejected BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
    )");
    
    // إضافة العمود is_rejected إذا لم يكن موجوداً
    $db->exec("ALTER TABLE reviews ADD COLUMN IF NOT EXISTS is_rejected BOOLEAN DEFAULT FALSE");
    
    // لا حاجة لبيانات تجريبية - سنعرض المراجعات الحقيقية من المنتجات
} catch (PDOException $e) {
    error_log("خطأ في إنشاء جدول المراجعات: " . $e->getMessage());
}

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'update_auto_approval':
                    $autoApproval = isset($_POST['auto_approval']) ? 1 : 0;
                    setSetting('reviews_auto_approval', $autoApproval, 'boolean');
                    $success = "تم تحديث إعداد التفعيل التلقائي بنجاح";
                    break;
                    
                case 'update_status':
                    $reviewId = (int)$_POST['review_id'];
                    $status = $_POST['status'];
                    
                    // التحقق من أن الحالة صحيحة وتحديث الحقول المناسبة
                    if (in_array($status, ['pending', 'approved', 'rejected'])) {
                        if ($status === 'approved') {
                            $stmt = $db->prepare("UPDATE reviews SET is_approved = 1, is_rejected = 0 WHERE id = ?");
                        } elseif ($status === 'rejected') {
                            $stmt = $db->prepare("UPDATE reviews SET is_approved = 0, is_rejected = 1 WHERE id = ?");
                        } else { // pending
                            $stmt = $db->prepare("UPDATE reviews SET is_approved = 0, is_rejected = 0 WHERE id = ?");
                        }
                        $stmt->execute([$reviewId]);
                    }
                    
                    // تحديث تقييم المنتج إذا تم اعتماد المراجعة
                    if ($status === 'approved') {
                        $review = $db->prepare("SELECT product_id FROM reviews WHERE id = ?");
                        $review->execute([(int)$_POST['review_id']]);
                        $reviewData = $review->fetch(PDO::FETCH_ASSOC);
                        
                        if ($reviewData && $reviewData['product_id']) {
                            // تحديث متوسط التقييم للمنتج
                            $avgQuery = $db->prepare("SELECT AVG(rating) as avg_rating, COUNT(*) as review_count FROM reviews WHERE product_id = ? AND is_approved = 1");
                            $avgQuery->execute([$reviewData['product_id']]);
                            $avgData = $avgQuery->fetch(PDO::FETCH_ASSOC);
                            
                            $updateProduct = $db->prepare("UPDATE products SET average_rating = ?, reviews_count = ? WHERE id = ?");
                            $updateProduct->execute([
                                round($avgData['avg_rating'], 1),
                                $avgData['review_count'],
                                $reviewData['product_id']
                            ]);
                        }
                    }
                    
                    $success = "تم تحديث حالة المراجعة بنجاح";
                    break;
                    
                case 'delete_review':
                    $stmt = $db->prepare("DELETE FROM reviews WHERE id = ?");
                    $stmt->execute([(int)$_POST['review_id']]);
                    $success = "تم حذف المراجعة بنجاح";
                    break;
            }
        }
    } catch (PDOException $e) {
        $error = "حدث خطأ: " . $e->getMessage();
    }
}

// جلب إحصائيات المراجعات
$stats = [
    'total' => 0,
    'approved' => 0,
    'pending' => 0,
    'rejected' => 0,
    'avg_rating' => 0
];

try {
    $stmt = $db->query("SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN is_approved = 1 THEN 1 ELSE 0 END) as approved,
        SUM(CASE WHEN is_rejected = 1 THEN 1 ELSE 0 END) as rejected,
        SUM(CASE WHEN is_approved = 0 AND is_rejected = 0 THEN 1 ELSE 0 END) as pending,
        AVG(rating) as avg_rating
        FROM reviews");
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['avg_rating'] = round($stats['avg_rating'] ?? 0, 1);
} catch (PDOException $e) {
    error_log("خطأ في جلب إحصائيات المراجعات: " . $e->getMessage());
}

// إعدادات ترقيم الصفحات
$recordsPerPage = 10;
$currentPage = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$offset = ($currentPage - 1) * $recordsPerPage;

// حساب العدد الإجمالي للمراجعات
$totalRecords = 0;
try {
    $countStmt = $db->query("SELECT COUNT(*) FROM reviews");
    $totalRecords = $countStmt->fetchColumn();
} catch (PDOException $e) {
    error_log("خطأ في حساب عدد المراجعات: " . $e->getMessage());
}

$totalPages = ceil($totalRecords / $recordsPerPage);

// جلب المراجعات مع معلومات المنتج مع ترقيم الصفحات
$reviews = [];
try {
    $stmt = $db->prepare("SELECT 
        r.*,
        p.name as product_name,
        p.category_id,
        pc.name as category_name
        FROM reviews r 
        LEFT JOIN products p ON r.product_id = p.id
        LEFT JOIN product_categories pc ON p.category_id = pc.id
        ORDER BY r.created_at DESC
        LIMIT ? OFFSET ?");
    $stmt->execute([$recordsPerPage, $offset]);
    $reviews = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("خطأ في جلب المراجعات: " . $e->getMessage());
}

require_once 'includes/layout.php';

// بدء التخطيط
startLayout();
showPageHeader();
showMessages();
?>

<div class="space-y-6">

    <!-- إحصائيات المراجعات -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-bold"><?php echo $stats['total']; ?></span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">إجمالي المراجعات</dt>
                            <dd class="text-lg font-medium text-gray-900"><?php echo $stats['total']; ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-bold"><?php echo $stats['approved']; ?></span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">المعتمدة</dt>
                            <dd class="text-lg font-medium text-gray-900"><?php echo $stats['approved']; ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-bold"><?php echo $stats['pending']; ?></span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">في الانتظار</dt>
                            <dd class="text-lg font-medium text-gray-900"><?php echo $stats['pending']; ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-bold"><?php echo $stats['rejected']; ?></span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">المرفوضة</dt>
                            <dd class="text-lg font-medium text-gray-900"><?php echo $stats['rejected']; ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-bold"><?php echo $stats['avg_rating']; ?></span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">متوسط التقييم</dt>
                            <dd class="text-lg font-medium text-gray-900"><?php echo $stats['avg_rating']; ?>/5</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إعدادات التفعيل التلقائي -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-lg font-medium text-gray-900">إعدادات التقييمات</h3>
                <p class="text-sm text-gray-500 mt-1">تحكم في كيفية معالجة التقييمات الجديدة</p>
            </div>
            <form method="POST" class="flex items-center space-x-4 space-x-reverse">
                <input type="hidden" name="action" value="update_auto_approval">
                <label class="flex items-center">
                    <input type="checkbox" name="auto_approval" value="1" 
                           <?php echo getSetting('reviews_auto_approval', false) ? 'checked' : ''; ?>
                           onchange="this.form.submit()"
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <span class="mr-2 text-sm text-gray-700">تفعيل التقييمات تلقائياً</span>
                </label>
            </form>
        </div>
        <div class="mt-4 p-4 bg-blue-50 rounded-lg">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="mr-3">
                    <h4 class="text-sm font-medium text-blue-800">معلومات مهمة</h4>
                    <div class="mt-2 text-sm text-blue-700">
                        <ul class="list-disc list-inside space-y-1">
                            <li>عند تفعيل هذا الخيار، ستظهر جميع التقييمات الجديدة تلقائياً دون الحاجة لموافقة يدوية</li>
                            <li>عند إلغاء تفعيل هذا الخيار، ستحتاج التقييمات الجديدة لموافقة يدوية قبل الظهور</li>
                            <li>هذا الإعداد يؤثر على التقييمات الجديدة فقط ولا يؤثر على التقييمات الموجودة</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- عنوان القسم -->
    <div class="flex justify-between items-center">
        <h2 class="text-2xl font-bold text-gray-900">قائمة المراجعات</h2>
        <div class="text-sm text-gray-600">
            المراجعات المضافة من صفحات المنتجات
        </div>
    </div>

    <!-- جدول المراجعات -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-center text-ms font-medium text-gray-500 uppercase tracking-wider">العميل</th>
                        <th class="px-6 py-3 text-center text-ms font-larg text-gray-500 uppercase tracking-wider">المنتج</th>
                        <th class="px-6 py-3 text-center text-ms font-medium text-gray-500 uppercase tracking-wider">التقييم</th>
                        <th class="px-6 py-3 text-center text-ms font-medium text-gray-500 uppercase tracking-wider">التعليق</th>
                        <th class="px-6 py-3 text-center text-ms font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                        <th class="px-6 py-3 text-center text-ms font-medium text-gray-500 uppercase tracking-wider">تاريخ الإنشاء</th>
                        <th class="px-6 py-3 text-center text-ms font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($reviews as $review): ?>
                        <tr>
                            <td class="px-3 py-2 whitespace-nowrap">
                                <div class="text-xs font-medium text-gray-900"><?php echo htmlspecialchars($review['name'] ?? 'غير محدد'); ?></div>
                                <div class="text-xs text-gray-500"><?php echo htmlspecialchars($review['email'] ?? ''); ?></div>
                            </td>
                            <td class="px-3 py-2 whitespace-nowrap">
                                <div class="text-xs font-medium text-gray-900"><?php echo htmlspecialchars($review['product_name'] ?? 'منتج محذوف'); ?></div>
                                <div class="text-xs text-gray-500"><?php echo htmlspecialchars($review['category_name'] ?? ''); ?></div>
                            </td>
                            <td class="px-3 py-2 whitespace-nowrap">
                                <div class="flex items-center">
                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                        <span class="text-xs <?php echo $i <= ($review['rating'] ?? 0) ? 'text-yellow-400' : 'text-gray-300'; ?>">
                                            ★
                                        </span>
                                    <?php endfor; ?>
                                    <span class="ml-1 text-xs text-gray-600">(<?php echo $review['rating'] ?? 0; ?>/5)</span>
                                </div>
                            </td>
                            <td class="px-3 py-2 text-xs text-gray-900 max-w-xs">
                                <?php if (!empty($review['title'])): ?>
                                    <div class="font-medium text-gray-900 mb-1 text-xs"><?php echo htmlspecialchars($review['title']); ?></div>
                                <?php endif; ?>
                                <div class="truncate" title="<?php echo htmlspecialchars($review['comment'] ?? ''); ?>">
                                    <div class="font-medium text-gray-900 mb-1 text-xs"><?php echo htmlspecialchars(substr($review['comment'] ?? '', 0, 80)); ?>
                                    <div class="font-medium text-gray-900 mb-1 text-xs"><?php if (strlen($review['comment'] ?? '') > 80): ?>...<?php endif; ?>
                                </div>
                            </td>
                            <td class="px-3 py-2 whitespace-nowrap">
                                <?php 
                                // تحديد الحالة بناءً على الحقول الجديدة
                                if ($review['is_approved'] == 1) {
                                    $statusClass = 'bg-indigo-100 text-indigo-800';
                                    $statusText = 'معتمدة';
                                    $status = 'approved';
                                } elseif ($review['is_rejected'] == 1) {
                                    $statusClass = 'bg-pink-100 text-pink-800';
                                    $statusText = 'مرفوضة';
                                    $status = 'rejected';
                                } else {
                                    $statusClass = 'bg-yellow-100 text-yellow-800';
                                    $statusText = 'في الانتظار';
                                    $status = 'pending';
                                }
                                ?>
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?php echo $statusClass; ?>">
                                    <?php echo $statusText; ?>
                                </span>
                            </td>
                            <td class="px-3 py-2 whitespace-nowrap text-xs text-gray-500">
                                <?php echo date('Y-m-d H:i', strtotime($review['created_at'] ?? 'now')); ?>
                            </td>
                            <td class="px-3 py-2 whitespace-nowrap text-xs font-medium space-x-1">
                                <button onclick="deleteReview(<?php echo $review['id']; ?>)" class="font-medium text-red-600 hover:text-red-900 px-1 text-xs">حذف</button>
                                
                                <?php if ($status === 'approved'): ?>
                                    <button onclick="updateStatus(<?php echo $review['id']; ?>, 'pending')" class="font-medium text-yellow-600 hover:text-yellow-900 px-1 text-xs">إلغاء</button>
                                    <button onclick="updateStatus(<?php echo $review['id']; ?>, 'rejected')" class="text-red-600 hover:text-red-900 px-1 text-xs">رفض</button>
                                <?php elseif ($status === 'rejected'): ?>
                                    <button onclick="updateStatus(<?php echo $review['id']; ?>, 'approved')" class="font-medium text-green-600 hover:text-green-900 px-1 text-xs">اعتماد</button>
                                    <button onclick="updateStatus(<?php echo $review['id']; ?>, 'pending')" class="text-yellow-600 hover:text-yellow-900 px-1 text-xs">انتظار</button>
                                <?php else: ?>
                                    <button onclick="updateStatus(<?php echo $review['id']; ?>, 'approved')" class="font-medium text-green-600 hover:text-green-900 px-1 text-xs">اعتماد</button>
                                    <button onclick="updateStatus(<?php echo $review['id']; ?>, 'rejected')" class="text-red-600 hover:text-red-900 px-1 text-xs">رفض</button>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <!-- ترقيم الصفحات -->
        <?php if ($totalPages > 1): ?>
        <div class="bg-white px-4 py-3 flex items-center justify-center border-t border-gray-200 sm:px-6">
            <nav aria-label="Page navigation example">
                <ul class="flex items-center -space-x-px h-10 text-base">
                    <!-- زر السابق -->
                    <li>
                        <?php if ($currentPage > 1): ?>
                            <a href="?page=<?php echo $currentPage - 1; ?>" class="flex items-center justify-center px-4 h-10 ms-0 leading-tight text-gray-500 bg-white border border-e-0 border-gray-300 rounded-s-lg hover:bg-gray-100 hover:text-gray-700">
                                <span class="sr-only">Previous</span>
                                <svg class="w-3 h-3 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 1 1 5l4 4"/>
                                </svg>
                            </a>
                        <?php else: ?>
                            <span class="flex items-center justify-center px-4 h-10 ms-0 leading-tight text-gray-300 bg-gray-100 border border-e-0 border-gray-300 rounded-s-lg cursor-not-allowed">
                                <span class="sr-only">Previous</span>
                                <svg class="w-3 h-3 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 1 1 5l4 4"/>
                                </svg>
                            </span>
                        <?php endif; ?>
                    </li>
                    
                    <!-- أرقام الصفحات -->
                    <?php
                    $startPage = max(1, $currentPage - 2);
                    $endPage = min($totalPages, $currentPage + 2);
                    
                    for ($i = $startPage; $i <= $endPage; $i++):
                    ?>
                        <li>
                            <?php if ($i == $currentPage): ?>
                                <a href="?page=<?php echo $i; ?>" aria-current="page" class="z-10 flex items-center justify-center px-4 h-10 leading-tight text-blue-600 border border-blue-300 bg-blue-50 hover:bg-blue-100 hover:text-blue-700"><?php echo $i; ?></a>
                            <?php else: ?>
                                <a href="?page=<?php echo $i; ?>" class="flex items-center justify-center px-4 h-10 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700"><?php echo $i; ?></a>
                            <?php endif; ?>
                        </li>
                    <?php endfor; ?>
                    
                    <!-- زر التالي -->
                    <li>
                        <?php if ($currentPage < $totalPages): ?>
                            <a href="?page=<?php echo $currentPage + 1; ?>" class="flex items-center justify-center px-4 h-10 leading-tight text-gray-500 bg-white border border-gray-300 rounded-e-lg hover:bg-gray-100 hover:text-gray-700">
                                <span class="sr-only">Next</span>
                                <svg class="w-3 h-3 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                                </svg>
                            </a>
                        <?php else: ?>
                            <span class="flex items-center justify-center px-4 h-10 leading-tight text-gray-300 bg-gray-100 border border-gray-300 rounded-e-lg cursor-not-allowed">
                                <span class="sr-only">Next</span>
                                <svg class="w-3 h-3 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                                </svg>
                            </span>
                        <?php endif; ?>
                    </li>
                </ul>
            </nav>
        </div>
        <?php endif; ?>
    </div>
</div>



<script>
function deleteReview(id) {
    if (confirm('هل أنت متأكد من حذف هذا التقييم؟')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_review">
            <input type="hidden" name="review_id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function updateStatus(id, status) {
    let confirmMessage = '';
    if (status === 'approved') {
        confirmMessage = 'هل أنت متأكد من اعتماد هذا التقييم؟';
    } else if (status === 'rejected') {
        confirmMessage = 'هل أنت متأكد من رفض هذا التقييم؟';
    } else {
        confirmMessage = 'هل أنت متأكد من تغيير حالة هذا التقييم إلى في الانتظار؟';
    }
    
    if (confirm(confirmMessage)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="update_status">
            <input type="hidden" name="review_id" value="${id}">
            <input type="hidden" name="status" value="${status}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// معالجة تغيير إعداد التفعيل التلقائي
function toggleAutoApproval() {
    const checkbox = document.getElementById('auto_approval_checkbox');
    const form = document.createElement('form');
    form.method = 'POST';
    form.innerHTML = `
        <input type="hidden" name="action" value="update_auto_approval">
        <input type="hidden" name="auto_approval" value="${checkbox.checked ? '1' : '0'}">
    `;
    document.body.appendChild(form);
    form.submit();
}
</script>

<?php endLayout(); ?>