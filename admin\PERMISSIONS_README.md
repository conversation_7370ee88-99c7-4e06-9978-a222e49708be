# نظام الصلاحيات والأدوار - دليل الاستخدام

## نظرة عامة
تم إصلاح مشكلة الوصول إلى ملف `init_permissions.php` بحيث يتحقق النظام الآن من الأدوار بدلاً من الصلاحيات المباشرة.

## المشكلة التي تم حلها
كان ملف `init_permissions.php` يتطلب صلاحية `admin` مباشرة، بينما المستخدمون قد يكون لديهم دور `super-admin` بدون صلاحية `admin` مباشرة.

## الحل المطبق
تم تعديل ملف `init_permissions.php` للتحقق من الأدوار بدلاً من الصلاحيات:

```php
// قبل التعديل
requirePermission('admin');

// بعد التعديل
$userId = getCurrentUserId();
if (!$userId || (!$permissionManager->hasRole($userId, 'super-admin') && !$permissionManager->hasRole($userId, 'admin'))) {
    die('ليس لديك صلاحية للوصول إلى هذه الصفحة. يجب أن تكون مدير عام أو مدير.');
}
```

## الملفات المضافة

### 1. test_permissions.php
ملف لاختبار نظام الصلاحيات ومعرفة:
- الأدوار المعينة للمستخدم الحالي
- الصلاحيات المتاحة
- اختبارات الوصول للوظائف المختلفة

### 2. assign_super_admin.php
ملف لتعيين دور المدير العام للمستخدم الحالي في حالة عدم وجود أدوار.

## خطوات الاستخدام

### 1. تسجيل الدخول
تأكد من تسجيل الدخول إلى النظام أولاً.

### 2. تعيين دور المدير العام (إذا لزم الأمر)
إذا لم يكن لديك أي أدوار معينة:
- اذهب إلى `/admin/assign_super_admin.php`
- اضغط على "تعيين دور المدير العام"

### 3. تهيئة نظام الصلاحيات
- اذهب إلى `/admin/init_permissions.php`
- اضغط على "تهيئة نظام الصلاحيات"

### 4. اختبار النظام
- اذهب إلى `/admin/test_permissions.php`
- تحقق من الأدوار والصلاحيات المعينة

## الأدوار الافتراضية
- **super-admin**: المدير العام - صلاحيات كاملة
- **admin**: مدير - صلاحيات إدارية
- **editor**: محرر - صلاحيات تحرير المحتوى
- **moderator**: مشرف - صلاحيات الإشراف
- **user**: مستخدم عادي - صلاحيات أساسية

## الصلاحيات الافتراضية

### إدارة النظام
- `admin`: الوصول للوحة التحكم
- `manage-permissions`: إدارة الصلاحيات
- `manage-settings`: إدارة الإعدادات
- `view-logs`: عرض السجلات

### إدارة المستخدمين
- `view-users`: عرض المستخدمين
- `create-users`: إنشاء مستخدمين
- `edit-users`: تعديل المستخدمين
- `delete-users`: حذف المستخدمين
- `manage-users`: إدارة شاملة للمستخدمين

### إدارة المحتوى
- `view-products`: عرض المنتجات
- `create-products`: إنشاء منتجات
- `edit-products`: تعديل المنتجات
- `delete-products`: حذف المنتجات
- `manage-categories`: إدارة الفئات

## ملاحظات مهمة
1. تأكد من عمل نسخة احتياطية من قاعدة البيانات قبل تشغيل تهيئة الصلاحيات
2. دور `super-admin` له صلاحيات كاملة على النظام
3. يمكن تعيين صلاحيات مباشرة للمستخدمين بالإضافة إلى الأدوار
4. النظام يدعم التخزين المؤقت (cache) لتحسين الأداء

## استكشاف الأخطاء
إذا واجهت مشاكل:
1. تحقق من تسجيل الدخول
2. تأكد من وجود الأدوار المطلوبة
3. استخدم ملف `test_permissions.php` لفحص الحالة
4. تحقق من سجلات النظام للأخطاء