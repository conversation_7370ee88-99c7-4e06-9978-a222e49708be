<?php
/**
 * API لجلب صلاحيات الدور
 * Get Role Permissions API
 */

require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/middleware.php';

// التحقق من تسجيل الدخول وصلاحية الإدارة
requirePermission('manage-permissions');

header('Content-Type: application/json');

try {
    $roleId = $_GET['role_id'] ?? null;
    
    if (!$roleId) {
        throw new Exception('معرف الدور مطلوب');
    }
    
    // جلب معلومات الدور
    $role = $database->fetch("SELECT * FROM roles WHERE id = :role_id", ['role_id' => $roleId]);
    
    if (!$role) {
        throw new Exception('الدور غير موجود');
    }
    
    // جلب جميع الصلاحيات
    $permissions = $database->fetchAll("
        SELECT * FROM permissions 
        ORDER BY category, name
    ");
    
    // جلب صلاحيات الدور الحالية
    $rolePermissionsResult = $database->fetchAll("
        SELECT permission_id 
        FROM role_permissions 
        WHERE role_id = :role_id
    ", ['role_id' => $roleId]);
    
    // استخراج معرفات الصلاحيات
    $rolePermissions = array_column($rolePermissionsResult, 'permission_id');
    
    // تحويل إلى أرقام صحيحة
    $rolePermissions = array_map('intval', $rolePermissions);
    
    echo json_encode([
        'success' => true,
        'role' => $role,
        'permissions' => $permissions,
        'rolePermissions' => $rolePermissions
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>