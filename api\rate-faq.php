<?php
require_once '../config/config.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        echo json_encode([
            'success' => false, 
            'message' => 'Invalid JSON data'
        ]);
        exit;
    }
    
    // Validate required fields
    if (empty($input['faq_id']) || empty($input['rating'])) {
        echo json_encode([
            'success' => false, 
            'message' => 'FAQ ID and rating are required'
        ]);
        exit;
    }
    
    $faq_id = (int)$input['faq_id'];
    $rating = $input['rating'];
    
    // Validate rating
    if (!in_array($rating, ['yes', 'no'])) {
        echo json_encode([
            'success' => false, 
            'message' => 'Invalid rating value'
        ]);
        exit;
    }
    
    // استخدام كلاس Database
    global $database;
    $pdo = $database->getConnection();
    
    // Check if FAQ exists
    $stmt = $pdo->prepare("SELECT id FROM faqs WHERE id = ?");
    $stmt->execute([$faq_id]);
    
    if (!$stmt->fetch()) {
        echo json_encode([
            'success' => false, 
            'message' => 'FAQ not found'
        ]);
        exit;
    }
    
    // Get client IP for duplicate prevention
    $client_ip = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    
    // Check if this IP already rated this FAQ today
    $stmt = $pdo->prepare("
        SELECT id FROM faq_ratings 
        WHERE faq_id = ? AND client_ip = ? AND DATE(created_at) = CURDATE()
    ");
    $stmt->execute([$faq_id, $client_ip]);
    
    if ($stmt->fetch()) {
        echo json_encode([
            'success' => false, 
            'message' => 'لقد قمت بتقييم هذا السؤال اليوم'
        ]);
        exit;
    }
    
    // Save rating
    $stmt = $pdo->prepare("
        INSERT INTO faq_ratings (faq_id, rating, client_ip, created_at) 
        VALUES (?, ?, ?, NOW())
    ");
    
    $result = $stmt->execute([$faq_id, $rating, $client_ip]);
    
    if ($result) {
        // Update FAQ statistics
        $rating_value = ($rating === 'yes') ? 1 : 0;
        $stmt = $pdo->prepare("
            UPDATE faqs SET 
                helpful_count = helpful_count + ?,
                total_ratings = total_ratings + 1
            WHERE id = ?
        ");
        $stmt->execute([$rating_value, $faq_id]);
        
        // Get updated counts
        $stmt = $pdo->prepare("
            SELECT helpful_count, total_ratings 
            FROM faqs 
            WHERE id = ?
        ");
        $stmt->execute([$faq_id]);
        $faq_data = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $new_counts = [
            'yes_count' => (int)$faq_data['helpful_count'],
            'no_count' => (int)$faq_data['total_ratings'] - (int)$faq_data['helpful_count'],
            'total_count' => (int)$faq_data['total_ratings']
        ];
        
        // Log activity (optional - don't fail if this fails)
        try {
            logActivity('faq_rating', "تقييم سؤال شائع #{$faq_id} - {$rating}");
        } catch (Exception $logError) {
            // تجاهل أخطاء تسجيل النشاط
            error_log('Log activity error: ' . $logError->getMessage());
        }
        
        echo json_encode([
            'success' => true, 
            'message' => 'شكراً لك على تقييمك!',
            'new_counts' => $new_counts
        ]);
    } else {
        echo json_encode([
            'success' => false, 
            'message' => 'حدث خطأ أثناء حفظ التقييم'
        ]);
    }
    
} catch (Exception $e) {
    error_log('FAQ rating error: ' . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'حدث خطأ في النظام: ' . $e->getMessage()
    ]);
}
?>