<?php
/**
 * فئة إدارة التقييمات المحسنة
 * Enhanced Review Management Class
 * 
 * <AUTHOR> Line Team
 * @version 2.0
 */

class ReviewManager {
    private $database;
    private $settings;
    private $logger;
    
    public function __construct($database) {
        $this->database = $database;
        $this->settings = $this->loadSettings();
        $this->logger = new Logger('reviews');
    }
    
    /**
     * إضافة تقييم جديد مع التحقق الشامل
     * Add new review with comprehensive validation
     */
    public function addReview($data) {
        try {
            // التحقق من صحة البيانات
            $validation = $this->validateReviewData($data);
            if (!$validation['valid']) {
                return ['success' => false, 'errors' => $validation['errors']];
            }
            
            // التحقق من التكرار
            if ($this->isDuplicateReview($data)) {
                return ['success' => false, 'error' => 'لقد قمت بتقييم هذا المنتج مسبقاً'];
            }
            
            // تنظيف البيانات
            $cleanData = $this->sanitizeReviewData($data);
            
            // تحديد حالة الموافقة
            $isApproved = $this->shouldAutoApprove($cleanData);
            
            // إدراج التقييم
            $reviewId = $this->insertReview($cleanData, $isApproved);
            
            if ($reviewId) {
                // تحديث إحصائيات المنتج
                $this->updateProductStats($cleanData['product_id']);
                
                // إرسال إشعارات
                $this->sendNotifications($reviewId, $isApproved);
                
                // تسجيل العملية
                $this->logger->info("Review added", [
                    'review_id' => $reviewId,
                    'product_id' => $cleanData['product_id'],
                    'auto_approved' => $isApproved
                ]);
                
                return [
                    'success' => true, 
                    'review_id' => $reviewId,
                    'auto_approved' => $isApproved,
                    'message' => $isApproved ? 'تم إضافة تقييمك بنجاح' : 'تم إرسال تقييمك وسيتم مراجعته قريباً'
                ];
            }
            
            return ['success' => false, 'error' => 'حدث خطأ أثناء إضافة التقييم'];
            
        } catch (Exception $e) {
            $this->logger->error("Error adding review", ['error' => $e->getMessage()]);
            return ['success' => false, 'error' => 'حدث خطأ غير متوقع'];
        }
    }
    
    /**
     * التحقق من صحة بيانات التقييم
     * Validate review data
     */
    private function validateReviewData($data) {
        $errors = [];
        
        // التحقق من المنتج
        if (empty($data['product_id']) || !$this->productExists($data['product_id'])) {
            $errors[] = 'المنتج غير موجود';
        }
        
        // التحقق من التقييم
        if (empty($data['rating']) || $data['rating'] < 1 || $data['rating'] > 5) {
            $errors[] = 'يجب أن يكون التقييم بين 1 و 5 نجوم';
        }
        
        // التحقق من التعليق
        $minLength = $this->settings['reviews_min_comment_length'] ?? 10;
        $maxLength = $this->settings['reviews_max_comment_length'] ?? 1000;
        
        if (empty($data['comment'])) {
            $errors[] = 'التعليق مطلوب';
        } elseif (strlen($data['comment']) < $minLength) {
            $errors[] = "التعليق يجب أن يكون على الأقل {$minLength} أحرف";
        } elseif (strlen($data['comment']) > $maxLength) {
            $errors[] = "التعليق يجب ألا يتجاوز {$maxLength} حرف";
        }
        
        // التحقق من المستخدم أو الضيف
        if (empty($data['user_id'])) {
            if ($this->settings['reviews_allow_guest_reviews'] != '1') {
                $errors[] = 'يجب تسجيل الدخول لإضافة تقييم';
            } elseif (empty($data['guest_name']) || empty($data['guest_email'])) {
                $errors[] = 'الاسم والبريد الإلكتروني مطلوبان للضيوف';
            } elseif (!filter_var($data['guest_email'], FILTER_VALIDATE_EMAIL)) {
                $errors[] = 'البريد الإلكتروني غير صحيح';
            }
        }
        
        // فحص المحتوى المشبوه
        if ($this->containsSpam($data['comment'])) {
            $errors[] = 'التعليق يحتوي على محتوى مشبوه';
        }
        
        return ['valid' => empty($errors), 'errors' => $errors];
    }
    
    /**
     * التحقق من تكرار التقييم
     * Check for duplicate reviews
     */
    private function isDuplicateReview($data) {
        $maxReviews = $this->settings['reviews_max_per_user_per_product'] ?? 1;
        
        if ($maxReviews == 0) return false; // لا حد أقصى
        
        $sql = "SELECT COUNT(*) FROM product_reviews WHERE product_id = ?";
        $params = [$data['product_id']];
        
        if (!empty($data['user_id'])) {
            $sql .= " AND user_id = ?";
            $params[] = $data['user_id'];
        } else {
            $sql .= " AND guest_email = ?";
            $params[] = $data['guest_email'];
        }
        
        $count = $this->database->fetchColumn($sql, $params);
        return $count >= $maxReviews;
    }
    
    /**
     * تنظيف بيانات التقييم
     * Sanitize review data
     */
    private function sanitizeReviewData($data) {
        return [
            'product_id' => (int)$data['product_id'],
            'user_id' => !empty($data['user_id']) ? (int)$data['user_id'] : null,
            'guest_name' => !empty($data['guest_name']) ? trim(strip_tags($data['guest_name'])) : null,
            'guest_email' => !empty($data['guest_email']) ? trim(strtolower($data['guest_email'])) : null,
            'rating' => (int)$data['rating'],
            'title' => !empty($data['title']) ? trim(strip_tags($data['title'])) : null,
            'comment' => trim(strip_tags($data['comment'])),
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
        ];
    }
    
    /**
     * تحديد ما إذا كان يجب الموافقة التلقائية
     * Determine if review should be auto-approved
     */
    private function shouldAutoApprove($data) {
        // إذا كانت الموافقة التلقائية مفعلة
        if ($this->settings['reviews_auto_approval'] == '1') {
            return true;
        }
        
        // موافقة تلقائية للمستخدمين المسجلين مع تقييمات سابقة جيدة
        if (!empty($data['user_id'])) {
            $userReputationScore = $this->getUserReputationScore($data['user_id']);
            if ($userReputationScore >= 80) { // نقاط سمعة عالية
                return true;
            }
        }
        
        // موافقة تلقائية للتقييمات الإيجابية من عملاء سابقين
        if ($data['rating'] >= 4 && $this->isVerifiedCustomer($data)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * إدراج التقييم في قاعدة البيانات
     * Insert review into database
     */
    private function insertReview($data, $isApproved) {
        $sql = "INSERT INTO product_reviews (
            product_id, user_id, guest_name, guest_email, rating, title, comment,
            is_approved, ip_address, user_agent, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
        
        $params = [
            $data['product_id'],
            $data['user_id'],
            $data['guest_name'],
            $data['guest_email'],
            $data['rating'],
            $data['title'],
            $data['comment'],
            $isApproved ? 1 : 0,
            $data['ip_address'],
            $data['user_agent']
        ];
        
        return $this->database->insert($sql, $params);
    }
    
    /**
     * الحصول على تقييمات المنتج مع التصفح
     * Get product reviews with pagination
     */
    public function getProductReviews($productId, $page = 1, $limit = null) {
        $limit = $limit ?? ($this->settings['reviews_display_per_page'] ?? 10);
        $offset = ($page - 1) * $limit;
        
        $sql = "SELECT 
            pr.*,
            COALESCE(u.name, pr.guest_name) as reviewer_name,
            (SELECT COUNT(*) FROM review_helpfulness rh WHERE rh.review_id = pr.id AND rh.is_helpful = 1) as helpful_count,
            (SELECT COUNT(*) FROM review_helpfulness rh WHERE rh.review_id = pr.id AND rh.is_helpful = 0) as unhelpful_count
        FROM product_reviews pr
        LEFT JOIN users u ON pr.user_id = u.id
        WHERE pr.product_id = ? AND pr.is_approved = 1
        ORDER BY pr.is_featured DESC, pr.created_at DESC
        LIMIT ? OFFSET ?";
        
        $reviews = $this->database->fetchAll($sql, [$productId, $limit, $offset]);
        
        // إحصائيات التقييمات
        $stats = $this->getProductRatingStats($productId);
        
        return [
            'reviews' => $reviews,
            'stats' => $stats,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $limit,
                'total' => $stats['total_reviews'],
                'total_pages' => ceil($stats['total_reviews'] / $limit)
            ]
        ];
    }
    
    /**
     * الحصول على إحصائيات تقييمات المنتج
     * Get product rating statistics
     */
    public function getProductRatingStats($productId) {
        $sql = "SELECT * FROM product_rating_stats WHERE product_id = ?";
        $stats = $this->database->fetch($sql, [$productId]);
        
        if (!$stats) {
            // إنشاء إحصائيات جديدة
            $this->updateProductStats($productId);
            $stats = $this->database->fetch($sql, [$productId]);
        }
        
        return $stats;
    }
    
    /**
     * تحديث إحصائيات المنتج
     * Update product statistics
     */
    private function updateProductStats($productId) {
        $sql = "CALL UpdateProductRatingStats(?)";
        $this->database->execute($sql, [$productId]);
    }
    
    /**
     * تسجيل مساعدة التقييم
     * Record review helpfulness
     */
    public function recordHelpfulness($reviewId, $isHelpful, $userId = null) {
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? null;
        
        // التحقق من التصويت السابق
        $sql = "SELECT id FROM review_helpfulness WHERE review_id = ? AND ";
        $params = [$reviewId];
        
        if ($userId) {
            $sql .= "user_id = ?";
            $params[] = $userId;
        } else {
            $sql .= "ip_address = ?";
            $params[] = $ipAddress;
        }
        
        $existing = $this->database->fetch($sql, $params);
        
        if ($existing) {
            // تحديث التصويت الموجود
            $sql = "UPDATE review_helpfulness SET is_helpful = ? WHERE id = ?";
            $this->database->execute($sql, [$isHelpful ? 1 : 0, $existing['id']]);
        } else {
            // إضافة تصويت جديد
            $sql = "INSERT INTO review_helpfulness (review_id, user_id, ip_address, is_helpful) VALUES (?, ?, ?, ?)";
            $this->database->execute($sql, [$reviewId, $userId, $ipAddress, $isHelpful ? 1 : 0]);
        }
        
        return ['success' => true];
    }
    
    /**
     * الإبلاغ عن تقييم
     * Report a review
     */
    public function reportReview($reviewId, $reason, $description = null, $userId = null) {
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? null;
        
        $sql = "INSERT INTO review_reports (review_id, reporter_user_id, reporter_ip, reason, description) 
                VALUES (?, ?, ?, ?, ?)";
        
        $reportId = $this->database->insert($sql, [$reviewId, $userId, $ipAddress, $reason, $description]);
        
        if ($reportId) {
            // إرسال إشعار للمشرفين
            $this->notifyAdminsOfReport($reportId);
            return ['success' => true, 'message' => 'تم إرسال البلاغ بنجاح'];
        }
        
        return ['success' => false, 'error' => 'حدث خطأ أثناء إرسال البلاغ'];
    }
    
    /**
     * موافقة على التقييم
     * Approve review
     */
    public function approveReview($reviewId, $adminId) {
        $sql = "UPDATE product_reviews SET 
                is_approved = 1, 
                is_rejected = 0, 
                approved_at = NOW(), 
                approved_by = ? 
                WHERE id = ?";
        
        $result = $this->database->execute($sql, [$adminId, $reviewId]);
        
        if ($result) {
            // الحصول على معلومات التقييم
            $review = $this->database->fetch("SELECT * FROM product_reviews WHERE id = ?", [$reviewId]);
            
            // تحديث إحصائيات المنتج
            $this->updateProductStats($review['product_id']);
            
            // إرسال إشعار للمراجع
            $this->notifyReviewerOfApproval($reviewId);
            
            return ['success' => true, 'message' => 'تم قبول التقييم بنجاح'];
        }
        
        return ['success' => false, 'error' => 'حدث خطأ أثناء قبول التقييم'];
    }
    
    /**
     * رفض التقييم
     * Reject review
     */
    public function rejectReview($reviewId, $adminId, $reason = null) {
        $sql = "UPDATE product_reviews SET 
                is_approved = 0, 
                is_rejected = 1, 
                admin_notes = ?, 
                approved_by = ? 
                WHERE id = ?";
        
        $result = $this->database->execute($sql, [$reason, $adminId, $reviewId]);
        
        if ($result) {
            // الحصول على معلومات التقييم
            $review = $this->database->fetch("SELECT * FROM product_reviews WHERE id = ?", [$reviewId]);
            
            // تحديث إحصائيات المنتج
            $this->updateProductStats($review['product_id']);
            
            // إرسال إشعار للمراجع
            $this->notifyReviewerOfRejection($reviewId, $reason);
            
            return ['success' => true, 'message' => 'تم رفض التقييم'];
        }
        
        return ['success' => false, 'error' => 'حدث خطأ أثناء رفض التقييم'];
    }
    
    // Helper methods
    private function loadSettings() {
        $sql = "SELECT setting_key, setting_value FROM settings WHERE setting_key LIKE 'reviews_%'";
        $settings = $this->database->fetchAll($sql);
        
        $result = [];
        foreach ($settings as $setting) {
            $result[$setting['setting_key']] = $setting['setting_value'];
        }
        
        return $result;
    }
    
    private function productExists($productId) {
        $sql = "SELECT id FROM products WHERE id = ? AND is_active = 1";
        return $this->database->fetch($sql, [$productId]) !== false;
    }
    
    private function containsSpam($text) {
        $spamWords = ['spam', 'fake', 'scam', 'viagra', 'casino'];
        $text = strtolower($text);
        
        foreach ($spamWords as $word) {
            if (strpos($text, $word) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    private function getUserReputationScore($userId) {
        // حساب نقاط السمعة بناءً على التقييمات السابقة
        $sql = "SELECT 
            COUNT(*) as total_reviews,
            AVG(helpful_count) as avg_helpful,
            COUNT(CASE WHEN is_approved = 1 THEN 1 END) as approved_reviews
        FROM (
            SELECT pr.id,
                (SELECT COUNT(*) FROM review_helpfulness rh WHERE rh.review_id = pr.id AND rh.is_helpful = 1) as helpful_count,
                pr.is_approved
            FROM product_reviews pr WHERE pr.user_id = ?
        ) user_reviews";
        
        $stats = $this->database->fetch($sql, [$userId]);
        
        if (!$stats || $stats['total_reviews'] == 0) {
            return 50; // نقاط افتراضية للمستخدمين الجدد
        }
        
        $approvalRate = ($stats['approved_reviews'] / $stats['total_reviews']) * 100;
        $helpfulnessScore = min($stats['avg_helpful'] * 10, 50);
        
        return min($approvalRate + $helpfulnessScore, 100);
    }
    
    private function isVerifiedCustomer($data) {
        if (empty($data['user_id'])) return false;
        
        // التحقق من وجود طلبات سابقة للمنتج
        $sql = "SELECT COUNT(*) FROM order_items oi 
                JOIN orders o ON oi.order_id = o.id 
                WHERE o.user_id = ? AND oi.product_id = ? AND o.status = 'completed'";
        
        $count = $this->database->fetchColumn($sql, [$data['user_id'], $data['product_id']]);
        return $count > 0;
    }
    
    private function sendNotifications($reviewId, $isApproved) {
        // إرسال إشعارات للمشرفين إذا لم تتم الموافقة التلقائية
        if (!$isApproved && !empty($this->settings['reviews_moderation_email'])) {
            // إرسال بريد إلكتروني للمشرفين
            $this->sendModerationEmail($reviewId);
        }
    }
    
    private function sendModerationEmail($reviewId) {
        // تنفيذ إرسال البريد الإلكتروني
    }
    
    private function notifyAdminsOfReport($reportId) {
        // إرسال إشعار للمشرفين عن البلاغ
    }
    
    private function notifyReviewerOfApproval($reviewId) {
        // إرسال إشعار للمراجع عن الموافقة
    }
    
    private function notifyReviewerOfRejection($reviewId, $reason) {
        // إرسال إشعار للمراجع عن الرفض
    }
}

/**
 * فئة تسجيل الأحداث البسيطة
 * Simple Logger Class
 */
class Logger {
    private $context;
    
    public function __construct($context) {
        $this->context = $context;
    }
    
    public function info($message, $data = []) {
        $this->log('INFO', $message, $data);
    }
    
    public function error($message, $data = []) {
        $this->log('ERROR', $message, $data);
    }
    
    private function log($level, $message, $data) {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => $level,
            'context' => $this->context,
            'message' => $message,
            'data' => $data
        ];
        
        $logFile = __DIR__ . '/../logs/reviews.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        file_put_contents($logFile, json_encode($logEntry, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND | LOCK_EX);
    }
}
?>