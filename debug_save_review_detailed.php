<?php
require_once 'config/config.php';
require_once 'includes/functions.php';

// بدء الجلسة
session_start();

// التحقق من تسجيل الدخول
$isLoggedIn = isLoggedIn();
$currentUser = $isLoggedIn ? getCurrentUser() : null;

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص تفصيلي لدالة saveUserReview</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-center mb-8">تشخيص تفصيلي لدالة saveUserReview</h1>

        <!-- معلومات المستخدم الحالي -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">معلومات المستخدم الحالي</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <p><strong>مسجل الدخول:</strong> <?= $isLoggedIn ? 'نعم ✅' : 'لا ❌' ?></p>
                    <?php if ($isLoggedIn && $currentUser): ?>
                        <p><strong>معرف المستخدم:</strong> <?= $currentUser['id'] ?></p>
                        <p><strong>الاسم:</strong> <?= htmlspecialchars($currentUser['name']) ?></p>
                        <p><strong>البريد الإلكتروني:</strong> <?= htmlspecialchars($currentUser['email']) ?></p>
                        <p><strong>الدور:</strong> <?= htmlspecialchars($currentUser['role'] ?? 'غير محدد') ?></p>
                    <?php endif; ?>
                </div>
                
                <div>
                    <h3 class="font-bold mb-2">بيانات الجلسة:</h3>
                    <pre class="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-32">
<?php print_r($_SESSION); ?>
                    </pre>
                </div>
            </div>
        </div>

        <?php if ($isLoggedIn): ?>
        <!-- اختبار دالة saveUserReview -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">اختبار دالة saveUserReview</h2>
            
            <?php
            // معاملات الاختبار
            $test_product_id = 1;
            $test_user_id = $currentUser['id'];
            $test_customer_name = $currentUser['name'];
            $test_customer_email = $currentUser['email'];
            $test_rating = 5;
            $test_review_title = 'اختبار تشخيصي مفصل';
            $test_review_text = 'هذا تقييم تشخيصي لاختبار دالة saveUserReview بالتفصيل';
            
            echo "<div class='mb-4 p-4 bg-blue-50 border border-blue-200 rounded'>";
            echo "<h3 class='font-bold mb-2'>معاملات الاختبار:</h3>";
            echo "<ul class='list-disc list-inside space-y-1 text-sm'>";
            echo "<li><strong>product_id:</strong> $test_product_id</li>";
            echo "<li><strong>user_id:</strong> $test_user_id</li>";
            echo "<li><strong>customer_name:</strong> " . htmlspecialchars($test_customer_name) . "</li>";
            echo "<li><strong>customer_email:</strong> " . htmlspecialchars($test_customer_email) . "</li>";
            echo "<li><strong>rating:</strong> $test_rating</li>";
            echo "<li><strong>review_title:</strong> " . htmlspecialchars($test_review_title) . "</li>";
            echo "<li><strong>review_text:</strong> " . htmlspecialchars($test_review_text) . "</li>";
            echo "</ul>";
            echo "</div>";
            
            // اختبار الدالة
            echo "<div class='mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded'>";
            echo "<h3 class='font-bold mb-2'>نتائج الاختبار:</h3>";
            
            try {
                // التحقق من وجود تقييم سابق أولاً
                global $database;
                $existing = $database->fetch(
                    "SELECT id FROM reviews WHERE product_id = :product_id AND user_id = :user_id",
                    ['product_id' => $test_product_id, 'user_id' => $test_user_id]
                );
                
                if ($existing) {
                    echo "<p class='text-orange-600'>⚠️ يوجد تقييم سابق للمنتج (ID: {$existing['id']})</p>";
                    echo "<p class='text-sm text-gray-600 mb-2'>سيتم حذف التقييم السابق لإجراء الاختبار...</p>";
                    
                    // حذف التقييم السابق للاختبار
                    $deleteResult = $database->delete('reviews', ['id' => $existing['id']]);
                    if ($deleteResult) {
                        echo "<p class='text-green-600'>✅ تم حذف التقييم السابق بنجاح</p>";
                    } else {
                        echo "<p class='text-red-600'>❌ فشل في حذف التقييم السابق</p>";
                    }
                }
                
                // اختبار دالة saveUserReview
                echo "<p class='font-bold mt-4 mb-2'>استدعاء دالة saveUserReview...</p>";
                $result = saveUserReview(
                    $test_product_id,
                    $test_user_id,
                    $test_customer_name,
                    $test_customer_email,
                    $test_rating,
                    $test_review_text,
                    $test_review_title
                );
                
                echo "<p><strong>نتيجة الدالة:</strong> ";
                if (is_numeric($result) && $result > 0) {
                    echo "<span class='text-green-600 font-bold'>نجح الحفظ ✅ (ID: $result)</span></p>";
                    
                    // التحقق من البيانات المحفوظة
                    $savedReview = $database->fetch("SELECT * FROM reviews WHERE id = ?", [$result]);
                    if ($savedReview) {
                        echo "<div class='mt-4 p-3 bg-green-50 border border-green-200 rounded'>";
                        echo "<h4 class='font-bold text-green-800 mb-2'>البيانات المحفوظة:</h4>";
                        echo "<div class='grid grid-cols-2 gap-2 text-sm'>";
                        echo "<p><strong>ID:</strong> {$savedReview['id']}</p>";
                        echo "<p><strong>Product ID:</strong> {$savedReview['product_id']}</p>";
                        echo "<p><strong>User ID:</strong> " . ($savedReview['user_id'] ?: 'NULL') . "</p>";
                        echo "<p><strong>الاسم:</strong> " . htmlspecialchars($savedReview['name']) . "</p>";
                        echo "<p><strong>البريد:</strong> " . htmlspecialchars($savedReview['email']) . "</p>";
                        echo "<p><strong>التقييم:</strong> {$savedReview['rating']}/5</p>";
                        echo "<p><strong>العنوان:</strong> " . htmlspecialchars($savedReview['title']) . "</p>";
                        echo "<p><strong>التعليق:</strong> " . htmlspecialchars($savedReview['comment']) . "</p>";
                        echo "<p><strong>معتمد:</strong> " . ($savedReview['is_approved'] ? 'نعم' : 'لا') . "</p>";
                        echo "<p><strong>تاريخ الإنشاء:</strong> {$savedReview['created_at']}</p>";
                        echo "</div>";
                        echo "</div>";
                        
                        // التحقق من صحة user_id
                        if ($savedReview['user_id'] == $test_user_id) {
                            echo "<p class='text-green-600 font-bold mt-2'>✅ تم حفظ user_id بشكل صحيح!</p>";
                        } else {
                            echo "<p class='text-red-600 font-bold mt-2'>❌ user_id غير صحيح! متوقع: $test_user_id، محفوظ: " . ($savedReview['user_id'] ?: 'NULL') . "</p>";
                        }
                    }
                } else {
                    echo "<span class='text-red-600 font-bold'>فشل الحفظ ❌</span></p>";
                    echo "<p><strong>رسالة الخطأ:</strong> " . htmlspecialchars($result) . "</p>";
                }
                
            } catch (Exception $e) {
                echo "<p class='text-red-600 font-bold'>❌ خطأ في التنفيذ: " . htmlspecialchars($e->getMessage()) . "</p>";
                echo "<p class='text-sm text-gray-600'>تفاصيل الخطأ: " . htmlspecialchars($e->getTraceAsString()) . "</p>";
            }
            
            echo "</div>";
            ?>
        </div>

        <!-- اختبار إعدادات النظام -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">إعدادات النظام ذات الصلة</h2>
            
            <?php
            try {
                $autoApproval = getSetting('reviews_auto_approval', false);
                echo "<p><strong>التفعيل التلقائي للتقييمات:</strong> " . ($autoApproval ? 'مفعل ✅' : 'معطل ❌') . "</p>";
                
                // اختبار الاتصال بقاعدة البيانات
                echo "<p><strong>الاتصال بقاعدة البيانات:</strong> ";
                $pdo = $database->getConnection();
                if ($pdo) {
                    echo "متصل ✅</p>";
                } else {
                    echo "غير متصل ❌</p>";
                }
                
                // اختبار جدول reviews
                echo "<p><strong>جدول reviews:</strong> ";
                $tableCheck = $database->fetch("SHOW TABLES LIKE 'reviews'");
                if ($tableCheck) {
                    echo "موجود ✅</p>";
                    
                    // فحص هيكل الجدول
                    $columns = $database->fetchAll("DESCRIBE reviews");
                    echo "<div class='mt-2'>";
                    echo "<p class='font-bold'>أعمدة الجدول:</p>";
                    echo "<ul class='list-disc list-inside text-sm'>";
                    foreach ($columns as $column) {
                        echo "<li>{$column['Field']} ({$column['Type']}) - " . ($column['Null'] == 'YES' ? 'يقبل NULL' : 'لا يقبل NULL') . "</li>";
                    }
                    echo "</ul>";
                    echo "</div>";
                } else {
                    echo "غير موجود ❌</p>";
                }
                
            } catch (Exception $e) {
                echo "<p class='text-red-600'>خطأ في فحص الإعدادات: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
            ?>
        </div>

        <!-- آخر التقييمات -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">آخر 5 تقييمات في قاعدة البيانات</h2>
            
            <?php
            try {
                $recentReviews = $database->fetchAll("SELECT * FROM reviews ORDER BY created_at DESC LIMIT 5");
                
                if (!empty($recentReviews)) {
                    echo "<div class='overflow-x-auto'>";
                    echo "<table class='min-w-full table-auto border-collapse border border-gray-300'>";
                    echo "<thead>";
                    echo "<tr class='bg-gray-50'>";
                    echo "<th class='border border-gray-300 px-2 py-1 text-right'>ID</th>";
                    echo "<th class='border border-gray-300 px-2 py-1 text-right'>Product</th>";
                    echo "<th class='border border-gray-300 px-2 py-1 text-right'>User ID</th>";
                    echo "<th class='border border-gray-300 px-2 py-1 text-right'>الاسم</th>";
                    echo "<th class='border border-gray-300 px-2 py-1 text-right'>التقييم</th>";
                    echo "<th class='border border-gray-300 px-2 py-1 text-right'>معتمد</th>";
                    echo "<th class='border border-gray-300 px-2 py-1 text-right'>التاريخ</th>";
                    echo "</tr>";
                    echo "</thead>";
                    echo "<tbody>";
                    
                    foreach ($recentReviews as $review) {
                        $userIdClass = $review['user_id'] ? 'text-green-600 font-bold' : 'text-red-600 font-bold';
                        echo "<tr>";
                        echo "<td class='border border-gray-300 px-2 py-1'>{$review['id']}</td>";
                        echo "<td class='border border-gray-300 px-2 py-1'>{$review['product_id']}</td>";
                        echo "<td class='border border-gray-300 px-2 py-1 $userIdClass'>" . ($review['user_id'] ?: 'NULL') . "</td>";
                        echo "<td class='border border-gray-300 px-2 py-1'>" . htmlspecialchars($review['name']) . "</td>";
                        echo "<td class='border border-gray-300 px-2 py-1'>{$review['rating']}/5</td>";
                        echo "<td class='border border-gray-300 px-2 py-1'>" . ($review['is_approved'] ? 'نعم' : 'لا') . "</td>";
                        echo "<td class='border border-gray-300 px-2 py-1'>" . date('Y-m-d H:i', strtotime($review['created_at'])) . "</td>";
                        echo "</tr>";
                    }
                    
                    echo "</tbody>";
                    echo "</table>";
                    echo "</div>";
                } else {
                    echo "<p class='text-gray-500'>لا توجد تقييمات في قاعدة البيانات</p>";
                }
                
            } catch (Exception $e) {
                echo "<p class='text-red-600'>خطأ في قراءة التقييمات: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
            ?>
        </div>

        <?php else: ?>
        <!-- رسالة للمستخدمين غير المسجلين -->
        <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-6">
            <strong>تحذير:</strong> يجب تسجيل الدخول أولاً لإجراء الاختبار.
            <a href="test_login_simple.php" class="underline ml-2">تسجيل الدخول</a>
        </div>
        <?php endif; ?>

        <!-- روابط التنقل -->
        <div class="text-center">
            <a href="index.php" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded mr-2">
                العودة للصفحة الرئيسية
            </a>
            <a href="debug_user_id_complete.php" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-2">
                صفحة التشخيص الشاملة
            </a>
            <a href="products/1" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                اختبار في صفحة المنتج
            </a>
        </div>
    </div>
</body>
</html>