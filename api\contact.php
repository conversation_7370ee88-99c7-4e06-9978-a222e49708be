<?php
require_once '../config/config.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Get JSON input
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    // If no JSON data, try $_POST (for backward compatibility)
    if (!$data) {
        $data = $_POST;
    }
    
    // Validate required fields
    $required_fields = ['name', 'email', 'phone', 'subject', 'message'];
    $missing_fields = [];
    
    foreach ($required_fields as $field) {
        if (empty($data[$field])) {
            $missing_fields[] = $field;
        }
    }
    
    if (!empty($missing_fields)) {
        echo json_encode([
            'success' => false, 
            'message' => 'الحقول التالية مطلوبة: ' . implode(', ', $missing_fields)
        ]);
        exit;
    }
    
    // Sanitize input
    $name = sanitizeInput($data['name']);
    $email = sanitizeInput($data['email']);
    $phone = sanitizeInput($data['phone']);
    $subject = sanitizeInput($data['subject']);
    $message = sanitizeInput($data['message']);
    $type = sanitizeInput($data['type'] ?? 'general');
    $product_id = isset($data['product_id']) ? (int)$data['product_id'] : null;
    
    // Validate email
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        echo json_encode([
            'success' => false, 
            'message' => 'البريد الإلكتروني غير صحيح'
        ]);
        exit;
    }
    
    // Validate phone
    if (!preg_match('/^[0-9+\-\s()]{10,}$/', $phone)) {
        echo json_encode([
            'success' => false, 
            'message' => 'رقم الهاتف غير صحيح'
        ]);
        exit;
    }
    
    // Save contact message
    $contact_data = [
        'name' => $name,
        'email' => $email,
        'phone' => $phone,
        'subject' => $subject,
        'message' => $message,
        'type' => $type,
        'product_id' => $product_id
    ];
    
    $message_id = saveContactMessage($contact_data);
    
    if ($message_id) {
        // Send notification email to admin
        $admin_email = getSetting('admin_email');
        if ($admin_email) {
            $email_subject = 'رسالة جديدة من موقع ' . getSetting('site_name');
            $email_body = "
                <h2>رسالة جديدة من الموقع</h2>
                <p><strong>الاسم:</strong> {$name}</p>
                <p><strong>البريد الإلكتروني:</strong> {$email}</p>
                <p><strong>الهاتف:</strong> {$phone}</p>
                <p><strong>الموضوع:</strong> {$subject}</p>
                <p><strong>النوع:</strong> {$type}</p>
                <p><strong>الرسالة:</strong></p>
                <div style='background: #f5f5f5; padding: 15px; border-radius: 5px;'>
                    {$message}
                </div>
                <p><strong>التاريخ:</strong> " . date('Y-m-d H:i:s') . "</p>
            ";
            
            sendEmail($admin_email, $email_subject, $email_body);
        }
        
        // Send auto-reply to customer
        $auto_reply_subject = 'شكراً لتواصلك معنا - ' . getSetting('site_name');
        $auto_reply_body = "
            <h2>شكراً لتواصلك معنا</h2>
            <p>عزيزي/عزيزتي {$name},</p>
            <p>شكراً لك على تواصلك معنا. لقد تم استلام رسالتك وسيقوم فريقنا بالرد عليك في أقرب وقت ممكن.</p>
            <p><strong>تفاصيل رسالتك:</strong></p>
            <p><strong>الموضوع:</strong> {$subject}</p>
            <p><strong>رقم المرجع:</strong> #CM{$message_id}</p>
            <p>مع أطيب التحيات,<br>فريق " . getSetting('site_name') . "</p>
        ";
        
        sendEmail($email, $auto_reply_subject, $auto_reply_body);
        
        // Log activity
        logActivity('contact_message', "رسالة جديدة من {$name} - {$subject}");
        
        echo json_encode([
            'success' => true, 
            'message' => 'تم إرسال رسالتك بنجاح. سنتواصل معك قريباً.',
            'reference_id' => "CM{$message_id}"
        ]);
    } else {
        echo json_encode([
            'success' => false, 
            'message' => 'حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.'
        ]);
    }
    
} catch (Exception $e) {
    error_log('Contact form error: ' . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'حدث خطأ في النظام. يرجى المحاولة مرة أخرى لاحقاً.'
    ]);
}
?>