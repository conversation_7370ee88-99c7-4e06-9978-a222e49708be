<?php
require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$db = new Database();
$message = '';
$error = '';

// معالجة طلب تنفيذ الإعداد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['setup_permissions'])) {
    try {
        // قراءة ملف SQL
        $sqlFile = '../setup/permissions_setup.sql';
        if (!file_exists($sqlFile)) {
            throw new Exception('ملف إعداد قاعدة البيانات غير موجود');
        }
        
        $sql = file_get_contents($sqlFile);
        if ($sql === false) {
            throw new Exception('فشل في قراءة ملف إعداد قاعدة البيانات');
        }
        
        // تقسيم الاستعلامات
        $queries = array_filter(array_map('trim', explode(';', $sql)));
        
        $successCount = 0;
        $errorCount = 0;
        $errors = [];
        
        foreach ($queries as $query) {
            if (empty($query) || strpos($query, '--') === 0 || strpos($query, 'DELIMITER') === 0) {
                continue;
            }
            
            try {
                $db->query($query);
                $successCount++;
            } catch (Exception $e) {
                $errorCount++;
                $errors[] = $e->getMessage();
            }
        }
        
        if ($errorCount === 0) {
            // تعيين دور المدير العام للمستخدم الحالي إذا لم يكن لديه أدوار
            $userId = $_SESSION['user_id'];
            $userRoles = $db->query("SELECT COUNT(*) as count FROM user_roles WHERE user_id = ?", [$userId])->fetch();
            
            if ($userRoles['count'] == 0) {
                $superAdminRole = $db->query("SELECT id FROM roles WHERE name = 'super-admin'", [])->fetch();
                if ($superAdminRole) {
                    $db->query(
                        "INSERT INTO user_roles (user_id, role_id, assigned_by) VALUES (?, ?, ?)",
                        [$userId, $superAdminRole['id'], $userId]
                    );
                }
            }
            
            $message = "تم إعداد نظام الصلاحيات بنجاح! تم تنفيذ {$successCount} استعلام.";
        } else {
            $error = "تم إعداد النظام مع بعض الأخطاء. نجح: {$successCount}, فشل: {$errorCount}";
            if (!empty($errors)) {
                $error .= "<br>الأخطاء: " . implode('<br>', array_slice($errors, 0, 5));
            }
        }
        
    } catch (Exception $e) {
        $error = 'خطأ في إعداد النظام: ' . $e->getMessage();
    }
}

// التحقق من حالة النظام
$systemStatus = [];
try {
    // فحص الجداول
    $tables = ['permissions', 'roles', 'role_permissions', 'user_roles', 'user_permissions', 'permission_logs'];
    foreach ($tables as $table) {
        try {
            $result = $db->query("SELECT COUNT(*) as count FROM {$table}", [])->fetch();
            $systemStatus[$table] = [
                'exists' => true,
                'count' => $result['count']
            ];
        } catch (Exception $e) {
            $systemStatus[$table] = [
                'exists' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    // فحص الصلاحيات والأدوار
    if ($systemStatus['permissions']['exists'] && $systemStatus['roles']['exists']) {
        $permissionsCount = $systemStatus['permissions']['count'];
        $rolesCount = $systemStatus['roles']['count'];
        
        // فحص دور المستخدم الحالي
        $userId = $_SESSION['user_id'];
        $userRoles = $db->query(
            "SELECT r.name, r.display_name FROM user_roles ur 
             JOIN roles r ON ur.role_id = r.id 
             WHERE ur.user_id = ? AND r.is_active = 1",
            [$userId]
        )->fetchAll();
        
        $systemStatus['current_user_roles'] = $userRoles;
    }
    
} catch (Exception $e) {
    $systemStatus['error'] = $e->getMessage();
}

include 'includes/layout.php';
startLayout('إعداد نظام الصلاحيات');
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-shield-alt"></i>
                        إعداد نظام الصلاحيات والأدوار
                    </h3>
                </div>
                <div class="card-body">
                    <?php if ($message): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle"></i>
                            <?php echo $message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($error): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle"></i>
                            <?php echo $error; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">حالة النظام</h5>
                                </div>
                                <div class="card-body">
                                    <?php if (isset($systemStatus['error'])): ?>
                                        <div class="alert alert-warning">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            خطأ في فحص النظام: <?php echo $systemStatus['error']; ?>
                                        </div>
                                    <?php else: ?>
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>الجدول</th>
                                                        <th>الحالة</th>
                                                        <th>عدد السجلات</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($systemStatus as $table => $status): ?>
                                                        <?php if ($table === 'current_user_roles' || $table === 'error') continue; ?>
                                                        <tr>
                                                            <td><?php echo $table; ?></td>
                                                            <td>
                                                                <?php if ($status['exists']): ?>
                                                                    <span class="badge bg-success">
                                                                        <i class="fas fa-check"></i> موجود
                                                                    </span>
                                                                <?php else: ?>
                                                                    <span class="badge bg-danger">
                                                                        <i class="fas fa-times"></i> غير موجود
                                                                    </span>
                                                                <?php endif; ?>
                                                            </td>
                                                            <td>
                                                                <?php if ($status['exists']): ?>
                                                                    <span class="badge bg-info"><?php echo $status['count']; ?></span>
                                                                <?php else: ?>
                                                                    <small class="text-muted">-</small>
                                                                <?php endif; ?>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                        
                                        <?php if (isset($systemStatus['current_user_roles'])): ?>
                                            <div class="mt-3">
                                                <h6>أدوارك الحالية:</h6>
                                                <?php if (empty($systemStatus['current_user_roles'])): ?>
                                                    <span class="badge bg-warning">لا توجد أدوار مُعيَّنة</span>
                                                <?php else: ?>
                                                    <?php foreach ($systemStatus['current_user_roles'] as $role): ?>
                                                        <span class="badge bg-primary me-1"><?php echo $role['display_name']; ?></span>
                                                    <?php endforeach; ?>
                                                <?php endif; ?>
                                            </div>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">إعداد النظام</h5>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted">
                                        سيقوم هذا الإعداد بإنشاء الجداول التالية وتهيئة البيانات الافتراضية:
                                    </p>
                                    
                                    <ul class="list-group list-group-flush mb-3">
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            جدول الصلاحيات
                                            <span class="badge bg-secondary">permissions</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            جدول الأدوار
                                            <span class="badge bg-secondary">roles</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            ربط الأدوار بالصلاحيات
                                            <span class="badge bg-secondary">role_permissions</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            ربط المستخدمين بالأدوار
                                            <span class="badge bg-secondary">user_roles</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            الصلاحيات المباشرة
                                            <span class="badge bg-secondary">user_permissions</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            سجل التغييرات
                                            <span class="badge bg-secondary">permission_logs</span>
                                        </li>
                                    </ul>
                                    
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i>
                                        <strong>ملاحظة:</strong> سيتم تعيين دور "المدير العام" لحسابك تلقائياً بعد الإعداد.
                                    </div>
                                    
                                    <form method="POST">
                                        <button type="submit" name="setup_permissions" class="btn btn-primary btn-lg w-100">
                                            <i class="fas fa-cogs"></i>
                                            تنفيذ إعداد النظام
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">الصلاحيات الافتراضية</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <h6 class="text-primary">صلاحيات النظام</h6>
                                            <ul class="list-unstyled small">
                                                <li><i class="fas fa-shield-alt text-muted"></i> الوصول إلى لوحة التحكم</li>
                                                <li><i class="fas fa-users-cog text-muted"></i> إدارة الصلاحيات والأدوار</li>
                                                <li><i class="fas fa-cog text-muted"></i> إدارة إعدادات النظام</li>
                                                <li><i class="fas fa-file-alt text-muted"></i> عرض سجلات النظام</li>
                                                <li><i class="fas fa-download text-muted"></i> النسخ الاحتياطي</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-3">
                                            <h6 class="text-success">صلاحيات المستخدمين</h6>
                                            <ul class="list-unstyled small">
                                                <li><i class="fas fa-eye text-muted"></i> عرض المستخدمين</li>
                                                <li><i class="fas fa-plus text-muted"></i> إنشاء مستخدمين</li>
                                                <li><i class="fas fa-edit text-muted"></i> تعديل المستخدمين</li>
                                                <li><i class="fas fa-trash text-muted"></i> حذف المستخدمين</li>
                                                <li><i class="fas fa-users text-muted"></i> إدارة شاملة</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-3">
                                            <h6 class="text-info">صلاحيات المنتجات</h6>
                                            <ul class="list-unstyled small">
                                                <li><i class="fas fa-box text-muted"></i> عرض المنتجات</li>
                                                <li><i class="fas fa-plus-circle text-muted"></i> إضافة منتجات</li>
                                                <li><i class="fas fa-edit text-muted"></i> تعديل المنتجات</li>
                                                <li><i class="fas fa-trash-alt text-muted"></i> حذف المنتجات</li>
                                                <li><i class="fas fa-tags text-muted"></i> إدارة الفئات</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-3">
                                            <h6 class="text-warning">صلاحيات أخرى</h6>
                                            <ul class="list-unstyled small">
                                                <li><i class="fas fa-star text-muted"></i> إدارة المراجعات</li>
                                                <li><i class="fas fa-chart-bar text-muted"></i> عرض التقارير</li>
                                                <li><i class="fas fa-shopping-cart text-muted"></i> إدارة الطلبات</li>
                                                <li><i class="fas fa-file-alt text-muted"></i> إدارة المحتوى</li>
                                                <li><i class="fas fa-cogs text-muted"></i> الإعدادات</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a href="index.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i>
                                    العودة إلى لوحة التحكم
                                </a>
                                <a href="permissions.php" class="btn btn-success">
                                    <i class="fas fa-shield-alt"></i>
                                    إدارة الصلاحيات
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php endLayout(); ?>