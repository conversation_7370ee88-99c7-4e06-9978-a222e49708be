# 🤝 دليل المساهمة - Green Line E-commerce

نرحب بمساهماتكم في تطوير منصة Green Line! هذا الدليل سيساعدكم على فهم كيفية المساهمة بفعالية.

## 📋 جدول المحتويات

- [قواعد السلوك](#قواعد-السلوك)
- [كيفية المساهمة](#كيفية-المساهمة)
- [تقارير الأخطاء](#تقارير-الأخطاء)
- [طلب ميزات جديدة](#طلب-ميزات-جديدة)
- [إرسال Pull Requests](#إرسال-pull-requests)
- [معايير الكود](#معايير-الكود)
- [إرشادات الاختبار](#إرشادات-الاختبار)
- [إرشادات التوثيق](#إرشادات-التوثيق)
- [عملية المراجعة](#عملية-المراجعة)

## 🤝 قواعد السلوك

### التزاماتنا

نحن ملتزمون بتوفير بيئة ترحيبية وشاملة للجميع، بغض النظر عن:
- العمر أو الجنس أو الهوية الجنسية
- العرق أو الجنسية أو الدين
- مستوى الخبرة أو التعليم
- الوضع الاقتصادي أو الاجتماعي

### السلوكيات المقبولة

- ✅ استخدام لغة ترحيبية وشاملة
- ✅ احترام وجهات النظر والخبرات المختلفة
- ✅ قبول النقد البناء بأدب
- ✅ التركيز على ما هو أفضل للمجتمع
- ✅ إظهار التعاطف مع أعضاء المجتمع الآخرين

### السلوكيات غير المقبولة

- ❌ استخدام لغة أو صور جنسية
- ❌ التنمر أو التعليقات المهينة
- ❌ المضايقة العامة أو الخاصة
- ❌ نشر معلومات خاصة للآخرين دون إذن
- ❌ أي سلوك غير مهني أو غير مناسب

## 🚀 كيفية المساهمة

### 1. إعداد البيئة المحلية

```bash
# استنساخ المستودع
git clone https://github.com/greenline/ecommerce.git
cd ecommerce

# تثبيت التبعيات
composer install

# إعداد البيئة
cp .env.example .env
# قم بتحرير .env وإضافة إعداداتك

# إعداد قاعدة البيانات
php -f sql/install.php

# تشغيل الاختبارات للتأكد من سلامة الإعداد
composer test
```

### 2. إنشاء فرع جديد

```bash
# إنشاء فرع للميزة الجديدة
git checkout -b feature/اسم-الميزة

# أو للإصلاح
git checkout -b fix/وصف-الإصلاح

# أو للتوثيق
git checkout -b docs/وصف-التحديث
```

### 3. أنواع المساهمات

#### 🐛 إصلاح الأخطاء
- ابحث في Issues الموجودة أولاً
- أنشئ Issue جديد إذا لم يكن موجوداً
- اربط Pull Request بالـ Issue

#### ✨ ميزات جديدة
- ناقش الميزة في Issue أولاً
- تأكد من توافقها مع رؤية المشروع
- اكتب الاختبارات المناسبة

#### 📚 تحسين التوثيق
- إصلاح الأخطاء الإملائية
- إضافة أمثلة وتوضيحات
- ترجمة المحتوى

#### 🧪 إضافة اختبارات
- اختبارات للكود الموجود
- اختبارات للميزات الجديدة
- تحسين تغطية الاختبارات

## 🐛 تقارير الأخطاء

### قبل إرسال التقرير

1. **ابحث في Issues الموجودة** - قد يكون الخطأ مُبلغ عنه مسبقاً
2. **تأكد من استخدام أحدث إصدار** - قد يكون الخطأ مُصلح
3. **اختبر في بيئة نظيفة** - تأكد أن المشكلة ليست محلية

### معلومات مطلوبة

```markdown
## وصف الخطأ
وصف واضح ومختصر للخطأ.

## خطوات إعادة الإنتاج
1. اذهب إلى '...'
2. اضغط على '...'
3. مرر إلى '...'
4. شاهد الخطأ

## السلوك المتوقع
وصف واضح لما كان متوقعاً أن يحدث.

## لقطات الشاشة
إذا كان مناسباً، أضف لقطات شاشة لتوضيح المشكلة.

## معلومات البيئة
- نظام التشغيل: [مثل Windows 10, Ubuntu 20.04]
- إصدار PHP: [مثل 8.1.0]
- إصدار MySQL: [مثل 8.0.28]
- المتصفح: [مثل Chrome 96.0.4664.110]

## معلومات إضافية
أي معلومات أخرى حول المشكلة.
```

## ✨ طلب ميزات جديدة

### قبل طلب الميزة

1. **ابحث في Issues والمناقشات** - قد تكون مطلوبة مسبقاً
2. **فكر في البدائل** - هل يمكن تحقيق الهدف بطريقة أخرى؟
3. **اعتبر التأثير** - كيف ستؤثر على المستخدمين الحاليين؟

### قالب طلب الميزة

```markdown
## ملخص الميزة
وصف مختصر للميزة المطلوبة.

## الدافع
لماذا تريد هذه الميزة؟ ما المشكلة التي تحلها؟

## الحل المقترح
وصف تفصيلي لكيفية عمل الميزة.

## البدائل المدروسة
وصف أي حلول أو ميزات بديلة فكرت فيها.

## معلومات إضافية
أي سياق أو لقطات شاشة حول طلب الميزة.
```

## 📤 إرسال Pull Requests

### قائمة التحقق

- [ ] **الفرع محدث** - تأكد من مزامنة فرعك مع `main`
- [ ] **الاختبارات تمر** - جميع الاختبارات الموجودة والجديدة
- [ ] **معايير الكود** - يتبع دليل الأسلوب
- [ ] **التوثيق محدث** - إذا كانت الميزة تحتاج توثيق
- [ ] **Commit messages واضحة** - تتبع التنسيق المطلوب

### تنسيق Commit Messages

```
نوع: وصف مختصر (50 حرف أو أقل)

وصف تفصيلي إذا كان ضرورياً. اشرح ماذا ولماذا،
وليس كيف. لف النص عند 72 حرف.

- يمكن إضافة نقاط
- للتوضيح أكثر

Closes #123
```

#### أنواع Commits

- `feat`: ميزة جديدة
- `fix`: إصلاح خطأ
- `docs`: تغييرات في التوثيق
- `style`: تنسيق الكود (مسافات، فواصل، إلخ)
- `refactor`: إعادة هيكلة الكود
- `test`: إضافة أو تعديل اختبارات
- `chore`: مهام صيانة (تحديث تبعيات، إلخ)

### مثال على Pull Request جيد

```markdown
## الوصف
إضافة نظام تحديد معدل الطلبات لحماية API من الإفراط في الاستخدام.

## نوع التغيير
- [x] ميزة جديدة
- [ ] إصلاح خطأ
- [ ] تغيير مُكسر
- [ ] تحديث توثيق

## الاختبارات
- [x] اختبارات الوحدة
- [x] اختبارات التكامل
- [x] اختبارات يدوية

## قائمة التحقق
- [x] الكود يتبع دليل الأسلوب
- [x] تمت مراجعة الكود ذاتياً
- [x] التعليقات واضحة ومفيدة
- [x] التوثيق محدث
- [x] لا توجد تحذيرات جديدة

## لقطات الشاشة (إذا كان مناسباً)
[أضف لقطات شاشة هنا]

## ملاحظات للمراجعين
يرجى التركيز على منطق تحديد المعدل في `RateLimiter.php`.
```

## 📝 معايير الكود

### PHP Standards

#### 1. PSR Standards
- **PSR-1**: Basic Coding Standard
- **PSR-2**: Coding Style Guide
- **PSR-4**: Autoloader Standard
- **PSR-12**: Extended Coding Style

#### 2. تسمية المتغيرات والدوال

```php
// ✅ جيد
$userName = 'أحمد';
$productPrice = 100.50;
function calculateTotalPrice($items) {}
class UserManager {}

// ❌ سيء
$un = 'أحمد';
$pp = 100.50;
function calc($items) {}
class usrmgr {}
```

#### 3. التعليقات

```php
/**
 * حساب إجمالي سعر المنتجات مع الضريبة
 * 
 * @param array $products قائمة المنتجات
 * @param float $taxRate معدل الضريبة (افتراضي 15%)
 * @return float إجمالي السعر شامل الضريبة
 * @throws InvalidArgumentException إذا كان معدل الضريبة سالب
 */
function calculateTotalWithTax(array $products, float $taxRate = 0.15): float
{
    if ($taxRate < 0) {
        throw new InvalidArgumentException('معدل الضريبة لا يمكن أن يكون سالباً');
    }
    
    $total = 0;
    foreach ($products as $product) {
        $total += $product['price'] * $product['quantity'];
    }
    
    return $total * (1 + $taxRate);
}
```

#### 4. معالجة الأخطاء

```php
// ✅ جيد - استخدام try-catch
try {
    $result = $database->query($sql);
    return $result;
} catch (PDOException $e) {
    error_log('خطأ في قاعدة البيانات: ' . $e->getMessage());
    throw new DatabaseException('فشل في تنفيذ الاستعلام');
}

// ✅ جيد - التحقق من المدخلات
function createUser(array $userData): User
{
    if (empty($userData['email'])) {
        throw new InvalidArgumentException('البريد الإلكتروني مطلوب');
    }
    
    if (!filter_var($userData['email'], FILTER_VALIDATE_EMAIL)) {
        throw new InvalidArgumentException('البريد الإلكتروني غير صحيح');
    }
    
    // باقي الكود...
}
```

### JavaScript Standards

#### 1. ES6+ Features

```javascript
// ✅ جيد - استخدام const/let
const API_URL = 'https://api.greenline.com';
let currentUser = null;

// ✅ جيد - Arrow functions
const calculateTotal = (items) => {
    return items.reduce((sum, item) => sum + item.price, 0);
};

// ✅ جيد - Template literals
const message = `مرحباً ${userName}، لديك ${itemCount} منتج في السلة`;
```

#### 2. Error Handling

```javascript
// ✅ جيد - async/await مع try-catch
async function fetchUserData(userId) {
    try {
        const response = await fetch(`/api/users/${userId}`);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error('خطأ في جلب بيانات المستخدم:', error);
        throw error;
    }
}
```

### CSS/SCSS Standards

#### 1. BEM Methodology

```scss
// ✅ جيد - BEM naming
.product-card {
    &__image {
        width: 100%;
        height: 200px;
        object-fit: cover;
    }
    
    &__title {
        font-size: 1.2rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    
    &__price {
        color: #e74c3c;
        font-size: 1.1rem;
        
        &--discounted {
            text-decoration: line-through;
            color: #95a5a6;
        }
    }
}
```

#### 2. Responsive Design

```scss
// ✅ جيد - Mobile-first approach
.container {
    padding: 1rem;
    
    @media (min-width: 768px) {
        padding: 2rem;
    }
    
    @media (min-width: 1024px) {
        max-width: 1200px;
        margin: 0 auto;
    }
}
```

## 🧪 إرشادات الاختبار

### 1. أنواع الاختبارات

#### Unit Tests
```php
class UserTest extends TestCase
{
    public function testCreateUserWithValidData()
    {
        $userData = [
            'name' => 'أحمد محمد',
            'email' => '<EMAIL>',
            'password' => 'password123'
        ];
        
        $user = new User($userData);
        
        $this->assertEquals('أحمد محمد', $user->getName());
        $this->assertEquals('<EMAIL>', $user->getEmail());
        $this->assertTrue($user->verifyPassword('password123'));
    }
    
    public function testCreateUserWithInvalidEmail()
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('البريد الإلكتروني غير صحيح');
        
        new User([
            'name' => 'أحمد محمد',
            'email' => 'invalid-email',
            'password' => 'password123'
        ]);
    }
}
```

#### Integration Tests
```php
class AuthIntegrationTest extends TestCase
{
    public function testUserLoginFlow()
    {
        // إنشاء مستخدم
        $user = $this->createTestUser();
        
        // محاولة تسجيل الدخول
        $response = $this->post('/login', [
            'email' => $user->email,
            'password' => 'password123'
        ]);
        
        // التحقق من النتيجة
        $this->assertResponseOk($response);
        $this->assertSessionHas('user_id', $user->id);
        $this->assertRedirect('/dashboard');
    }
}
```

### 2. تغطية الاختبارات

- **الهدف**: 80% أو أكثر
- **الأولوية**: الدوال الحرجة والمعقدة
- **التقرير**: استخدم `composer test:coverage`

### 3. تشغيل الاختبارات

```bash
# جميع الاختبارات
composer test

# اختبارات محددة
composer test -- --filter=UserTest

# مع تقرير التغطية
composer test:coverage

# اختبارات الأداء
composer test:performance
```

## 📚 إرشادات التوثيق

### 1. التعليقات في الكود

```php
/**
 * إدارة عمليات المصادقة والتحقق من الهوية
 * 
 * هذه الفئة تتعامل مع تسجيل الدخول والخروج وإدارة الجلسات
 * وتوفر طرق آمنة للتحقق من صحة بيانات المستخدمين
 * 
 * @package App\Auth
 * <AUTHOR> Green Line
 * @version 2.0.0
 * @since 1.0.0
 */
class AuthManager
{
    /**
     * تسجيل دخول المستخدم
     * 
     * @param string $email البريد الإلكتروني
     * @param string $password كلمة المرور
     * @param bool $remember تذكر تسجيل الدخول (افتراضي false)
     * @return User|null المستخدم إذا نجح التسجيل، null إذا فشل
     * @throws AuthException إذا كان الحساب محظور
     */
    public function login(string $email, string $password, bool $remember = false): ?User
    {
        // تنفيذ الدالة...
    }
}
```

### 2. README للميزات الجديدة

عند إضافة ميزة جديدة، أضف قسماً في README يشرح:
- الغرض من الميزة
- كيفية الاستخدام
- أمثلة عملية
- إعدادات مطلوبة

### 3. تحديث CHANGELOG

أضف تغييراتك في `CHANGELOG.md` تحت قسم "Unreleased":

```markdown
## [Unreleased]

### Added
- إضافة نظام إشعارات الدفع الفوري
- دعم المدفوعات بـ Apple Pay

### Fixed
- إصلاح مشكلة في حساب الضريبة للمنتجات المخفضة
```

## 🔍 عملية المراجعة

### 1. مراجعة تلقائية

- **GitHub Actions**: تشغيل الاختبارات تلقائياً
- **Code Quality**: فحص جودة الكود
- **Security Scan**: فحص الثغرات الأمنية

### 2. مراجعة بشرية

#### ما نبحث عنه:
- **الوظائف**: هل الكود يعمل كما هو متوقع؟
- **الأداء**: هل هناك تحسينات ممكنة؟
- **الأمان**: هل هناك ثغرات أمنية؟
- **القابلية للقراءة**: هل الكود واضح ومفهوم؟
- **الاختبارات**: هل التغييرات مغطاة بالاختبارات؟

#### عملية المراجعة:
1. **مراجعة أولية** - فحص سريع للتأكد من اتباع المعايير
2. **مراجعة تفصيلية** - فحص المنطق والتنفيذ
3. **اختبار يدوي** - تجربة الميزة عملياً
4. **موافقة نهائية** - دمج في الفرع الرئيسي

### 3. معايير الموافقة

- ✅ **مراجعة من عضوين** على الأقل من الفريق الأساسي
- ✅ **جميع الاختبارات تمر** بنجاح
- ✅ **لا توجد تعارضات** مع الفرع الرئيسي
- ✅ **التوثيق محدث** إذا كان ضرورياً
- ✅ **معايير الكود متبعة** بدقة

## 🏷️ إصدار النسخ

نتبع [Semantic Versioning](https://semver.org/):

- **MAJOR** (مثل 2.0.0): تغييرات مُكسرة
- **MINOR** (مثل 1.1.0): ميزات جديدة متوافقة
- **PATCH** (مثل 1.0.1): إصلاحات متوافقة

## 🎯 أولويات التطوير

### عالية الأولوية
1. 🔒 **الأمان** - إصلاح الثغرات الأمنية
2. 🐛 **الأخطاء الحرجة** - مشاكل تؤثر على الوظائف الأساسية
3. 📱 **تجربة المستخدم** - تحسينات واجهة المستخدم

### متوسطة الأولوية
1. ⚡ **الأداء** - تحسينات السرعة والاستجابة
2. 🧪 **الاختبارات** - زيادة تغطية الاختبارات
3. 📚 **التوثيق** - تحسين وتحديث الوثائق

### منخفضة الأولوية
1. 🎨 **التصميم** - تحسينات بصرية غير حرجة
2. 🔧 **إعادة الهيكلة** - تحسين بنية الكود
3. 🌟 **ميزات إضافية** - وظائف جديدة غير أساسية

## 📞 التواصل والدعم

### قنوات التواصل

- 💬 **Discord**: [Green Line Community](https://discord.gg/greenline)
- 📧 **البريد الإلكتروني**: <EMAIL>
- 🐛 **GitHub Issues**: لتقارير الأخطاء
- 💡 **GitHub Discussions**: للمناقشات العامة

### أوقات الاستجابة المتوقعة

- **الأخطاء الحرجة**: خلال 24 ساعة
- **الأخطاء العادية**: خلال 3-5 أيام
- **طلبات الميزات**: خلال أسبوع
- **أسئلة عامة**: خلال 2-3 أيام

### فريق المراجعة

- **@ahmed-lead**: المطور الرئيسي - مراجعة الأمان والبنية
- **@sara-frontend**: مطورة الواجهة الأمامية - مراجعة UI/UX
- **@omar-backend**: مطور الخلفية - مراجعة API وقاعدة البيانات
- **@fatima-qa**: مهندسة ضمان الجودة - مراجعة الاختبارات

## 🙏 شكر وتقدير

نشكر جميع المساهمين في تطوير منصة Green Line:

- **المطورين**: لكتابة الكود وإصلاح الأخطاء
- **المصممين**: لتحسين تجربة المستخدم
- **المختبرين**: لضمان جودة المنتج
- **الكتاب**: لتحسين التوثيق
- **المجتمع**: للتغذية الراجعة والاقتراحات

---

**مساهمتكم تجعل Green Line أفضل! 🌱**

شكراً لكم على وقتكم وجهدكم في تطوير هذا المشروع.