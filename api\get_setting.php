<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../includes/database.php';
require_once '../includes/functions.php';

try {
    $database = new Database();
    
    if (!isset($_GET['key'])) {
        echo json_encode([
            'success' => false,
            'message' => 'مفتاح الإعداد مطلوب'
        ]);
        exit;
    }
    
    $key = $_GET['key'];
    
    // Get setting from database
    $setting = $database->fetch(
        "SELECT setting_value, setting_type FROM settings WHERE setting_key = :key",
        ['key' => $key]
    );
    
    if ($setting) {
        $value = $setting['setting_value'];
        
        // Convert value based on type
        switch ($setting['setting_type']) {
            case 'number':
                $value = (float) $value;
                break;
            case 'boolean':
                $value = (bool) $value;
                break;
            case 'json':
                $value = $value; // Keep as string for JSON
                break;
            default:
                $value = (string) $value;
                break;
        }
        
        echo json_encode([
            'success' => true,
            'value' => $value,
            'type' => $setting['setting_type']
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'الإعداد غير موجود'
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في الخادم: ' . $e->getMessage()
    ]);
}
?>