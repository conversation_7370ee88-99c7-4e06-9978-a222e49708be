<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نموذج التواصل</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background: #007cba;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #005a87;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .error {
            border-color: #e74c3c !important;
        }
        .error-message {
            color: #e74c3c;
            font-size: 12px;
            margin-top: 5px;
        }
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }
        .notification.show {
            transform: translateX(0);
        }
        .notification-success {
            background: #27ae60;
        }
        .notification-error {
            background: #e74c3c;
        }
        .notification-info {
            background: #3498db;
        }
        .test-section {
            background: #f8f9fa;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
            border-left: 4px solid #007cba;
        }
        .debug-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار نموذج التواصل</h1>
        
        <div class="test-section">
            <h3>معلومات الاختبار:</h3>
            <div class="debug-info">
                <strong>URL الحالي:</strong> <span id="current-url"></span><br>
                <strong>Base URL:</strong> <span id="base-url"></span><br>
                <strong>API URL:</strong> <span id="api-url"></span>
            </div>
        </div>

        <form id="contact-form">
            <div class="form-group">
                <label for="name">الاسم *</label>
                <input type="text" id="name" name="name" required>
            </div>

            <div class="form-group">
                <label for="email">البريد الإلكتروني *</label>
                <input type="email" id="email" name="email" required>
            </div>

            <div class="form-group">
                <label for="phone">رقم الهاتف *</label>
                <input type="tel" id="phone" name="phone" required>
            </div>

            <div class="form-group">
                <label for="subject">الموضوع *</label>
                <input type="text" id="subject" name="subject" required>
            </div>

            <div class="form-group">
                <label for="type">نوع الرسالة</label>
                <select id="type" name="type">
                    <option value="general">عام</option>
                    <option value="support">دعم فني</option>
                    <option value="sales">مبيعات</option>
                    <option value="complaint">شكوى</option>
                </select>
            </div>

            <div class="form-group">
                <label for="message">الرسالة *</label>
                <textarea id="message" name="message" required></textarea>
            </div>

            <button type="submit">إرسال الرسالة</button>
        </form>

        <div class="test-section">
            <h3>سجل الاختبار:</h3>
            <div id="test-log" class="debug-info"></div>
        </div>
    </div>

    <script>
        // تحديث معلومات الصفحة
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('base-url').textContent = window.location.origin;
        
        // تحديد مسار API
        const apiPaths = [
            '/api/contact.php',
            './api/contact.php',
            'api/contact.php',
            window.location.origin + '/api/contact.php'
        ];
        
        let selectedApiPath = apiPaths[0];
        document.getElementById('api-url').textContent = selectedApiPath;

        // دالة لإضافة سجل
        function addLog(message, type = 'info') {
            const log = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logEntry.style.color = type === 'error' ? '#e74c3c' : type === 'success' ? '#27ae60' : '#333';
            log.appendChild(logEntry);
            log.scrollTop = log.scrollHeight;
        }

        // دالة التحقق من صحة النموذج
        function validateForm(form) {
            const name = form.querySelector('[name="name"]').value.trim();
            const email = form.querySelector('[name="email"]').value.trim();
            const phone = form.querySelector('[name="phone"]').value.trim();
            const subject = form.querySelector('[name="subject"]').value.trim();
            const message = form.querySelector('[name="message"]').value.trim();
            
            // Clear previous errors
            form.querySelectorAll('.error-message').forEach(error => error.remove());
            form.querySelectorAll('.error').forEach(field => field.classList.remove('error'));
            
            let isValid = true;
            
            // Validate name
            if (!name) {
                showFieldError(form.querySelector('[name="name"]'), 'الاسم مطلوب');
                isValid = false;
            }
            
            // Validate email
            if (!email) {
                showFieldError(form.querySelector('[name="email"]'), 'البريد الإلكتروني مطلوب');
                isValid = false;
            } else if (!isValidEmail(email)) {
                showFieldError(form.querySelector('[name="email"]'), 'البريد الإلكتروني غير صحيح');
                isValid = false;
            }
            
            // Validate phone
            if (!phone) {
                showFieldError(form.querySelector('[name="phone"]'), 'رقم الهاتف مطلوب');
                isValid = false;
            }
            
            // Validate subject
            if (!subject) {
                showFieldError(form.querySelector('[name="subject"]'), 'الموضوع مطلوب');
                isValid = false;
            }
            
            // Validate message
            if (!message) {
                showFieldError(form.querySelector('[name="message"]'), 'الرسالة مطلوبة');
                isValid = false;
            }
            
            addLog(`التحقق من صحة النموذج: ${isValid ? 'نجح' : 'فشل'}`, isValid ? 'success' : 'error');
            return isValid;
        }

        function showFieldError(field, message) {
            field.classList.add('error');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.textContent = message;
            field.parentNode.appendChild(errorDiv);
        }

        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        // دالة إرسال النموذج
        async function submitContactForm(form) {
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            
            // Show loading state
            submitBtn.textContent = 'جاري الإرسال...';
            submitBtn.disabled = true;
            
            const formData = new FormData(form);
            
            // إضافة البيانات إلى السجل
            const data = {};
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            addLog('البيانات المرسلة: ' + JSON.stringify(data, null, 2));
            
            // تجربة مسارات API مختلفة
            for (let i = 0; i < apiPaths.length; i++) {
                const apiPath = apiPaths[i];
                addLog(`تجربة مسار API: ${apiPath}`);
                
                try {
                    const response = await fetch(apiPath, {
                        method: 'POST',
                        body: formData
                    });
                    
                    addLog(`استجابة الخادم: ${response.status} ${response.statusText}`);
                    
                    if (response.ok) {
                        const responseText = await response.text();
                        addLog(`محتوى الاستجابة: ${responseText}`);
                        
                        try {
                            const data = JSON.parse(responseText);
                            if (data.success) {
                                showNotification('تم إرسال رسالتك بنجاح!', 'success');
                                form.reset();
                                addLog('تم إرسال الرسالة بنجاح!', 'success');
                                break;
                            } else {
                                addLog(`خطأ من API: ${data.message}`, 'error');
                                showNotification(data.message || 'حدث خطأ أثناء الإرسال', 'error');
                            }
                        } catch (jsonError) {
                            addLog(`خطأ في تحليل JSON: ${jsonError.message}`, 'error');
                            addLog(`الاستجابة الخام: ${responseText}`);
                        }
                    } else {
                        addLog(`خطأ HTTP: ${response.status}`, 'error');
                    }
                } catch (error) {
                    addLog(`خطأ في الشبكة: ${error.message}`, 'error');
                }
            }
            
            // Restore button
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }

        // دالة عرض الإشعارات
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            // Show notification
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);
            
            // Hide notification
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 5000);
        }

        // ربط النموذج
        document.getElementById('contact-form').addEventListener('submit', function(e) {
            e.preventDefault();
            addLog('تم إرسال النموذج');
            
            if (validateForm(this)) {
                submitContactForm(this);
            }
        });

        // ملء النموذج بقيم تجريبية
        document.getElementById('name').value = 'اختبار النظام';
        document.getElementById('email').value = '<EMAIL>';
        document.getElementById('phone').value = '0501234567';
        document.getElementById('subject').value = 'اختبار نموذج التواصل';
        document.getElementById('message').value = 'هذه رسالة اختبار للتأكد من عمل نموذج التواصل بشكل صحيح.';

        addLog('تم تحميل صفحة الاختبار بنجاح');
    </script>
</body>
</html>