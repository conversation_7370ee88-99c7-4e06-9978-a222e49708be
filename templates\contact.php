<?php
$pageTitle = 'اتصل بنا';
$page = 'contact';

// جلب معلومات الاتصال من الإعدادات
$contact_info = getContactInfo();
$product_id = $_GET['product'] ?? null;
$type = $_GET['type'] ?? null;
$product = null;

if ($product_id) {
    $product = getProduct($product_id);
}

include 'header.php';
?>

<!-- Hero Section -->
<section class="bg-primary py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">
            اتصل بنا
        </h1>
        <p class="text-xl text-white opacity-90 max-w-2xl mx-auto">
            نحن هنا لمساعدتك. تواصل معنا وسنكون سعداء للإجابة على استفساراتك
        </p>
    </div>
</section>

<!-- Contact Section -->
<section class="py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            
            <!-- Contact Form -->
            <div class="bg-white rounded-lg shadow-lg p-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">أرسل لنا رسالة</h2>
                
                <?php if ($product): ?>
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-blue-500 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-blue-700 font-medium">استفسار حول: <?php echo htmlspecialchars($product['name']); ?></span>
                        </div>
                    </div>
                <?php endif; ?>
                
                <form id="contact-form" class="space-y-6">
                    <?php if ($product): ?>
                        <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                    <?php endif; ?>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل *</label>
                            <input type="text" id="name" name="name" required 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300"
                                   placeholder="أدخل اسمك الكامل">
                        </div>
                        
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني *</label>
                            <input type="email" id="email" name="email" required 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300"
                                   placeholder="أدخل بريدك الإلكتروني">
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف *</label>
                            <input type="tel" id="phone" name="phone" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300"
                                   placeholder="أدخل رقم هاتفك">
                        </div>
                        
                        <div>
                            <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">الموضوع *</label>
                            <select id="subject" name="subject" required 
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300">
                                <option value="">اختر الموضوع</option>
                                <option value="استفسار عام" <?php echo ($product || $type) ? '' : 'selected'; ?>>استفسار عام</option>
                                <option value="طلب عرض سعر" <?php echo $product ? 'selected' : ''; ?>>طلب عرض سعر</option>
                                <option value="الدعم الفني">الدعم الفني</option>
                                <option value="شراكة" <?php echo $type === 'partnership' ? 'selected' : ''; ?>>شراكة</option>
                                <option value="شكوى">شكوى</option>
                                <option value="اقتراح">اقتراح</option>
                            </select>
                        </div>
                    </div>
                    
                    <div>
                        <label for="message" class="block text-sm font-medium text-gray-700 mb-2">الرسالة *</label>
                        <textarea id="message" name="message" rows="6" required 
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300"
                                  placeholder="اكتب رسالتك هنا..."></textarea>
                    </div>
                    
                    <button type="submit" 
                            class="w-full bg-primary hover:bg-secondary text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105">
                        إرسال الرسالة
                    </button>
                </form>
            </div>
            
            <!-- Contact Information -->
            <div class="space-y-8">
                
                <!-- Contact Details -->
                <div class="bg-gray-50 rounded-lg p-8">
                    <h3 class="text-xl font-bold text-gray-900 mb-6">معلومات الاتصال</h3>
                    
                    <div class="space-y-6">
                        <?php if (!empty($contact_info['address'])): ?>
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center hover:bg-secondary transition-colors duration-300">
                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="mr-4">
                                    <h4 class="text-lg font-medium text-gray-900">العنوان</h4>
                                    <p class="text-gray-600 mt-1"><?php echo nl2br(htmlspecialchars($contact_info['address'])); ?></p>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($contact_info['phone'])): ?>
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center hover:bg-secondary transition-colors duration-300">
                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="mr-4">
                                    <h4 class="text-lg font-medium text-gray-900">الهاتف</h4>
                                    <p class="text-gray-600 mt-1">
                                        <a href="tel:<?php echo $contact_info['phone']; ?>" class="hover:text-primary transition-colors">
                                            <?php echo htmlspecialchars($contact_info['phone']); ?>
                                        </a>
                                    </p>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($contact_info['email'])): ?>
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center hover:bg-secondary transition-colors duration-300">
                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="mr-4">
                                    <h4 class="text-lg font-medium text-gray-900">البريد الإلكتروني</h4>
                                    <p class="text-gray-600 mt-1">
                                        <a href="mailto:<?php echo $contact_info['email']; ?>" class="hover:text-primary transition-colors">
                                            <?php echo htmlspecialchars($contact_info['email']); ?>
                                        </a>
                                    </p>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($contact_info['working_hours'])): ?>
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center hover:bg-secondary transition-colors duration-300">
                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="mr-4">
                                    <h4 class="text-lg font-medium text-gray-900">ساعات العمل</h4>
                                    <p class="text-gray-600 mt-1"><?php echo nl2br(htmlspecialchars($contact_info['working_hours'])); ?></p>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Social Media -->
                <div class="bg-white rounded-lg shadow-lg p-8">
                    <h3 class="text-xl font-bold text-gray-900 mb-6 text-center">تابعنا على</h3>
                    
                    <div class="flex justify-center items-center gap-6">
                        <?php 
                        // جلب بيانات وسائل التواصل الاجتماعي من قاعدة البيانات
                        $social_media_data = isset($contact_info['social_media_data']) ? $contact_info['social_media_data'] : [];
                        
                        // قائمة المنصات مع أيقوناتها
                        $social_platforms = [
                            'facebook' => ['name' => 'فيسبوك', 'icon' => 'fab fa-facebook-f'],
                            'twitter' => ['name' => 'تويتر', 'icon' => 'fab fa-twitter'],
                            'instagram' => ['name' => 'إنستغرام', 'icon' => 'fab fa-instagram'],
                            'linkedin' => ['name' => 'لينكد إن', 'icon' => 'fab fa-linkedin-in'],
                            'youtube' => ['name' => 'يوتيوب', 'icon' => 'fab fa-youtube'],
                            'tiktok' => ['name' => 'تيك توك', 'icon' => 'fab fa-tiktok'],
                            'snapchat' => ['name' => 'سناب شات', 'icon' => 'fab fa-snapchat-ghost'],
                            'telegram' => ['name' => 'تيليجرام', 'icon' => 'fab fa-telegram-plane'],
                            'discord' => ['name' => 'ديسكورد', 'icon' => 'fab fa-discord'],
                            'pinterest' => ['name' => 'بينتيريست', 'icon' => 'fab fa-pinterest'],
                            'reddit' => ['name' => 'ريديت', 'icon' => 'fab fa-reddit'],
                            'tumblr' => ['name' => 'تمبلر', 'icon' => 'fab fa-tumblr'],
                            'twitch' => ['name' => 'تويتش', 'icon' => 'fab fa-twitch'],
                            'github' => ['name' => 'جيت هاب', 'icon' => 'fab fa-github'],
                            'behance' => ['name' => 'بيهانس', 'icon' => 'fab fa-behance'],
                            'dribbble' => ['name' => 'دريبل', 'icon' => 'fab fa-dribbble']
                        ];
                        
                        foreach ($social_platforms as $platform => $data):
                            // استخدام البيانات من جدول contact_info
                            $url = isset($social_media_data[$platform]) ? $social_media_data[$platform] : '';
                            if (!empty($url)):
                        ?>
                            <a href="<?php echo htmlspecialchars($url); ?>" target="_blank" 
                               class="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center text-white hover:bg-green-700 hover:text-white transition-all duration-300 transform hover:scale-110 shadow-md hover:shadow-lg"
                               title="<?php echo $data['name']; ?>">
                                <i class="<?php echo $data['icon']; ?> text-lg"></i>
                            </a>
                        <?php 
                            endif;
                        endforeach;
                        ?>
                    </div>
                </div>
                
                <!-- Quick Contact -->
                <div class="bg-primary rounded-lg p-8 text-white">
                    <h3 class="text-xl font-bold mb-4">تحتاج مساعدة فورية؟</h3>
                    <p class="mb-6 opacity-90">تواصل معنا مباشرة عبر الواتساب للحصول على رد سريع</p>
                    
                    <?php if (!empty($contact_info['whatsapp'])): ?>
                        <a href="https://wa.me/<?php echo preg_replace('/[^0-9]/', '', $contact_info['whatsapp']); ?>" 
                           target="_blank" 
                           class="inline-flex items-center bg-white text-primary px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-300">
                            <svg class="w-5 h-5 ml-2" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.465 3.488"/>
                            </svg>
                            تواصل عبر الواتساب
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Map Section -->
<?php if (!empty($contact_info['map_embed'])): ?>
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">موقعنا</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">
                يمكنك زيارتنا في مقرنا الرئيسي أو التواصل معنا عبر الوسائل المتاحة
            </p>
        </div>
        
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="aspect-w-16 aspect-h-9">
                <?php echo $contact_info['map_embed']; ?>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- FAQ Section -->
<section class="py-16">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">الأسئلة الشائعة</h2>
            <p class="text-gray-600">
                إجابات على أكثر الأسئلة شيوعاً
            </p>
        </div>
        
        <div class="space-y-4">
            <?php 
            $faqs = getFAQs(5); // جلب 5 أسئلة شائعة
            foreach ($faqs as $index => $faq):
            ?>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <button class="faq-toggle w-full px-6 py-4 text-right focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50" 
                            data-target="faq-<?php echo $index; ?>">
                        <div class="flex items-center justify-between">
                            <span class="font-medium text-gray-900"><?php echo htmlspecialchars($faq['question']); ?></span>
                            <svg class="faq-icon w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                    </button>
                    
                    <div id="faq-<?php echo $index; ?>" class="faq-content hidden px-6 pb-4">
                        <p class="text-gray-600"><?php echo nl2br(htmlspecialchars($faq['answer'])); ?></p>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <div class="text-center mt-8">
            <a href="<?php echo SITE_URL; ?>/faq" class="inline-flex items-center text-primary hover:text-secondary font-medium">
                عرض جميع الأسئلة الشائعة
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </a>
        </div>
    </div>
</section>

<script>
// FAQ Toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    const faqToggles = document.querySelectorAll('.faq-toggle');
    
    faqToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            const targetId = this.dataset.target;
            const content = document.getElementById(targetId);
            const icon = this.querySelector('.faq-icon');
            
            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                icon.style.transform = 'rotate(180deg)';
            } else {
                content.classList.add('hidden');
                icon.style.transform = 'rotate(0deg)';
            }
        });
    });
    
    // Contact Form Handler
    const contactForm = document.getElementById('contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            // جمع بيانات النموذج
            const formData = new FormData(contactForm);
            const data = {
                name: formData.get('name'),
                email: formData.get('email'),
                phone: formData.get('phone'),
                subject: formData.get('subject'),
                message: formData.get('message'),
                product_id: formData.get('product_id') || null
            };
            
            // التحقق من صحة البيانات
            if (!data.name || !data.email || !data.phone || !data.subject || !data.message) {
                showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
                return;
            }
            
            // التحقق من صحة البريد الإلكتروني
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(data.email)) {
                showNotification('يرجى إدخال بريد إلكتروني صحيح', 'error');
                return;
            }
            
            // التحقق من رقم الهاتف
            const phoneRegex = /^[0-9+\-\s()]{10,}$/;
            if (!phoneRegex.test(data.phone)) {
                showNotification('يرجى إدخال رقم هاتف صحيح', 'error');
                return;
            }
            
            // تعطيل زر الإرسال
            const submitBtn = contactForm.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.disabled = true;
            submitBtn.textContent = 'جاري الإرسال...';
            
            try {
                // إرسال البيانات إلى API
                const response = await fetch('<?php echo SITE_URL; ?>/api/contact.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showNotification('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً', 'success');
                    contactForm.reset();
                } else {
                    showNotification(result.message || 'حدث خطأ أثناء إرسال الرسالة', 'error');
                }
            } catch (error) {
                console.error('Error:', error);
                showNotification('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى', 'error');
            } finally {
                // إعادة تفعيل زر الإرسال
                submitBtn.disabled = false;
                submitBtn.textContent = originalText;
            }
        });
    }
});

// دالة عرض الإشعارات
function showNotification(message, type = 'info') {
    // إزالة الإشعارات السابقة
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());
    
    // إنشاء الإشعار الجديد
    const notification = document.createElement('div');
    notification.className = `notification fixed top-4 right-4 z-50 max-w-sm p-4 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full`;
    
    // تحديد لون الإشعار حسب النوع
    if (type === 'success') {
        notification.classList.add('bg-green-500', 'text-white');
    } else if (type === 'error') {
        notification.classList.add('bg-red-500', 'text-white');
    } else {
        notification.classList.add('bg-blue-500', 'text-white');
    }
    
    notification.innerHTML = `
        <div class="flex items-center">
            <div class="flex-1">
                <p class="text-sm font-medium">${message}</p>
            </div>
            <button onclick="this.parentElement.parentElement.remove()" class="mr-2 text-white hover:text-gray-200">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // عرض الإشعار
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    // إخفاء الإشعار تلقائياً بعد 5 ثوان
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}
</script>

<?php include 'footer.php'; ?>