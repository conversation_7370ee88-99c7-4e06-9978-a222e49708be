<?php
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

$message = '';

if ($_POST) {
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    
    if ($email && $password) {
        $result = login($email, $password);
        if ($result['success']) {
            $message = "<div style='background: #d4edda; color: #155724; padding: 10px; border-radius: 4px; margin: 10px 0;'>تم تسجيل الدخول بنجاح! سيتم توجيهك...</div>";
            echo "<script>setTimeout(function(){ window.location.href = 'test_main_page_auth.php'; }, 2000);</script>";
        } else {
            $message = "<div style='background: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin: 10px 0;'>خطأ: " . $result['message'] . "</div>";
        }
    }
}

if (isLoggedIn()) {
    $user = getCurrentUser();
    echo "<div style='background: #d1ecf1; color: #0c5460; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h2>مرحباً " . $user['name'] . "!</h2>";
    echo "<p>أنت مسجل الدخول بالفعل.</p>";
    echo "<a href='test_main_page_auth.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>اختبار المصادقة</a>";
    echo "<a href='products/1' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>صفحة المنتج</a>";
    echo "<a href='admin/logout.php' style='background: #dc3545; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>تسجيل الخروج</a>";
    echo "</div>";
    exit;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - اختبار</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 400px;
            margin: 50px auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            background: #007bff;
            color: white;
            padding: 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        .links {
            text-align: center;
            margin-top: 20px;
        }
        .links a {
            color: #007bff;
            text-decoration: none;
            margin: 0 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; margin-bottom: 30px;">تسجيل الدخول</h1>
        
        <?php echo $message; ?>
        
        <form method="POST">
            <div class="form-group">
                <label for="email">البريد الإلكتروني:</label>
                <input type="email" id="email" name="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" name="password" value="admin123" required>
            </div>
            
            <button type="submit">تسجيل الدخول</button>
        </form>
        
        <div class="links">
            <a href="test_main_page_auth.php">اختبار المصادقة</a>
            <a href="products/1">صفحة المنتج</a>
            <a href="test_user_review_fix.php">صفحة الاختبار</a>
        </div>
        
        <div style="background: #e2e3e5; padding: 15px; border-radius: 4px; margin-top: 20px; font-size: 14px;">
            <strong>بيانات تجريبية:</strong><br>
            البريد: <EMAIL><br>
            كلمة المرور: admin123
        </div>
    </div>
</body>
</html>