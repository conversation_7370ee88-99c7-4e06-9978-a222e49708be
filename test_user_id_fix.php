<?php
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
$isLoggedIn = isLoggedIn();
$currentUser = $isLoggedIn ? getCurrentUser() : null;

// Handle form submission
$testResult = '';
if ($_POST && isset($_POST['test_review'])) {
    $product_id = 1; // Test product ID
    $user_id = $_SESSION['user_id'] ?? null;
    $customer_name = $_SESSION['user_name'] ?? 'Test User';
    $customer_email = $_SESSION['user_email'] ?? '<EMAIL>';
    $rating = 5;
    $review_title = 'Test Review Title';
    $review_text = 'This is a test review to check if user_id is saved correctly.';
    
    if ($user_id) {
        // Try to save review with user_id
        $result = saveUserReview($product_id, $user_id, $customer_name, $customer_email, $rating, $review_title, $review_text);
        
        if ($result) {
            // Get the last inserted review to check user_id
            $pdo = getDBConnection();
            $stmt = $pdo->prepare("SELECT * FROM reviews WHERE product_id = ? AND user_id = ? ORDER BY created_at DESC LIMIT 1");
            $stmt->execute([$product_id, $user_id]);
            $savedReview = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($savedReview && $savedReview['user_id'] == $user_id) {
                $testResult = '<div class="alert alert-success">✅ نجح! تم حفظ user_id بشكل صحيح: ' . $savedReview['user_id'] . '</div>';
            } else {
                $testResult = '<div class="alert alert-danger">❌ فشل! لم يتم حفظ user_id بشكل صحيح</div>';
            }
        } else {
            $testResult = '<div class="alert alert-danger">❌ فشل في حفظ التقييم</div>';
        }
    } else {
        $testResult = '<div class="alert alert-warning">⚠️ المستخدم غير مسجل الدخول</div>';
    }
}

// Get recent reviews to display
$pdo = getDBConnection();
$stmt = $pdo->prepare("SELECT * FROM reviews ORDER BY created_at DESC LIMIT 5");
$stmt->execute();
$recentReviews = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار حفظ user_id في التقييمات</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .alert {
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0.5rem;
        }
        .alert-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .alert-danger {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .alert-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-center mb-8">اختبار حفظ user_id في التقييمات</h1>
        
        <!-- Current User Status -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">حالة المستخدم الحالي</h2>
            
            <?php if ($isLoggedIn): ?>
                <div class="alert alert-success">
                    <strong>✅ مسجل الدخول</strong><br>
                    <strong>معرف المستخدم:</strong> <?php echo $_SESSION['user_id'] ?? 'غير محدد'; ?><br>
                    <strong>الاسم:</strong> <?php echo $_SESSION['user_name'] ?? 'غير محدد'; ?><br>
                    <strong>البريد الإلكتروني:</strong> <?php echo $_SESSION['user_email'] ?? 'غير محدد'; ?>
                </div>
            <?php else: ?>
                <div class="alert alert-warning">
                    <strong>⚠️ غير مسجل الدخول</strong><br>
                    <a href="test_login_simple.php" class="text-blue-600 underline">تسجيل الدخول للاختبار</a>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Test Form -->
        <?php if ($isLoggedIn): ?>
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">اختبار حفظ التقييم</h2>
            
            <?php echo $testResult; ?>
            
            <form method="POST" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        هذا الاختبار سيحفظ تقييم تجريبي للمنتج رقم 1 مع user_id الخاص بك
                    </label>
                </div>
                
                <button type="submit" name="test_review" 
                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    اختبار حفظ التقييم مع user_id
                </button>
            </form>
        </div>
        <?php endif; ?>
        
        <!-- Recent Reviews -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">آخر 5 تقييمات في قاعدة البيانات</h2>
            
            <?php if (!empty($recentReviews)): ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full table-auto">
                        <thead>
                            <tr class="bg-gray-50">
                                <th class="px-4 py-2 text-right">ID</th>
                                <th class="px-4 py-2 text-right">Product ID</th>
                                <th class="px-4 py-2 text-right">User ID</th>
                                <th class="px-4 py-2 text-right">الاسم</th>
                                <th class="px-4 py-2 text-right">البريد الإلكتروني</th>
                                <th class="px-4 py-2 text-right">التقييم</th>
                                <th class="px-4 py-2 text-right">التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recentReviews as $review): ?>
                            <tr class="border-b">
                                <td class="px-4 py-2"><?php echo $review['id']; ?></td>
                                <td class="px-4 py-2"><?php echo $review['product_id']; ?></td>
                                <td class="px-4 py-2">
                                    <span class="<?php echo $review['user_id'] ? 'text-green-600 font-bold' : 'text-red-600'; ?>">
                                        <?php echo $review['user_id'] ?: 'NULL'; ?>
                                    </span>
                                </td>
                                <td class="px-4 py-2"><?php echo htmlspecialchars($review['name']); ?></td>
                                <td class="px-4 py-2"><?php echo htmlspecialchars($review['email']); ?></td>
                                <td class="px-4 py-2"><?php echo $review['rating']; ?>/5</td>
                                <td class="px-4 py-2"><?php echo date('Y-m-d H:i', strtotime($review['created_at'])); ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <p class="text-gray-500">لا توجد تقييمات في قاعدة البيانات</p>
            <?php endif; ?>
        </div>
        
        <!-- Navigation -->
        <div class="mt-8 text-center">
            <a href="index.php" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded mr-2">
                العودة للصفحة الرئيسية
            </a>
            <?php if ($isLoggedIn): ?>
                <a href="products/1" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    اختبار التقييم في صفحة المنتج
                </a>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>