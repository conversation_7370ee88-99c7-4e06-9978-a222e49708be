<?php
/**
 * Sitemap Page Template
 * Displays a comprehensive site map with all pages and sections
 */

require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/LegalPageManager.php';

// التأكد من توفر متغير قاعدة البيانات
global $database;
if (!isset($database)) {
    require_once 'config/database.php';
}

// إنشاء مدير الصفحات القانونية
$legalManager = new LegalPageManager($database);

// جلب بيانات صفحة خريطة الموقع
$pageData = $legalManager->getPage('sitemap');

// تعيين meta tags ديناميكية
$page_title = $pageData['title'];
$meta_description = $pageData['meta_description'];
$meta_keywords = $pageData['meta_keywords'];
$page = 'sitemap';

include 'header.php'; 
?>

<!-- Page Title Section -->
<section class="bg-primary py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <div class="inline-flex items-center justify-center w-20 h-20 bg-white/20 rounded-full mb-6">
                <i class="fas fa-sitemap text-3xl text-white"></i>
            </div>
            <h1 class="text-4xl md:text-6xl font-bold text-white mb-6"><?php echo htmlspecialchars($pageData['title']); ?></h1>
            <p class="text-xl md:text-2xl text-white/90 max-w-3xl mx-auto leading-relaxed"><?php echo htmlspecialchars($pageData['meta_description']); ?></p>
        </div>
    </div>
</section>

<!-- Sitemap Content -->
<section class="py-16 bg-gray-50">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <!-- المحتوى الديناميكي من قاعدة البيانات -->
        <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
            <div class="prose prose-lg max-w-none">
                <?php 
                // تطبيق تنسيق محسن على المحتوى الديناميكي
                $content = $pageData['content'];
                
                // تحسين العناوين
                $content = preg_replace('/<h1([^>]*)>/', '<h1$1 class="text-3xl font-bold text-gray-900 mb-6 flex items-center"><i class="fas fa-home text-primary ml-3"></i>', $content);
                $content = preg_replace('/<h2([^>]*)>/', '<h2$1 class="text-2xl font-bold text-gray-900 mb-6 flex items-center"><i class="fas fa-folder text-primary ml-3"></i>', $content);
                $content = preg_replace('/<h3([^>]*)>/', '<h3$1 class="text-xl font-semibold text-gray-800 mb-4 flex items-center"><i class="fas fa-chevron-left text-primary ml-2"></i>', $content);
                
                // تحسين الفقرات
                $content = preg_replace('/<p([^>]*)>/', '<p$1 class="text-gray-700 leading-relaxed mb-4">', $content);
                
                // تحسين القوائم
                $content = preg_replace('/<ul([^>]*)>/', '<ul$1 class="space-y-3 mb-6">', $content);
                $content = preg_replace('/<ol([^>]*)>/', '<ol$1 class="space-y-3 mb-6 list-decimal list-inside">', $content);
                $content = preg_replace('/<li([^>]*)>/', '<li$1 class="text-gray-700 flex items-center"><i class="fas fa-chevron-left text-xs text-primary ml-2"></i>', $content);
                
                // تحسين الروابط
                $content = preg_replace('/<a([^>]*href[^>]*)>/', '<a$1 class="text-primary hover:text-green-600 transition-colors font-medium">', $content);
                
                echo $content;
                ?>
            </div>
        </div>

       

        <!-- Additional Information -->
        <div class="mt-12 bg-white rounded-lg shadow-lg p-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center">معلومات إضافية</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <!-- Quick Links -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">روابط سريعة</h3>
                    <div class="space-y-2">
                        <?php if (!empty($pageData['contact_phone'])): ?>
                        <p class="text-gray-700">
                            <i class="fas fa-phone text-primary ml-2"></i>
                            <strong>الهاتف:</strong> <?php echo htmlspecialchars($pageData['contact_phone']); ?>
                        </p>
                        <?php endif; ?>
                        
                        <?php if (!empty($pageData['contact_email'])): ?>
                        <p class="text-gray-700">
                            <i class="fas fa-envelope text-primary ml-2"></i>
                            <strong>البريد الإلكتروني:</strong> <?php echo htmlspecialchars($pageData['contact_email']); ?>
                        </p>
                        <?php endif; ?>
                        
                        <?php if (!empty($pageData['contact_address'])): ?>
                        <p class="text-gray-700">
                            <i class="fas fa-map-marker-alt text-primary ml-2"></i>
                            <strong>العنوان:</strong> <?php echo htmlspecialchars($pageData['contact_address']); ?>
                        </p>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Social Media -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">تابعنا على</h3>
                    <div class="flex space-x-4 space-x-reverse">
                        <?php if (!empty($pageData['facebook_url']) && $pageData['facebook_url'] !== '#'): ?>
                        <a href="<?php echo htmlspecialchars($pageData['facebook_url']); ?>" target="_blank" class="text-primary hover:text-primary transition-colors">
                            <i class="fab fa-facebook-f text-2xl"></i>
                        </a>
                        <?php endif; ?>
                        
                        <?php if (!empty($pageData['twitter_url']) && $pageData['twitter_url'] !== '#'): ?>
                        <a href="<?php echo htmlspecialchars($pageData['twitter_url']); ?>" target="_blank" class="text-primary hover:text-primary transition-colors">
                            <i class="fab fa-twitter text-2xl"></i>
                        </a>
                        <?php endif; ?>
                        
                        <?php if (!empty($pageData['instagram_url']) && $pageData['instagram_url'] !== '#'): ?>
                        <a href="<?php echo htmlspecialchars($pageData['instagram_url']); ?>" target="_blank" class="text-primary hover:text-primary transition-colors">
                            <i class="fab fa-instagram text-2xl"></i>
                        </a>
                        <?php endif; ?>
                        
                        <?php if (!empty($pageData['linkedin_url']) && $pageData['linkedin_url'] !== '#'): ?>
                        <a href="<?php echo htmlspecialchars($pageData['linkedin_url']); ?>" target="_blank" class="text-primary hover:text-primary transition-colors">
                            <i class="fab fa-linkedin-in text-2xl"></i>
                        </a>
                        <?php endif; ?>
                        
                        <?php if (!empty($pageData['youtube_url']) && $pageData['youtube_url'] !== '#'): ?>
                        <a href="<?php echo htmlspecialchars($pageData['youtube_url']); ?>" target="_blank" class="text-primary hover:text-primary transition-colors">
                            <i class="fab fa-youtube text-2xl"></i>
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            
        </div>

        <!-- تاريخ آخر تحديث -->
        <?php if (isset($pageData['updated_at'])): ?>
        <div class="mt-8 bg-white rounded-lg shadow-lg p-6">
            <div class="text-center">
                <p class="text-sm text-gray-500">
                    <i class="fas fa-clock text-primary ml-2"></i>
                    آخر تحديث: <?php echo date('d/m/Y', strtotime($pageData['updated_at'])); ?>
                </p>
            </div>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- JavaScript for search functionality -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('input[placeholder="ابحث عن صفحة أو منتج..."]');
    const searchButton = document.querySelector('.fa-search').parentElement;
    
    function performSearch() {
        const query = searchInput.value.trim();
        if (query) {
            // Redirect to search results or filter current page
            window.location.href = '<?php echo SITE_URL; ?>/search?q=' + encodeURIComponent(query);
        }
    }
    
    searchButton.addEventListener('click', performSearch);
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    });
});
</script>

<?php include 'footer.php'; ?>