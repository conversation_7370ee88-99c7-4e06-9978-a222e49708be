<?php
/**
 * صفحة إدارة الأدوار
 * Manage Roles Page
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/middleware.php';

// التحقق من تسجيل الدخول وصلاحية الإدارة
requirePermission('manage-roles');

// معالجة طلبات POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        switch ($action) {
            case 'create':
                $name = trim($_POST['name'] ?? '');
                $description = trim($_POST['description'] ?? '');
                
                if (empty($name)) {
                    throw new Exception('اسم الدور مطلوب');
                }
                
                // التحقق من عدم وجود دور بنفس الاسم
                $stmt = $database->prepare("SELECT id FROM roles WHERE name = ?");
                $stmt->execute([$name]);
                if ($stmt->fetch()) {
                    throw new Exception('يوجد دور بهذا الاسم بالفعل');
                }
                
                // إنشاء الدور الجديد
                $stmt = $database->prepare("INSERT INTO roles (name, description, created_at) VALUES (?, ?, NOW())");
                $stmt->execute([$name, $description]);
                
                $_SESSION['success_message'] = 'تم إنشاء الدور بنجاح';
                break;
                
            case 'update':
                $id = $_POST['id'] ?? '';
                $name = trim($_POST['name'] ?? '');
                $description = trim($_POST['description'] ?? '');
                
                if (empty($id) || empty($name)) {
                    throw new Exception('معرف الدور والاسم مطلوبان');
                }
                
                // التحقق من عدم وجود دور آخر بنفس الاسم
                $stmt = $database->prepare("SELECT id FROM roles WHERE name = ? AND id != ?");
                $stmt->execute([$name, $id]);
                if ($stmt->fetch()) {
                    throw new Exception('يوجد دور آخر بهذا الاسم');
                }
                
                // تحديث الدور
                $stmt = $database->prepare("UPDATE roles SET name = ?, description = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$name, $description, $id]);
                
                $_SESSION['success_message'] = 'تم تحديث الدور بنجاح';
                break;
                
            case 'delete':
                $id = $_POST['id'] ?? '';
                
                if (empty($id)) {
                    throw new Exception('معرف الدور مطلوب');
                }
                
                // التحقق من عدم استخدام الدور
                $stmt = $database->prepare("SELECT COUNT(*) FROM user_roles WHERE role_id = ?");
                $stmt->execute([$id]);
                $userCount = $stmt->fetchColumn();
                
                if ($userCount > 0) {
                    throw new Exception('لا يمكن حذف الدور لأنه مستخدم من قبل ' . $userCount . ' مستخدم');
                }
                
                // حذف صلاحيات الدور أولاً
                $stmt = $database->prepare("DELETE FROM role_permissions WHERE role_id = ?");
                $stmt->execute([$id]);
                
                // حذف الدور
                $stmt = $database->prepare("DELETE FROM roles WHERE id = ?");
                $stmt->execute([$id]);
                
                $_SESSION['success_message'] = 'تم حذف الدور بنجاح';
                break;
                
            default:
                throw new Exception('عملية غير صحيحة');
        }
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = $e->getMessage();
    }
    
    header('Location: manage_roles.php');
    exit;
}

// جلب جميع الأدوار
$stmt = $database->prepare("
    SELECT r.*, 
           COUNT(DISTINCT ur.user_id) as user_count,
           COUNT(DISTINCT rp.permission_id) as permission_count
    FROM roles r
    LEFT JOIN user_roles ur ON r.id = ur.role_id
    LEFT JOIN role_permissions rp ON r.id = rp.role_id
    GROUP BY r.id
    ORDER BY r.name
");
$stmt->execute();
$roles = $stmt->fetchAll(PDO::FETCH_ASSOC);

// إحصائيات سريعة
$stmt = $database->prepare("SELECT COUNT(*) FROM roles");
$stmt->execute();
$totalRoles = $stmt->fetchColumn();

$stmt = $database->prepare("SELECT COUNT(DISTINCT role_id) FROM user_roles");
$stmt->execute();
$usedRoles = $stmt->fetchColumn();

$stmt = $database->prepare("SELECT COUNT(DISTINCT role_id) FROM role_permissions");
$stmt->execute();
$rolesWithPermissions = $stmt->fetchColumn();

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">إدارة الأدوار</h4>
                <div class="page-title-right">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createRoleModal">
                        <i class="mdi mdi-plus"></i> إضافة دور جديد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row">
        <div class="col-md-4">
            <div class="card widget-flat">
                <div class="card-body">
                    <div class="float-end">
                        <i class="mdi mdi-account-group widget-icon"></i>
                    </div>
                    <h5 class="text-muted fw-normal mt-0" title="إجمالي الأدوار">إجمالي الأدوار</h5>
                    <h3 class="mt-3 mb-3"><?php echo $totalRoles; ?></h3>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card widget-flat">
                <div class="card-body">
                    <div class="float-end">
                        <i class="mdi mdi-account-check widget-icon bg-success-lighten text-success"></i>
                    </div>
                    <h5 class="text-muted fw-normal mt-0" title="الأدوار المستخدمة">الأدوار المستخدمة</h5>
                    <h3 class="mt-3 mb-3"><?php echo $usedRoles; ?></h3>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card widget-flat">
                <div class="card-body">
                    <div class="float-end">
                        <i class="mdi mdi-shield-check widget-icon bg-warning-lighten text-warning"></i>
                    </div>
                    <h5 class="text-muted fw-normal mt-0" title="الأدوار مع صلاحيات">الأدوار مع صلاحيات</h5>
                    <h3 class="mt-3 mb-3"><?php echo $rolesWithPermissions; ?></h3>
                </div>
            </div>
        </div>
    </div>

    <!-- رسائل النجاح والخطأ -->
    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- جدول الأدوار -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-centered table-nowrap mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>الاسم</th>
                                    <th>الوصف</th>
                                    <th>عدد المستخدمين</th>
                                    <th>عدد الصلاحيات</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($roles as $role): ?>
                                <tr>
                                    <td>
                                        <h5 class="font-14 my-1"><?php echo htmlspecialchars($role['name']); ?></h5>
                                    </td>
                                    <td><?php echo htmlspecialchars($role['description'] ?? 'لا يوجد وصف'); ?></td>
                                    <td>
                                        <span class="badge bg-info"><?php echo $role['user_count']; ?></span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success"><?php echo $role['permission_count']; ?></span>
                                    </td>
                                    <td><?php echo date('Y-m-d', strtotime($role['created_at'])); ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" 
                                                    onclick="editRole(<?php echo $role['id']; ?>)" title="تعديل">
                                                <i class="mdi mdi-pencil"></i>
                                            </button>
                                            <a href="role_permissions.php?role_id=<?php echo $role['id']; ?>" 
                                               class="btn btn-sm btn-outline-info" title="إدارة الصلاحيات">
                                                <i class="mdi mdi-shield-check"></i>
                                            </a>
                                            <?php if ($role['user_count'] == 0): ?>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteRole(<?php echo $role['id']; ?>)" title="حذف">
                                                <i class="mdi mdi-delete"></i>
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة إنشاء دور جديد -->
<div class="modal fade" id="createRoleModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">إضافة دور جديد</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create">
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">اسم الدور *</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إنشاء الدور</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نافذة تعديل الدور -->
<div class="modal fade" id="editRoleModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">تعديل الدور</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" name="id" id="edit_role_id">
                    
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">اسم الدور *</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editRole(roleId) {
    fetch(`get_role.php?id=${roleId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('edit_role_id').value = data.role.id;
                document.getElementById('edit_name').value = data.role.name;
                document.getElementById('edit_description').value = data.role.description || '';
                
                const modal = new bootstrap.Modal(document.getElementById('editRoleModal'));
                modal.show();
            } else {
                alert('خطأ في جلب بيانات الدور: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في جلب بيانات الدور');
        });
}

function deleteRole(roleId) {
    if (confirm('هل أنت متأكد من حذف هذا الدور؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="id" value="${roleId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<?php include '../includes/footer.php'; ?>