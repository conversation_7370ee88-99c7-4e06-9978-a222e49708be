{"name": "greenline/greenline-AC", "description": "Green Line greenline-Ac Platform - نظام التجارة الإلكترونية الأخضر", "type": "project", "keywords": ["ecommerce", "php", "mysql", "redis", "arabic"], "license": "MIT", "authors": [{"name": "Green Line Team", "email": "<EMAIL>"}], "require": {"php": ">=7.4", "ext-pdo": "*", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-curl": "*", "predis/predis": "^2.0", "phpmailer/phpmailer": "^6.8", "monolog/monolog": "^3.0", "vlucas/phpdotenv": "^5.5", "ramsey/uuid": "^4.7", "firebase/php-jwt": "^6.8", "intervention/image": "^2.7", "league/flysystem": "^3.0", "symfony/console": "^6.0", "twig/twig": "^3.0", "guzzlehttp/guzzle": "^7.0"}, "require-dev": {"phpunit/phpunit": "^9.0", "squizlabs/php_codesniffer": "^3.7", "phpstan/phpstan": "^1.10", "friendsofphp/php-cs-fixer": "^3.0", "mockery/mockery": "^1.5", "fakerphp/faker": "^1.21"}, "autoload": {"psr-4": {"GreenLine\\": "src/", "GreenLine\\Core\\": "includes/", "GreenLine\\Tests\\": "tests/"}, "files": ["includes/functions.php", "includes/helpers.php"]}, "autoload-dev": {"psr-4": {"GreenLine\\Tests\\": "tests/"}}, "scripts": {"test": "php tests/run_tests.php", "test:unit": "php tests/run_tests.php --unit", "test:integration": "php tests/run_tests.php --integration", "test:setup": "php tests/run_tests.php --setup", "test:cleanup": "php tests/run_tests.php --cleanup", "phpunit": "vendor/bin/phpunit", "phpcs": "vendor/bin/phpcs --standard=PSR12 src/ includes/", "phpcs:fix": "vendor/bin/phpcbf --standard=PSR12 src/ includes/", "phpstan": "vendor/bin/phpstan analyse src/ includes/ --level=5", "cs-fix": "vendor/bin/php-cs-fixer fix src/ includes/", "quality": ["@phpcs", "@phpstan"], "install-hooks": "php scripts/install-git-hooks.php", "post-install-cmd": ["@install-hooks"], "post-update-cmd": ["@install-hooks"]}, "config": {"optimize-autoloader": true, "sort-packages": true, "allow-plugins": {"composer/installers": true}}, "minimum-stability": "stable", "prefer-stable": true, "extra": {"branch-alias": {"dev-main": "1.0-dev"}}, "support": {"issues": "https://github.com/greenline/ecommerce/issues", "source": "https://github.com/greenline/ecommerce", "docs": "https://docs.greenline.com"}}