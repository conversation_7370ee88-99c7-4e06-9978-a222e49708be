<?php
/**
 * تذييل لوحة التحكم
 * Admin Footer
 */
?>

<!-- Footer -->
<footer class="bg-white border-t border-gray-200 mt-auto">
    <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between">
            <div class="flex items-center text-sm text-gray-500">
                <p>&copy; <?php echo date('Y'); ?> جرين لاين. جميع الحقوق محفوظة.</p>
            </div>
            <div class="flex items-center space-x-4 space-x-reverse text-sm text-gray-500">
                <span>الإصدار 1.0.0</span>
                <span>|</span>
                <a href="/admin/help.php" class="hover:text-gray-700">المساعدة</a>
                <span>|</span>
                <a href="/admin/support.php" class="hover:text-gray-700">الدعم الفني</a>
            </div>
        </div>
    </div>
</footer>

<!-- Scripts -->
<script>
// إغلاق الرسائل التلقائية
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('.alert-auto-close');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.style.opacity = '0';
            setTimeout(() => {
                alert.remove();
            }, 300);
        }, 5000);
    });
});

// تأكيد الحذف
function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
    return confirm(message);
}

// إظهار/إخفاء كلمة المرور
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
    input.setAttribute('type', type);
}
</script>

</body>
</html>