<?php
session_start();
require_once 'config/config.php';

// محاكاة تسجيل الدخول للاختبار
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['user_name'] = 'مستخدم تجريبي';
    $_SESSION['user_email'] = '<EMAIL>';
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار حالة التقييمات</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .form-group { margin: 10px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        textarea { height: 80px; resize: vertical; }
        #results { margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار حالة التقييمات بعد التراجع</h1>
        
        <!-- معلومات الحالة الحالية -->
        <div class="test-section">
            <h3>معلومات الحالة الحالية</h3>
            <div class="status info">
                <strong>معرف المستخدم:</strong> <?php echo $_SESSION['user_id'] ?? 'غير محدد'; ?><br>
                <strong>اسم المستخدم:</strong> <?php echo $_SESSION['user_name'] ?? 'غير محدد'; ?><br>
                <strong>البريد الإلكتروني:</strong> <?php echo $_SESSION['user_email'] ?? 'غير محدد'; ?><br>
                <strong>حالة الجلسة:</strong> <?php echo session_status() === PHP_SESSION_ACTIVE ? 'نشطة' : 'غير نشطة'; ?>
            </div>
        </div>

        <!-- اختبار الاتصال بقاعدة البيانات -->
        <div class="test-section">
            <h3>اختبار قاعدة البيانات</h3>
            <?php
            try {
                $testQuery = $database->fetch("SELECT COUNT(*) as count FROM reviews");
                echo '<div class="status success">✓ الاتصال بقاعدة البيانات ناجح - عدد التقييمات: ' . $testQuery['count'] . '</div>';
                
                // فحص المنتجات المتاحة
                $productsQuery = $database->fetchAll("SELECT id, name, is_active FROM products ORDER BY id ASC LIMIT 5");
                if ($productsQuery) {
                    echo '<div class="status info"><strong>المنتجات المتاحة:</strong><br>';
                    foreach ($productsQuery as $product) {
                        $status = $product['is_active'] ? 'نشط' : 'غير نشط';
                        echo "ID: {$product['id']} | الاسم: {$product['name']} | الحالة: {$status}<br>";
                    }
                    echo '</div>';
                } else {
                    echo '<div class="status error">✗ لا توجد منتجات في قاعدة البيانات</div>';
                }
                
                // عرض آخر 3 تقييمات
                $recentReviews = $database->fetchAll("SELECT id, product_id, user_id, name, rating, comment, created_at FROM reviews ORDER BY created_at DESC LIMIT 3");
                if ($recentReviews) {
                    echo '<div class="status info"><strong>آخر 3 تقييمات:</strong><br>';
                    foreach ($recentReviews as $review) {
                        echo "ID: {$review['id']} | المنتج: {$review['product_id']} | المستخدم: " . ($review['user_id'] ?: 'ضيف') . " | التقييم: {$review['rating']}/5<br>";
                    }
                    echo '</div>';
                }
            } catch (Exception $e) {
                echo '<div class="status error">✗ خطأ في قاعدة البيانات: ' . $e->getMessage() . '</div>';
            }
            ?>
        </div>

        <!-- اختبار سريع للـ API -->
        <div class="test-section">
            <h3>اختبار سريع للـ API</h3>
            <button onclick="testAPI()">اختبار API التقييمات</button>
            <button onclick="testGetProduct()">اختبار دالة getProduct</button>
            <div id="api-result"></div>
        </div>

        <!-- نموذج إرسال تقييم -->
        <div class="test-section">
            <h3>نموذج اختبار التقييم</h3>
            <form id="reviewForm">
                <div class="form-group">
                    <label>معرف المنتج:</label>
                    <select name="product_id" id="product_id" required>
                        <option value="">اختر منتج...</option>
                        <?php
                        // جلب المنتجات المتاحة للاختبار
                        try {
                            $availableProducts = $database->fetchAll("SELECT id, name FROM products WHERE is_active = 1 ORDER BY id ASC LIMIT 10");
                            foreach ($availableProducts as $product) {
                                echo "<option value='{$product['id']}'>{$product['name']} (ID: {$product['id']})</option>";
                            }
                        } catch (Exception $e) {
                            echo "<option value='1'>منتج تجريبي (ID: 1)</option>";
                        }
                        ?>
                    </select>
                </div>
                <div class="form-group">
                    <label>التقييم:</label>
                    <select name="rating" required>
                        <option value="5">5 نجوم</option>
                        <option value="4">4 نجوم</option>
                        <option value="3">3 نجوم</option>
                        <option value="2">نجمتان</option>
                        <option value="1">نجمة واحدة</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>نص التقييم:</label>
                    <textarea name="review_text" placeholder="اكتب تقييمك هنا..." required></textarea>
                </div>
                <button type="submit">إرسال التقييم</button>
            </form>
        </div>

        <div id="results"></div>
    </div>

    <script>
        // اختبار API
        function testAPI() {
            const resultDiv = document.getElementById('api-result');
            const productSelect = document.getElementById('product_id');
            const productId = productSelect.value || '1';
            
            resultDiv.innerHTML = '<div class="status info">جاري الاختبار...</div>';
            
            fetch('api/review.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `product_id=${productId}&rating=5&review_text=تقييم تجريبي للاختبار`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = '<div class="status success">✓ API يعمل بشكل صحيح: ' + data.message + '</div>';
                } else {
                    resultDiv.innerHTML = '<div class="status error">✗ خطأ في API: ' + data.message + '</div>';
                }
            })
            .catch(error => {
                resultDiv.innerHTML = '<div class="status error">✗ خطأ في الشبكة: ' + error.message + '</div>';
            });
        }

        // اختبار دالة getProduct
        function testGetProduct() {
            const resultDiv = document.getElementById('api-result');
            const productSelect = document.getElementById('product_id');
            const productId = productSelect.value || '1';
            
            resultDiv.innerHTML = '<div class="status info">جاري اختبار getProduct...</div>';
            
            // إنشاء صفحة اختبار مؤقتة
            fetch('test_get_product.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `product_id=${productId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = '<div class="status success">✓ دالة getProduct تعمل بشكل صحيح<br>المنتج: ' + data.product.name + '<br>الحالة: ' + (data.product.is_active ? 'نشط' : 'غير نشط') + '</div>';
                } else {
                    resultDiv.innerHTML = '<div class="status error">✗ خطأ في getProduct: ' + data.message + '</div>';
                }
            })
            .catch(error => {
                resultDiv.innerHTML = '<div class="status error">✗ خطأ في الشبكة: ' + error.message + '</div>';
            });
        }

        // معالج النموذج
        document.getElementById('reviewForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="status info">جاري الإرسال...</div>';
            
            fetch('api/review.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultsDiv.innerHTML = '<div class="status success">✓ تم إرسال التقييم بنجاح: ' + data.message + '</div>';
                    // إعادة تحميل الصفحة بعد 2 ثانية لعرض التقييم الجديد
                    setTimeout(() => location.reload(), 2000);
                } else {
                    resultsDiv.innerHTML = '<div class="status error">✗ فشل في إرسال التقييم: ' + data.message + '</div>';
                }
            })
            .catch(error => {
                resultsDiv.innerHTML = '<div class="status error">✗ خطأ في الشبكة: ' + error.message + '</div>';
            });
        });
    </script>
</body>
</html>