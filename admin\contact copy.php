<?php
/**
 * صفحة إدارة معلومات التواصل
 * Contact Information Management Page
 */

require_once 'includes/layout.php';

$currentPage = 'contact';
$pageTitle = 'إدارة معلومات التواصل';
$pageDescription = 'إدارة وتحديث معلومات التواصل والعنوان وأرقام الهاتف';
$additionalCSS = ['assets/css/contact-management.css'];
$breadcrumbs = [
    ['title' => 'المحتوى الإضافي'],
    ['title' => 'معلومات التواصل']
];
$message = '';
$error = '';
$success = '';

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $sectionKey = $_POST['section_key'] ?? '';
        $title = $_POST['title'] ?? '';
        $content = $_POST['content'] ?? '';
        $isActive = isset($_POST['is_active']) ? 1 : 0;
        
        // معالجة البيانات الإضافية (JSON)
        $data = [];
        if (isset($_POST['data']) && is_array($_POST['data'])) {
            $data = $_POST['data'];
        }
        
        // معالجة خاصة لكل قسم
        switch ($sectionKey) {
            case 'address':
                $data = [
                    'street' => $_POST['data']['street'] ?? '',
                    'district' => $_POST['data']['district'] ?? '',
                    'city' => $_POST['data']['city'] ?? '',
                    'postal_code' => $_POST['data']['postal_code'] ?? '',
                    'country' => $_POST['data']['country'] ?? '',
                    'coordinates' => [
                        'lat' => floatval($_POST['data']['lat'] ?? 0),
                        'lng' => floatval($_POST['data']['lng'] ?? 0)
                    ]
                ];
                break;
                
            case 'phone':
                $data = [
                    'primary' => $_POST['data']['primary'] ?? '',
                    'secondary' => $_POST['data']['secondary'] ?? '',
                    'mobile' => $_POST['data']['mobile'] ?? '',
                    'fax' => $_POST['data']['fax'] ?? ''
                ];
                break;
                
            case 'email':
                $data = [
                    'general' => $_POST['data']['general'] ?? '',
                    'sales' => $_POST['data']['sales'] ?? '',
                    'support' => $_POST['data']['support'] ?? '',
                    'hr' => $_POST['data']['hr'] ?? ''
                ];
                break;
                
            case 'working_hours':
                $data = [
                    'sunday_thursday' => [
                        'open' => $_POST['data']['sunday_thursday_open'] ?? '',
                        'close' => $_POST['data']['sunday_thursday_close'] ?? ''
                    ],
                    'friday' => [
                        'open' => $_POST['data']['friday_open'] ?? '',
                        'close' => $_POST['data']['friday_close'] ?? ''
                    ],
                    'saturday' => $_POST['data']['saturday'] ?? 'closed',
                    'timezone' => $_POST['data']['timezone'] ?? 'Asia/Riyadh'
                ];
                break;
                
            case 'whatsapp':
                $data = [
                    'number' => $_POST['data']['number'] ?? '',
                    'business_hours_only' => isset($_POST['data']['business_hours_only']),
                    'auto_reply' => $_POST['data']['auto_reply'] ?? ''
                ];
                break;
                
            case 'map_embed':
                $data = [
                    'embed_code' => $_POST['data']['embed_code'] ?? '',
                    'coordinates' => [
                        'lat' => floatval($_POST['data']['lat'] ?? 0),
                        'lng' => floatval($_POST['data']['lng'] ?? 0)
                    ],
                    'zoom_level' => intval($_POST['data']['zoom_level'] ?? 15)
                ];
                break;
                
            case 'social_media':
                // معالجة ديناميكية لجميع منصات التواصل الاجتماعي
                $data = [];
                if (isset($_POST['data']) && is_array($_POST['data'])) {
                    foreach ($_POST['data'] as $platform => $url) {
                        // تنظيف الرابط وحفظه فقط إذا لم يكن فارغاً
                        $cleanUrl = trim($url);
                        if (!empty($cleanUrl)) {
                            $data[$platform] = $cleanUrl;
                        }
                    }
                }
                break;
                
            case 'company_info':
                $data = [
                    'company_name' => $_POST['data']['company_name'] ?? '',
                    'commercial_register' => $_POST['data']['commercial_register'] ?? '',
                    'tax_number' => $_POST['data']['tax_number'] ?? '',
                    'established_year' => intval($_POST['data']['established_year'] ?? date('Y')),
                    'license_number' => $_POST['data']['license_number'] ?? ''
                ];
                break;
        }
        
        $dataJson = json_encode($data, JSON_UNESCAPED_UNICODE);
        
        // التحقق من وجود السجل
        $existing = $database->fetch("SELECT id FROM contact_info WHERE section_key = ?", [$sectionKey]);
        
        if ($existing) {
            // تحديث السجل الموجود
            $database->query(
                "UPDATE contact_info SET title = ?, content = ?, data = ?, is_active = ?, updated_at = NOW() WHERE section_key = ?",
                [$title, $content, $dataJson, $isActive, $sectionKey]
            );
        } else {
            // إنشاء سجل جديد
            $database->query(
                "INSERT INTO contact_info (section_key, title, content, data, is_active, created_at, updated_at) VALUES (?, ?, ?, ?, ?, NOW(), NOW())",
                [$sectionKey, $title, $content, $dataJson, $isActive]
            );
        }
        
        $message = 'تم حفظ معلومات التواصل بنجاح';
        $success = 'تم حفظ معلومات التواصل بنجاح';
        
    } catch (Exception $e) {
        $message = 'حدث خطأ أثناء حفظ البيانات: ' . $e->getMessage();
        $error = 'حدث خطأ أثناء حفظ البيانات: ' . $e->getMessage();
    }
}

// الحصول على التبويب النشط
$activeTab = $_GET['tab'] ?? 'address';

// الحصول على جميع معلومات التواصل
$contactInfo = [];
try {
    $results = $database->fetchAll("SELECT * FROM contact_info ORDER BY section_key");
    foreach ($results as $row) {
        $contactInfo[$row['section_key']] = $row;
        if ($row['data']) {
            $contactInfo[$row['section_key']]['data_parsed'] = json_decode($row['data'], true);
        }
    }
} catch (Exception $e) {
    // في حالة عدم وجود الجدول أو خطأ
}

startLayout();
showPageHeader();
showMessages();

// إعداد التبويبات
$tabs = [
    'address' => [
        'title' => 'العنوان',
        'icon' => 'fas fa-map-marker-alt',
        'content' => getAddressTabContent($contactInfo)
    ],
    'phone' => [
        'title' => 'الهاتف',
        'icon' => 'fas fa-phone',
        'content' => getPhoneTabContent($contactInfo)
    ],
    'email' => [
        'title' => 'البريد الإلكتروني',
        'icon' => 'fas fa-envelope',
        'content' => getEmailTabContent($contactInfo)
    ],
    'working_hours' => [
        'title' => 'ساعات العمل',
        'icon' => 'fas fa-clock',
        'content' => getWorkingHoursTabContent($contactInfo)
    ],
    'whatsapp' => [
        'title' => 'واتساب',
        'icon' => 'fab fa-whatsapp',
        'content' => getWhatsappTabContent($contactInfo)
    ],
    'map_embed' => [
        'title' => 'الخريطة',
        'icon' => 'fas fa-map',
        'content' => getMapTabContent($contactInfo)
    ],
    'social_media' => [
        'title' => 'وسائل التواصل',
        'icon' => 'fas fa-share-alt',
        'content' => getSocialMediaTabContent($contactInfo)
    ],
    'company_info' => [
        'title' => 'معلومات الشركة',
        'icon' => 'fas fa-building',
        'content' => getCompanyInfoTabContent($contactInfo)
    ],
    'download_center' => [
        'title' => 'مركز التحميل',
        'icon' => 'fas fa-download',
        'content' => getDownloadCenterTabContent()
    ]
];

createTabs($tabs);

// دوال محتوى التبويبات
function getAddressTabContent($contactInfo) {
    $data = $contactInfo['address']['data_parsed'] ?? [];
    
    ob_start();
    ?>
    <form method="POST" class="space-y-6">
        <input type="hidden" name="section_key" value="address">
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label for="title" class="block text-sm font-medium text-gray-700 mb-2">عنوان القسم</label>
                <input type="text" id="title" name="title" value="<?php echo htmlspecialchars($contactInfo['address']['title'] ?? 'العنوان'); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
            </div>
            
            <div>
                <label for="content" class="block text-sm font-medium text-gray-700 mb-2">العنوان المختصر</label>
                <input type="text" id="content" name="content" value="<?php echo htmlspecialchars($contactInfo['address']['content'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            
            <div>
                <label for="street" class="block text-sm font-medium text-gray-700 mb-2">الشارع</label>
                <input type="text" id="street" name="data[street]" value="<?php echo htmlspecialchars($data['street'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            
            <div>
                <label for="district" class="block text-sm font-medium text-gray-700 mb-2">الحي</label>
                <input type="text" id="district" name="data[district]" value="<?php echo htmlspecialchars($data['district'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            
            <div>
                <label for="city" class="block text-sm font-medium text-gray-700 mb-2">المدينة</label>
                <input type="text" id="city" name="data[city]" value="<?php echo htmlspecialchars($data['city'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            
            <div>
                <label for="postal_code" class="block text-sm font-medium text-gray-700 mb-2">الرمز البريدي</label>
                <input type="text" id="postal_code" name="data[postal_code]" value="<?php echo htmlspecialchars($data['postal_code'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            
            <div>
                <label for="country" class="block text-sm font-medium text-gray-700 mb-2">الدولة</label>
                <input type="text" id="country" name="data[country]" value="<?php echo htmlspecialchars($data['country'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            
            <div>
                <label for="lat" class="block text-sm font-medium text-gray-700 mb-2">خط العرض (Latitude)</label>
                <input type="number" step="any" id="lat" name="data[lat]" value="<?php echo $data['coordinates']['lat'] ?? ''; ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            
            <div>
                <label for="lng" class="block text-sm font-medium text-gray-700 mb-2">خط الطول (Longitude)</label>
                <input type="number" step="any" id="lng" name="data[lng]" value="<?php echo $data['coordinates']['lng'] ?? ''; ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
        </div>
        
        <div class="flex items-center">
            <input type="checkbox" id="is_active" name="is_active" <?php echo ($contactInfo['address']['is_active'] ?? 1) ? 'checked' : ''; ?> class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
            <label for="is_active" class="mr-2 block text-sm text-gray-900">فعال</label>
        </div>
        
        <div class="flex justify-end">
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                <i class="fas fa-save mr-2"></i>حفظ التغييرات
            </button>
        </div>
    </form>
    <?php
    return ob_get_clean();
}
function getPhoneTabContent($contactInfo) {
    $data = $contactInfo['phone']['data_parsed'] ?? [];
    
    ob_start();
    ?>
    <form method="POST" class="space-y-6">
        <input type="hidden" name="section_key" value="phone">
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label for="title" class="block text-sm font-medium text-gray-700 mb-2">عنوان القسم</label>
                <input type="text" id="title" name="title" value="<?php echo htmlspecialchars($contactInfo['phone']['title'] ?? 'الهاتف'); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
            </div>
            
            <div>
                <label for="content" class="block text-sm font-medium text-gray-700 mb-2">الرقم الأساسي</label>
                <input type="text" id="content" name="content" value="<?php echo htmlspecialchars($contactInfo['phone']['content'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            
            <div>
                <label for="primary" class="block text-sm font-medium text-gray-700 mb-2">الهاتف الأساسي</label>
                <input type="text" id="primary" name="data[primary]" value="<?php echo htmlspecialchars($data['primary'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            
            <div>
                <label for="secondary" class="block text-sm font-medium text-gray-700 mb-2">الهاتف الثانوي</label>
                <input type="text" id="secondary" name="data[secondary]" value="<?php echo htmlspecialchars($data['secondary'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            
            <div>
                <label for="mobile" class="block text-sm font-medium text-gray-700 mb-2">الجوال</label>
                <input type="text" id="mobile" name="data[mobile]" value="<?php echo htmlspecialchars($data['mobile'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            
            <div>
                <label for="fax" class="block text-sm font-medium text-gray-700 mb-2">الفاكس</label>
                <input type="text" id="fax" name="data[fax]" value="<?php echo htmlspecialchars($data['fax'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
        </div>
        
        <div class="flex items-center">
            <input type="checkbox" id="is_active" name="is_active" <?php echo ($contactInfo['phone']['is_active'] ?? 1) ? 'checked' : ''; ?> class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
            <label for="is_active" class="mr-2 block text-sm text-gray-900">فعال</label>
        </div>
        
        <div class="flex justify-end">
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                <i class="fas fa-save mr-2"></i>حفظ التغييرات
            </button>
        </div>
    </form>
    <?php
    return ob_get_clean();
}

function getEmailTabContent($contactInfo) {
    $data = $contactInfo['email']['data_parsed'] ?? [];
    
    ob_start();
    ?>
    <form method="POST" class="space-y-6">
        <input type="hidden" name="section_key" value="email">
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label for="title" class="block text-sm font-medium text-gray-700 mb-2">عنوان القسم</label>
                <input type="text" id="title" name="title" value="<?php echo htmlspecialchars($contactInfo['email']['title'] ?? 'البريد الإلكتروني'); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
            </div>
            
            <div>
                <label for="content" class="block text-sm font-medium text-gray-700 mb-2">البريد الأساسي</label>
                <input type="email" id="content" name="content" value="<?php echo htmlspecialchars($contactInfo['email']['content'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            
            <div>
                <label for="general" class="block text-sm font-medium text-gray-700 mb-2">البريد العام</label>
                <input type="email" id="general" name="data[general]" value="<?php echo htmlspecialchars($data['general'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            
            <div>
                <label for="sales" class="block text-sm font-medium text-gray-700 mb-2">بريد المبيعات</label>
                <input type="email" id="sales" name="data[sales]" value="<?php echo htmlspecialchars($data['sales'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            
            <div>
                <label for="support" class="block text-sm font-medium text-gray-700 mb-2">بريد الدعم الفني</label>
                <input type="email" id="support" name="data[support]" value="<?php echo htmlspecialchars($data['support'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            
            <div>
                <label for="hr" class="block text-sm font-medium text-gray-700 mb-2">بريد الموارد البشرية</label>
                <input type="email" id="hr" name="data[hr]" value="<?php echo htmlspecialchars($data['hr'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
        </div>
        
        <div class="flex items-center">
            <input type="checkbox" id="is_active" name="is_active" <?php echo ($contactInfo['email']['is_active'] ?? 1) ? 'checked' : ''; ?> class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
            <label for="is_active" class="mr-2 block text-sm text-gray-900">فعال</label>
        </div>
        
        <div class="flex justify-end">
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                <i class="fas fa-save mr-2"></i>حفظ التغييرات
            </button>
        </div>
    </form>
    <?php
    return ob_get_clean();
}

function getWorkingHoursTabContent($contactInfo) {
    $data = $contactInfo['working_hours']['data_parsed'] ?? [];
    
    ob_start();
    ?>
    <form method="POST" class="space-y-6">
        <input type="hidden" name="section_key" value="working_hours">
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label for="title" class="block text-sm font-medium text-gray-700 mb-2">عنوان القسم</label>
                <input type="text" id="title" name="title" value="<?php echo htmlspecialchars($contactInfo['working_hours']['title'] ?? 'ساعات العمل'); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
            </div>
            
            <div>
                <label for="timezone" class="block text-sm font-medium text-gray-700 mb-2">المنطقة الزمنية</label>
                <select id="timezone" name="data[timezone]" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="Asia/Riyadh" <?php echo ($data['timezone'] ?? '') === 'Asia/Riyadh' ? 'selected' : ''; ?>>الرياض (Asia/Riyadh)</option>
                    <option value="Asia/Dubai" <?php echo ($data['timezone'] ?? '') === 'Asia/Dubai' ? 'selected' : ''; ?>>دبي (Asia/Dubai)</option>
                    <option value="Asia/Kuwait" <?php echo ($data['timezone'] ?? '') === 'Asia/Kuwait' ? 'selected' : ''; ?>>الكويت (Asia/Kuwait)</option>
                </select>
            </div>
        </div>
        
        <div class="space-y-4">
            <h3 class="text-lg font-medium text-gray-900">الأحد - الخميس</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="sunday_thursday_open" class="block text-sm font-medium text-gray-700 mb-2">وقت الفتح</label>
                    <input type="time" id="sunday_thursday_open" name="data[sunday_thursday_open]" value="<?php echo htmlspecialchars($data['sunday_thursday']['open'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label for="sunday_thursday_close" class="block text-sm font-medium text-gray-700 mb-2">وقت الإغلاق</label>
                    <input type="time" id="sunday_thursday_close" name="data[sunday_thursday_close]" value="<?php echo htmlspecialchars($data['sunday_thursday']['close'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
        </div>
        
        <div class="space-y-4">
            <h3 class="text-lg font-medium text-gray-900">الجمعة</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="friday_open" class="block text-sm font-medium text-gray-700 mb-2">وقت الفتح</label>
                    <input type="time" id="friday_open" name="data[friday_open]" value="<?php echo htmlspecialchars($data['friday']['open'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label for="friday_close" class="block text-sm font-medium text-gray-700 mb-2">وقت الإغلاق</label>
                    <input type="time" id="friday_close" name="data[friday_close]" value="<?php echo htmlspecialchars($data['friday']['close'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
        </div>
        
        <div>
            <label for="saturday" class="block text-sm font-medium text-gray-700 mb-2">السبت</label>
            <select id="saturday" name="data[saturday]" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="closed" <?php echo ($data['saturday'] ?? '') === 'closed' ? 'selected' : ''; ?>>مغلق</option>
                <option value="open" <?php echo ($data['saturday'] ?? '') === 'open' ? 'selected' : ''; ?>>مفتوح</option>
            </select>
        </div>
        
        <div>
            <label for="content" class="block text-sm font-medium text-gray-700 mb-2">وصف ساعات العمل</label>
            <textarea id="content" name="content" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo htmlspecialchars($contactInfo['working_hours']['content'] ?? ''); ?></textarea>
        </div>
        
        <div class="flex items-center">
            <input type="checkbox" id="is_active" name="is_active" <?php echo ($contactInfo['working_hours']['is_active'] ?? 1) ? 'checked' : ''; ?> class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
            <label for="is_active" class="mr-2 block text-sm text-gray-900">فعال</label>
        </div>
        
        <div class="flex justify-end">
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                <i class="fas fa-save mr-2"></i>حفظ التغييرات
            </button>
        </div>
    </form>
    <?php
    return ob_get_clean();
}

// إنهاء الملف
endLayout();
?>
// Contact Management JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Auto-save functionality
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('change', function() {
                // Add visual feedback for unsaved changes
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.classList.add('bg-orange-500', 'hover:bg-orange-600');
                    submitBtn.classList.remove('bg-blue-600', 'hover:bg-blue-700');
                    submitBtn.textContent = 'حفظ التغييرات *';
                }
            });
        });
    });

    // WhatsApp number formatting
    const whatsappInput = document.getElementById('number');
    if (whatsappInput) {
        whatsappInput.addEventListener('input', function() {
            let value = this.value.replace(/\D/g, '');
            if (value.startsWith('966')) {
                this.value = '+' + value;
            } else if (value.startsWith('0')) {
                this.value = '+966' + value.substring(1);
            }
        });
    }

    // Coordinates validation
    const latInputs = document.querySelectorAll('input[name*="lat"]');
    const lngInputs = document.querySelectorAll('input[name*="lng"]');
    
    latInputs.forEach(input => {
        input.addEventListener('blur', function() {
            const value = parseFloat(this.value);
            if (value < -90 || value > 90) {
                this.setCustomValidity('خط العرض يجب أن يكون بين -90 و 90');
            } else {
                this.setCustomValidity('');
            }
        });
    });
    
    lngInputs.forEach(input => {
        input.addEventListener('blur', function() {
            const value = parseFloat(this.value);
            if (value < -180 || value > 180) {
                this.setCustomValidity('خط الطول يجب أن يكون بين -180 و 180');
            } else {
                this.setCustomValidity('');
            }
        });
    });

    // Email validation
    const emailInputs = document.querySelectorAll('input[type="email"]');
    emailInputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.value && !this.value.includes('@')) {
                this.setCustomValidity('يرجى إدخال بريد إلكتروني صحيح');
            } else {
                this.setCustomValidity('');
            }
        });
    });

    // URL validation for social media
    const urlInputs = document.querySelectorAll('input[type="url"]');
    urlInputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.value && !this.value.startsWith('http')) {
                this.value = 'https://' + this.value;
            }
        });
    });

    // Working hours validation
    const openTimeInputs = document.querySelectorAll('input[name*="_open"]');
    const closeTimeInputs = document.querySelectorAll('input[name*="_close"]');
    
    function validateWorkingHours() {
        openTimeInputs.forEach((openInput, index) => {
            const closeInput = closeTimeInputs[index];
            if (openInput && closeInput && openInput.value && closeInput.value) {
                if (openInput.value >= closeInput.value) {
                    closeInput.setCustomValidity('وقت الإغلاق يجب أن يكون بعد وقت الفتح');
                } else {
                    closeInput.setCustomValidity('');
                }
            }
        });
    }
    
    openTimeInputs.forEach(input => {
        input.addEventListener('change', validateWorkingHours);
    });
    
    closeTimeInputs.forEach(input => {
        input.addEventListener('change', validateWorkingHours);
    });

    // Working Hours Auto-Update Description
    initializeWorkingHoursAutoUpdate();

    function initializeWorkingHoursAutoUpdate() {
        // التحقق من وجود عناصر ساعات العمل
        const sundayThursdayOpen = document.getElementById('sunday_thursday_open');
        const sundayThursdayClose = document.getElementById('sunday_thursday_close');
        const fridayOpen = document.getElementById('friday_open');
        const fridayClose = document.getElementById('friday_close');
        const saturdaySelect = document.getElementById('saturday');
        const contentTextarea = document.getElementById('content');
        
        // التحقق من وجود جميع العناصر المطلوبة
        if (!sundayThursdayOpen || !sundayThursdayClose || !fridayOpen || !fridayClose || !saturdaySelect || !contentTextarea) {
            return; // إذا لم تكن في صفحة ساعات العمل
        }
        
        // إضافة مستمعي الأحداث لجميع الحقول
        [sundayThursdayOpen, sundayThursdayClose, fridayOpen, fridayClose, saturdaySelect].forEach(element => {
            element.addEventListener('change', updateWorkingHoursDescription);
        });
        
        // تحديث أولي عند تحميل الصفحة
        updateWorkingHoursDescription();
        
        function updateWorkingHoursDescription() {
            let description = '';
            
            // ساعات العمل للأحد - الخميس
            const sundayThursdayOpenTime = sundayThursdayOpen.value;
            const sundayThursdayCloseTime = sundayThursdayClose.value;
            
            if (sundayThursdayOpenTime && sundayThursdayCloseTime) {
                const openTime = formatTime(sundayThursdayOpenTime);
                const closeTime = formatTime(sundayThursdayCloseTime);
                description += `الأحد - الخميس: ${openTime} - ${closeTime}`;
            } else if (sundayThursdayOpenTime || sundayThursdayCloseTime) {
                description += 'الأحد - الخميس: يرجى تحديد أوقات الفتح والإغلاق';
            } else {
                description += 'الأحد - الخميس: مغلق';
            }
            
            // ساعات العمل للجمعة
            const fridayOpenTime = fridayOpen.value;
            const fridayCloseTime = fridayClose.value;
            
            if (fridayOpenTime && fridayCloseTime) {
                const openTime = formatTime(fridayOpenTime);
                const closeTime = formatTime(fridayCloseTime);
                description += `\nالجمعة: ${openTime} - ${closeTime}`;
            } else if (fridayOpenTime || fridayCloseTime) {
                description += '\nالجمعة: يرجى تحديد أوقات الفتح والإغلاق';
            } else {
                description += '\nالجمعة: مغلق';
            }
            
            // حالة يوم السبت
            const saturdayStatus = saturdaySelect.value;
            if (saturdayStatus === 'open') {
                description += '\nالسبت: مفتوح';
            } else {
                description += '\nالسبت: مغلق';
            }
            
            // تحديث محتوى textarea
            contentTextarea.value = description;
            
            // إضافة تأثير بصري لإظهار التحديث
            contentTextarea.style.backgroundColor = '#f0f9ff';
            setTimeout(() => {
                contentTextarea.style.backgroundColor = '';
            }, 500);
        }
        
        function formatTime(timeString) {
            if (!timeString) return '';
            
            const [hours, minutes] = timeString.split(':');
            const hour = parseInt(hours);
            const minute = minutes;
            
            if (hour === 0) {
                return `12:${minute} ص`;
            } else if (hour < 12) {
                return `${hour}:${minute} ص`;
            } else if (hour === 12) {
                return `12:${minute} م`;
            } else {
                return `${hour - 12}:${minute} م`;
            }
        }
    }

    // Social Media Dynamic Management
    const socialMediaForm = document.getElementById('social-media-form');
    if (socialMediaForm) {
        const addPlatformBtn = document.getElementById('add-platform-btn');
        const availablePlatforms = document.getElementById('available-platforms');
        const cancelAddBtn = document.getElementById('cancel-add-platform');
        const platformsContainer = document.getElementById('social-platforms-container');

        // Show available platforms
        addPlatformBtn.addEventListener('click', function() {
            // Hide platforms that are already added
            const addedPlatforms = Array.from(platformsContainer.querySelectorAll('.social-platform-item:not(.hidden)')).map(item => item.dataset.platform);
            const platformOptions = availablePlatforms.querySelectorAll('.add-platform-option');
            
            platformOptions.forEach(option => {
                if (addedPlatforms.includes(option.dataset.platform)) {
                    option.style.display = 'none';
                } else {
                    option.style.display = 'block';
                }
            });

            availablePlatforms.classList.remove('hidden');
            addPlatformBtn.style.display = 'none';
        });

        // Cancel adding platform
        cancelAddBtn.addEventListener('click', function() {
            availablePlatforms.classList.add('hidden');
            addPlatformBtn.style.display = 'block';
        });

        // Add platform
        availablePlatforms.addEventListener('click', function(e) {
            if (e.target.closest('.add-platform-option')) {
                const option = e.target.closest('.add-platform-option');
                const platform = option.dataset.platform;
                
                // Find the hidden platform item and show it
                const platformItem = platformsContainer.querySelector(`[data-platform="${platform}"]`);
                if (platformItem) {
                    platformItem.classList.remove('hidden');
                    
                    // Focus on the input field
                    const input = platformItem.querySelector('input[type="url"]');
                    if (input) {
                        input.focus();
                    }
                }

                // Hide the available platforms section
                availablePlatforms.classList.add('hidden');
                addPlatformBtn.style.display = 'block';
            }
        });

        // Remove platform
        platformsContainer.addEventListener('click', function(e) {
            if (e.target.closest('.remove-platform-btn')) {
                const platformItem = e.target.closest('.social-platform-item');
                const input = platformItem.querySelector('input[type="url"]');
                
                // Clear the input value and hide the platform
                if (input) {
                    input.value = '';
                }
                platformItem.classList.add('hidden');
                
                // Update preview
                updateSocialPreview();
            }
        });

        // Update preview when input changes
        platformsContainer.addEventListener('input', function(e) {
            if (e.target.type === 'url') {
                updateSocialPreview();
            }
        });

        // Auto-hide empty platforms on form submit
        socialMediaForm.addEventListener('submit', function(e) {
            const platformItems = platformsContainer.querySelectorAll('.social-platform-item');
            platformItems.forEach(item => {
                const input = item.querySelector('input[type="url"]');
                if (input && !input.value.trim()) {
                    item.classList.add('hidden');
                }
            });
        });

        // Function to update social media preview
        function updateSocialPreview() {
            const socialPreview = document.getElementById('social_preview');
            if (!socialPreview) return;

            const previewContainer = socialPreview.querySelector('.social-links-preview');
            if (!previewContainer) return;

            // Clear current preview
            previewContainer.innerHTML = '';

            // Get all visible platform items with values
            const visiblePlatforms = platformsContainer.querySelectorAll('.social-platform-item:not(.hidden)');
            
            visiblePlatforms.forEach(item => {
                const input = item.querySelector('input[type="url"]');
                const platform = item.dataset.platform;
                
                if (input && input.value.trim()) {
                    const icon = item.querySelector('i').className;
                    const colorClass = icon.includes('facebook') ? 'blue' : 
                                     icon.includes('twitter') ? 'blue' : 
                                     icon.includes('instagram') ? 'pink' : 
                                     icon.includes('linkedin') ? 'blue' : 
                                     icon.includes('youtube') ? 'red' : 
                                     icon.includes('snapchat') ? 'yellow' : 
                                     icon.includes('tiktok') ? 'gray' : 
                                     icon.includes('pinterest') ? 'red' : 
                                     icon.includes('telegram') ? 'blue' : 
                                     icon.includes('whatsapp') ? 'green' : 
                                     icon.includes('discord') ? 'indigo' : 
                                     icon.includes('reddit') ? 'orange' : 
                                     icon.includes('tumblr') ? 'indigo' : 
                                     icon.includes('twitch') ? 'purple' : 
                                     icon.includes('behance') ? 'blue' : 
                                     icon.includes('dribbble') ? 'pink' : 'gray';
                    
                    const platformName = item.querySelector('label').textContent;
                    
                    const linkElement = document.createElement('a');
                    linkElement.href = input.value;
                    linkElement.target = '_blank';
                    linkElement.className = `inline-flex items-center px-3 py-2 border border-${colorClass}-300 rounded-md text-sm font-medium text-${colorClass}-700 bg-${colorClass}-50 hover:bg-${colorClass}-100 transition-colors duration-200`;
                    linkElement.innerHTML = `<i class="${icon} mr-2"></i> ${platformName}`;
                    
                    previewContainer.appendChild(linkElement);
                }
            });

            // Show message if no platforms are added
            if (previewContainer.children.length === 0) {
                previewContainer.innerHTML = '<p class="text-gray-500 text-sm">لم يتم إضافة أي منصات تواصل اجتماعي بعد</p>';
            }
        }

        // Initial preview update
        updateSocialPreview();
    }

    // WhatsApp Tab Functionality
    const whatsappForm = document.getElementById('whatsapp-form');
    if (whatsappForm) {
        const whatsappNumber = document.getElementById('whatsapp_number');
        const whatsappHours = document.getElementById('whatsapp_hours');
        const previewNumber = document.getElementById('preview_number');
        const previewHours = document.getElementById('preview_hours');

        // Update preview when inputs change
        if (whatsappNumber) {
            whatsappNumber.addEventListener('input', function() {
                if (previewNumber) {
                    previewNumber.textContent = this.value || 'لم يتم تحديد رقم بعد';
                }
            });
        }

        if (whatsappHours) {
            whatsappHours.addEventListener('input', function() {
                if (previewHours) {
                    previewHours.textContent = this.value || 'لم يتم تحديد ساعات العمل بعد';
                }
            });
        }
    }

    // Download Center Functionality
    const downloadCenterTab = document.querySelector('[data-tab="download-center"]');
    if (downloadCenterTab) {
        // File upload form
        const addFileForm = document.getElementById('add-file-form');
        if (addFileForm) {
            addFileForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const formData = new FormData(this);
                const submitBtn = this.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                
                // Show loading state
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>جاري الرفع...';
                submitBtn.disabled = true;
                
                // Simulate file upload (replace with actual upload logic)
                setTimeout(() => {
                    // Reset form
                    this.reset();
                    
                    // Show success message
                    showNotification('تم رفع الملف بنجاح!', 'success');
                    
                    // Reset button
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                    
                    // Refresh file list (you would implement this based on your backend)
                    // refreshFileList();
                }, 2000);
            });
        }

        // File filter
        const filterCategory = document.getElementById('filter_category');
        if (filterCategory) {
            filterCategory.addEventListener('change', function() {
                const selectedCategory = this.value;
                const tableRows = document.querySelectorAll('#files-table-body tr');
                
                tableRows.forEach(row => {
                    if (!selectedCategory) {
                        row.style.display = '';
                    } else {
                        const categorySpan = row.querySelector('.inline-flex');
                        const rowCategory = categorySpan ? categorySpan.textContent.trim() : '';
                        
                        const categoryMap = {
                            'catalogs': 'الكتالوجات',
                            'brochures': 'البروشورات',
                            'manuals': 'الأدلة',
                            'certificates': 'الشهادات',
                            'other': 'أخرى'
                        };
                        
                        if (rowCategory === categoryMap[selectedCategory]) {
                            row.style.display = '';
                        } else {
                            row.style.display = 'none';
                        }
                    }
                });
            });
        }

        // Edit file buttons
        document.addEventListener('click', function(e) {
            if (e.target.closest('.edit-file-btn')) {
                const fileId = e.target.closest('.edit-file-btn').dataset.fileId;
                editFile(fileId);
            }
            
            if (e.target.closest('.delete-file-btn')) {
                const fileId = e.target.closest('.delete-file-btn').dataset.fileId;
                deleteFile(fileId);
            }
        });
    }

    // Utility functions for download center
    function editFile(fileId) {
        // Create edit modal or redirect to edit page
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';
        modal.innerHTML = `
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">تعديل الملف</h3>
                    <form id="edit-file-form">
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">عنوان الملف</label>
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md" value="كتالوج المنتجات 2024">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">الفئة</label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md">
                                    <option value="catalogs" selected>الكتالوجات</option>
                                    <option value="brochures">البروشورات</option>
                                    <option value="manuals">الأدلة</option>
                                    <option value="certificates">الشهادات</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                                <textarea rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-md">كتالوج شامل لجميع منتجاتنا</textarea>
                            </div>
                        </div>
                        <div class="flex justify-end space-x-2 space-x-reverse mt-6">
                            <button type="button" onclick="closeEditModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">إلغاء</button>
                            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">حفظ التغييرات</button>
                        </div>
                    </form>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Handle form submission
        modal.querySelector('#edit-file-form').addEventListener('submit', function(e) {
            e.preventDefault();
            showNotification('تم تحديث الملف بنجاح!', 'success');
            closeEditModal();
        });
    }

    function closeEditModal() {
        const modal = document.querySelector('.fixed.inset-0');
        if (modal) {
            modal.remove();
        }
    }

    function deleteFile(fileId) {
        if (confirm('هل أنت متأكد من حذف هذا الملف؟')) {
            showNotification('تم حذف الملف بنجاح!', 'success');
            // Remove row from table
            const row = document.querySelector(`[data-file-id="${fileId}"]`).closest('tr');
            if (row) {
                row.remove();
            }
        }
    }

    function downloadFile(fileId) {
        // Simulate download
        showNotification('بدء تحميل الملف...', 'info');
        // In real implementation, this would trigger the actual download
    }

    // Notification system
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        const bgColor = type === 'success' ? 'bg-green-500' : 
                       type === 'error' ? 'bg-red-500' : 
                       type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500';
        
        notification.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
});
</script>

<?php

// دالة محتوى تبويب الواتساب
function getWhatsappTabContent() {
    return '
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <form id="whatsapp-form" method="POST" action="contact.php">
            <input type="hidden" name="action" value="save_whatsapp">
            
            <div class="space-y-6">
                <!-- حالة التفعيل -->
                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">تفعيل الواتساب</h3>
                        <p class="text-sm text-gray-600">تفعيل أو إلغاء تفعيل خدمة الواتساب</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" name="whatsapp_active" value="1" class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[\'\'] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                    </label>
                </div>

                <!-- العنوان -->
                <div>
                    <label for="whatsapp_title" class="block text-sm font-medium text-gray-700 mb-2">العنوان</label>
                    <input type="text" id="whatsapp_title" name="whatsapp_title" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                           placeholder="أدخل عنوان قسم الواتساب">
                </div>

                <!-- رقم الواتساب -->
                <div>
                    <label for="whatsapp_number" class="block text-sm font-medium text-gray-700 mb-2">رقم الواتساب</label>
                    <input type="tel" id="whatsapp_number" name="whatsapp_number" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                           placeholder="مثال: +966501234567">
                    <p class="mt-1 text-sm text-gray-500">يرجى إدخال الرقم مع رمز الدولة (مثال: +966501234567)</p>
                </div>

                <!-- رسالة افتراضية -->
                <div>
                    <label for="whatsapp_message" class="block text-sm font-medium text-gray-700 mb-2">الرسالة الافتراضية</label>
                    <textarea id="whatsapp_message" name="whatsapp_message" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                              placeholder="أدخل الرسالة الافتراضية التي ستظهر عند النقر على رابط الواتساب"></textarea>
                </div>

                <!-- ساعات العمل -->
                <div>
                    <label for="whatsapp_hours" class="block text-sm font-medium text-gray-700 mb-2">ساعات العمل</label>
                    <input type="text" id="whatsapp_hours" name="whatsapp_hours" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                           placeholder="مثال: من 9 صباحاً إلى 5 مساءً">
                </div>

                <!-- معاينة -->
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h4 class="text-sm font-medium text-green-800 mb-2">معاينة رابط الواتساب</h4>
                    <div id="whatsapp_preview" class="space-y-2">
                        <a href="#" class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200">
                            <i class="fab fa-whatsapp mr-2"></i>
                            تواصل معنا عبر الواتساب
                        </a>
                        <p class="text-sm text-green-700">الرقم: <span id="preview_number">لم يتم تحديد رقم بعد</span></p>
                        <p class="text-sm text-green-700">ساعات العمل: <span id="preview_hours">لم يتم تحديد ساعات العمل بعد</span></p>
                    </div>
                </div>

                <!-- زر الحفظ -->
                <div class="flex justify-end pt-4 border-t border-gray-200">
                    <button type="submit" class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors duration-200">
                        <i class="fas fa-save mr-2"></i>
                        حفظ التغييرات
                    </button>
                </div>
            </div>
        </form>
    </div>';
}

// دالة محتوى تبويب الخريطة
function getMapTabContent($contactInfo) {
    $data = $contactInfo['map_embed']['data_parsed'] ?? [];
    
    ob_start();
    ?>
    <form method="POST" class="space-y-6">
        <input type="hidden" name="section_key" value="map_embed">
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label for="title" class="block text-sm font-medium text-gray-700 mb-2">عنوان القسم</label>
                <input type="text" id="title" name="title" value="<?php echo htmlspecialchars($contactInfo['map_embed']['title'] ?? 'الخريطة'); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
            </div>
            
            <div>
                <label for="content" class="block text-sm font-medium text-gray-700 mb-2">وصف الخريطة</label>
                <input type="text" id="content" name="content" value="<?php echo htmlspecialchars($contactInfo['map_embed']['content'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            
            <div class="md:col-span-2">
                <label for="embed_code" class="block text-sm font-medium text-gray-700 mb-2">كود تضمين الخريطة</label>
                <textarea id="embed_code" name="data[embed_code]" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="أدخل كود تضمين الخريطة من Google Maps"><?php echo htmlspecialchars($data['embed_code'] ?? ''); ?></textarea>
            </div>
            
            <div>
                <label for="latitude" class="block text-sm font-medium text-gray-700 mb-2">خط العرض</label>
                <input type="number" step="any" id="latitude" name="data[latitude]" value="<?php echo $data['latitude'] ?? ''; ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            
            <div>
                <label for="longitude" class="block text-sm font-medium text-gray-700 mb-2">خط الطول</label>
                <input type="number" step="any" id="longitude" name="data[longitude]" value="<?php echo $data['longitude'] ?? ''; ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
        </div>
        
        <div class="flex items-center">
            <input type="checkbox" id="is_active" name="is_active" <?php echo ($contactInfo['map_embed']['is_active'] ?? 1) ? 'checked' : ''; ?> class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
            <label for="is_active" class="mr-2 block text-sm text-gray-900">فعال</label>
        </div>
        
        <div class="flex justify-end">
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                <i class="fas fa-save mr-2"></i>حفظ التغييرات
            </button>
        </div>
    </form>
    <?php
    return ob_get_clean();
}

// دالة محتوى تبويب وسائل التواصل الاجتماعي
function getSocialMediaTabContent($contactInfo) {
    $data = $contactInfo['social_media']['data_parsed'] ?? [];
    
    ob_start();
    ?>
    <form method="POST" class="space-y-6">
        <input type="hidden" name="section_key" value="social_media">
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label for="title" class="block text-sm font-medium text-gray-700 mb-2">عنوان القسم</label>
                <input type="text" id="title" name="title" value="<?php echo htmlspecialchars($contactInfo['social_media']['title'] ?? 'وسائل التواصل الاجتماعي'); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
            </div>
            
            <div>
                <label for="content" class="block text-sm font-medium text-gray-700 mb-2">وصف القسم</label>
                <input type="text" id="content" name="content" value="<?php echo htmlspecialchars($contactInfo['social_media']['content'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            
            <div>
                <label for="facebook" class="block text-sm font-medium text-gray-700 mb-2">فيسبوك</label>
                <input type="url" id="facebook" name="data[facebook]" value="<?php echo htmlspecialchars($data['facebook'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="https://facebook.com/yourpage">
            </div>
            
            <div>
                <label for="twitter" class="block text-sm font-medium text-gray-700 mb-2">تويتر</label>
                <input type="url" id="twitter" name="data[twitter]" value="<?php echo htmlspecialchars($data['twitter'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="https://twitter.com/yourhandle">
            </div>
            
            <div>
                <label for="instagram" class="block text-sm font-medium text-gray-700 mb-2">إنستغرام</label>
                <input type="url" id="instagram" name="data[instagram]" value="<?php echo htmlspecialchars($data['instagram'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="https://instagram.com/yourhandle">
            </div>
            
            <div>
                <label for="linkedin" class="block text-sm font-medium text-gray-700 mb-2">لينكد إن</label>
                <input type="url" id="linkedin" name="data[linkedin]" value="<?php echo htmlspecialchars($data['linkedin'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="https://linkedin.com/company/yourcompany">
            </div>
            
            <div>
                <label for="youtube" class="block text-sm font-medium text-gray-700 mb-2">يوتيوب</label>
                <input type="url" id="youtube" name="data[youtube]" value="<?php echo htmlspecialchars($data['youtube'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="https://youtube.com/channel/yourchannel">
            </div>
            
            <div>
                <label for="snapchat" class="block text-sm font-medium text-gray-700 mb-2">سناب شات</label>
                <input type="url" id="snapchat" name="data[snapchat]" value="<?php echo htmlspecialchars($data['snapchat'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="https://snapchat.com/add/yourusername">
            </div>
        </div>
        
        <div class="flex items-center">
            <input type="checkbox" id="is_active" name="is_active" <?php echo ($contactInfo['social_media']['is_active'] ?? 1) ? 'checked' : ''; ?> class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
            <label for="is_active" class="mr-2 block text-sm text-gray-900">فعال</label>
        </div>
        
        <div class="flex justify-end">
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                <i class="fas fa-save mr-2"></i>حفظ التغييرات
            </button>
        </div>
    </form>
    <?php
    return ob_get_clean();
}

// دالة محتوى تبويب معلومات الشركة
function getCompanyInfoTabContent($contactInfo) {
    $data = $contactInfo['company_info']['data_parsed'] ?? [];
    
    ob_start();
    ?>
    <form method="POST" class="space-y-6">
        <input type="hidden" name="section_key" value="company_info">
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label for="title" class="block text-sm font-medium text-gray-700 mb-2">عنوان القسم</label>
                <input type="text" id="title" name="title" value="<?php echo htmlspecialchars($contactInfo['company_info']['title'] ?? 'معلومات الشركة'); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
            </div>
            
            <div>
                <label for="content" class="block text-sm font-medium text-gray-700 mb-2">وصف مختصر</label>
                <input type="text" id="content" name="content" value="<?php echo htmlspecialchars($contactInfo['company_info']['content'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            
            <div>
                <label for="company_name" class="block text-sm font-medium text-gray-700 mb-2">اسم الشركة</label>
                <input type="text" id="company_name" name="data[company_name]" value="<?php echo htmlspecialchars($data['company_name'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            
            <div>
                <label for="commercial_register" class="block text-sm font-medium text-gray-700 mb-2">السجل التجاري</label>
                <input type="text" id="commercial_register" name="data[commercial_register]" value="<?php echo htmlspecialchars($data['commercial_register'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            
            <div>
                <label for="tax_number" class="block text-sm font-medium text-gray-700 mb-2">الرقم الضريبي</label>
                <input type="text" id="tax_number" name="data[tax_number]" value="<?php echo htmlspecialchars($data['tax_number'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            
            <div>
                <label for="establishment_year" class="block text-sm font-medium text-gray-700 mb-2">سنة التأسيس</label>
                <input type="number" id="establishment_year" name="data[establishment_year]" value="<?php echo $data['establishment_year'] ?? ''; ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" min="1900" max="<?php echo date('Y'); ?>">
            </div>
            
            <div class="md:col-span-2">
                <label for="about_company" class="block text-sm font-medium text-gray-700 mb-2">نبذة عن الشركة</label>
                <textarea id="about_company" name="data[about_company]" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="نبذة مختصرة عن الشركة وأنشطتها"><?php echo htmlspecialchars($data['about_company'] ?? ''); ?></textarea>
            </div>
            
            <div class="md:col-span-2">
                <label for="vision" class="block text-sm font-medium text-gray-700 mb-2">الرؤية</label>
                <textarea id="vision" name="data[vision]" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="رؤية الشركة"><?php echo htmlspecialchars($data['vision'] ?? ''); ?></textarea>
            </div>
            
            <div class="md:col-span-2">
                <label for="mission" class="block text-sm font-medium text-gray-700 mb-2">الرسالة</label>
                <textarea id="mission" name="data[mission]" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="رسالة الشركة"><?php echo htmlspecialchars($data['mission'] ?? ''); ?></textarea>
            </div>
        </div>
        
        <div class="flex items-center">
            <input type="checkbox" id="is_active" name="is_active" <?php echo ($contactInfo['company_info']['is_active'] ?? 1) ? 'checked' : ''; ?> class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
            <label for="is_active" class="mr-2 block text-sm text-gray-900">فعال</label>
        </div>
        
        <div class="flex justify-end">
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                <i class="fas fa-save mr-2"></i>حفظ التغييرات
            </button>
        </div>
    </form>
    <?php
    return ob_get_clean();
}

// دالة محتوى تبويب مركز التحميل
function getDownloadCenterTabContent() {
    return '
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <!-- إضافة ملف جديد -->
        <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 class="text-lg font-medium text-blue-900 mb-4">إضافة ملف جديد</h3>
            <form id="add-file-form" method="POST" action="contact.php" enctype="multipart/form-data" class="space-y-4">
                <input type="hidden" name="action" value="add_download_file">
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="file_title" class="block text-sm font-medium text-gray-700 mb-2">عنوان الملف</label>
                        <input type="text" id="file_title" name="file_title" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="أدخل عنوان الملف">
                    </div>
                    
                    <div>
                        <label for="file_category" class="block text-sm font-medium text-gray-700 mb-2">الفئة</label>
                        <select id="file_category" name="file_category" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">اختر الفئة</option>
                            <option value="catalogs">الكتالوجات</option>
                            <option value="brochures">البروشورات</option>
                            <option value="manuals">الأدلة</option>
                            <option value="certificates">الشهادات</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>
                </div>
                
                <div>
                    <label for="file_description" class="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                    <textarea id="file_description" name="file_description" rows="2"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              placeholder="وصف مختصر للملف"></textarea>
                </div>
                
                <div>
                    <label for="file_upload" class="block text-sm font-medium text-gray-700 mb-2">الملف</label>
                    <input type="file" id="file_upload" name="file_upload" required
                           accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.zip,.rar"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <p class="mt-1 text-sm text-gray-500">الملفات المدعومة: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, ZIP, RAR</p>
                </div>
                
                <div class="flex justify-end">
                    <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200">
                        <i class="fas fa-upload mr-2"></i>
                        رفع الملف
                    </button>
                </div>
            </form>
        </div>

        <!-- قائمة الملفات -->
        <div>
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">الملفات المتاحة</h3>
                <div class="flex space-x-2 space-x-reverse">
                    <select id="filter_category" class="px-3 py-2 border border-gray-300 rounded-md text-sm">
                        <option value="">جميع الفئات</option>
                        <option value="catalogs">الكتالوجات</option>
                        <option value="brochures">البروشورات</option>
                        <option value="manuals">الأدلة</option>
                        <option value="certificates">الشهادات</option>
                        <option value="other">أخرى</option>
                    </select>
                </div>
            </div>

            <!-- جدول الملفات -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">العنوان</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الفئة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">حجم الملف</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الرفع</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">عدد التحميلات</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="files-table-body" class="bg-white divide-y divide-gray-200">
                        <!-- مثال على ملف -->
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <i class="fas fa-file-pdf text-red-500 mr-3"></i>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">كتالوج المنتجات 2024</div>
                                        <div class="text-sm text-gray-500">كتالوج شامل لجميع منتجاتنا</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                    الكتالوجات
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2.5 ميجابايت</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-01-15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">156</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2 space-x-reverse">
                                    <button class="text-blue-600 hover:text-blue-900 edit-file-btn" data-file-id="1" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-green-600 hover:text-green-900" onclick="downloadFile(1)" title="تحميل">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="text-red-600 hover:text-red-900 delete-file-btn" data-file-id="1" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <i class="fas fa-file-word text-blue-500 mr-3"></i>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">دليل المستخدم</div>
                                        <div class="text-sm text-gray-500">دليل شامل لاستخدام المنتجات</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                    الأدلة
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1.8 ميجابايت</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-01-10</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">89</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2 space-x-reverse">
                                    <button class="text-blue-600 hover:text-blue-900 edit-file-btn" data-file-id="2" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-green-600 hover:text-green-900" onclick="downloadFile(2)" title="تحميل">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="text-red-600 hover:text-red-900 delete-file-btn" data-file-id="2" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- زر حفظ إعدادات مركز التحميل -->
        <div class="mt-6 pt-6 border-t border-gray-200">
            <form method="POST" class="space-y-4">
                <input type="hidden" name="section_key" value="download_center">
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="download_title" class="block text-sm font-medium text-gray-700 mb-2">عنوان القسم</label>
                        <input type="text" id="download_title" name="title" value="مركز التحميل" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                    </div>
                    
                    <div>
                        <label for="max_file_size" class="block text-sm font-medium text-gray-700 mb-2">الحد الأقصى لحجم الملف (ميجابايت)</label>
                        <input type="number" id="max_file_size" name="data[max_file_size]" value="10" min="1" max="100"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                
                <div>
                    <label for="download_description" class="block text-sm font-medium text-gray-700 mb-2">وصف القسم</label>
                    <textarea id="download_description" name="content" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              placeholder="وصف مختصر لمركز التحميل">تحميل الكتالوجات والأدلة والمستندات المهمة</textarea>
                </div>
                
                <div class="flex items-center">
                    <input type="checkbox" id="download_is_active" name="is_active" checked 
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="download_is_active" class="mr-2 block text-sm text-gray-900">فعال</label>
                </div>
                
                <div class="flex justify-end">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                        <i class="fas fa-save mr-2"></i>حفظ إعدادات مركز التحميل
                    </button>
                </div>
            </form>
        </div>
    </div>';
}

endLayout(); ?>