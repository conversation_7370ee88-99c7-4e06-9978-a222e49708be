<?php
require_once 'config/config.php';

echo "<h1>تشخيص مشكلة الصفحات القانونية</h1>";

// التحقق من الاتصال بقاعدة البيانات
echo "<h2>1. اختبار الاتصال بقاعدة البيانات:</h2>";
try {
    if (isset($database)) {
        echo "✅ كائن قاعدة البيانات موجود<br>";
        
        // اختبار الاتصال
        $connection = $database->getConnection();
        if ($connection) {
            echo "✅ الاتصال بقاعدة البيانات نجح<br>";
        } else {
            echo "❌ فشل الاتصال بقاعدة البيانات<br>";
        }
    } else {
        echo "❌ كائن قاعدة البيانات غير موجود<br>";
    }
} catch (Exception $e) {
    echo "❌ خطأ في الاتصال: " . $e->getMessage() . "<br>";
}

// التحقق من وجود جدول legal_pages
echo "<h2>2. التحقق من جدول legal_pages:</h2>";
try {
    $tables = $database->query("SHOW TABLES LIKE 'legal_pages'");
    if ($tables && count($tables) > 0) {
        echo "✅ جدول legal_pages موجود<br>";
        
        // عرض هيكل الجدول
        echo "<h3>هيكل الجدول:</h3>";
        $structure = $database->query("DESCRIBE legal_pages");
        if ($structure) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
            foreach ($structure as $column) {
                echo "<tr>";
                echo "<td>" . $column['Field'] . "</td>";
                echo "<td>" . $column['Type'] . "</td>";
                echo "<td>" . $column['Null'] . "</td>";
                echo "<td>" . $column['Key'] . "</td>";
                echo "<td>" . $column['Default'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "❌ جدول legal_pages غير موجود<br>";
    }
} catch (Exception $e) {
    echo "❌ خطأ في فحص الجدول: " . $e->getMessage() . "<br>";
}

// التحقق من البيانات الموجودة
echo "<h2>3. البيانات الموجودة في الجدول:</h2>";
try {
    $allData = $database->query("SELECT * FROM legal_pages");
    if ($allData && count($allData) > 0) {
        echo "✅ يوجد " . count($allData) . " سجل في الجدول<br>";
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr><th>ID</th><th>نوع الصفحة</th><th>العنوان</th><th>نشط</th><th>تاريخ الإنشاء</th><th>تاريخ التحديث</th></tr>";
        foreach ($allData as $row) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . $row['page_type'] . "</td>";
            echo "<td>" . htmlspecialchars(substr($row['title'], 0, 50)) . "</td>";
            echo "<td>" . ($row['is_active'] ? 'نعم' : 'لا') . "</td>";
            echo "<td>" . $row['created_at'] . "</td>";
            echo "<td>" . $row['updated_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "❌ لا توجد بيانات في الجدول<br>";
    }
} catch (Exception $e) {
    echo "❌ خطأ في جلب البيانات: " . $e->getMessage() . "<br>";
}

// اختبار جلب البيانات لكل صفحة
echo "<h2>4. اختبار جلب البيانات لكل صفحة:</h2>";

$pages = ['privacy', 'terms', 'sitemap'];
foreach ($pages as $pageType) {
    echo "<h3>صفحة $pageType:</h3>";
    try {
        $pageData = $database->fetch("SELECT * FROM legal_pages WHERE page_type = '$pageType' AND is_active = 1");
        if ($pageData) {
            echo "✅ تم جلب البيانات بنجاح<br>";
            echo "العنوان: " . htmlspecialchars($pageData['title']) . "<br>";
            echo "طول المحتوى: " . strlen($pageData['content']) . " حرف<br>";
            echo "آخر تحديث: " . $pageData['updated_at'] . "<br>";
        } else {
            echo "❌ لم يتم العثور على بيانات نشطة<br>";
            
            // البحث عن بيانات غير نشطة
            $inactiveData = $database->fetch("SELECT * FROM legal_pages WHERE page_type = '$pageType'");
            if ($inactiveData) {
                echo "⚠️ توجد بيانات لكنها غير نشطة (is_active = " . $inactiveData['is_active'] . ")<br>";
            } else {
                echo "❌ لا توجد بيانات على الإطلاق لهذا النوع<br>";
            }
        }
    } catch (Exception $e) {
        echo "❌ خطأ في جلب البيانات: " . $e->getMessage() . "<br>";
    }
    echo "<hr>";
}

// اختبار إدراج بيانات تجريبية إذا لم توجد
echo "<h2>5. إضافة بيانات تجريبية إذا لم توجد:</h2>";
try {
    $existingCount = $database->fetchOne("SELECT COUNT(*) as count FROM legal_pages");
    if ($existingCount['count'] == 0) {
        echo "لا توجد بيانات، سيتم إضافة بيانات تجريبية...<br>";
        
        // إدراج بيانات تجريبية
        $sampleData = [
            [
                'page_type' => 'privacy',
                'title' => 'سياسة الخصوصية',
                'content' => '<h2>سياسة الخصوصية</h2><p>هذا محتوى تجريبي لسياسة الخصوصية.</p>',
                'meta_description' => 'سياسة الخصوصية لموقع الخط الأخضر',
                'meta_keywords' => 'خصوصية، حماية البيانات'
            ],
            [
                'page_type' => 'terms',
                'title' => 'الشروط والأحكام',
                'content' => '<h2>الشروط والأحكام</h2><p>هذا محتوى تجريبي للشروط والأحكام.</p>',
                'meta_description' => 'الشروط والأحكام لموقع الخط الأخضر',
                'meta_keywords' => 'شروط، أحكام، استخدام'
            ],
            [
                'page_type' => 'sitemap',
                'title' => 'خريطة الموقع',
                'content' => '<h2>خريطة الموقع</h2><p>هذا محتوى تجريبي لخريطة الموقع.</p>',
                'meta_description' => 'خريطة الموقع لموقع الخط الأخضر',
                'meta_keywords' => 'خريطة، فهرس، صفحات'
            ]
        ];
        
        foreach ($sampleData as $data) {
            $result = $database->insert('legal_pages', $data);
            if ($result) {
                echo "✅ تم إدراج بيانات " . $data['page_type'] . "<br>";
            } else {
                echo "❌ فشل إدراج بيانات " . $data['page_type'] . "<br>";
            }
        }
    } else {
        echo "يوجد " . $existingCount['count'] . " سجل في الجدول<br>";
    }
} catch (Exception $e) {
    echo "❌ خطأ في إضافة البيانات: " . $e->getMessage() . "<br>";
}

echo "<h2>6. اختبار نهائي:</h2>";
echo "<p>يمكنك الآن اختبار الصفحات:</p>";
echo "<ul>";
echo "<li><a href='privacy.php' target='_blank'>صفحة سياسة الخصوصية</a></li>";
echo "<li><a href='terms.php' target='_blank'>صفحة الشروط والأحكام</a></li>";
echo "<li><a href='sitemap.php' target='_blank'>صفحة خريطة الموقع</a></li>";
echo "</ul>";
?>