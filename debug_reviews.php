<?php
require_once 'config/config.php';
require_once 'includes/functions.php';

// الجلسة تبدأ تلقائياً في config.php

// محاكاة تسجيل دخول المستخدم
if (!isLoggedIn()) {
    $_SESSION['user_id'] = 1;
    $_SESSION['user_name'] = 'Test User';
    $_SESSION['user_email'] = '<EMAIL>';
}

$pageTitle = 'تشخيص مشكلة التقييمات في صفحة المنتج';

// معالجة اختبار التقييم
$test_result = '';
if ($_POST && isset($_POST['test_product_review'])) {
    $product_id = 8;
    $rating = 5;
    $title = 'تقييم تجريبي من صفحة التشخيص';
    $comment = 'هذا تقييم تجريبي لاختبار النظام - ' . date('Y-m-d H:i:s');
    
    // اختبار مباشر لدالة saveProductReview
    $result = saveProductReview($product_id, $rating, $title, $comment);
    
    if ($result === true) {
        $test_result = '<div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            ✅ تم حفظ التقييم بنجاح!
        </div>';
    } else {
        $error_msg = is_string($result) ? $result : 'حدث خطأ غير محدد';
        $test_result = '<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            ❌ فشل في حفظ التقييم: ' . htmlspecialchars($error_msg) . '
        </div>';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            <h1 class="text-3xl font-bold text-center mb-8"><?php echo $pageTitle; ?></h1>
            
            <?php echo $test_result; ?>
            
            <!-- معلومات النظام -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                
                <!-- حالة قاعدة البيانات -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold mb-4">حالة قاعدة البيانات</h2>
                    
                    <?php
                    try {
                        global $database;
                        $pdo = $database->getConnection();
                        
                        // فحص جدول التقييمات
                        $stmt = $pdo->query("DESCRIBE reviews");
                        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                        
                        echo '<div class="mb-4">';
                        echo '<h3 class="font-medium text-green-600">✅ الاتصال بقاعدة البيانات: نجح</h3>';
                        echo '<h3 class="font-medium text-green-600">✅ جدول reviews موجود</h3>';
                        echo '<p class="text-sm text-gray-600">الأعمدة: ' . implode(', ', $columns) . '</p>';
                        echo '</div>';
                        
                        // عدد التقييمات
                        $stmt = $pdo->query("SELECT COUNT(*) FROM reviews");
                        $total_reviews = $stmt->fetchColumn();
                        
                        $stmt = $pdo->query("SELECT COUNT(*) FROM reviews WHERE is_approved = 1");
                        $approved_reviews = $stmt->fetchColumn();
                        
                        echo '<div class="text-sm">';
                        echo '<p>إجمالي التقييمات: ' . $total_reviews . '</p>';
                        echo '<p>التقييمات المعتمدة: ' . $approved_reviews . '</p>';
                        echo '</div>';
                        
                    } catch (Exception $e) {
                        echo '<div class="text-red-600">';
                        echo '<h3 class="font-medium">❌ خطأ في قاعدة البيانات:</h3>';
                        echo '<p class="text-sm">' . htmlspecialchars($e->getMessage()) . '</p>';
                        echo '</div>';
                    }
                    ?>
                </div>
                
                <!-- إعدادات النظام -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold mb-4">إعدادات النظام</h2>
                    
                    <?php
                    $auto_approval = getSetting('reviews_auto_approval', false);
                    $auto_approval_raw = null;
                    
                    try {
                        global $database;
                        $pdo = $database->getConnection();
                        $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
                        $stmt->execute(['reviews_auto_approval']);
                        $auto_approval_raw = $stmt->fetchColumn();
                    } catch (Exception $e) {
                        // تجاهل الخطأ
                    }
                    ?>
                    
                    <div class="space-y-2 text-sm">
                        <p><strong>التفعيل التلقائي (getSetting):</strong> 
                            <span class="<?php echo $auto_approval ? 'text-green-600' : 'text-red-600'; ?>">
                                <?php echo $auto_approval ? 'مفعل' : 'معطل'; ?>
                            </span>
                        </p>
                        <p><strong>القيمة الخام في قاعدة البيانات:</strong> 
                            <?php echo $auto_approval_raw !== false ? htmlspecialchars($auto_approval_raw) : 'غير موجود'; ?>
                        </p>
                        <p><strong>حالة تسجيل الدخول:</strong> 
                            <span class="text-green-600">✅ مسجل الدخول</span>
                        </p>
                        <p><strong>معرف المستخدم:</strong> <?php echo $_SESSION['user_id']; ?></p>
                        <p><strong>اسم المستخدم:</strong> <?php echo $_SESSION['user_name']; ?></p>
                    </div>
                </div>
            </div>
            
            <!-- اختبار دالة saveProductReview -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">اختبار دالة saveProductReview</h2>
                
                <form method="POST" class="mb-4">
                    <button type="submit" name="test_product_review" 
                            class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg">
                        اختبار حفظ تقييم جديد
                    </button>
                </form>
                
                <div class="text-sm text-gray-600">
                    <p>سيتم اختبار حفظ تقييم للمنتج رقم 8 باستخدام دالة saveProductReview مباشرة</p>
                </div>
            </div>
            
            <!-- آخر التقييمات للمنتج 8 -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">آخر التقييمات للمنتج 8</h2>
                
                <?php
                try {
                    global $database;
                    $pdo = $database->getConnection();
                    $stmt = $pdo->prepare("
                        SELECT r.*, u.name as user_name 
                        FROM reviews r 
                        LEFT JOIN users u ON r.user_id = u.id 
                        WHERE r.product_id = ? 
                        ORDER BY r.created_at DESC 
                        LIMIT 10
                    ");
                    $stmt->execute([8]);
                    $reviews = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    if (!empty($reviews)):
                ?>
                    <div class="overflow-x-auto">
                        <table class="min-w-full table-auto">
                            <thead>
                                <tr class="bg-gray-50">
                                    <th class="px-4 py-2 text-right">المستخدم</th>
                                    <th class="px-4 py-2 text-right">التقييم</th>
                                    <th class="px-4 py-2 text-right">التعليق</th>
                                    <th class="px-4 py-2 text-right">الحالة</th>
                                    <th class="px-4 py-2 text-right">التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($reviews as $review): ?>
                                    <tr class="border-b">
                                        <td class="px-4 py-2">
                                            <?php echo htmlspecialchars($review['user_name'] ?: (isset($review['guest_name']) ? $review['guest_name'] : 'غير محدد')); ?>
                                        </td>
                                        <td class="px-4 py-2">
                                            <?php echo str_repeat('⭐', $review['rating']); ?>
                                        </td>
                                        <td class="px-4 py-2">
                                            <?php echo htmlspecialchars(substr($review['comment'], 0, 50)) . '...'; ?>
                                        </td>
                                        <td class="px-4 py-2">
                                            <span class="px-2 py-1 text-xs rounded <?php echo $review['is_approved'] ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'; ?>">
                                                <?php echo $review['is_approved'] ? 'معتمد' : 'في الانتظار'; ?>
                                            </span>
                                        </td>
                                        <td class="px-4 py-2 text-sm">
                                            <?php echo $review['created_at']; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <p class="text-gray-500">لا توجد تقييمات للمنتج 8</p>
                <?php endif; ?>
                
                <?php
                } catch (Exception $e) {
                    echo '<div class="text-red-600">';
                    echo '<p>خطأ في جلب التقييمات: ' . htmlspecialchars($e->getMessage()) . '</p>';
                    echo '</div>';
                }
                ?>
            </div>
            
            <!-- فحص دالة saveProductReview -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">فحص كود دالة saveProductReview</h2>
                
                <?php
                $function_file = __DIR__ . '/includes/functions.php';
                if (file_exists($function_file)) {
                    $content = file_get_contents($function_file);
                    
                    // البحث عن دالة saveProductReview
                    $pattern = '/function\s+saveProductReview\s*\([^}]+\}/s';
                    if (preg_match($pattern, $content, $matches)) {
                        echo '<div class="bg-gray-50 p-4 rounded text-sm overflow-x-auto">';
                        echo '<pre>' . htmlspecialchars($matches[0]) . '</pre>';
                        echo '</div>';
                    } else {
                        echo '<p class="text-red-600">لم يتم العثور على دالة saveProductReview</p>';
                    }
                } else {
                    echo '<p class="text-red-600">ملف functions.php غير موجود</p>';
                }
                ?>
            </div>
            
            <!-- روابط مفيدة -->
            <div class="text-center space-x-4 space-x-reverse">
                <a href="<?php echo SITE_URL; ?>/products/8" 
                   class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded inline-block">
                    صفحة المنتج 8
                </a>
                <a href="<?php echo SITE_URL; ?>/test_api_review.php" 
                   class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded inline-block">
                    اختبار API
                </a>
                <a href="<?php echo SITE_URL; ?>/admin/reviews.php" 
                   class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded inline-block">
                    إدارة التقييمات
                </a>
                <a href="<?php echo SITE_URL; ?>/test_auto_approval_status.php" 
                   class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded inline-block">
                   حالة التفعيل التلقائي
                </a>
            </div>
        </div>
    </div>
</body>
</html>