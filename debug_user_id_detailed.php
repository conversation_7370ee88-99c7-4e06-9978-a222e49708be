<?php
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// محاكاة تسجيل دخول المستخدم
if (!isLoggedIn()) {
    $_SESSION['user_id'] = 1;
    $_SESSION['user_name'] = 'أحمد محمد';
    $_SESSION['user_email'] = '<EMAIL>';
    $_SESSION['logged_in'] = true;
}

$debug_info = [];
$test_result = '';

// اختبار حفظ تقييم مع تتبع مفصل
if ($_POST && isset($_POST['test_save'])) {
    $debug_info[] = "=== بدء اختبار حفظ التقييم ===";
    
    // التحقق من حالة تسجيل الدخول
    $isLoggedIn = isLoggedIn();
    $currentUser = $isLoggedIn ? getCurrentUser() : null;
    
    $debug_info[] = "حالة تسجيل الدخول: " . ($isLoggedIn ? 'مسجل' : 'غير مسجل');
    if ($currentUser) {
        $debug_info[] = "بيانات المستخدم: " . json_encode($currentUser, JSON_UNESCAPED_UNICODE);
    }
    
    // إعداد البيانات
    $product_id = 1;
    $rating = 5;
    $review_text = 'تقييم تجريبي مفصل - ' . date('Y-m-d H:i:s');
    $review_title = 'تقييم ممتاز';
    
    if ($isLoggedIn) {
        $user_id = $currentUser['id'];
        $customer_name = $currentUser['name'];
        $customer_email = $currentUser['email'];
        
        $debug_info[] = "معرف المستخدم: " . $user_id;
        $debug_info[] = "اسم المستخدم: " . $customer_name;
        $debug_info[] = "بريد المستخدم: " . $customer_email;
        
        // محاولة حفظ التقييم
        $debug_info[] = "=== محاولة حفظ تقييم المستخدم المسجل ===";
        $result = saveUserReview($product_id, $user_id, $customer_name, $customer_email, $rating, $review_text, $review_title);
        
        $debug_info[] = "نتيجة الحفظ: " . json_encode($result, JSON_UNESCAPED_UNICODE);
        
        if (is_numeric($result)) {
            $debug_info[] = "تم حفظ التقييم بنجاح - معرف التقييم: " . $result;
            
            // التحقق من البيانات المحفوظة
            global $database;
            $saved_review = $database->fetch(
                "SELECT * FROM reviews WHERE id = :id",
                ['id' => $result]
            );
            
            if ($saved_review) {
                $debug_info[] = "=== البيانات المحفوظة في قاعدة البيانات ===";
                $debug_info[] = "معرف التقييم: " . $saved_review['id'];
                $debug_info[] = "معرف المنتج: " . $saved_review['product_id'];
                $debug_info[] = "معرف المستخدم: " . ($saved_review['user_id'] ?? 'NULL');
                $debug_info[] = "اسم المستخدم: " . $saved_review['name'];
                $debug_info[] = "بريد المستخدم: " . $saved_review['email'];
                $debug_info[] = "التقييم: " . $saved_review['rating'];
                $debug_info[] = "العنوان: " . $saved_review['title'];
                $debug_info[] = "النص: " . $saved_review['comment'];
                $debug_info[] = "معتمد: " . ($saved_review['is_approved'] ? 'نعم' : 'لا');
                $debug_info[] = "تاريخ الإنشاء: " . $saved_review['created_at'];
                
                $test_result = 'success';
            } else {
                $debug_info[] = "خطأ: لم يتم العثور على التقييم المحفوظ!";
                $test_result = 'error';
            }
        } else {
            $debug_info[] = "فشل في حفظ التقييم: " . $result;
            $test_result = 'error';
        }
    } else {
        $debug_info[] = "المستخدم غير مسجل دخول";
        $test_result = 'error';
    }
}

// عرض آخر 5 تقييمات من قاعدة البيانات
global $database;
$recent_reviews = $database->fetchAll(
    "SELECT r.*, p.name as product_name 
     FROM reviews r 
     LEFT JOIN products p ON r.product_id = p.id 
     ORDER BY r.created_at DESC 
     LIMIT 5"
);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مفصل لحفظ user_id</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .debug-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
        }
        .debug-log div {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .null-value {
            color: #dc3545;
            font-style: italic;
        }
        .user-id-highlight {
            background: #fff3cd;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>تشخيص مفصل لحفظ user_id في التقييمات</h1>
        
        <!-- معلومات المستخدم الحالي -->
        <div class="card">
            <h2>حالة المستخدم الحالي</h2>
            <?php if (isLoggedIn()): ?>
                <?php $user = getCurrentUser(); ?>
                <div class="success" style="padding: 15px; border-radius: 4px;">
                    <p><strong>✅ مسجل الدخول:</strong> نعم</p>
                    <p><strong>المعرف:</strong> <?php echo $user['id']; ?></p>
                    <p><strong>الاسم:</strong> <?php echo $user['name']; ?></p>
                    <p><strong>البريد:</strong> <?php echo $user['email']; ?></p>
                    <p><strong>بيانات الجلسة:</strong></p>
                    <pre><?php print_r($_SESSION); ?></pre>
                </div>
            <?php else: ?>
                <div class="error" style="padding: 15px; border-radius: 4px;">
                    <p><strong>❌ مسجل الدخول:</strong> لا</p>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- اختبار حفظ التقييم -->
        <div class="card">
            <h2>اختبار حفظ التقييم</h2>
            <form method="POST">
                <input type="hidden" name="test_save" value="1">
                <button type="submit">اختبار حفظ تقييم جديد</button>
            </form>
            
            <?php if (!empty($debug_info)): ?>
            <div class="debug-log <?php echo $test_result; ?>" style="margin-top: 20px;">
                <h3>سجل التشخيص:</h3>
                <?php foreach ($debug_info as $info): ?>
                    <div><?php echo htmlspecialchars($info); ?></div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>
        
        <!-- آخر التقييمات -->
        <div class="card">
            <h2>آخر 5 تقييمات في قاعدة البيانات</h2>
            <?php if (!empty($recent_reviews)): ?>
            <table>
                <thead>
                    <tr>
                        <th>معرف التقييم</th>
                        <th>معرف المنتج</th>
                        <th>اسم المنتج</th>
                        <th class="user-id-highlight">معرف المستخدم</th>
                        <th>اسم المستخدم</th>
                        <th>البريد</th>
                        <th>التقييم</th>
                        <th>معتمد</th>
                        <th>التاريخ</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recent_reviews as $review): ?>
                    <tr>
                        <td><?php echo $review['id']; ?></td>
                        <td><?php echo $review['product_id']; ?></td>
                        <td><?php echo $review['product_name'] ?? 'غير محدد'; ?></td>
                        <td class="user-id-highlight">
                            <?php 
                            if ($review['user_id'] === null) {
                                echo '<span class="null-value">NULL</span>';
                            } else {
                                echo $review['user_id'];
                            }
                            ?>
                        </td>
                        <td><?php echo $review['name']; ?></td>
                        <td><?php echo $review['email']; ?></td>
                        <td><?php echo $review['rating']; ?>/5</td>
                        <td><?php echo $review['is_approved'] ? '✅' : '❌'; ?></td>
                        <td><?php echo $review['created_at']; ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            <?php else: ?>
            <div class="warning" style="padding: 15px; border-radius: 4px;">
                <p>لا توجد تقييمات في قاعدة البيانات</p>
            </div>
            <?php endif; ?>
        </div>
        
        <!-- اختبار مباشر للدوال -->
        <div class="card">
            <h2>اختبار مباشر للدوال</h2>
            <div class="debug-log">
                <h3>اختبار دالة isLoggedIn():</h3>
                <div>النتيجة: <?php echo isLoggedIn() ? 'true' : 'false'; ?></div>
                
                <h3>اختبار دالة getCurrentUser():</h3>
                <div>النتيجة: <?php var_dump(getCurrentUser()); ?></div>
                
                <h3>اختبار $_SESSION:</h3>
                <div>المحتوى: <?php var_dump($_SESSION); ?></div>
                
                <h3>اختبار الاتصال بقاعدة البيانات:</h3>
                <?php
                try {
                    global $database;
                    $test_query = $database->fetch("SELECT COUNT(*) as count FROM reviews");
                    echo "<div>✅ الاتصال ناجح - عدد التقييمات: " . $test_query['count'] . "</div>";
                } catch (Exception $e) {
                    echo "<div>❌ خطأ في الاتصال: " . $e->getMessage() . "</div>";
                }
                ?>
            </div>
        </div>
        
        <!-- روابط مفيدة -->
        <div class="card">
            <h2>روابط مفيدة</h2>
            <a href="test_api_direct.php" style="background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">اختبار API مباشر</a>
            <a href="products/1" style="background: #17a2b8; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">صفحة المنتج</a>
            <a href="admin/reviews.php" style="background: #fd7e14; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">إدارة التقييمات</a>
        </div>
    </div>
</body>
</html>