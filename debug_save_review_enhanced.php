<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// بدء الجلسة فقط إذا لم تكن نشطة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>تشخيص محسن لدالة saveUserReview</h1>";

// عرض معلومات المستخدم الحالي
echo "<h2>معلومات المستخدم الحالي</h2>";
$currentUser = getCurrentUser();
$isLoggedIn = isLoggedIn();

echo "<p><strong>مسجل الدخول:</strong> " . ($isLoggedIn ? "نعم ✅" : "لا ❌") . "</p>";

if ($isLoggedIn && $currentUser) {
    echo "<p><strong>معرف المستخدم:</strong> " . $currentUser['id'] . "</p>";
    echo "<p><strong>الاسم:</strong> " . $currentUser['name'] . "</p>";
    echo "<p><strong>البريد الإلكتروني:</strong> " . $currentUser['email'] . "</p>";
    echo "<p><strong>الدور:</strong> " . $currentUser['role'] . "</p>";
}

echo "<p><strong>بيانات الجلسة:</strong></p>";
echo "<pre>" . print_r($_SESSION, true) . "</pre>";

echo "<hr>";

// اختبار الاتصال بقاعدة البيانات
echo "<h2>اختبار الاتصال بقاعدة البيانات</h2>";
try {
    global $database;
    $pdo = $database->getConnection();
    echo "<p>✅ الاتصال بقاعدة البيانات ناجح</p>";
    
    // فحص جدول reviews
    $stmt = $pdo->query("DESCRIBE reviews");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<p><strong>أعمدة جدول reviews:</strong></p>";
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li>{$column['Field']} - {$column['Type']} - " . 
             ($column['Null'] == 'YES' ? 'NULL' : 'NOT NULL') . 
             ($column['Default'] ? " (Default: {$column['Default']})" : '') . "</li>";
    }
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
}

echo "<hr>";

// اختبار إعدادات النظام
echo "<h2>اختبار إعدادات النظام</h2>";
try {
    $autoApproval = getSetting('reviews_auto_approval', false);
    echo "<p><strong>التفعيل التلقائي للتقييمات:</strong> " . ($autoApproval ? "مفعل" : "غير مفعل") . "</p>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في قراءة الإعدادات: " . $e->getMessage() . "</p>";
}

echo "<hr>";

// اختبار دالة saveUserReview مع معالجة مفصلة للأخطاء
if ($isLoggedIn && $currentUser) {
    echo "<h2>اختبار دالة saveUserReview المحسن</h2>";
    
    $product_id = 1;
    $user_id = $currentUser['id'];
    $customer_name = $currentUser['name'];
    $customer_email = $currentUser['email'];
    $rating = 5;
    $review_title = "اختبار تشخيصي محسن";
    $review_text = "هذا تقييم تشخيصي لاختبار دالة saveUserReview مع معالجة محسنة للأخطاء";
    
    echo "<p><strong>معاملات الاختبار:</strong></p>";
    echo "<ul>";
    echo "<li>product_id: $product_id</li>";
    echo "<li>user_id: $user_id</li>";
    echo "<li>customer_name: $customer_name</li>";
    echo "<li>customer_email: $customer_email</li>";
    echo "<li>rating: $rating</li>";
    echo "<li>review_title: $review_title</li>";
    echo "<li>review_text: $review_text</li>";
    echo "</ul>";
    
    // حذف أي تقييم سابق للاختبار
    try {
        global $database;
        $stmt = $database->delete(
            "reviews", 
            "product_id = :product_id AND user_id = :user_id",
            ['product_id' => $product_id, 'user_id' => $user_id]
        );
        $deleted = $stmt->rowCount();
        echo "<p>✅ تم حذف التقييمات السابقة للاختبار (عدد المحذوف: $deleted)</p>";
    } catch (Exception $e) {
        echo "<p>⚠️ تحذير: لم يتم حذف التقييمات السابقة: " . $e->getMessage() . "</p>";
    }
    
    echo "<p><strong>نتائج الاختبار:</strong></p>";
    echo "<p>استدعاء دالة saveUserReview...</p>";
    
    // تفعيل عرض الأخطاء
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    
    try {
        $result = saveUserReview($product_id, $user_id, $customer_name, $customer_email, $rating, $review_text, $review_title);
        
        echo "<p><strong>نتيجة الدالة:</strong> ";
        if ($result === true || (is_numeric($result) && $result > 0)) {
            echo "نجح الحفظ ✅</p>";
            echo "<p><strong>معرف التقييم:</strong> $result</p>";
            
            // التحقق من البيانات المحفوظة
            try {
                $savedReview = $database->fetch(
                    "SELECT * FROM reviews WHERE product_id = :product_id AND user_id = :user_id ORDER BY created_at DESC LIMIT 1",
                    ['product_id' => $product_id, 'user_id' => $user_id]
                );
                
                if ($savedReview) {
                    echo "<p><strong>البيانات المحفوظة:</strong></p>";
                    echo "<pre>" . print_r($savedReview, true) . "</pre>";
                } else {
                    echo "<p>❌ لم يتم العثور على التقييم المحفوظ</p>";
                }
            } catch (Exception $e) {
                echo "<p>❌ خطأ في قراءة البيانات المحفوظة: " . $e->getMessage() . "</p>";
            }
            
        } else {
            echo "فشل الحفظ ❌</p>";
            echo "<p><strong>رسالة الخطأ:</strong> " . (is_string($result) ? $result : 'خطأ غير محدد') . "</p>";
        }
        
    } catch (Exception $e) {
        echo "خطأ في التنفيذ ❌</p>";
        echo "<p><strong>تفاصيل الخطأ:</strong> " . $e->getMessage() . "</p>";
        echo "<p><strong>ملف الخطأ:</strong> " . $e->getFile() . "</p>";
        echo "<p><strong>سطر الخطأ:</strong> " . $e->getLine() . "</p>";
        echo "<p><strong>Stack Trace:</strong></p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    echo "<hr>";
    
    // عرض آخر 5 تقييمات من قاعدة البيانات
    echo "<h2>آخر 5 تقييمات في قاعدة البيانات</h2>";
    try {
        $recentReviews = $database->fetchAll(
            "SELECT r.*, p.name as product_name FROM reviews r 
             LEFT JOIN products p ON r.product_id = p.id 
             ORDER BY r.created_at DESC LIMIT 5"
        );
        
        if ($recentReviews) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>Product</th><th>User ID</th><th>Name</th><th>Email</th><th>Rating</th><th>Title</th><th>Created</th></tr>";
            foreach ($recentReviews as $review) {
                echo "<tr>";
                echo "<td>{$review['id']}</td>";
                echo "<td>{$review['product_name']}</td>";
                echo "<td>" . ($review['user_id'] ?: 'NULL') . "</td>";
                echo "<td>{$review['name']}</td>";
                echo "<td>{$review['email']}</td>";
                echo "<td>{$review['rating']}</td>";
                echo "<td>{$review['title']}</td>";
                echo "<td>{$review['created_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>لا توجد تقييمات في قاعدة البيانات</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ خطأ في قراءة التقييمات: " . $e->getMessage() . "</p>";
    }
    
} else {
    echo "<h2>❌ المستخدم غير مسجل الدخول</h2>";
    echo "<p>يرجى تسجيل الدخول أولاً لاختبار دالة saveUserReview</p>";
}

echo "<hr>";

// نموذج اختبار يدوي
echo "<h2>نموذج اختبار يدوي</h2>";
echo '<form method="POST" action="">
    <input type="hidden" name="test_manual" value="1">
    <p><label>معرف المنتج: <input type="number" name="product_id" value="1" required></label></p>
    <p><label>التقييم: <select name="rating" required>
        <option value="1">1</option>
        <option value="2">2</option>
        <option value="3">3</option>
        <option value="4">4</option>
        <option value="5" selected>5</option>
    </select></label></p>
    <p><label>عنوان التقييم: <input type="text" name="review_title" value="تقييم اختبار يدوي" required></label></p>
    <p><label>نص التقييم: <textarea name="review_text" required>هذا تقييم اختبار يدوي</textarea></label></p>
    <p><button type="submit">إرسال التقييم</button></p>
</form>';

// معالجة النموذج اليدوي
if (isset($_POST['test_manual']) && $isLoggedIn && $currentUser) {
    echo "<h3>نتائج الاختبار اليدوي</h3>";
    
    $product_id = (int)$_POST['product_id'];
    $rating = (int)$_POST['rating'];
    $review_title = $_POST['review_title'];
    $review_text = $_POST['review_text'];
    
    try {
        $result = saveUserReview(
            $product_id, 
            $currentUser['id'], 
            $currentUser['name'], 
            $currentUser['email'], 
            $rating, 
            $review_text, 
            $review_title
        );
        
        if ($result === true || (is_numeric($result) && $result > 0)) {
            echo "<p>✅ تم حفظ التقييم بنجاح! معرف التقييم: $result</p>";
        } else {
            echo "<p>❌ فشل في حفظ التقييم: " . (is_string($result) ? $result : 'خطأ غير محدد') . "</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ خطأ في التنفيذ: " . $e->getMessage() . "</p>";
    }
}
?>