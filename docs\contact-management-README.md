# نظام إدارة معلومات التواصل
## Contact Information Management System

### نظرة عامة
نظام شامل لإدارة معلومات التواصل في موقع جرين لاين، يتيح للمسؤولين إدارة وتحديث جميع معلومات التواصل بسهولة من خلال واجهة إدارية متقدمة.

### المميزات الرئيسية

#### 🏢 إدارة معلومات العنوان
- العنوان الكامل مع تفاصيل الشارع والحي والمدينة
- الرمز البريدي والدولة
- إحداثيات GPS للموقع الدقيق
- دعم عرض الموقع على الخرائط

#### 📞 إدارة أرقام الهاتف
- الهاتف الرئيسي والثانوي
- الهاتف المحمول والفاكس
- تنسيق تلقائي لأرقام الهاتف السعودية
- التحقق من صحة تنسيق الأرقام

#### 📧 إدارة البريد الإلكتروني
- بريد إلكتروني عام للاستفسارات
- بريد إلكتروني للمبيعات
- بريد إلكتروني للدعم الفني
- بريد إلكتروني للموارد البشرية
- التحقق من صحة عناوين البريد الإلكتروني

#### ⏰ إدارة ساعات العمل
- ساعات العمل للأيام العادية (الأحد - الخميس)
- ساعات العمل ليوم الجمعة
- إعداد يوم السبت (مغلق/مفتوح)
- دعم المنطقة الزمنية
- التحقق من صحة أوقات الفتح والإغلاق

#### 💬 إدارة الواتساب
- رقم الواتساب للتواصل السريع
- إعداد الرد التلقائي
- تحديد ساعات العمل للواتساب
- تنسيق تلقائي للرقم

#### 🗺️ إدارة الخريطة
- كود الخريطة المدمج من Google Maps
- معاينة مباشرة للخريطة
- إحداثيات GPS
- مستوى التكبير المفضل

#### 📱 إدارة وسائل التواصل الاجتماعي
- روابط فيسبوك، تويتر، إنستغرام
- روابط لينكد إن ويوتيوب
- معاينة مباشرة للروابط مع الأيقونات
- التحقق من صحة الروابط

#### 🏛️ معلومات الشركة
- اسم الشركة الرسمي
- رقم السجل التجاري
- الرقم الضريبي
- سنة التأسيس
- رقم الترخيص

### الملفات المضافة/المحدثة

#### 1. قاعدة البيانات
- **`create_contact_info_table.sql`**: إنشاء جدول معلومات التواصل
- **`insert_contact_info.sql`**: إدراج البيانات الأساسية
- **`setup_contact_info.php`**: ملف إعداد تلقائي للبيانات

#### 2. صفحات الإدارة
- **`admin/contact.php`**: الصفحة الرئيسية لإدارة معلومات التواصل
- **`admin/includes/sidebar.php`**: تحديث القائمة الجانبية لإضافة رابط جديد

#### 3. API والوظائف
- **`api/contact_info.php`**: API شامل لإدارة معلومات التواصل
- **`includes/functions.php`**: تحديث دالة `getContactInfo()`

#### 4. الأصول (Assets)
- **`admin/assets/css/contact-management.css`**: تنسيقات CSS مخصصة
- **`admin/assets/js/contact-management.js`**: وظائف JavaScript متقدمة

### كيفية الاستخدام

#### 1. الإعداد الأولي
```bash
# 1. تشغيل ملف إعداد البيانات
http://localhost:8000/setup_contact_info.php

# 2. الوصول لصفحة الإدارة
http://localhost:8000/admin/contact.php
```

#### 2. إدارة المعلومات
1. **الوصول للصفحة**: من لوحة التحكم > المحتوى الإضافي > معلومات التواصل
2. **التنقل بين الأقسام**: استخدم التبويبات للتنقل بين أقسام المعلومات المختلفة
3. **تحديث البيانات**: املأ الحقول المطلوبة واضغط "حفظ التغييرات"
4. **المعاينة**: شاهد معاينة مباشرة للخريطة ووسائل التواصل الاجتماعي

#### 3. استخدام API
```php
// جلب جميع معلومات التواصل
GET /api/contact_info.php

// جلب قسم محدد
GET /api/contact_info.php?section=address

// تحديث معلومات
POST /api/contact_info.php
{
    "section_key": "address",
    "title": "العنوان",
    "content": "الرياض، المملكة العربية السعودية",
    "data": {
        "street": "شارع الملك فهد",
        "city": "الرياض"
    }
}
```

### المميزات التقنية

#### 🔒 الأمان
- التحقق من صحة البيانات المدخلة
- حماية من SQL Injection
- تشفير البيانات الحساسة
- التحقق من الصلاحيات

#### 📱 الاستجابة
- تصميم متجاوب يعمل على جميع الأجهزة
- واجهة مستخدم حديثة ومتطورة
- دعم اللغة العربية بالكامل

#### ⚡ الأداء
- تحميل سريع للصفحات
- حفظ تلقائي للبيانات
- معاينة مباشرة للتغييرات
- تحسين استعلامات قاعدة البيانات

#### 🛠️ سهولة الصيانة
- كود منظم وموثق
- فصل المنطق عن العرض
- استخدام أفضل الممارسات
- دعم للتوسعات المستقبلية

### هيكل قاعدة البيانات

```sql
CREATE TABLE contact_info (
    id INT AUTO_INCREMENT PRIMARY KEY,
    section_key VARCHAR(50) UNIQUE NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT,
    data JSON,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### الأقسام المدعومة
- `address`: معلومات العنوان
- `phone`: أرقام الهاتف
- `email`: عناوين البريد الإلكتروني
- `working_hours`: ساعات العمل
- `whatsapp`: معلومات الواتساب
- `map_embed`: الخريطة المدمجة
- `social_media`: وسائل التواصل الاجتماعي
- `company_info`: معلومات الشركة

### التحديثات المستقبلية
- [ ] إضافة دعم للغات متعددة
- [ ] تكامل مع خدمات الخرائط المختلفة
- [ ] إضافة إحصائيات التفاعل
- [ ] دعم القوالب المخصصة
- [ ] تصدير واستيراد البيانات
- [ ] نظام النسخ الاحتياطي التلقائي

### الدعم والمساعدة
للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

---
**تم التطوير بواسطة**: فريق تطوير جرين لاين  
**التاريخ**: يناير 2025  
**الإصدار**: 1.0.0