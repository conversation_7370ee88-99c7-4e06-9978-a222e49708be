<?php
require_once '../config/config.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Validate required fields
    $required_fields = ['customer_name', 'customer_phone', 'service_type', 'customer_address', 'issue_description'];
    $missing_fields = [];
    
    foreach ($required_fields as $field) {
        if (empty($_POST[$field])) {
            $missing_fields[] = $field;
        }
    }
    
    if (!empty($missing_fields)) {
        echo json_encode([
            'success' => false, 
            'message' => 'الحقول التالية مطلوبة: ' . implode(', ', $missing_fields)
        ]);
        exit;
    }
    
    // Sanitize input
    $customer_name = sanitizeInput($_POST['customer_name']);
    $customer_phone = sanitizeInput($_POST['customer_phone']);
    $customer_email = sanitizeInput($_POST['customer_email'] ?? '');
    $service_type = (int)$_POST['service_type'];
    $product_model = sanitizeInput($_POST['product_model'] ?? '');
    $serial_number = sanitizeInput($_POST['serial_number'] ?? '');
    $purchase_date = sanitizeInput($_POST['purchase_date'] ?? '');
    $priority = sanitizeInput($_POST['priority'] ?? 'normal');
    $customer_address = sanitizeInput($_POST['customer_address']);
    $issue_description = sanitizeInput($_POST['issue_description']);
    $preferred_time = sanitizeInput($_POST['preferred_time'] ?? '');
    
    // Validate email if provided
    if (!empty($customer_email) && !filter_var($customer_email, FILTER_VALIDATE_EMAIL)) {
        echo json_encode([
            'success' => false, 
            'message' => 'البريد الإلكتروني غير صحيح'
        ]);
        exit;
    }
    
    // Validate phone number
    if (!preg_match('/^[0-9+\-\s()]+$/', $customer_phone)) {
        echo json_encode([
            'success' => false, 
            'message' => 'رقم الهاتف غير صحيح'
        ]);
        exit;
    }
    
    // Generate unique request ID
    $request_id = 'SR' . date('Ymd') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
    
    // Save service request to database
    global $database;
    $pdo = $database->getConnection();
    $stmt = $pdo->prepare("
        INSERT INTO service_requests (
            request_id, customer_name, customer_phone, customer_email, 
            service_type, product_model, serial_number, purchase_date, 
            priority, customer_address, issue_description, preferred_time, 
            status, created_at
        ) VALUES (
            ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', NOW()
        )
    ");
    
    $result = $stmt->execute([
        $request_id, $customer_name, $customer_phone, $customer_email,
        $service_type, $product_model, $serial_number, $purchase_date,
        $priority, $customer_address, $issue_description, $preferred_time
    ]);
    
    if ($result) {
        // Get service type name
        $service_stmt = $pdo->prepare("SELECT title FROM after_sales_services WHERE id = ?");
        $service_stmt->execute([$service_type]);
        $service_info = $service_stmt->fetch();
        $service_name = $service_info ? $service_info['title'] : 'خدمة غير محددة';
        
        // Send notification email to admin
        $admin_email = getSetting('admin_email');
        if ($admin_email) {
            $priority_text = [
                'normal' => 'عادي',
                'high' => 'عالي', 
                'urgent' => 'عاجل'
            ];
            
            $preferred_time_text = [
                'morning' => 'صباحاً (8:00 - 12:00)',
                'afternoon' => 'بعد الظهر (12:00 - 17:00)',
                'evening' => 'مساءً (17:00 - 20:00)',
                'anytime' => 'أي وقت'
            ];
            
            $email_subject = 'طلب خدمة جديد - ' . $request_id;
            $email_body = "
                <h2>طلب خدمة ما بعد البيع جديد</h2>
                <p><strong>رقم الطلب:</strong> {$request_id}</p>
                <p><strong>نوع الخدمة:</strong> {$service_name}</p>
                <p><strong>الأولوية:</strong> {$priority_text[$priority]}</p>
                <hr>
                <h3>بيانات العميل:</h3>
                <p><strong>الاسم:</strong> {$customer_name}</p>
                <p><strong>الهاتف:</strong> {$customer_phone}</p>
                <p><strong>البريد الإلكتروني:</strong> {$customer_email}</p>
                <p><strong>العنوان:</strong> {$customer_address}</p>
                <hr>
                <h3>بيانات المنتج:</h3>
                <p><strong>الموديل:</strong> {$product_model}</p>
                <p><strong>الرقم التسلسلي:</strong> {$serial_number}</p>
                <p><strong>تاريخ الشراء:</strong> {$purchase_date}</p>
                <hr>
                <h3>تفاصيل الطلب:</h3>
                <p><strong>وصف المشكلة:</strong></p>
                <div style='background: #f5f5f5; padding: 15px; border-radius: 5px;'>
                    {$issue_description}
                </div>
                <p><strong>الوقت المفضل:</strong> {$preferred_time_text[$preferred_time]}</p>
                <p><strong>تاريخ الطلب:</strong> " . date('Y-m-d H:i:s') . "</p>
                <hr>
                <p><a href='" . SITE_URL . "/admin/service-requests'>إدارة طلبات الخدمة</a></p>
            ";
            
            sendEmail($admin_email, $email_subject, $email_body);
        }
        
        // Send confirmation email to customer
        if (!empty($customer_email)) {
            $customer_subject = 'تأكيد طلب الخدمة - ' . $request_id;
            $customer_body = "
                <h2>تأكيد طلب خدمة ما بعد البيع</h2>
                <p>عزيزي/عزيزتي {$customer_name},</p>
                <p>شكراً لك على طلب خدمة ما بعد البيع. لقد تم استلام طلبك بنجاح.</p>
                <p><strong>تفاصيل الطلب:</strong></p>
                <p><strong>رقم الطلب:</strong> {$request_id}</p>
                <p><strong>نوع الخدمة:</strong> {$service_name}</p>
                <p><strong>حالة الطلب:</strong> قيد المراجعة</p>
                <p>سيتواصل معك فريقنا خلال 24 ساعة لتحديد موعد الزيارة.</p>
                <p>يمكنك تتبع حالة طلبك من خلال الرابط التالي:</p>
                <p><a href='" . SITE_URL . "/after-sales?track={$request_id}'>تتبع الطلب</a></p>
                <p>مع أطيب التحيات,<br>فريق " . getSetting('site_name') . "</p>
            ";
            
            sendEmail($customer_email, $customer_subject, $customer_body);
        }
        
        // Log activity
        logActivity('service_request', "طلب خدمة جديد {$request_id} من {$customer_name}");
        
        echo json_encode([
            'success' => true, 
            'message' => 'تم إرسال طلب الخدمة بنجاح. سيتم التواصل معك قريباً.',
            'request_id' => $request_id
        ]);
    } else {
        echo json_encode([
            'success' => false, 
            'message' => 'حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى.'
        ]);
    }
    
} catch (Exception $e) {
    error_log('Service request error: ' . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'حدث خطأ في النظام. يرجى المحاولة مرة أخرى لاحقاً.'
    ]);
}
?>