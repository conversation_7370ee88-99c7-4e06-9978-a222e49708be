<?php
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

echo "<h1>اختبار حالة المصادقة في الصفحة الأساسية</h1>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h2>معلومات الجلسة:</h2>";
echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
echo "<p><strong>Session Status:</strong> " . session_status() . "</p>";
echo "<p><strong>Session Data:</strong></p>";
echo "<pre>" . print_r($_SESSION, true) . "</pre>";
echo "</div>";

echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h2>حالة تسجيل الدخول:</h2>";
echo "<p><strong>isLoggedIn():</strong> " . (isLoggedIn() ? 'نعم' : 'لا') . "</p>";

if (isLoggedIn()) {
    $user = getCurrentUser();
    echo "<p><strong>معرف المستخدم:</strong> " . $user['id'] . "</p>";
    echo "<p><strong>اسم المستخدم:</strong> " . $user['name'] . "</p>";
    echo "<p><strong>البريد الإلكتروني:</strong> " . $user['email'] . "</p>";
    echo "<p><strong>الدور:</strong> " . $user['role'] . "</p>";
} else {
    echo "<p>المستخدم غير مسجل الدخول</p>";
}
echo "</div>";

// اختبار إرسال تقييم
if ($_POST) {
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h2>بيانات النموذج المرسلة:</h2>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    
    // محاكاة إرسال التقييم
    $product_id = $_POST['product_id'] ?? 1;
    $rating = $_POST['rating'] ?? 5;
    $review_text = $_POST['review_text'] ?? 'تقييم تجريبي';
    
    if (isLoggedIn()) {
        $user = getCurrentUser();
        $result = saveUserReview($product_id, $user['id'], $user['name'], $user['email'], $rating, $review_text);
        echo "<p><strong>نتيجة حفظ التقييم:</strong> " . ($result ? 'نجح' : 'فشل') . "</p>";
    } else {
        echo "<p><strong>خطأ:</strong> المستخدم غير مسجل الدخول</p>";
    }
    echo "</div>";
}
?>

<div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h2>نموذج اختبار التقييم:</h2>
    <form method="POST" style="max-width: 400px;">
        <div style="margin-bottom: 15px;">
            <label>معرف المنتج:</label>
            <input type="number" name="product_id" value="1" min="1" max="15" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
        </div>
        <div style="margin-bottom: 15px;">
            <label>التقييم:</label>
            <select name="rating" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                <option value="5">5 نجوم</option>
                <option value="4">4 نجوم</option>
                <option value="3">3 نجوم</option>
                <option value="2">نجمتان</option>
                <option value="1">نجمة واحدة</option>
            </select>
        </div>
        <div style="margin-bottom: 15px;">
            <label>التعليق:</label>
            <textarea name="review_text" rows="3" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">تقييم تجريبي من الصفحة الأساسية</textarea>
        </div>
        <button type="submit" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">إرسال التقييم</button>
    </form>
</div>

<div style="margin: 20px 0;">
    <h2>روابط مفيدة:</h2>
    <a href="test_user_review_fix.php" style="background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">صفحة الاختبار المخصصة</a>
    <a href="products/1" style="background: #17a2b8; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;">صفحة تفاصيل المنتج</a>
    <a href="admin/login.php" style="background: #ffc107; color: black; padding: 10px 15px; text-decoration: none; border-radius: 4px;">تسجيل الدخول</a>
</div>