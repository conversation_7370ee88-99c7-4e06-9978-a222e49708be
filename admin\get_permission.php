<?php
/**
 * API لجلب بيانات صلاحية محددة
 * Get Permission Data API
 */

require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/middleware.php';

// التحقق من تسجيل الدخول وصلاحية الإدارة
requirePermission('manage-permissions');

header('Content-Type: application/json');

try {
    $permissionId = $_GET['id'] ?? null;
    
    if (!$permissionId) {
        throw new Exception('معرف الصلاحية مطلوب');
    }
    
    // جلب بيانات الصلاحية
    $permission = $database->fetch("SELECT * FROM permissions WHERE id = :id", ['id' => $permissionId]);
    
    if (!$permission) {
        throw new Exception('الصلاحية غير موجودة');
    }
    
    echo json_encode([
        'success' => true,
        'permission' => $permission
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>