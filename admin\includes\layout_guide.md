# دليل استخدام التخطيط الموحد لصفحات الأدمن

## نظرة عامة

تم إنشاء نظام تخطيط موحد لصفحات الأدمن لضمان التناسق في التصميم وسهولة الصيانة. يحتوي النظام على:

- **Header موحد** مع شريط التنقل والإشعارات
- **Sidebar موحد** مع قائمة التنقل
- **دوال مساعدة** لإنشاء العناصر الشائعة
- **JavaScript مشترك** للوظائف الأساسية

## الملفات المطلوبة

```
admin/includes/
├── layout.php          # الملف الرئيسي للتخطيط
├── header.php          # رأس الصفحة
├── sidebar.php         # الشريط الجانبي
└── layout_guide.md     # هذا الدليل
```

## كيفية استخدام التخطيط الموحد

### 1. إعداد الصفحة الجديدة

```php
<?php
// تضمين الملفات الأساسية
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../config/database.php';

// إعداد متغيرات الصفحة
$pageTitle = 'عنوان الصفحة';                    // مطلوب
$currentPage = 'page_name';                     // مطلوب للسايدبار
$pageDescription = 'وصف الصفحة';               // اختياري
$breadcrumbs = [                                // اختياري
    ['title' => 'الرئيسية', 'url' => 'index.php'],
    ['title' => 'عنوان الصفحة']
];
$additionalCSS = [                              // اختياري
    '../assets/css/custom.css'
];
$additionalJS = [                               // اختياري
    '../assets/js/custom.js'
];

// تضمين التخطيط الموحد
require_once 'includes/layout.php';

// منطق الصفحة (معالجة POST، جلب البيانات، إلخ)
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // معالجة الطلبات
}

// جلب البيانات
$data = [];

// بدء التخطيط
startLayout();
showPageHeader();
showMessages();

// محتوى الصفحة هنا
?>
<div class="bg-white rounded-lg shadow-md p-6">
    <h2>محتوى الصفحة</h2>
    <!-- المحتوى -->
</div>

<?php
// إنهاء التخطيط
endLayout();
?>
```

### 2. استخدام الدوال المساعدة

#### إنشاء كارت

```php
<?php
ob_start();
?>
<p>محتوى الكارت</p>
<button class="btn btn-primary">زر</button>
<?php
$cardContent = ob_get_clean();

createCard('عنوان الكارت', $cardContent, 'mb-6');
?>
```

#### إنشاء تبويبات

```php
<?php
$tabs = [
    'tab1' => [
        'title' => 'التبويب الأول',
        'icon' => 'fas fa-home',
        'content' => getTab1Content()
    ],
    'tab2' => [
        'title' => 'التبويب الثاني', 
        'icon' => 'fas fa-user',
        'content' => getTab2Content()
    ]
];

createTabs($tabs, 'tab1'); // tab1 هو التبويب الافتراضي

function getTab1Content() {
    ob_start();
    ?>
    <div class="space-y-4">
        <p>محتوى التبويب الأول</p>
    </div>
    <?php
    return ob_get_clean();
}

function getTab2Content() {
    ob_start();
    ?>
    <div class="space-y-4">
        <p>محتوى التبويب الثاني</p>
    </div>
    <?php
    return ob_get_clean();
}
?>
```

### 3. عرض الرسائل

```php
// في منطق الصفحة
$message = 'تم الحفظ بنجاح';     // رسالة نجاح
$error = 'حدث خطأ';              // رسالة خطأ
$success = 'تم بنجاح';           // رسالة نجاح بديلة

// سيتم عرضها تلقائياً عند استدعاء showMessages()
```

### 4. JavaScript المشترك

يتضمن التخطيط دوال JavaScript مفيدة:

```javascript
// تأكيد الحذف
function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
    return confirm(message);
}

// معاينة الصورة
function previewImage(input, previewId) {
    // يعرض معاينة للصورة المختارة
}

// مثال على الاستخدام في HTML
<button onclick="if(confirmDelete('هل تريد حذف هذا العنصر؟')) deleteItem(123)">
    حذف
</button>

<input type="file" onchange="previewImage(this, 'preview')">
<div id="preview" class="hidden">
    <img class="w-32 h-32 object-cover rounded">
</div>
```

## تحويل الصفحات الموجودة

### الخطوات:

1. **إزالة HTML الثابت**
   ```php
   // احذف هذا
   <!DOCTYPE html>
   <html lang="ar" dir="rtl">
   <head>...</head>
   <body>
       <?php include 'includes/sidebar.php'; ?>
       <?php include 'includes/header.php'; ?>
   ```

2. **إضافة متغيرات الصفحة**
   ```php
   // أضف هذا في بداية الملف
   $pageTitle = 'عنوان الصفحة';
   $currentPage = 'page_name';
   require_once 'includes/layout.php';
   ```

3. **استبدال بداية المحتوى**
   ```php
   // بدلاً من
   <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6">
   
   // استخدم
   startLayout();
   showPageHeader();
   showMessages();
   ```

4. **استبدال نهاية المحتوى**
   ```php
   // بدلاً من
   </main>
   </body>
   </html>
   
   // استخدم
   endLayout();
   ```

## المتغيرات المتاحة

| المتغير | النوع | الوصف | مطلوب |
|---------|-------|-------|-------|
| `$pageTitle` | string | عنوان الصفحة | ✅ |
| `$currentPage` | string | معرف الصفحة للسايدبار | ✅ |
| `$pageDescription` | string | وصف الصفحة | ❌ |
| `$breadcrumbs` | array | مسار التنقل | ❌ |
| `$additionalCSS` | array | ملفات CSS إضافية | ❌ |
| `$additionalJS` | array | ملفات JS إضافية | ❌ |
| `$message` | string | رسالة نجاح | ❌ |
| `$error` | string | رسالة خطأ | ❌ |
| `$success` | string | رسالة نجاح بديلة | ❌ |

## الدوال المتاحة

| الدالة | الوصف |
|--------|-------|
| `startLayout()` | بدء التخطيط وعرض HTML الأساسي |
| `showPageHeader()` | عرض رأس الصفحة مع العنوان والوصف |
| `showMessages()` | عرض رسائل النجاح والخطأ |
| `endLayout()` | إنهاء التخطيط وإغلاق HTML |
| `createCard($title, $content, $classes)` | إنشاء كارت |
| `createTabs($tabs, $defaultTab)` | إنشاء تبويبات |

## مثال كامل

انظر إلى ملف `home_new_layout.php` كمثال كامل على كيفية استخدام التخطيط الموحد.

## الفوائد

### الإيجابيات:
✅ **التناسق**: جميع الصفحات تبدو متشابهة
✅ **سهولة الصيانة**: تغيير واحد يؤثر على جميع الصفحات
✅ **توفير الوقت**: لا حاجة لكتابة HTML متكرر
✅ **أقل أخطاء**: كود مختبر ومعاد استخدامه
✅ **مرونة**: يمكن تخصيص كل صفحة حسب الحاجة
✅ **SEO أفضل**: عناوين وأوصاف منظمة
✅ **JavaScript مشترك**: دوال مفيدة جاهزة

### السلبيات المحتملة:
⚠️ **منحنى التعلم**: المطورون الجدد يحتاجون وقت للتعلم
⚠️ **قيود التخصيص**: قد تحتاج صفحات خاصة لتخصيص إضافي
⚠️ **الاعتمادية**: تغيير في layout.php يؤثر على جميع الصفحات

## نصائح للاستخدام الأمثل

1. **اختبر التغييرات**: أي تعديل على layout.php يجب اختباره على جميع الصفحات
2. **استخدم الدوال المساعدة**: لا تكتب HTML مكرر
3. **وثق التخصيصات**: إذا احتجت تخصيص خاص، وثقه
4. **حافظ على البساطة**: لا تعقد التخطيط أكثر من اللازم
5. **استخدم المتغيرات**: استفد من جميع المتغيرات المتاحة

## الخلاصة

التخطيط الموحد يحل مشكلة عدم التناسق ويوفر نظام إدارة أفضل. الفوائد تفوق السلبيات بكثير، خاصة في المشاريع الكبيرة التي تحتوي على عدد كبير من صفحات الأدمن.

يُنصح بتطبيق هذا النظام على جميع الصفحات الجديدة وتحويل الصفحات الموجودة تدريجياً.