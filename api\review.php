<?php
session_start(); // بدء الجلسة للتحقق من تسجيل الدخول
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // التحقق من حالة تسجيل الدخول
    $isLoggedIn = isLoggedIn();
    $currentUser = $isLoggedIn ? getCurrentUser() : null;
    
    // Validate required fields
    $required_fields = ['product_id', 'rating', 'review_text'];
    
    // إذا لم يكن مسجل دخول، نحتاج اسم وبريد إلكتروني
    if (!$isLoggedIn) {
        $required_fields[] = 'customer_name';
        $required_fields[] = 'customer_email';
    }
    
    $missing_fields = [];
    
    foreach ($required_fields as $field) {
        if (empty($_POST[$field])) {
            $missing_fields[] = $field;
        }
    }
    
    if (!empty($missing_fields)) {
        echo json_encode([
            'success' => false, 
            'message' => 'الحقول التالية مطلوبة: ' . implode(', ', $missing_fields)
        ]);
        exit;
    }
    
    // Sanitize input
    $product_id = (int)$_POST['product_id'];
    $rating = (int)$_POST['rating'];
    $review_text = sanitizeInput($_POST['review_text']);
    $review_title = sanitizeInput($_POST['review_title'] ?? ''); // حقل اختياري
    
    // تحديد بيانات المستخدم
    if ($isLoggedIn) {
        $user_id = $currentUser['id'];
        $customer_name = $currentUser['name'];
        $customer_email = $currentUser['email'];
    } else {
        $user_id = null;
        $customer_name = sanitizeInput($_POST['customer_name']);
        $customer_email = sanitizeInput($_POST['customer_email'] ?? '');
    }
    
    // Validate rating
    if ($rating < 1 || $rating > 5) {
        echo json_encode([
            'success' => false, 
            'message' => 'التقييم يجب أن يكون بين 1 و 5 نجوم'
        ]);
        exit;
    }
    
    // Validate email if provided
    if (!empty($customer_email) && !filter_var($customer_email, FILTER_VALIDATE_EMAIL)) {
        echo json_encode([
            'success' => false, 
            'message' => 'البريد الإلكتروني غير صحيح'
        ]);
        exit;
    }
    
    // Check if product exists - التحقق المحسن من وجود المنتج
    $product = getProduct($product_id);
    if (!$product) {
        // التحقق من وجود المنتج حتى لو كان غير نشط
        global $database;
        $productCheck = $database->fetch(
            "SELECT id, name, is_active, is_visible FROM products WHERE id = :id",
            ['id' => $product_id]
        );
        
        if (!$productCheck) {
            echo json_encode([
                'success' => false, 
                'message' => 'المنتج غير موجود'
            ]);
            exit;
        } else if ($productCheck['is_active'] != 1) {
            echo json_encode([
                'success' => false, 
                'message' => 'المنتج غير نشط حالياً'
            ]);
            exit;
        } else if (isset($productCheck['is_visible']) && $productCheck['is_visible'] != 1) {
            echo json_encode([
                'success' => false, 
                'message' => 'المنتج غير متاح للعرض'
            ]);
            exit;
        } else {
            // إذا وصلنا هنا، فهناك مشكلة في دالة getProduct
            echo json_encode([
                'success' => false, 
                'message' => 'خطأ في تحميل بيانات المنتج'
            ]);
            exit;
        }
    }
    
    // Check for duplicate reviews
    if ($isLoggedIn) {
        // للمستخدمين المسجلين: التحقق بواسطة user_id
        if (hasUserReviewed($product_id, $user_id)) {
            echo json_encode([
                'success' => false, 
                'message' => 'لقد قمت بتقييم هذا المنتج من قبل'
            ]);
            exit;
        }
    } else {
        // للزوار: التحقق بواسطة البريد الإلكتروني
        if (!empty($customer_email)) {
            global $database;
            $pdo = $database->getConnection();
            $stmt = $pdo->prepare("SELECT id FROM reviews WHERE product_id = ? AND email = ?");
            $stmt->execute([$product_id, $customer_email]);
            
            if ($stmt->fetch()) {
                echo json_encode([
                    'success' => false, 
                    'message' => 'لقد قمت بتقييم هذا المنتج من قبل'
                ]);
                exit;
            }
        }
    }
    
    // Save review
    if ($isLoggedIn) {
        // حفظ تقييم المستخدم المسجل
        $review_id = saveUserReview($product_id, $user_id, $customer_name, $customer_email, $rating, $review_text, $review_title);
    } else {
        // حفظ تقييم الزائر
        $review_id = saveGuestReview($product_id, $customer_name, $customer_email, $rating, $review_text, $review_title);
    }
    
    if ($review_id) {
        // Update product rating
        updateProductRating($product_id);
        
        // Send notification email to admin
        $admin_email = getSetting('admin_email');
        if ($admin_email) {
            $email_subject = 'مراجعة جديدة على المنتج: ' . $product['name'];
            $email_body = "
                <h2>مراجعة جديدة</h2>
                <p><strong>المنتج:</strong> {$product['name']}</p>
                <p><strong>اسم العميل:</strong> {$customer_name}</p>
                <p><strong>البريد الإلكتروني:</strong> {$customer_email}</p>
                <p><strong>التقييم:</strong> {$rating}/5 نجوم</p>
                <p><strong>المراجعة:</strong></p>
                <div style='background: #f5f5f5; padding: 15px; border-radius: 5px;'>
                    {$review_text}
                </div>
                <p><strong>التاريخ:</strong> " . date('Y-m-d H:i:s') . "</p>
                <p><a href='" . SITE_URL . "/admin/reviews'>إدارة المراجعات</a></p>
            ";
            
            sendEmail($admin_email, $email_subject, $email_body);
        }
        
        // Log activity
        logActivity('review_added', "مراجعة جديدة من {$customer_name} على المنتج {$product['name']}");
        
        echo json_encode([
            'success' => true, 
            'message' => 'تم إضافة مراجعتك بنجاح. شكراً لك!',
            'review_id' => $review_id
        ]);
    } else {
        echo json_encode([
            'success' => false, 
            'message' => 'حدث خطأ أثناء إضافة المراجعة. يرجى المحاولة مرة أخرى.'
        ]);
    }
    
} catch (Exception $e) {
    error_log('Review submission error: ' . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'حدث خطأ في النظام. يرجى المحاولة مرة أخرى لاحقاً.'
    ]);
}
?>