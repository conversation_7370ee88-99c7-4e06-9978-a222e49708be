<?php
/**
 * نظام تشغيل الاختبارات
 * Test Runner System
 */

class TestRunner {
    private $tests = [];
    private $results = [];
    private $totalTests = 0;
    private $passedTests = 0;
    private $failedTests = 0;
    private $startTime;
    
    public function __construct() {
        $this->startTime = microtime(true);
    }
    
    /**
     * إضافة اختبار
     */
    public function addTest($testClass) {
        $this->tests[] = $testClass;
    }
    
    /**
     * تشغيل جميع الاختبارات
     */
    public function runAll() {
        echo "\n=== بدء تشغيل الاختبارات ===\n\n";
        
        foreach ($this->tests as $testClass) {
            $this->runTestClass($testClass);
        }
        
        $this->printSummary();
    }
    
    /**
     * تشغيل فئة اختبار
     */
    private function runTestClass($testClass) {
        echo "تشغيل اختبارات: " . get_class($testClass) . "\n";
        echo str_repeat('-', 50) . "\n";
        
        $reflection = new ReflectionClass($testClass);
        $methods = $reflection->getMethods(ReflectionMethod::IS_PUBLIC);
        
        foreach ($methods as $method) {
            if (strpos($method->getName(), 'test') === 0) {
                $this->runTest($testClass, $method->getName());
            }
        }
        
        echo "\n";
    }
    
    /**
     * تشغيل اختبار واحد
     */
    private function runTest($testClass, $methodName) {
        $this->totalTests++;
        
        try {
            // إعداد الاختبار
            if (method_exists($testClass, 'setUp')) {
                $testClass->setUp();
            }
            
            // تشغيل الاختبار
            $testClass->$methodName();
            
            // تنظيف الاختبار
            if (method_exists($testClass, 'tearDown')) {
                $testClass->tearDown();
            }
            
            $this->passedTests++;
            echo "✓ {$methodName}\n";
            
        } catch (Exception $e) {
            $this->failedTests++;
            echo "✗ {$methodName}: " . $e->getMessage() . "\n";
            $this->results[] = [
                'test' => get_class($testClass) . '::' . $methodName,
                'status' => 'failed',
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * طباعة ملخص النتائج
     */
    private function printSummary() {
        $endTime = microtime(true);
        $duration = round($endTime - $this->startTime, 3);
        
        echo "\n" . str_repeat('=', 60) . "\n";
        echo "ملخص الاختبارات\n";
        echo str_repeat('=', 60) . "\n";
        echo "إجمالي الاختبارات: {$this->totalTests}\n";
        echo "نجح: {$this->passedTests}\n";
        echo "فشل: {$this->failedTests}\n";
        echo "الوقت المستغرق: {$duration} ثانية\n";
        
        if ($this->failedTests > 0) {
            echo "\nالاختبارات الفاشلة:\n";
            echo str_repeat('-', 30) . "\n";
            foreach ($this->results as $result) {
                if ($result['status'] === 'failed') {
                    echo "- {$result['test']}: {$result['message']}\n";
                }
            }
        }
        
        $successRate = $this->totalTests > 0 ? round(($this->passedTests / $this->totalTests) * 100, 1) : 0;
        echo "\nمعدل النجاح: {$successRate}%\n";
        echo str_repeat('=', 60) . "\n";
    }
}

/**
 * فئة أساسية للاختبارات
 */
class TestCase {
    
    /**
     * التحقق من المساواة
     */
    protected function assertEquals($expected, $actual, $message = '') {
        if ($expected !== $actual) {
            $msg = $message ?: "Expected '{$expected}' but got '{$actual}'";
            throw new Exception($msg);
        }
    }
    
    /**
     * التحقق من الصحة
     */
    protected function assertTrue($condition, $message = '') {
        if (!$condition) {
            $msg = $message ?: 'Expected true but got false';
            throw new Exception($msg);
        }
    }
    
    /**
     * التحقق من الخطأ
     */
    protected function assertFalse($condition, $message = '') {
        if ($condition) {
            $msg = $message ?: 'Expected false but got true';
            throw new Exception($msg);
        }
    }
    
    /**
     * التحقق من عدم الفراغ
     */
    protected function assertNotEmpty($value, $message = '') {
        if (empty($value)) {
            $msg = $message ?: 'Expected non-empty value';
            throw new Exception($msg);
        }
    }
    
    /**
     * التحقق من الفراغ
     */
    protected function assertEmpty($value, $message = '') {
        if (!empty($value)) {
            $msg = $message ?: 'Expected empty value';
            throw new Exception($msg);
        }
    }
    
    /**
     * التحقق من وجود مفتاح في المصفوفة
     */
    protected function assertArrayHasKey($key, $array, $message = '') {
        if (!array_key_exists($key, $array)) {
            $msg = $message ?: "Array does not contain key '{$key}'";
            throw new Exception($msg);
        }
    }
    
    /**
     * التحقق من النوع
     */
    protected function assertInstanceOf($expected, $actual, $message = '') {
        if (!($actual instanceof $expected)) {
            $actualType = is_object($actual) ? get_class($actual) : gettype($actual);
            $msg = $message ?: "Expected instance of '{$expected}' but got '{$actualType}'";
            throw new Exception($msg);
        }
    }
    
    /**
     * التحقق من احتواء النص
     */
    protected function assertContains($needle, $haystack, $message = '') {
        if (strpos($haystack, $needle) === false) {
            $msg = $message ?: "String '{$haystack}' does not contain '{$needle}'";
            throw new Exception($msg);
        }
    }
    
    /**
     * فشل الاختبار مع رسالة
     */
    protected function fail($message = 'Test failed') {
        throw new Exception($message);
    }
    
    /**
     * إعداد الاختبار (يتم استدعاؤها قبل كل اختبار)
     */
    protected function setUp() {
        // يمكن تخصيصها في الفئات المشتقة
    }
    
    /**
     * تنظيف الاختبار (يتم استدعاؤها بعد كل اختبار)
     */
    protected function tearDown() {
        // يمكن تخصيصها في الفئات المشتقة
    }
}

/**
 * اختبارات الوحدة للمصادقة
 */
class AuthTest extends TestCase {
    private $database;
    
    protected function setUp() {
        // إعداد قاعدة بيانات اختبار
        global $database;
        $this->database = $database;
    }
    
    public function testValidateEmail() {
        // اختبار التحقق من صحة البريد الإلكتروني
        $this->assertTrue(filter_var('<EMAIL>', FILTER_VALIDATE_EMAIL) !== false);
        $this->assertFalse(filter_var('invalid-email', FILTER_VALIDATE_EMAIL) !== false);
    }
    
    public function testPasswordHashing() {
        $password = 'test123';
        $hash = password_hash($password, PASSWORD_DEFAULT);
        
        $this->assertNotEmpty($hash);
        $this->assertTrue(password_verify($password, $hash));
        $this->assertFalse(password_verify('wrong-password', $hash));
    }
    
    public function testValidateRegistrationData() {
        require_once '../includes/auth.php';
        
        // بيانات صحيحة
        $validData = [
            'name' => 'أحمد محمد',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'confirm_password' => 'password123',
            'phone' => '0501234567'
        ];
        
        $result = validateRegistrationData($validData);
        $this->assertTrue($result['valid']);
        
        // بيانات غير صحيحة
        $invalidData = [
            'name' => '',
            'email' => 'invalid-email',
            'password' => '123',
            'confirm_password' => '456'
        ];
        
        $result = validateRegistrationData($invalidData);
        $this->assertFalse($result['valid']);
        $this->assertNotEmpty($result['errors']);
    }
}

/**
 * اختبارات الوحدة للصلاحيات
 */
class PermissionsTest extends TestCase {
    private $permissionManager;
    
    protected function setUp() {
        require_once '../includes/permissions.php';
        $this->permissionManager = new PermissionManager();
    }
    
    public function testHasPermission() {
        // اختبار التحقق من الصلاحيات
        // ملاحظة: يتطلب بيانات اختبار في قاعدة البيانات
        $this->assertTrue(true); // placeholder
    }
    
    public function testAssignRole() {
        // اختبار إسناد الأدوار
        $this->assertTrue(true); // placeholder
    }
}

/**
 * اختبارات الوحدة للتخزين المؤقت
 */
class CacheTest extends TestCase {
    private $cache;
    
    protected function setUp() {
        require_once '../includes/redis_cache.php';
        $this->cache = new RedisCache();
    }
    
    public function testSetAndGet() {
        if (!$this->cache->isConnected()) {
            $this->assertTrue(true); // تخطي الاختبار إذا لم يكن Redis متاحاً
            return;
        }
        
        $key = 'test_key';
        $value = 'test_value';
        
        $this->assertTrue($this->cache->set($key, $value));
        $this->assertEquals($value, $this->cache->get($key));
    }
    
    public function testDelete() {
        if (!$this->cache->isConnected()) {
            $this->assertTrue(true);
            return;
        }
        
        $key = 'test_delete_key';
        $value = 'test_value';
        
        $this->cache->set($key, $value);
        $this->assertTrue($this->cache->delete($key));
        $this->assertEmpty($this->cache->get($key));
    }
}

/**
 * اختبارات الوحدة لـ CSRF
 */
class CSRFTest extends TestCase {
    private $csrf;
    
    protected function setUp() {
        require_once '../includes/csrf.php';
        $this->csrf = new CSRFProtection();
    }
    
    public function testGenerateToken() {
        $token = $this->csrf->generateToken();
        $this->assertNotEmpty($token);
        $this->assertEquals(64, strlen($token)); // طول الرمز المتوقع
    }
    
    public function testValidateToken() {
        $token = $this->csrf->generateToken();
        
        // محاكاة الطلب
        $_POST['csrf_token'] = $token;
        
        $this->assertTrue($this->csrf->validateRequest());
        
        // اختبار رمز خاطئ
        $_POST['csrf_token'] = 'invalid_token';
        $this->assertFalse($this->csrf->validateRequest());
    }
}

/**
 * اختبارات الوحدة لـ Rate Limiting
 */
class RateLimiterTest extends TestCase {
    private $rateLimiter;
    
    protected function setUp() {
        require_once '../includes/rate_limiter.php';
        $this->rateLimiter = new RateLimiter();
    }
    
    public function testRateLimit() {
        $action = 'test_action';
        $identifier = 'test_user';
        
        // أول طلب يجب أن ينجح
        $this->assertFalse($this->rateLimiter->isLimitExceeded($action, $identifier));
        
        // تسجيل عدة محاولات
        for ($i = 0; $i < 10; $i++) {
            $this->rateLimiter->recordAttempt($action, $identifier);
        }
        
        // يجب أن يتم تجاوز الحد الآن
        $this->assertTrue($this->rateLimiter->isLimitExceeded($action, $identifier));
    }
}

/**
 * اختبارات التكامل
 */
class IntegrationTest extends TestCase {
    
    public function testDatabaseConnection() {
        require_once '../includes/database.php';
        global $database;
        
        $this->assertInstanceOf('Database', $database);
        $this->assertTrue($database->isConnected());
    }
    
    public function testUserRegistrationFlow() {
        require_once '../includes/auth.php';
        
        // بيانات مستخدم اختبار
        $userData = [
            'name' => 'مستخدم اختبار',
            'email' => 'test_' . time() . '@example.com',
            'password' => 'password123',
            'phone' => '0501234567'
        ];
        
        // محاولة التسجيل
        $result = register($userData['name'], $userData['email'], $userData['password'], $userData['phone']);
        
        if ($result['success']) {
            $this->assertTrue($result['success']);
            
            // محاولة تسجيل الدخول
            $loginResult = login($userData['email'], $userData['password']);
            $this->assertTrue($loginResult['success']);
            
            // تنظيف - حذف المستخدم
            global $database;
            $database->delete('users', 'email = :email', ['email' => $userData['email']]);
        } else {
            // إذا فشل التسجيل، تحقق من السبب
            $this->assertTrue(true); // تخطي الاختبار
        }
    }
    
    public function testProductDisplay() {
        global $database;
        
        // الحصول على منتج للاختبار
        $product = $database->fetch("SELECT * FROM products WHERE status = 'active' LIMIT 1");
        
        if ($product) {
            $this->assertNotEmpty($product['name']);
            $this->assertNotEmpty($product['description']);
            $this->assertTrue($product['price'] >= 0);
        } else {
            $this->assertTrue(true); // تخطي الاختبار إذا لم توجد منتجات
        }
    }
    
    public function testSEOMetaTags() {
        require_once '../includes/seo.php';
        global $seoManager;
        
        $metaTags = $seoManager->renderMetaTags('home');
        
        $this->assertContains('<title>', $metaTags);
        $this->assertContains('description', $metaTags);
        $this->assertContains('og:title', $metaTags);
    }
}

/**
 * اختبارات الأداء
 */
class PerformanceTest extends TestCase {
    
    public function testDatabaseQueryPerformance() {
        global $database;
        
        $startTime = microtime(true);
        
        // تشغيل استعلام
        $products = $database->fetchAll("SELECT * FROM products LIMIT 10");
        
        $endTime = microtime(true);
        $duration = $endTime - $startTime;
        
        // يجب أن يكون الاستعلام سريعاً (أقل من ثانية)
        $this->assertTrue($duration < 1.0, "Query took too long: {$duration} seconds");
    }
    
    public function testCachePerformance() {
        require_once '../includes/redis_cache.php';
        $cache = new RedisCache();
        
        if (!$cache->isConnected()) {
            $this->assertTrue(true);
            return;
        }
        
        $startTime = microtime(true);
        
        // اختبار سرعة التخزين والاسترجاع
        for ($i = 0; $i < 100; $i++) {
            $cache->set("test_key_{$i}", "test_value_{$i}");
            $cache->get("test_key_{$i}");
        }
        
        $endTime = microtime(true);
        $duration = $endTime - $startTime;
        
        // يجب أن تكون عمليات الكاش سريعة
        $this->assertTrue($duration < 2.0, "Cache operations took too long: {$duration} seconds");
    }
}

?>