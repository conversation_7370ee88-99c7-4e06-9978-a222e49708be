<?php
require_once 'includes/layout.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$pageTitle = 'إدارة الفئات';
$currentPage = 'categories';
$pageDescription = 'إدارة وتنظيم فئات المنتجات في النظام';
$breadcrumbs = [
    ['title' => 'الفئات']
];

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_category':
                $name = trim($_POST['name']);
                $description = trim($_POST['description']);
                $is_active = isset($_POST['is_active']) ? 1 : 0;
                
                if (!empty($name)) {
                    $stmt = $db->prepare("INSERT INTO product_categories (name, description, is_active, created_at) VALUES (?, ?, ?, NOW())");
                    if ($stmt->execute([$name, $description, $is_active])) {
                        $success = "تم إضافة الفئة بنجاح";
                    } else {
                        $error = "حدث خطأ أثناء إضافة الفئة";
                    }
                } else {
                    $error = "اسم الفئة مطلوب";
                }
                break;
                
            case 'edit_category':
                $id = (int)$_POST['id'];
                $name = trim($_POST['name']);
                $description = trim($_POST['description']);
                $is_active = isset($_POST['is_active']) ? 1 : 0;
                
                if (!empty($name) && $id > 0) {
                    $stmt = $db->prepare("UPDATE product_categories SET name = ?, description = ?, is_active = ?, updated_at = NOW() WHERE id = ?");
                    if ($stmt->execute([$name, $description, $is_active, $id])) {
                        $success = "تم تحديث الفئة بنجاح";
                    } else {
                        $error = "حدث خطأ أثناء تحديث الفئة";
                    }
                } else {
                    $error = "البيانات غير صحيحة";
                }
                break;
                
            case 'delete_category':
                $id = (int)$_POST['id'];
                if ($id > 0) {
                    // التحقق من وجود منتجات مرتبطة بهذه الفئة
                    $stmt = $db->prepare("SELECT COUNT(*) FROM products WHERE category_id = ?");
                    $stmt->execute([$id]);
                    $productCount = $stmt->fetchColumn();
                    
                    if ($productCount > 0) {
                        $error = "لا يمكن حذف هذه الفئة لأنها تحتوي على منتجات";
                    } else {
                        $stmt = $db->prepare("DELETE FROM product_categories WHERE id = ?");
                        if ($stmt->execute([$id])) {
                            $success = "تم حذف الفئة بنجاح";
                        } else {
                            $error = "حدث خطأ أثناء حذف الفئة";
                        }
                    }
                }
                break;
        }
    }
}

// جلب جميع الفئات
$stmt = $database->getConnection()->query("SELECT c.*, COUNT(p.id) as product_count FROM product_categories c LEFT JOIN products p ON c.id = p.category_id GROUP BY c.id ORDER BY c.created_at DESC");
$categories = $stmt->fetchAll();

// تعيين المتغيرات للرسائل
if (isset($success)) {
    $message = $success;
}
if (isset($error)) {
    $error = $error;
}

startLayout();
showPageHeader();
showMessages();
?>

<div class="flex justify-between items-center mb-6">
    <h2 class="text-2xl font-bold text-gray-900">قائمة الفئات</h2>
    <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700" onclick="openAddModal()">
        <i class="fas fa-plus ml-2"></i> إضافة فئة جديدة
    </button>
</div>
<div class="bg-white rounded-lg shadow overflow-hidden">
    <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الرقم</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">اسم الفئة</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الوصف</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">عدد المنتجات</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الإنشاء</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            <?php if (empty($categories)): ?>
                <tr>
                    <td colspan="7" class="px-6 py-4 text-center text-gray-500">لا توجد فئات</td>
                </tr>
            <?php else: ?>
                <?php foreach ($categories as $category): ?>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo $category['id']; ?></td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"><?php echo htmlspecialchars($category['name']); ?></td>
                        <td class="px-6 py-4 text-sm text-gray-500"><?php echo htmlspecialchars($category['description'] ?: 'لا يوجد وصف'); ?></td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo $category['product_count']; ?></td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $category['is_active'] ? 'bg-indigo-100 text-indigo-800' : 'bg-pink-100 text-pink-800'; ?>">
                                <?php echo $category['is_active'] ? 'نشط' : 'غير نشط'; ?>
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><?php echo date('Y-m-d H:i', strtotime($category['created_at'])); ?></td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button class="text-indigo-600 hover:text-indigo-900 ml-3" onclick="editCategory(<?php echo htmlspecialchars(json_encode($category)); ?>)">
                                تعديل
                            </button>
                            <?php if ($category['product_count'] == 0): ?>
                                <button class="text-red-600 hover:text-red-900" onclick="deleteCategory(<?php echo $category['id']; ?>, '<?php echo htmlspecialchars($category['name']); ?>')">
                                    حذف
                                </button>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<!-- نافذة إضافة/تعديل فئة -->
<div id="categoryModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center pb-3">
            <h3 id="modalTitle" class="text-lg font-bold text-gray-900">إضافة فئة جديدة</h3>
            <button class="text-gray-400 hover:text-gray-600" onclick="closeModal()">
                <span class="sr-only">إغلاق</span>
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
        <form id="categoryForm" method="POST">
            <input type="hidden" name="action" id="formAction" value="add_category">
            <input type="hidden" name="id" id="categoryId">
            
            <div class="mb-4">
                <label for="categoryName" class="block text-sm font-medium text-gray-700 mb-2">اسم الفئة *</label>
                <input type="text" id="categoryName" name="name" required 
                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            
            <div class="mb-4">
                <label for="categoryDescription" class="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                <textarea id="categoryDescription" name="description" rows="3"
                          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
            </div>
            
            <div class="mb-4">
                <label class="flex items-center">
                    <input type="checkbox" id="categoryActive" name="is_active" checked
                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                    <span class="mr-2 text-sm font-medium text-gray-700">فئة نشطة</span>
                </label>
            </div>
            
            <div class="flex justify-end space-x-3 space-x-reverse pt-4">
                <button type="button" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400" onclick="closeModal()">إلغاء</button>
                <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">حفظ</button>
            </div>
        </form>
    </div>
</div>

<!-- نافذة تأكيد الحذف -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center pb-3">
            <h3 class="text-lg font-bold text-gray-900">تأكيد الحذف</h3>
            <button class="text-gray-400 hover:text-gray-600" onclick="closeDeleteModal()">
                <span class="sr-only">إغلاق</span>
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
        <div class="mt-2">
            <p class="text-sm text-gray-500">هل أنت متأكد من حذف الفئة "<span id="deleteCategoryName" class="font-medium"></span>"؟</p>
            <p class="text-sm text-red-600 mt-2">هذا الإجراء لا يمكن التراجع عنه.</p>
        </div>
        <div class="flex justify-end space-x-3 space-x-reverse pt-4">
            <button type="button" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400" onclick="closeDeleteModal()">إلغاء</button>
            <form id="deleteForm" method="POST" style="display: inline;">
                <input type="hidden" name="action" value="delete_category">
                <input type="hidden" name="id" id="deleteCategoryId">
                <button type="submit" class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700">حذف</button>
            </form>
        </div>
    </div>
</div>



<script>
function openAddModal() {
    document.getElementById('modalTitle').textContent = 'إضافة فئة جديدة';
    document.getElementById('formAction').value = 'add_category';
    document.getElementById('categoryId').value = '';
    document.getElementById('categoryName').value = '';
    document.getElementById('categoryDescription').value = '';
    document.getElementById('categoryActive').checked = true;
    document.getElementById('categoryModal').classList.remove('hidden');
}

function editCategory(category) {
    document.getElementById('modalTitle').textContent = 'تعديل الفئة';
    document.getElementById('formAction').value = 'edit_category';
    document.getElementById('categoryId').value = category.id;
    document.getElementById('categoryName').value = category.name;
    document.getElementById('categoryDescription').value = category.description || '';
    document.getElementById('categoryActive').checked = category.is_active == 1;
    document.getElementById('categoryModal').classList.remove('hidden');
}

function deleteCategory(id, name) {
    document.getElementById('deleteCategoryName').textContent = name;
    document.getElementById('deleteCategoryId').value = id;
    document.getElementById('deleteModal').classList.remove('hidden');
}

function closeModal() {
    document.getElementById('categoryModal').classList.add('hidden');
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
}

// إغلاق النافذة عند النقر خارجها
window.onclick = function(event) {
    const categoryModal = document.getElementById('categoryModal');
    const deleteModal = document.getElementById('deleteModal');
    
    if (event.target == categoryModal) {
        categoryModal.classList.add('hidden');
    }
    if (event.target == deleteModal) {
        deleteModal.classList.add('hidden');
    }
}
</script>

<?php endLayout(); ?>