<?php
/**
 * صفحة إدارة الأدوار والصلاحيات
 * Roles and Permissions Management
 */

// إعداد متغيرات الصفحة
$pageTitle = 'إدارة الأدوار والصلاحيات';
$pageDescription = 'إدارة أدوار المستخدمين والصلاحيات المختلفة في النظام';
$currentPage = 'roles';
$breadcrumbs = [
    ['title' => 'المستخدمين', 'url' => '/admin/users.php'],
    ['title' => 'الأدوار والصلاحيات']
];

// تضمين التخطيط
require_once 'includes/layout.php';
require_once '../includes/permissions.php';

// إنشاء كائن إدارة الصلاحيات
$permissionManager = new PermissionManager($database);

// معالجة الطلبات
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'create_role':
                $name = trim($_POST['role_name'] ?? '');
                $description = trim($_POST['role_description'] ?? '');
                $permissions = $_POST['permissions'] ?? [];
                
                if (empty($name)) {
                    throw new Exception('اسم الدور مطلوب');
                }
                
                // إنشاء الدور
                $roleId = $database->insert('roles', [
                    'name' => $name,
                    'description' => $description,
                    'created_at' => date('Y-m-d H:i:s')
                ]);
                
                // إضافة الصلاحيات للدور
                foreach ($permissions as $permissionName) {
                    // البحث عن معرف الصلاحية
                    $permission = $database->fetch("SELECT id FROM permissions WHERE name = :name", ['name' => $permissionName]);
                    if ($permission) {
                        $database->insert('role_permissions', [
                            'role_id' => $roleId,
                            'permission_id' => $permission['id']
                        ]);
                    }
                }
                
                $message = 'تم إنشاء الدور بنجاح';
                break;
                
            case 'update_role':
                $roleId = (int)($_POST['role_id'] ?? 0);
                $name = trim($_POST['role_name'] ?? '');
                $description = trim($_POST['role_description'] ?? '');
                $permissions = $_POST['permissions'] ?? [];
                
                if (empty($name) || $roleId <= 0) {
                    throw new Exception('بيانات غير صحيحة');
                }
                
                // تحديث الدور
                $database->update('roles', [
                    'name' => $name,
                    'description' => $description,
                    'updated_at' => date('Y-m-d H:i:s')
                ], 'id = :id', ['id' => $roleId]);
                
                // حذف الصلاحيات القديمة
                $database->delete('role_permissions', 'role_id = :role_id', ['role_id' => $roleId]);
                
                // إضافة الصلاحيات الجديدة
                foreach ($permissions as $permissionName) {
                    // البحث عن معرف الصلاحية
                    $permission = $database->fetch("SELECT id FROM permissions WHERE name = :name", ['name' => $permissionName]);
                    if ($permission) {
                        $database->insert('role_permissions', [
                            'role_id' => $roleId,
                            'permission_id' => $permission['id']
                        ]);
                    }
                }
                
                $message = 'تم تحديث الدور بنجاح';
                break;
                
            case 'delete_role':
                $roleId = (int)($_POST['role_id'] ?? 0);
                
                if ($roleId <= 0) {
                    throw new Exception('معرف الدور غير صحيح');
                }
                
                // التحقق من عدم وجود مستخدمين مرتبطين بهذا الدور
                $usersCount = $database->fetch("SELECT COUNT(*) as count FROM users WHERE role = (SELECT name FROM roles WHERE id = ?)", [$roleId])['count'];
                
                if ($usersCount > 0) {
                    throw new Exception('لا يمكن حذف الدور لوجود مستخدمين مرتبطين به');
                }
                
                // حذف صلاحيات الدور
                $database->delete('role_permissions', 'role_id = :role_id', ['role_id' => $roleId]);
                
                // حذف الدور
                $database->delete('roles', 'id = :id', ['id' => $roleId]);
                
                $message = 'تم حذف الدور بنجاح';
                break;
                
            case 'assign_role':
                $userId = (int)($_POST['user_id'] ?? 0);
                $roleName = trim($_POST['role_name'] ?? '');
                
                if ($userId <= 0 || empty($roleName)) {
                    throw new Exception('بيانات غير صحيحة');
                }
                
                $database->update('users', [
                    'role' => $roleName,
                    'updated_at' => date('Y-m-d H:i:s')
                ], 'id = :id', ['id' => $userId]);
                
                $message = 'تم تعيين الدور للمستخدم بنجاح';
                break;
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// جلب البيانات
try {
    // جلب جميع الأدوار
    $roles = $database->fetchAll("
        SELECT r.*, 
               COUNT(DISTINCT u.id) as users_count,
               GROUP_CONCAT(DISTINCT p.name) as permissions
        FROM roles r 
        LEFT JOIN users u ON u.role = r.name 
        LEFT JOIN role_permissions rp ON rp.role_id = r.id
        LEFT JOIN permissions p ON p.id = rp.permission_id
        GROUP BY r.id 
        ORDER BY r.created_at DESC
    ");
    
    // جلب جميع المستخدمين
    $users = $database->fetchAll("
        SELECT id, name, email, role, status, created_at 
        FROM users 
        ORDER BY created_at DESC
    ");
    
    // جلب الصلاحيات من قاعدة البيانات
    $allPermissions = $database->fetchAll("SELECT * FROM permissions WHERE is_active = 1 ORDER BY category, name");
    
    // تجميع الصلاحيات حسب الفئة
    $availablePermissions = [];
    foreach ($allPermissions as $permission) {
        $category = $permission['category'] ?? 'general';
        $availablePermissions[$category][$permission['name']] = $permission['description'];
    }
    
} catch (Exception $e) {
    $error = 'خطأ في جلب البيانات: ' . $e->getMessage();
    $roles = [];
    $users = [];
    $availablePermissions = [];
}

// بدء التخطيط
startLayout();
?>

<!-- رأس الصفحة -->
<?php showPageHeader(); ?>

<!-- عرض الرسائل -->
<?php showMessages(); ?>

<!-- إحصائيات سريعة -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                <i class="fas fa-user-tag text-xl"></i>
            </div>
            <div class="mr-4">
                <h3 class="text-lg font-semibold text-gray-900"><?php echo count($roles); ?></h3>
                <p class="text-gray-600">إجمالي الأدوار</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-pink-100 text-pink-600">
                <i class="fas fa-users text-xl"></i>
            </div>
            <div class="mr-4">
                <h3 class="text-lg font-semibold text-gray-900"><?php echo count($users); ?></h3>
                <p class="text-gray-600">إجمالي المستخدمين</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                <i class="fas fa-key text-xl"></i>
            </div>
            <div class="mr-4">
                <h3 class="text-lg font-semibold text-gray-900"><?php echo array_sum(array_map('count', $availablePermissions)); ?></h3>
                <p class="text-gray-600">إجمالي الصلاحيات</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-orange-100 text-orange-600">
                <i class="fas fa-shield-alt text-xl"></i>
            </div>
            <div class="mr-4">
                <h3 class="text-lg font-semibold text-gray-900"><?php echo count(array_filter($users, function($u) { return $u['status'] === 'active'; })); ?></h3>
                <p class="text-gray-600">المستخدمين النشطين</p>
            </div>
        </div>
    </div>
</div>

<!-- التبويبات -->
<?php
$tabs = [
    'roles' => [
        'title' => 'إدارة الأدوار',
        'icon' => 'fas fa-user-tag',
        'content' => ''
    ],
    'permissions' => [
        'title' => 'الصلاحيات المتاحة',
        'icon' => 'fas fa-key',
        'content' => ''
    ],
    'assignments' => [
        'title' => 'تعيين الأدوار',
        'icon' => 'fas fa-user-cog',
        'content' => ''
    ]
];

// محتوى تبويب إدارة الأدوار
ob_start();
?>
<div class="space-y-6">
    <!-- زر إضافة دور جديد -->
    <div class="flex justify-between items-center">
        <h3 class="text-lg font-medium text-gray-900">قائمة الأدوار</h3>
        <button onclick="openModal('addRoleModal')" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200">
            <i class="fas fa-plus ml-2"></i>
            إضافة دور جديد
        </button>
    </div>
    
    <!-- جدول الأدوار -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">اسم الدور</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الوصف</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">عدد المستخدمين</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">عدد الصلاحيات</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الإنشاء</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <?php foreach ($roles as $role): ?>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center ml-3">
                                    <i class="fas fa-user-tag text-blue-600"></i>
                                </div>
                                <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($role['name']); ?></div>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-gray-900"><?php echo htmlspecialchars($role['description'] ?: 'لا يوجد وصف'); ?></div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                <?php echo $role['users_count']; ?> مستخدم
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pink-100 text-pink-800">
                                <?php echo $role['permissions'] ? count(explode(',', $role['permissions'])) : 0; ?> صلاحية
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <?php echo date('Y/m/d', strtotime($role['created_at'])); ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2 space-x-reverse">
                            <button onclick="editRole(<?php echo htmlspecialchars(json_encode($role)); ?>)" 
                                    class="text-blue-600 hover:text-blue-900 transition-colors duration-200">
                                <i class="fas fa-edit"></i> تعديل
                            </button>
                            <button onclick="deleteRole(<?php echo $role['id']; ?>, '<?php echo htmlspecialchars($role['name']); ?>')" 
                                    class="text-red-600 hover:text-red-900 transition-colors duration-200">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                        </td>
                    </tr>
                <?php endforeach; ?>
                
                <?php if (empty($roles)): ?>
                    <tr>
                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                            <i class="fas fa-user-tag text-4xl text-gray-300 mb-4"></i>
                            <p>لا توجد أدوار محددة بعد</p>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>
<?php
$tabs['roles']['content'] = ob_get_clean();

// محتوى تبويب الصلاحيات المتاحة
ob_start();
?>
<div class="space-y-6">
    <h3 class="text-lg font-medium text-gray-900">الصلاحيات المتاحة في النظام</h3>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <?php foreach ($availablePermissions as $category => $permissions): ?>
            <div class="bg-white rounded-lg shadow-md p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-folder text-blue-600 ml-2"></i>
                    <?php 
                    $categoryNames = [
                        'admin' => 'الإدارة',
                        'users' => 'المستخدمين',
                        'roles' => 'الأدوار',
                        'products' => 'المنتجات',
                        'reviews' => 'المراجعات',
                        'reports' => 'التقارير',
                        'content' => 'المحتوى',
                        'orders' => 'الطلبات',
                        'settings' => 'الإعدادات',
                        'general' => 'عام'
                    ];
                    echo $categoryNames[$category] ?? $category;
                    ?>
                </h4>
                <div class="space-y-2">
                    <?php foreach ($permissions as $key => $name): ?>
                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                            <span class="text-sm text-gray-700"><?php echo htmlspecialchars($name); ?></span>
                            <code class="text-xs bg-gray-200 px-2 py-1 rounded"><?php echo htmlspecialchars($key); ?></code>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
</div>
<?php
$tabs['permissions']['content'] = ob_get_clean();

// محتوى تبويب تعيين الأدوار
ob_start();
?>
<div class="space-y-6">
    <h3 class="text-lg font-medium text-gray-900">تعيين الأدوار للمستخدمين</h3>
    
    <!-- جدول المستخدمين -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المستخدم</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">البريد الإلكتروني</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الدور الحالي</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ التسجيل</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <?php foreach ($users as $user): ?>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center ml-3">
                                    <span class="text-sm font-medium text-blue-600">
                                        <?php echo strtoupper(substr($user['name'], 0, 1)); ?>
                                    </span>
                                </div>
                                <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($user['name']); ?></div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <?php echo htmlspecialchars($user['email']); ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                <?php 
                                switch($user['role']) {
                                    case 'admin': echo 'bg-red-100 text-red-800'; break;
                                    case 'manager': echo 'bg-yellow-100 text-yellow-800'; break;
                                    case 'user': echo 'bg-pink-100 text-pink-800'; break;
                                    default: echo 'bg-gray-100 text-gray-800';
                                }
                                ?>">
                                <?php 
                                $roleNames = [
                                    'admin' => 'مدير',
                                    'manager' => 'مشرف',
                                    'user' => 'مستخدم'
                                ];
                                echo $roleNames[$user['role']] ?? $user['role'];
                                ?>
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                <?php echo $user['status'] === 'active' ? 'bg-pink-100 text-pink-800' : 'bg-red-100 text-red-800'; ?>">
                                <?php echo $user['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <?php echo date('Y/m/d', strtotime($user['created_at'])); ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button onclick="assignRole(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['name']); ?>', '<?php echo htmlspecialchars($user['role']); ?>')" 
                                    class="text-blue-600 hover:text-blue-900 transition-colors duration-200">
                                <i class="fas fa-user-cog"></i> تغيير الدور
                            </button>
                        </td>
                    </tr>
                <?php endforeach; ?>
                
                <?php if (empty($users)): ?>
                    <tr>
                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                            <i class="fas fa-users text-4xl text-gray-300 mb-4"></i>
                            <p>لا توجد مستخدمين مسجلين بعد</p>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>
<?php
$tabs['assignments']['content'] = ob_get_clean();

// عرض التبويبات
createTabs($tabs, 'roles');
?>

<!-- نماذج منبثقة (Modals) -->

<!-- نموذج إضافة دور جديد -->
<div id="addRoleModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">إضافة دور جديد</h3>
                <button onclick="closeModal('addRoleModal')" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form method="POST" class="space-y-4">
                <input type="hidden" name="action" value="create_role">
                
                <div>
                    <label for="role_name" class="block text-sm font-medium text-gray-700 mb-2">اسم الدور *</label>
                    <input type="text" id="role_name" name="role_name" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="مثال: مشرف المبيعات">
                </div>
                
                <div>
                    <label for="role_description" class="block text-sm font-medium text-gray-700 mb-2">وصف الدور</label>
                    <textarea id="role_description" name="role_description" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              placeholder="وصف مختصر لمهام ومسؤوليات هذا الدور"></textarea>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">الصلاحيات</label>
                    <div class="max-h-64 overflow-y-auto border border-gray-300 rounded-md p-4">
                        <?php foreach ($availablePermissions as $category => $permissions): ?>
                            <div class="mb-4">
                                <h4 class="font-medium text-gray-900 mb-2">
                                    <?php echo $categoryNames[$category] ?? $category; ?>
                                </h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                                    <?php foreach ($permissions as $key => $name): ?>
                                        <label class="flex items-center">
                                            <input type="checkbox" name="permissions[]" value="<?php echo htmlspecialchars($key); ?>"
                                                   class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                            <span class="mr-2 text-sm text-gray-700"><?php echo htmlspecialchars($name); ?></span>
                                        </label>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 space-x-reverse pt-4">
                    <button type="button" onclick="closeModal('addRoleModal')"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors duration-200">
                        إلغاء
                    </button>
                    <button type="submit"
                            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors duration-200">
                        <i class="fas fa-save ml-2"></i>
                        حفظ الدور
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نموذج تعديل دور -->
<div id="editRoleModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">تعديل الدور</h3>
                <button onclick="closeModal('editRoleModal')" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form method="POST" class="space-y-4">
                <input type="hidden" name="action" value="update_role">
                <input type="hidden" id="edit_role_id" name="role_id">
                
                <div>
                    <label for="edit_role_name" class="block text-sm font-medium text-gray-700 mb-2">اسم الدور *</label>
                    <input type="text" id="edit_role_name" name="role_name" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label for="edit_role_description" class="block text-sm font-medium text-gray-700 mb-2">وصف الدور</label>
                    <textarea id="edit_role_description" name="role_description" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">الصلاحيات</label>
                    <div class="max-h-64 overflow-y-auto border border-gray-300 rounded-md p-4">
                        <?php foreach ($availablePermissions as $category => $permissions): ?>
                            <div class="mb-4">
                                <h4 class="font-medium text-gray-900 mb-2">
                                    <?php echo $categoryNames[$category] ?? $category; ?>
                                </h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                                    <?php foreach ($permissions as $key => $name): ?>
                                        <label class="flex items-center">
                                            <input type="checkbox" name="permissions[]" value="<?php echo htmlspecialchars($key); ?>"
                                                   class="edit-permission rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                            <span class="mr-2 text-sm text-gray-700"><?php echo htmlspecialchars($name); ?></span>
                                        </label>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 space-x-reverse pt-4">
                    <button type="button" onclick="closeModal('editRoleModal')"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors duration-200">
                        إلغاء
                    </button>
                    <button type="submit"
                            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors duration-200">
                        <i class="fas fa-save ml-2"></i>
                        حفظ التغييرات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نموذج تعيين دور -->
<div id="assignRoleModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">تعيين دور</h3>
                <button onclick="closeModal('assignRoleModal')" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form method="POST" class="space-y-4">
                <input type="hidden" name="action" value="assign_role">
                <input type="hidden" id="assign_user_id" name="user_id">
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">المستخدم</label>
                    <p id="assign_user_name" class="text-sm text-gray-900 bg-gray-50 p-2 rounded"></p>
                </div>
                
                <div>
                    <label for="assign_role_name" class="block text-sm font-medium text-gray-700 mb-2">الدور الجديد *</label>
                    <select id="assign_role_name" name="role_name" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">اختر الدور</option>
                        <?php foreach ($roles as $role): ?>
                            <option value="<?php echo htmlspecialchars($role['name']); ?>">
                                <?php echo htmlspecialchars($role['name']); ?>
                            </option>
                        <?php endforeach; ?>
                        <option value="admin">مدير</option>
                        <option value="manager">مشرف</option>
                        <option value="user">مستخدم</option>
                    </select>
                </div>
                
                <div class="flex justify-end space-x-3 space-x-reverse pt-4">
                    <button type="button" onclick="closeModal('assignRoleModal')"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors duration-200">
                        إلغاء
                    </button>
                    <button type="submit"
                            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors duration-200">
                        <i class="fas fa-user-cog ml-2"></i>
                        تعيين الدور
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// دوال JavaScript لإدارة النماذج المنبثقة
function openModal(modalId) {
    document.getElementById(modalId).classList.remove('hidden');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

// دالة تعديل الدور
function editRole(role) {
    document.getElementById('edit_role_id').value = role.id;
    document.getElementById('edit_role_name').value = role.name;
    document.getElementById('edit_role_description').value = role.description || '';
    
    // إعادة تعيين جميع الصلاحيات
    const checkboxes = document.querySelectorAll('.edit-permission');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    
    // تحديد الصلاحيات الحالية
    if (role.permissions) {
        const permissions = role.permissions.split(',');
        permissions.forEach(permission => {
            const permissionName = permission.trim();
            const checkbox = document.querySelector(`.edit-permission[value="${permissionName}"]`);
            if (checkbox) {
                checkbox.checked = true;
            }
        });
    }
    
    openModal('editRoleModal');
}

// دالة حذف الدور
function deleteRole(roleId, roleName) {
    if (confirm(`هل أنت متأكد من حذف الدور "${roleName}"؟\n\nسيتم حذف جميع الصلاحيات المرتبطة بهذا الدور.`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_role">
            <input type="hidden" name="role_id" value="${roleId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// دالة تعيين دور
function assignRole(userId, userName, currentRole) {
    document.getElementById('assign_user_id').value = userId;
    document.getElementById('assign_user_name').textContent = userName;
    document.getElementById('assign_role_name').value = currentRole;
    
    openModal('assignRoleModal');
}

// إغلاق النماذج عند النقر خارجها
window.onclick = function(event) {
    const modals = ['addRoleModal', 'editRoleModal', 'assignRoleModal'];
    modals.forEach(modalId => {
        const modal = document.getElementById(modalId);
        if (event.target === modal) {
            closeModal(modalId);
        }
    });
}

// إغلاق النماذج بمفتاح Escape
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        const modals = ['addRoleModal', 'editRoleModal', 'assignRoleModal'];
        modals.forEach(modalId => {
            closeModal(modalId);
        });
    }
});
</script>

<?php
// إنهاء التخطيط
endLayout();
?>