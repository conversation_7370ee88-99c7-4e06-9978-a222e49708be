<?php
session_start();
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 تشخيص دالة saveUserReview</h1>";

// محاكاة تسجيل دخول المستخدم
if (!isLoggedIn()) {
    $_SESSION['user_id'] = 3;
    $_SESSION['user_name'] = 'المدير العام';
    $_SESSION['user_email'] = '<EMAIL>';
    $_SESSION['user_role'] = 'super-admin';
}

$current_user = getCurrentUser();
echo "<h2>1. معلومات المستخدم:</h2>";
echo "<pre>" . print_r($current_user, true) . "</pre>";

// بيانات الاختبار
$product_id = 3;
$user_id = $current_user['id'];
$customer_name = $current_user['name'];
$customer_email = $current_user['email'];
$rating = 5;
$review_text = 'اختبار تشخيصي';
$review_title = 'تشخيص';

echo "<h2>2. بيانات الاختبار:</h2>";
echo "<ul>";
echo "<li><strong>product_id:</strong> $product_id</li>";
echo "<li><strong>user_id:</strong> $user_id</li>";
echo "<li><strong>customer_name:</strong> $customer_name</li>";
echo "<li><strong>customer_email:</strong> $customer_email</li>";
echo "<li><strong>rating:</strong> $rating</li>";
echo "<li><strong>review_text:</strong> $review_text</li>";
echo "<li><strong>review_title:</strong> $review_title</li>";
echo "</ul>";

// حذف أي تقييم سابق
echo "<h2>3. حذف التقييمات السابقة:</h2>";
try {
    global $database;
    $deleted = $database->query("DELETE FROM reviews WHERE product_id = ? AND user_id = ?", [$product_id, $user_id]);
    echo "✅ تم حذف التقييمات السابقة<br>";
} catch (Exception $e) {
    echo "❌ خطأ في الحذف: " . $e->getMessage() . "<br>";
}

// اختبار دالة saveUserReview مع تفاصيل الأخطاء
echo "<h2>4. اختبار دالة saveUserReview:</h2>";

try {
    // فحص وجود المنتج أولاً
    $product = getProduct($product_id);
    if (!$product) {
        echo "❌ المنتج غير موجود<br>";
        exit;
    } else {
        echo "✅ المنتج موجود: " . $product['name'] . "<br>";
    }
    
    // فحص وجود تقييم سابق
    $existing = $database->fetch(
        "SELECT id FROM reviews WHERE product_id = :product_id AND user_id = :user_id",
        ['product_id' => $product_id, 'user_id' => $user_id]
    );
    
    if ($existing) {
        echo "⚠️ يوجد تقييم سابق: " . $existing['id'] . "<br>";
    } else {
        echo "✅ لا يوجد تقييم سابق<br>";
    }
    
    // فحص إعداد التفعيل التلقائي
    $autoApproval = getSetting('reviews_auto_approval', false);
    echo "📋 التفعيل التلقائي: " . ($autoApproval ? 'مفعل' : 'غير مفعل') . "<br>";
    
    // محاولة الإدراج مباشرة
    echo "<h3>أ. محاولة الإدراج المباشر:</h3>";
    
    $insertData = [
        'product_id' => (int) $product_id,
        'user_id' => (int) $user_id,
        'name' => sanitizeInput($customer_name),
        'email' => sanitizeInput($customer_email),
        'rating' => (int) $rating,
        'title' => sanitizeInput($review_title),
        'comment' => sanitizeInput($review_text),
        'is_approved' => $autoApproval ? 1 : 0,
        'is_rejected' => 0,
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    echo "<strong>بيانات الإدراج:</strong><br>";
    echo "<pre>" . print_r($insertData, true) . "</pre>";
    
    $result = $database->insert('reviews', $insertData);
    
    if ($result && $result > 0) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
        echo "✅ نجح الإدراج المباشر! معرف التقييم: $result";
        echo "</div>";
        
        // فحص البيانات المحفوظة
        $saved = $database->fetch("SELECT * FROM reviews WHERE id = ?", [$result]);
        if ($saved) {
            echo "<h3>البيانات المحفوظة:</h3>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            foreach ($saved as $key => $value) {
                $color = ($key == 'user_id' && $value == $user_id) ? 'green' : 'black';
                echo "<tr><td><strong>$key</strong></td><td style='color: $color;'>" . ($value ?? 'NULL') . "</td></tr>";
            }
            echo "</table>";
            
            if ($saved['user_id'] == $user_id) {
                echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
                echo "🎉 <strong>نجح!</strong> user_id محفوظ بشكل صحيح: " . $saved['user_id'];
                echo "</div>";
            } else {
                echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
                echo "❌ <strong>فشل!</strong> user_id المحفوظ: " . ($saved['user_id'] ?? 'NULL') . " بدلاً من $user_id";
                echo "</div>";
            }
        }
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
        echo "❌ فشل الإدراج المباشر. النتيجة: " . var_export($result, true);
        echo "</div>";
    }
    
    echo "<h3>ب. اختبار دالة saveUserReview:</h3>";
    
    // حذف التقييم المحفوظ للاختبار
    if (isset($result) && $result > 0) {
        $database->query("DELETE FROM reviews WHERE id = ?", [$result]);
        echo "🗑️ تم حذف التقييم السابق للاختبار<br>";
    }
    
    $saveResult = saveUserReview($product_id, $user_id, $customer_name, $customer_email, $rating, $review_text, $review_title);
    
    if (is_numeric($saveResult) && $saveResult > 0) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
        echo "✅ نجحت دالة saveUserReview! معرف التقييم: $saveResult";
        echo "</div>";
        
        $functionSaved = $database->fetch("SELECT * FROM reviews WHERE id = ?", [$saveResult]);
        if ($functionSaved && $functionSaved['user_id'] == $user_id) {
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
            echo "🎉 <strong>الدالة تعمل بشكل صحيح!</strong> user_id: " . $functionSaved['user_id'];
            echo "</div>";
        }
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
        echo "❌ فشلت دالة saveUserReview: " . (is_string($saveResult) ? $saveResult : var_export($saveResult, true));
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
    echo "❌ خطأ في التنفيذ: " . $e->getMessage();
    echo "<br><strong>Stack trace:</strong><br>" . $e->getTraceAsString();
    echo "</div>";
}

echo "<h2>5. فحص آخر التقييمات في قاعدة البيانات:</h2>";
try {
    $recent_reviews = $database->fetchAll("SELECT * FROM reviews ORDER BY created_at DESC LIMIT 5");
    if (!empty($recent_reviews)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Product ID</th><th>User ID</th><th>Name</th><th>Rating</th><th>Created At</th></tr>";
        foreach ($recent_reviews as $review) {
            $user_id_color = $review['user_id'] ? 'green' : 'red';
            echo "<tr>";
            echo "<td>" . $review['id'] . "</td>";
            echo "<td>" . $review['product_id'] . "</td>";
            echo "<td style='color: $user_id_color; font-weight: bold;'>" . ($review['user_id'] ?? 'NULL') . "</td>";
            echo "<td>" . $review['name'] . "</td>";
            echo "<td>" . $review['rating'] . "</td>";
            echo "<td>" . $review['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>لا توجد تقييمات في قاعدة البيانات</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ في جلب التقييمات: " . $e->getMessage() . "</p>";
}
?>
