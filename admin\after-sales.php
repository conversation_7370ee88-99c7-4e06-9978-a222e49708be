<?php
// تعريف متغيرات الصفحة
$pageTitle = 'إدارة خدمات ما بعد البيع';
$currentPage = 'after-sales';
$pageDescription = 'إدارة محتوى صفحة خدمات ما بعد البيع';
$breadcrumbs = [
    ['title' => 'خدمات ما بعد البيع']
];

// تضمين التخطيط
require_once 'includes/layout.php';

// بدء التخطيط
startLayout();

$message = '';
$error = '';

// معالجة رفع الملفات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['support_file'])) {
    $upload_dir = '../uploads/support/';
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0777, true);
    }
    
    $file_name = $_FILES['support_file']['name'];
    $file_tmp = $_FILES['support_file']['tmp_name'];
    $file_size = $_FILES['support_file']['size'];
    $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
    
    $allowed_extensions = ['pdf', 'doc', 'docx', 'txt', 'zip', 'rar'];
    
    if (in_array($file_ext, $allowed_extensions) && $file_size <= 10485760) { // 10MB
        $new_file_name = time() . '_' . $file_name;
        $file_path = $upload_dir . $new_file_name;
        
        if (move_uploaded_file($file_tmp, $file_path)) {
            $file_title = $_POST['file_title'] ?? $file_name;
            $file_description = $_POST['file_description'] ?? '';
            
            $stmt = $db->prepare("INSERT INTO support_files (title, description, file_path, file_size, upload_date) VALUES (?, ?, ?, ?, NOW())");
            if ($stmt->execute([$file_title, $file_description, 'uploads/support/' . $new_file_name, $file_size])) {
                $message = 'تم رفع الملف بنجاح';
            } else {
                $error = 'خطأ في حفظ بيانات الملف';
            }
        } else {
            $error = 'خطأ في رفع الملف';
        }
    } else {
        $error = 'نوع الملف غير مدعوم أو حجم الملف كبير جداً';
    }
}

// معالجة حذف الملفات
if (isset($_GET['delete_file'])) {
    $file_id = $_GET['delete_file'];
    $stmt = $db->prepare("SELECT file_path FROM support_files WHERE id = ?");
    $stmt->execute([$file_id]);
    $file = $stmt->fetch();
    
    if ($file) {
        if (file_exists('../' . $file['file_path'])) {
            unlink('../' . $file['file_path']);
        }
        
        $stmt = $db->prepare("DELETE FROM support_files WHERE id = ?");
        if ($stmt->execute([$file_id])) {
            $message = 'تم حذف الملف بنجاح';
        }
    }
}

// معالجة النماذج
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !isset($_FILES['support_file'])) {
    $section = $_POST['section'] ?? '';
    
    switch ($section) {
        case 'header':
            $stmt = $db->prepare("INSERT INTO contact_info (section, field_name, field_value) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE field_value = VALUES(field_value)");
            $stmt->execute(['header', 'main_title', $_POST['header_main_title']]);
            $stmt->execute(['header', 'sub_title', $_POST['header_sub_title']]);
            $message = 'تم حفظ بيانات الهيدر بنجاح';
            break;
            
        case 'contact_methods':
            $stmt = $db->prepare("INSERT INTO contact_info (section, field_name, field_value) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE field_value = VALUES(field_value)");
            $stmt->execute(['contact_methods', 'main_title', $_POST['contact_main_title']]);
            $stmt->execute(['contact_methods', 'sub_title', $_POST['contact_sub_title']]);
            $stmt->execute(['contact_methods', 'show_section', $_POST['show_contact_section'] ?? '0']);
            
            // حفظ بيانات كروت التواصل
            if (isset($_POST['contact_cards'])) {
                foreach ($_POST['contact_cards'] as $index => $card) {
                    $stmt->execute(['contact_methods', "card_{$index}_title", $card['title']]);
                    $stmt->execute(['contact_methods', "card_{$index}_description", $card['description']]);
                    $stmt->execute(['contact_methods', "card_{$index}_icon", $card['icon']]);
                    $stmt->execute(['contact_methods', "card_{$index}_link", $card['link']]);
                    $stmt->execute(['contact_methods', "card_{$index}_show", $card['show'] ?? '0']);
                }
            }
            $message = 'تم حفظ بيانات طرق التواصل بنجاح';
            break;
            
        case 'services':
            $stmt = $db->prepare("INSERT INTO contact_info (section, field_name, field_value) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE field_value = VALUES(field_value)");
            $stmt->execute(['services', 'main_title', $_POST['services_main_title']]);
            $stmt->execute(['services', 'sub_title', $_POST['services_sub_title']]);
            $stmt->execute(['services', 'show_section', $_POST['show_services_section'] ?? '0']);
            
            // حفظ بيانات كروت الخدمات
            if (isset($_POST['service_cards'])) {
                foreach ($_POST['service_cards'] as $index => $card) {
                    $stmt->execute(['services', "card_{$index}_title", $card['title']]);
                    $stmt->execute(['services', "card_{$index}_description", $card['description']]);
                    $stmt->execute(['services', "card_{$index}_icon", $card['icon']]);
                    $stmt->execute(['services', "card_{$index}_show", $card['show'] ?? '0']);
                }
            }
            $message = 'تم حفظ بيانات الخدمات بنجاح';
            break;
            
        case 'warranty_plans':
            // حذف الخطط القديمة
            $stmt = $db->prepare("DELETE FROM warranty_plans");
            $stmt->execute();
            
            // إضافة الخطط الجديدة
            if (isset($_POST['warranty_plans'])) {
                $stmt = $db->prepare("INSERT INTO warranty_plans (title, description, price, features, is_featured, is_popular, is_visible) VALUES (?, ?, ?, ?, ?, ?, ?)");
                foreach ($_POST['warranty_plans'] as $plan) {
                    $stmt->execute([
                        $plan['title'],
                        $plan['description'],
                        $plan['price'],
                        $plan['features'],
                        $plan['is_featured'] ?? 0,
                        $plan['is_popular'] ?? 0,
                        $plan['is_visible'] ?? 1
                    ]);
                }
            }
            $message = 'تم حفظ خطط الضمان بنجاح';
            break;
            
        case 'why_choose':
            $stmt = $db->prepare("INSERT INTO contact_info (section, field_name, field_value) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE field_value = VALUES(field_value)");
            $stmt->execute(['why_choose', 'show_section', $_POST['show_why_choose'] ?? '0']);
            $stmt->execute(['why_choose', 'main_title', $_POST['why_choose_title']]);
            $stmt->execute(['why_choose', 'content', $_POST['why_choose_content']]);
            $message = 'تم حفظ قسم "لماذا تختار خطط الضمان" بنجاح';
            break;
            
        case 'testimonials':
            $stmt = $db->prepare("INSERT INTO contact_info (section, field_name, field_value) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE field_value = VALUES(field_value)");
            $stmt->execute(['testimonials', 'show_section', $_POST['show_testimonials'] ?? '0']);
            $stmt->execute(['testimonials', 'display_count', $_POST['testimonials_count']]);
            $message = 'تم حفظ إعدادات آراء العملاء بنجاح';
            break;
            
        case 'faqs':
            $stmt = $db->prepare("INSERT INTO contact_info (section, field_name, field_value) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE field_value = VALUES(field_value)");
            $stmt->execute(['faqs', 'show_section', $_POST['show_faqs'] ?? '0']);
            $stmt->execute(['faqs', 'display_count', $_POST['faqs_count']]);
            $message = 'تم حفظ إعدادات الأسئلة الشائعة بنجاح';
            break;
    }
}

// جلب البيانات الحالية
function getSupportContactInfo($section, $field) {
    global $db;
    $stmt = $db->prepare("SELECT field_value FROM contact_info WHERE section_key = ? AND field_name = ?");
    $stmt->execute([$section, $field]);
    $result = $stmt->fetch();
    return $result ? $result['field_value'] : '';
}

// جلب ملفات الدعم
$stmt = $db->prepare("SELECT * FROM support_files ORDER BY upload_date DESC");
$stmt->execute();
$support_files = $stmt->fetchAll();

// جلب خطط الضمان
$stmt = $db->prepare("SELECT * FROM warranty_plans ORDER BY id");
$stmt->execute();
$warranty_plans = $stmt->fetchAll();

// جلب التقييمات
$stmt = $db->prepare("SELECT * FROM testimonials WHERE status = 'approved' ORDER BY created_at DESC");
$stmt->execute();
$testimonials = $stmt->fetchAll();

// جلب الأسئلة الشائعة
$stmt = $db->prepare("SELECT * FROM faqs WHERE status = 'active' ORDER BY sort_order");
$stmt->execute();
$faqs = $stmt->fetchAll();

$active_tab = $_GET['tab'] ?? 'header';

// عرض رأس الصفحة والرسائل
showPageHeader();
showMessages($message, $error);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة صفحة الدعم الفني</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .nav-tabs .nav-link { border-radius: 10px 10px 0 0; margin-left: 5px; }
        .nav-tabs .nav-link.active { background-color: #0d6efd; color: white; }
        .card { border-radius: 15px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .btn { border-radius: 8px; }
        .form-control, .form-select { border-radius: 8px; }
        .dynamic-card { border: 2px dashed #dee2e6; padding: 20px; margin: 10px 0; border-radius: 10px; }
        .dynamic-card.filled { border: 2px solid #0d6efd; background-color: #f8f9fa; }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="fas fa-cogs me-2"></i>إدارة صفحة الدعم الفني</h4>
                    </div>
                    <div class="card-body">
                        <?php if ($message): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="fas fa-check-circle me-2"></i><?php echo $message; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($error): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <!-- التبويبات -->
                        <ul class="nav nav-tabs mb-4" id="supportTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link <?php echo $active_tab === 'header' ? 'active' : ''; ?>" 
                                        onclick="location.href='?tab=header'">
                                    <i class="fas fa-home me-2"></i>الصفحة الرئيسية
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link <?php echo $active_tab === 'contact_methods' ? 'active' : ''; ?>" 
                                        onclick="location.href='?tab=contact_methods'">
                                    <i class="fas fa-phone me-2"></i>طرق التواصل
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link <?php echo $active_tab === 'services' ? 'active' : ''; ?>" 
                                        onclick="location.href='?tab=services'">
                                    <i class="fas fa-tools me-2"></i>خدماتنا
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link <?php echo $active_tab === 'warranty_plans' ? 'active' : ''; ?>" 
                                        onclick="location.href='?tab=warranty_plans'">
                                    <i class="fas fa-shield-alt me-2"></i>خطط الضمان
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link <?php echo $active_tab === 'why_choose' ? 'active' : ''; ?>" 
                                        onclick="location.href='?tab=why_choose'">
                                    <i class="fas fa-star me-2"></i>لماذا تختارنا
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link <?php echo $active_tab === 'testimonials' ? 'active' : ''; ?>" 
                                        onclick="location.href='?tab=testimonials'">
                                    <i class="fas fa-comments me-2"></i>آراء العملاء
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link <?php echo $active_tab === 'faqs' ? 'active' : ''; ?>" 
                                        onclick="location.href='?tab=faqs'">
                                    <i class="fas fa-question-circle me-2"></i>الأسئلة الشائعة
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link <?php echo $active_tab === 'download_center' ? 'active' : ''; ?>" 
                                        onclick="location.href='?tab=download_center'">
                                    <i class="fas fa-download me-2"></i>مركز التحميل
                                </button>
                            </li>
                        </ul>

                        <!-- محتوى التبويبات -->
                        <div class="tab-content">
                            
                            <!-- تبويب الصفحة الرئيسية -->
                            <?php if ($active_tab === 'header'): ?>
                            <div class="tab-pane fade show active">
                                <form method="POST" class="row g-3">
                                    <input type="hidden" name="section" value="header">
                                    <div class="col-md-6">
                                        <label class="form-label">العنوان الرئيسي</label>
                                        <input type="text" class="form-control" name="header_main_title" 
                                               value="<?php echo htmlspecialchars(getSupportContactInfo('header', 'main_title')); ?>" 
                                               placeholder="الدعم الفني">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">العنوان الفرعي</label>
                                        <input type="text" class="form-control" name="header_sub_title" 
                                               value="<?php echo htmlspecialchars(getSupportContactInfo('header', 'sub_title')); ?>" 
                                               placeholder="نحن هنا لمساعدتك في أي وقت">
                                    </div>
                                    <div class="col-12">
                                        <button type="button" class="btn btn-primary" onclick="saveTestimonialsSettings()">
                                            <i class="fas fa-save me-2"></i>حفظ إعدادات التقييمات
                                        </button>
                                    </div>
                                </form>
                            </div>
                            <?php endif; ?>

                            <!-- تبويب طرق التواصل -->
                            <?php if ($active_tab === 'contact_methods'): ?>
                            <div class="tab-pane fade show active">
                                <form method="POST" class="row g-3">
                                    <input type="hidden" name="section" value="contact_methods">
                                    
                                    <div class="col-12">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="show_contact_section" value="1" 
                                                   <?php echo getSupportContactInfo('contact_methods', 'show_section') ? 'checked' : ''; ?>>
                                            <label class="form-check-label">إظهار قسم طرق التواصل</label>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <label class="form-label">العنوان الرئيسي</label>
                                        <input type="text" class="form-control" name="contact_main_title" 
                                               value="<?php echo htmlspecialchars(getSupportContactInfo('contact_methods', 'main_title')); ?>" 
                                               placeholder="طرق التواصل معنا">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">العنوان الفرعي</label>
                                        <input type="text" class="form-control" name="contact_sub_title" 
                                               value="<?php echo htmlspecialchars(getSupportContactInfo('contact_methods', 'sub_title')); ?>" 
                                               placeholder="تواصل معنا بالطريقة التي تناسبك">
                                    </div>
                                    
                                    <div class="col-12">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <h5 class="mb-0">كروت التواصل</h5>
                                            <button type="button" class="btn btn-success btn-sm" onclick="addContactCard()">
                                                <i class="fas fa-plus me-1"></i>إضافة كارد جديد
                                            </button>
                                        </div>
                                        <div id="contact-cards-container">
                                            <?php for ($i = 0; $i < 4; $i++): ?>
                                            <div class="dynamic-card">
                                                <div class="row g-3">
                                                    <div class="col-md-3">
                                                        <label class="form-label">عنوان الكارد</label>
                                                        <input type="text" class="form-control" name="contact_cards[<?php echo $i; ?>][title]" 
                                                               value="<?php echo htmlspecialchars(getSupportContactInfo('contact_methods', "card_{$i}_title")); ?>">
                                                    </div>
                                                    <div class="col-md-3">
                                                        <label class="form-label">الوصف</label>
                                                        <input type="text" class="form-control" name="contact_cards[<?php echo $i; ?>][description]" 
                                                               value="<?php echo htmlspecialchars(getSupportContactInfo('contact_methods', "card_{$i}_description")); ?>">
                                                    </div>
                                                    <div class="col-md-2">
                                                        <label class="form-label">الأيقونة</label>
                                                        <input type="text" class="form-control" name="contact_cards[<?php echo $i; ?>][icon]" 
                                                               value="<?php echo htmlspecialchars(getSupportContactInfo('contact_methods', "card_{$i}_icon")); ?>" 
                                                               placeholder="fas fa-phone">
                                                    </div>
                                                    <div class="col-md-3">
                                                        <label class="form-label">الرابط</label>
                                                        <input type="text" class="form-control" name="contact_cards[<?php echo $i; ?>][link]" 
                                                               value="<?php echo htmlspecialchars(getSupportContactInfo('contact_methods', "card_{$i}_link")); ?>">
                                                    </div>
                                                    <div class="col-md-1">
                                                        <label class="form-label">إظهار</label>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" name="contact_cards[<?php echo $i; ?>][show]" value="1" 
                                                                   <?php echo getSupportContactInfo('contact_methods', "card_{$i}_show") ? 'checked' : ''; ?>>
                                                        </div>
                                                    </div>
                                                    <div class="col-12">
                                                        <button type="button" class="btn btn-danger btn-sm" onclick="removeCard(this)">
                                                            <i class="fas fa-trash me-1"></i>حذف الكارد
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php endfor; ?>
                                        </div>
                                    </div>
                                    
                                    <div class="col-12">
                                        <button type="button" class="btn btn-primary" onclick="saveFaqsSettings()">
                                            <i class="fas fa-save me-2"></i>حفظ إعدادات الأسئلة الشائعة
                                        </button>
                                    </div>
                                </form>
                            </div>
                            <?php endif; ?>

                            <!-- تبويب الخدمات -->
                            <?php if ($active_tab === 'services'): ?>
                            <div class="tab-pane fade show active">
                                <form method="POST" class="row g-3">
                                    <input type="hidden" name="section" value="services">
                                    
                                    <div class="col-12">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="show_services_section" value="1" 
                                                   <?php echo getSupportContactInfo('services', 'show_section') ? 'checked' : ''; ?>>
                                            <label class="form-check-label">إظهار قسم الخدمات</label>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <label class="form-label">العنوان الرئيسي</label>
                                        <input type="text" class="form-control" name="services_main_title" 
                                               value="<?php echo htmlspecialchars(getSupportContactInfo('services', 'main_title')); ?>" 
                                               placeholder="خدماتنا">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">العنوان الفرعي</label>
                                        <input type="text" class="form-control" name="services_sub_title" 
                                               value="<?php echo htmlspecialchars(getSupportContactInfo('services', 'sub_title')); ?>" 
                                               placeholder="نقدم مجموعة شاملة من خدمات الدعم والصيانة">
                                    </div>
                                    
                                    <div class="col-12">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <h5 class="mb-0">كروت الخدمات</h5>
                                            <button type="button" class="btn btn-success btn-sm" onclick="addServiceCard()">
                                                <i class="fas fa-plus me-1"></i>إضافة كارد جديد
                                            </button>
                                        </div>
                                        <div id="service-cards-container">
                                            <?php for ($i = 0; $i < 4; $i++): ?>
                                            <div class="dynamic-card">
                                                <div class="row g-3">
                                                    <div class="col-md-3">
                                                        <label class="form-label">عنوان الخدمة</label>
                                                        <input type="text" class="form-control" name="service_cards[<?php echo $i; ?>][title]" 
                                                               value="<?php echo htmlspecialchars(getSupportContactInfo('services', "card_{$i}_title")); ?>">
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label class="form-label">وصف الخدمة</label>
                                                        <textarea class="form-control" name="service_cards[<?php echo $i; ?>][description]" rows="2"><?php echo htmlspecialchars(getSupportContactInfo('services', "card_{$i}_description")); ?></textarea>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <label class="form-label">الأيقونة</label>
                                                        <input type="text" class="form-control" name="service_cards[<?php echo $i; ?>][icon]" 
                                                               value="<?php echo htmlspecialchars(getSupportContactInfo('services', "card_{$i}_icon")); ?>" 
                                                               placeholder="fas fa-tools">
                                                    </div>
                                                    <div class="col-md-1">
                                                        <label class="form-label">إظهار</label>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" name="service_cards[<?php echo $i; ?>][show]" value="1" 
                                                                   <?php echo getSupportContactInfo('services', "card_{$i}_show") ? 'checked' : ''; ?>>
                                                        </div>
                                                    </div>
                                                    <div class="col-12">
                                                        <button type="button" class="btn btn-danger btn-sm" onclick="removeCard(this)">
                                                            <i class="fas fa-trash me-1"></i>حذف الكارد
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php endfor; ?>
                                        </div>
                                    </div>
                                    
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>حفظ التغييرات
                                        </button>
                                    </div>
                                </form>
                            </div>
                            <?php endif; ?>

                            <!-- تبويب خطط الضمان -->
                            <?php if ($active_tab === 'warranty_plans'): ?>
                            <div class="tab-pane fade show active">
                                <form method="POST" class="row g-3">
                                    <input type="hidden" name="section" value="warranty_plans">
                                    
                                    <div class="col-12 d-flex justify-content-between align-items-center">
                                        <h5>خطط الضمان</h5>
                                        <button type="button" class="btn btn-success" onclick="addWarrantyPlan()">
                                            <i class="fas fa-plus me-2"></i>إضافة خطة جديدة
                                        </button>
                                    </div>
                                    
                                    <div class="col-12">
                                        <div id="warranty-plans-container">
                                            <?php foreach ($warranty_plans as $index => $plan): ?>
                                            <div class="dynamic-card filled">
                                                <div class="row g-3">
                                                    <div class="col-md-3">
                                                        <label class="form-label">عنوان الخطة</label>
                                                        <input type="text" class="form-control" name="warranty_plans[<?php echo $index; ?>][title]" 
                                                               value="<?php echo htmlspecialchars($plan['title'] ?? ''); ?>">
                                                    </div>
                                                    <div class="col-md-3">
                                                        <label class="form-label">السعر</label>
                                                        <input type="text" class="form-control" name="warranty_plans[<?php echo $index; ?>][price]" 
                                                               value="<?php echo htmlspecialchars($plan['price'] ?? ''); ?>">
                                                    </div>
                                                    <div class="col-md-4">
                                                        <label class="form-label">الوصف</label>
                                                        <textarea class="form-control" name="warranty_plans[<?php echo $index; ?>][description]" rows="2"><?php echo htmlspecialchars($plan['description'] ?? ''); ?></textarea>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <label class="form-label">الخيارات</label>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" name="warranty_plans[<?php echo $index; ?>][is_featured]" value="1" 
                                                                   <?php echo $plan['is_featured'] ? 'checked' : ''; ?>>
                                                            <label class="form-check-label">مميز</label>
                                                        </div>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" name="warranty_plans[<?php echo $index; ?>][is_popular]" value="1" 
                                                                   <?php echo ($plan['is_popular'] ?? false) ? 'checked' : ''; ?>>
                                                            <label class="form-check-label">الأكثر شعبية</label>
                                                        </div>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" name="warranty_plans[<?php echo $index; ?>][is_visible]" value="1" 
                                                                   <?php echo ($plan['is_visible'] ?? false) ? 'checked' : ''; ?>>
                                                            <label class="form-check-label">مرئي</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-12">
                                                        <label class="form-label">المميزات (كل مميزة في سطر منفصل)</label>
                                                        <textarea class="form-control" name="warranty_plans[<?php echo $index; ?>][features]" rows="3"><?php echo htmlspecialchars($plan['features'] ?? ''); ?></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                    
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>حفظ التغييرات
                                        </button>
                                    </div>
                                </form>
                            </div>
                            <?php endif; ?>

                            <!-- تبويب لماذا تختارنا -->
                            <?php if ($active_tab === 'why_choose'): ?>
                            <div class="tab-pane fade show active">
                                <form method="POST" class="row g-3">
                                    <input type="hidden" name="section" value="why_choose">
                                    
                                    <div class="col-12">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="show_why_choose" value="1" 
                                                   <?php echo getSupportContactInfo('why_choose', 'show_section') ? 'checked' : ''; ?>>
                                            <label class="form-check-label">إظهار قسم "لماذا تختار خطط الضمان الخاصة بنا؟"</label>
                                        </div>
                                    </div>
                                    
                                    <div class="col-12">
                                        <label class="form-label">العنوان الرئيسي</label>
                                        <input type="text" class="form-control" name="why_choose_title" 
                                               value="<?php echo htmlspecialchars(getSupportContactInfo('why_choose', 'main_title') ?? ''); ?>" 
                                               placeholder="لماذا تختار خطط الضمان الخاصة بنا؟">
                                    </div>
                                    
                                    <div class="col-12">
                                        <label class="form-label">المحتوى</label>
                                        <textarea class="form-control" name="why_choose_content" rows="8"><?php echo htmlspecialchars(getSupportContactInfo('why_choose', 'content') ?? ''); ?></textarea>
                                    </div>
                                    
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>حفظ التغييرات
                                        </button>
                                    </div>
                                </form>
                            </div>
                            <?php endif; ?>

                            <!-- تبويب آراء العملاء -->
                            <?php if ($active_tab === 'testimonials'): ?>
                            <div class="tab-pane fade show active">
                                <form method="POST" class="row g-3">
                                    <input type="hidden" name="section" value="testimonials">
                                    
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="show_testimonials" value="1" 
                                                   <?php echo getSupportContactInfo('testimonials', 'show_section') ? 'checked' : ''; ?>>
                                            <label class="form-check-label">إظهار قسم آراء العملاء</label>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <label class="form-label">عدد التقييمات المعروضة</label>
                                        <select class="form-select" name="testimonials_count">
                                            <?php for ($i = 1; $i <= 10; $i++): ?>
                                            <option value="<?php echo $i; ?>" <?php echo getSupportContactInfo('testimonials', 'display_count') == $i ? 'selected' : ''; ?>>
                                                <?php echo $i; ?>
                                            </option>
                                            <?php endfor; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="col-12">
                                        <h5>التقييمات المتاحة (<?php echo count($testimonials); ?>)</h5>
                                        <div class="table-responsive">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>اسم العميل</th>
                                                        <th>التقييم</th>
                                                        <th>التعليق</th>
                                                        <th>تاريخ الإضافة</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($testimonials as $testimonial): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($testimonial['customer_name'] ?? ''); ?></td>
                                                        <td>
                                                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                                                <i class="fas fa-star <?php echo $i <= ($testimonial['rating'] ?? 0) ? 'text-warning' : 'text-muted'; ?>"></i>
                                                            <?php endfor; ?>
                                                        </td>
                                                        <td><?php echo htmlspecialchars(substr($testimonial['comment'] ?? '', 0, 50)) . '...'; ?></td>
                                                        <td><?php echo date('Y-m-d', strtotime($testimonial['created_at'] ?? 'now')); ?></td>
                                                    </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>حفظ التغييرات
                                        </button>
                                    </div>
                                </form>
                            </div>
                            <?php endif; ?>

                            <!-- تبويب الأسئلة الشائعة -->
                            <?php if ($active_tab === 'faqs'): ?>
                            <div class="tab-pane fade show active">
                                <form method="POST" class="row g-3">
                                    <input type="hidden" name="section" value="faqs">
                                    
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="show_faqs" value="1" 
                                                   <?php echo getSupportContactInfo('faqs', 'show_section') ? 'checked' : ''; ?>>
                                            <label class="form-check-label">إظهار كارد الأسئلة الشائعة</label>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <label class="form-label">عدد الأسئلة المعروضة</label>
                                        <select class="form-select" name="faqs_count">
                                            <?php for ($i = 1; $i <= 10; $i++): ?>
                                            <option value="<?php echo $i; ?>" <?php echo getSupportContactInfo('faqs', 'display_count') == $i ? 'selected' : ''; ?>>
                                                <?php echo $i; ?>
                                            </option>
                                            <?php endfor; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="col-12">
                                        <h5>الأسئلة الشائعة المتاحة (<?php echo count($faqs); ?>)</h5>
                                        <div class="table-responsive">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>السؤال</th>
                                                        <th>الإجابة</th>
                                                        <th>الترتيب</th>
                                                        <th>الحالة</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($faqs as $faq): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($faq['question'] ?? ''); ?></td>
                                                        <td><?php echo htmlspecialchars(substr($faq['answer'] ?? '', 0, 50)) . '...'; ?></td>
                                                        <td><?php echo $faq['sort_order'] ?? 0; ?></td>
                                                        <td>
                                                            <span class="badge bg-<?php echo ($faq['status'] ?? 'inactive') === 'active' ? 'success' : 'secondary'; ?>">
                                                                <?php echo ($faq['status'] ?? 'inactive') === 'active' ? 'نشط' : 'غير نشط'; ?>
                                                            </span>
                                                        </td>
                                                    </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>حفظ التغييرات
                                        </button>
                                    </div>
                                </form>
                            </div>
                            <?php endif; ?>

                            <!-- تبويب مركز التحميل -->
                            <?php if ($active_tab === 'download_center'): ?>
                            <div class="tab-pane fade show active">
                                <div class="row g-3">
                                    <div class="col-12 d-flex justify-content-between align-items-center">
                                        <h5>مركز التحميل</h5>
                                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#uploadModal">
                                            <i class="fas fa-plus me-2"></i>إضافة ملف جديد
                                        </button>
                                    </div>
                                    
                                    <div class="col-12">
                                        <div class="table-responsive">
                                            <table class="table table-striped">
                                                <thead>
                                                        <tr>
                                                            <th>العنوان</th>
                                                            <th>الوصف</th>
                                                            <th>حجم الملف</th>
                                                            <th>تاريخ الرفع</th>
                                                            <th>الحالة</th>
                                                            <th>الإجراءات</th>
                                                        </tr>
                                                    </thead>
                                                <tbody>
                                                    <?php foreach ($support_files as $file): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($file['title'] ?? ''); ?></td>
                                                        <td><?php echo htmlspecialchars($file['description'] ?? ''); ?></td>
                                                        <td><?php echo number_format(($file['file_size'] ?? 0) / 1024, 2); ?> KB</td>
                                                        <td><?php echo date('Y-m-d H:i', strtotime($file['upload_date'] ?? 'now')); ?></td>
                                                        <td>
                                                            <span class="badge bg-<?php echo ($file['is_visible'] ?? false) ? 'success' : 'secondary'; ?>">
                                                                <?php echo ($file['is_visible'] ?? false) ? 'مرئي' : 'مخفي'; ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <a href="../<?php echo $file['file_path'] ?? '#'; ?>" class="btn btn-sm btn-info" target="_blank" title="تحميل">
                                                                <i class="fas fa-download"></i>
                                                            </a>
                                                            <button type="button" class="btn btn-sm btn-<?php echo ($file['is_visible'] ?? false) ? 'warning' : 'success'; ?>" 
                                                                    onclick="toggleFileVisibility(<?php echo $file['id'] ?? 0; ?>)" 
                                                                    title="<?php echo ($file['is_visible'] ?? false) ? 'إخفاء' : 'إظهار'; ?>">
                                                                <i class="fas fa-<?php echo ($file['is_visible'] ?? false) ? 'eye-slash' : 'eye'; ?>"></i>
                                                            </button>
                                                            <a href="?tab=download_center&delete_file=<?php echo $file['id'] ?? 0; ?>" 
                                                               class="btn btn-sm btn-danger" 
                                                               onclick="return confirm('هل أنت متأكد من حذف هذا الملف؟')"
                                                               title="حذف">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة رفع الملفات -->
    <div class="modal fade" id="uploadModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">رفع ملف جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" enctype="multipart/form-data">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">عنوان الملف</label>
                            <input type="text" class="form-control" name="file_title" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">وصف الملف</label>
                            <textarea class="form-control" name="file_description" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الملف</label>
                            <input type="file" class="form-control" name="support_file" required 
                                   accept=".pdf,.doc,.docx,.txt,.zip,.rar">
                            <div class="form-text">الأنواع المدعومة: PDF, DOC, DOCX, TXT, ZIP, RAR (حد أقصى 10 ميجابايت)</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">رفع الملف</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        let warrantyPlanIndex = <?php echo count($warranty_plans); ?>;
        
        // إضافة خطة ضمان جديدة
        function addWarrantyPlan() {
            const container = document.getElementById('warranty-plans-container');
            const newPlan = document.createElement('div');
            newPlan.className = 'dynamic-card';
            newPlan.innerHTML = `
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">عنوان الخطة</label>
                        <input type="text" class="form-control" name="warranty_plans[${warrantyPlanIndex}][title]" required>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">السعر</label>
                        <input type="text" class="form-control" name="warranty_plans[${warrantyPlanIndex}][price]" required>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">الوصف</label>
                        <textarea class="form-control" name="warranty_plans[${warrantyPlanIndex}][description]" rows="2" required></textarea>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">الخيارات</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="warranty_plans[${warrantyPlanIndex}][is_featured]" value="1">
                            <label class="form-check-label">مميز</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="warranty_plans[${warrantyPlanIndex}][is_popular]" value="1">
                            <label class="form-check-label">الأكثر شعبية</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="warranty_plans[${warrantyPlanIndex}][is_visible]" value="1" checked>
                            <label class="form-check-label">مرئي</label>
                        </div>
                    </div>
                    <div class="col-12">
                        <label class="form-label">المميزات (كل مميزة في سطر منفصل)</label>
                        <textarea class="form-control" name="warranty_plans[${warrantyPlanIndex}][features]" rows="3" placeholder="مميزة 1&#10;مميزة 2&#10;مميزة 3"></textarea>
                    </div>
                    <div class="col-12">
                        <button type="button" class="btn btn-danger btn-sm" onclick="removePlan(this)">
                            <i class="fas fa-trash me-1"></i>حذف الخطة
                        </button>
                    </div>
                </div>
            `;
            container.appendChild(newPlan);
            warrantyPlanIndex++;
            
            // إضافة تأثير بصري
            newPlan.style.opacity = '0';
            newPlan.style.transform = 'translateY(20px)';
            setTimeout(() => {
                newPlan.style.transition = 'all 0.3s ease';
                newPlan.style.opacity = '1';
                newPlan.style.transform = 'translateY(0)';
            }, 10);
        }
        
        // حذف خطة ضمان
        function removePlan(button) {
            if (confirm('هل أنت متأكد من حذف هذه الخطة؟')) {
                const card = button.closest('.dynamic-card');
                card.style.transition = 'all 0.3s ease';
                card.style.opacity = '0';
                card.style.transform = 'translateY(-20px)';
                setTimeout(() => {
                    card.remove();
                }, 300);
            }
        }
        
        // إضافة كارد تواصل جديد
        function addContactCard() {
            const container = document.getElementById('contact-cards-container');
            const cardCount = container.children.length;
            const newCard = document.createElement('div');
            newCard.className = 'dynamic-card';
            newCard.innerHTML = `
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">عنوان الكارد</label>
                        <input type="text" class="form-control" name="contact_cards[${cardCount}][title]">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">الوصف</label>
                        <input type="text" class="form-control" name="contact_cards[${cardCount}][description]">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">الأيقونة</label>
                        <input type="text" class="form-control" name="contact_cards[${cardCount}][icon]" placeholder="fas fa-phone">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">الرابط</label>
                        <input type="text" class="form-control" name="contact_cards[${cardCount}][link]">
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">إظهار</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="contact_cards[${cardCount}][show]" value="1" checked>
                        </div>
                    </div>
                    <div class="col-12">
                        <button type="button" class="btn btn-danger btn-sm" onclick="removeCard(this)">
                            <i class="fas fa-trash me-1"></i>حذف الكارد
                        </button>
                    </div>
                </div>
            `;
            container.appendChild(newCard);
        }
        
        // إضافة كارد خدمة جديد
        function addServiceCard() {
            const container = document.getElementById('service-cards-container');
            const cardCount = container.children.length;
            const newCard = document.createElement('div');
            newCard.className = 'dynamic-card';
            newCard.innerHTML = `
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">عنوان الخدمة</label>
                        <input type="text" class="form-control" name="service_cards[${cardCount}][title]">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">وصف الخدمة</label>
                        <textarea class="form-control" name="service_cards[${cardCount}][description]" rows="2"></textarea>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">الأيقونة</label>
                        <input type="text" class="form-control" name="service_cards[${cardCount}][icon]" placeholder="fas fa-tools">
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">إظهار</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="service_cards[${cardCount}][show]" value="1" checked>
                        </div>
                    </div>
                    <div class="col-12">
                        <button type="button" class="btn btn-danger btn-sm" onclick="removeCard(this)">
                            <i class="fas fa-trash me-1"></i>حذف الكارد
                        </button>
                    </div>
                </div>
            `;
            container.appendChild(newCard);
        }
        
        // حذف كارد
        function removeCard(button) {
            if (confirm('هل أنت متأكد من حذف هذا الكارد؟')) {
                const card = button.closest('.dynamic-card');
                card.style.transition = 'all 0.3s ease';
                card.style.opacity = '0';
                card.style.transform = 'translateY(-20px)';
                setTimeout(() => {
                    card.remove();
                }, 300);
            }
        }
        
        // حفظ البيانات باستخدام AJAX
        function saveSection(formData, section) {
            $.ajax({
                url: '../api/support_management.php',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    const result = JSON.parse(response);
                    if (result.success) {
                        showAlert('success', result.message);
                    } else {
                        showAlert('danger', result.message);
                    }
                },
                error: function() {
                    showAlert('danger', 'حدث خطأ في الاتصال');
                }
            });
        }
        
        // عرض التنبيهات
        function showAlert(type, message) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('.card-body').prepend(alertHtml);
            
            // إزالة التنبيه تلقائياً بعد 5 ثوان
            setTimeout(() => {
                $('.alert').fadeOut();
            }, 5000);
        }
        
        // تحديث عداد الملفات
        function updateFileCounter() {
            const fileCount = $('#support-files-table tbody tr').length;
            $('#files-count').text(fileCount);
        }
        
        // حذف ملف باستخدام AJAX
        function deleteFile(fileId) {
            if (confirm('هل أنت متأكد من حذف هذا الملف؟')) {
                $.ajax({
                    url: '../api/support_management.php',
                    type: 'POST',
                    data: {
                        action: 'delete_support_file',
                        id: fileId
                    },
                    success: function(response) {
                        const result = JSON.parse(response);
                        if (result.success) {
                            $(`#file-row-${fileId}`).fadeOut(300, function() {
                                $(this).remove();
                                updateFileCounter();
                            });
                            showAlert('success', result.message);
                        } else {
                            showAlert('danger', result.message);
                        }
                    },
                    error: function() {
                        showAlert('danger', 'حدث خطأ في الاتصال');
                    }
                });
            }
        }
        
        // رفع ملف باستخدام AJAX
        $('#upload-form').on('submit', function(e) {
            e.preventDefault();
            const formData = new FormData(this);
            formData.append('action', 'upload_support_file');
            
            $.ajax({
                url: '../api/support_management.php',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    const result = JSON.parse(response);
                    if (result.success) {
                        showAlert('success', result.message);
                        $('#uploadModal').modal('hide');
                        location.reload(); // إعادة تحميل الصفحة لإظهار الملف الجديد
                    } else {
                        showAlert('danger', result.message);
                    }
                },
                error: function() {
                    showAlert('danger', 'حدث خطأ في رفع الملف');
                }
            });
        });
        
        // تحديث حالة الإظهار/الإخفاء
        function toggleVisibility(type, id) {
            $.ajax({
                url: '../api/support_management.php',
                type: 'POST',
                data: {
                    action: `toggle_${type}_visibility`,
                    id: id
                },
                success: function(response) {
                    const result = JSON.parse(response);
                    if (result.success) {
                        showAlert('success', result.message);
                        // تحديث الواجهة
                        const button = $(`#toggle-${type}-${id}`);
                        button.toggleClass('btn-success btn-secondary');
                        const icon = button.find('i');
                        icon.toggleClass('fa-eye fa-eye-slash');
                    } else {
                        showAlert('danger', result.message);
                    }
                },
                error: function() {
                    showAlert('danger', 'حدث خطأ في الاتصال');
                }
            });
        }
        
        // تهيئة الصفحة
        $(document).ready(function() {
            // إضافة تأثيرات بصرية للكروت
            $('.dynamic-card').hover(
                function() {
                    $(this).css('transform', 'translateY(-2px)');
                    $(this).css('box-shadow', '0 6px 12px rgba(0,0,0,0.15)');
                },
                function() {
                    $(this).css('transform', 'translateY(0)');
                    $(this).css('box-shadow', 'none');
                }
            );
            
            // تحديث عداد الملفات
            updateFileCounter();
            
            // إضافة تأثير التحميل للنماذج
            $('form').on('submit', function() {
                const submitBtn = $(this).find('button[type="submit"]');
                const originalText = submitBtn.html();
                submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...');
                submitBtn.prop('disabled', true);
                
                setTimeout(() => {
                    submitBtn.html(originalText);
                    submitBtn.prop('disabled', false);
                }, 2000);
            });
        });
        // حفظ إعدادات التقييمات
        function saveTestimonialsSettings() {
            const showSection = document.getElementById('show_testimonials').checked;
            const count = document.querySelector('select[name="testimonials_count"]').value;
            
            const formData = new FormData();
            formData.append('action', 'update_testimonials_settings');
            formData.append('show_testimonials_section', showSection ? '1' : '0');
            formData.append('testimonials_count', count);
            
            fetch('../api/support_management.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('تم حفظ إعدادات التقييمات بنجاح', 'success');
                } else {
                    showAlert('خطأ في حفظ الإعدادات: ' + data.message, 'danger');
                }
            })
            .catch(error => {
                showAlert('خطأ في الاتصال بالخادم', 'danger');
            });
        }
        
        // حفظ إعدادات الأسئلة الشائعة
        function saveFaqsSettings() {
            const showSection = document.getElementById('show_faqs').checked;
            const count = document.querySelector('select[name="faqs_count"]').value;
            
            const formData = new FormData();
            formData.append('action', 'update_faqs_settings');
            formData.append('show_faqs_section', showSection ? '1' : '0');
            formData.append('faqs_count', count);
            
            fetch('../api/support_management.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('تم حفظ إعدادات الأسئلة الشائعة بنجاح', 'success');
                } else {
                    showAlert('خطأ في حفظ الإعدادات: ' + data.message, 'danger');
                }
            })
            .catch(error => {
                showAlert('خطأ في الاتصال بالخادم', 'danger');
            });
        }
        
        // تبديل حالة إظهار الملف
        function toggleFileVisibility(fileId) {
            const formData = new FormData();
            formData.append('action', 'toggle_file_visibility');
            formData.append('id', fileId);
            
            fetch('../api/support_management.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('تم تحديث حالة الإظهار بنجاح', 'success');
                    location.reload();
                } else {
                    showAlert('خطأ في تحديث حالة الإظهار: ' + data.message, 'danger');
                }
            })
            .catch(error => {
                showAlert('خطأ في الاتصال بالخادم', 'danger');
            });
        }
        
        // تحديث عداد الملفات
        function updateFileCounter() {
            const fileCount = document.querySelectorAll('#support-files-table tbody tr').length;
            document.getElementById('files-count').textContent = fileCount;
        }
        
        // تحديث عداد خطط الضمان
        function updateWarrantyCounter() {
            const warrantyCount = document.querySelectorAll('#warranty-plans-table tbody tr').length;
            document.getElementById('warranty-count').textContent = warrantyCount;
        }
        
        // تحديث عداد التقييمات
        function updateTestimonialsCounter() {
            const testimonialsCount = document.querySelectorAll('#testimonials-table tbody tr').length;
            document.getElementById('testimonials-count').textContent = testimonialsCount;
        }
        
        // تحديث عداد الأسئلة الشائعة
        function updateFaqsCounter() {
            const faqsCount = document.querySelectorAll('#faqs-table tbody tr').length;
            document.getElementById('faqs-count').textContent = faqsCount;
        }
        
        // تحديث جميع العدادات
        function updateAllCounters() {
            updateFileCounter();
            updateWarrantyCounter();
            updateTestimonialsCounter();
            updateFaqsCounter();
        }
        
        // تشغيل تحديث العدادات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateAllCounters();
        });
    </script>

<?php
// إنهاء التخطيط
endLayout();
?>