# حل مشكلة الإجراءات المخزنة في phpMyAdmin

## المشكلة

عند تصدير قاعدة البيانات من phpMyAdmin واستيرادها مرة أخرى، يظهر خطأ في الإجراء المخزن `GetTicketStats()`:

```sql
Error SQL query:
CREATE DEFINER=root@localhost PROCEDURE GetTicketStats () MODIFIES DATA SQL SECURITY INVOKER BEGIN
...
MySQL said:
#1064 - You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'DATA SQL SECURITY INVOKER BEGIN'
```

## سبب المشكلة

1. **phpMyAdmin يضيف معلومات إضافية**: عند التصدير، phpMyAdmin يضيف تلقائياً معلومات مثل `DEFINER`, `MODIFIES DATA`, `SQL SECURITY INVOKER`
2. **ترتيب خاطئ للكلمات المفتاحية**: الترتيب الصحيح يجب أن يكون:
   ```sql
   CREATE PROCEDURE procedure_name()
   [READS SQL DATA | MODIFIES SQL DATA]
   [SQL SECURITY DEFINER | SQL SECURITY INVOKER]
   BEGIN
   ```
3. **عدم توافق الإصدارات**: بعض إصدارات MySQL/MariaDB تتطلب ترتيب محدد للكلمات المفتاحية

## الحلول

### الحل الأول: تحديث الملف الأصلي

تم تحديث ملف `support_tables.sql` ليتضمن:
```sql
CREATE PROCEDURE GetTicketStats()
READS SQL DATA
SQL SECURITY DEFINER
BEGIN
    -- محتوى الإجراء
END
```

### الحل الثاني: استخدام الملف المحسن

تم إنشاء ملف `support_procedures_fixed.sql` يحتوي على:
- إجراءات محسنة مع معالجة الأخطاء
- توافق كامل مع phpMyAdmin
- إجراءات إضافية مفيدة

## كيفية الاستخدام

### 1. عند الاستيراد الأولي
```sql
-- تشغيل الملف الأساسي أولاً
source support_tables.sql;

-- ثم تشغيل الإجراءات المحسنة
source support_procedures_fixed.sql;
```

### 2. عند حدوث مشكلة في الاستيراد
```sql
-- حذف الإجراءات المعطلة
DROP PROCEDURE IF EXISTS GetTicketStats;

-- تشغيل الملف المحسن
source support_procedures_fixed.sql;
```

### 3. استخدام الإجراءات
```sql
-- إحصائيات آخر 30 يوم
CALL GetTicketStats();

-- إحصائيات مفصلة لآخر 60 يوم
CALL GetDetailedTicketStats(60);

-- إحصائيات يومية
CALL GetDailyTicketStats('2024-01-01', '2024-01-07');

-- عرض سريع للإحصائيات
SELECT * FROM quick_ticket_stats;
```

## نصائح لتجنب المشكلة مستقبلاً

### 1. عند التصدير من phpMyAdmin
- اختر "Custom" في خيارات التصدير
- في قسم "Object creation options":
  - قم بإلغاء تحديد "Add DROP PROCEDURE"
  - تأكد من تحديد "Add IF NOT EXISTS"

### 2. عند الاستيراد
- استخدم خيار "Ignore foreign key checks"
- قم بتشغيل الاستيراد على دفعات صغيرة

### 3. البديل الآمن
```sql
-- استخدم هذا التنسيق دائماً
DROP PROCEDURE IF EXISTS procedure_name;

DELIMITER //
CREATE PROCEDURE procedure_name()
READS SQL DATA
SQL SECURITY DEFINER
BEGIN
    -- محتوى الإجراء
END //
DELIMITER ;
```

## الإجراءات المتوفرة

### 1. GetTicketStats()
- **الوظيفة**: إحصائيات أساسية لآخر 30 يوم
- **المخرجات**: عدد التذاكر حسب الحالة والأولوية

### 2. GetDetailedTicketStats(days_back)
- **الوظيفة**: إحصائيات مفصلة لفترة محددة
- **المعاملات**: `days_back` (افتراضي: 30)
- **المخرجات**: إحصائيات شاملة بما في ذلك الفئات ووقت الحل

### 3. GetDailyTicketStats(start_date, end_date)
- **الوظيفة**: إحصائيات يومية لفترة محددة
- **المعاملات**: تاريخ البداية والنهاية
- **المخرجات**: إحصائيات مجمعة حسب اليوم

### 4. quick_ticket_stats (View)
- **الوظيفة**: عرض سريع للإحصائيات
- **الاستخدام**: `SELECT * FROM quick_ticket_stats;`

## استكشاف الأخطاء

### خطأ: "PROCEDURE already exists"
```sql
DROP PROCEDURE IF EXISTS GetTicketStats;
-- ثم أعد إنشاء الإجراء
```

### خطأ: "Table 'support_tickets' doesn't exist"
```sql
-- تأكد من تشغيل ملف الجداول أولاً
source support_tables.sql;
```

### خطأ: "Access denied"
```sql
-- تأكد من صلاحيات المستخدم
GRANT CREATE ROUTINE ON database_name.* TO 'username'@'localhost';
FLUSH PRIVILEGES;
```

## ملاحظات مهمة

1. **النسخ الاحتياطية**: احتفظ دائماً بنسخة احتياطية قبل تشغيل الإجراءات
2. **الصلاحيات**: تأكد من وجود صلاحية `CREATE ROUTINE`
3. **الإصدارات**: هذه الإجراءات متوافقة مع MySQL 5.7+ و MariaDB 10.2+
4. **الأداء**: الإجراءات محسنة للأداء مع فهارس مناسبة

---

**تاريخ الإنشاء**: ديسمبر 2024  
**آخر تحديث**: ديسمبر 2024  
**الإصدار**: 1.0.0