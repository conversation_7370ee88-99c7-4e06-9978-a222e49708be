<?php
/**
 * ملف اختبار نظام الصلاحيات
 * Test Permissions System
 */

require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../includes/permissions.php';

// التحقق من تسجيل الدخول
requireLogin();

$pageTitle = 'اختبار نظام الصلاحيات';
$currentPage = 'test';

// إنشاء مدير الصلاحيات
$permissionManager = new PermissionManager($database);
$userId = getCurrentUserId();

// جمع معلومات المستخدم الحالي
$userInfo = [
    'id' => $userId,
    'roles' => $permissionManager->getUserRoles($userId),
    'permissions' => $permissionManager->getUserPermissions($userId),
    'has_admin_role' => $permissionManager->hasRole($userId, 'admin'),
    'has_super_admin_role' => $permissionManager->hasRole($userId, 'super-admin'),
    'has_admin_permission' => $permissionManager->hasPermission($userId, 'admin')
];

require_once 'includes/layout.php';
startLayout();
showPageHeader();
?>

<div class="max-w-4xl mx-auto">
    <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">اختبار نظام الصلاحيات</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- معلومات المستخدم -->
            <div class="border border-gray-200 rounded-lg p-4">
                <h3 class="font-medium text-gray-900 mb-3">معلومات المستخدم الحالي</h3>
                <div class="space-y-2 text-sm">
                    <div><strong>معرف المستخدم:</strong> <?php echo $userInfo['id']; ?></div>
                    <div><strong>لديه دور admin:</strong> 
                        <span class="<?php echo $userInfo['has_admin_role'] ? 'text-green-600' : 'text-red-600'; ?>">
                            <?php echo $userInfo['has_admin_role'] ? 'نعم' : 'لا'; ?>
                        </span>
                    </div>
                    <div><strong>لديه دور super-admin:</strong> 
                        <span class="<?php echo $userInfo['has_super_admin_role'] ? 'text-green-600' : 'text-red-600'; ?>">
                            <?php echo $userInfo['has_super_admin_role'] ? 'نعم' : 'لا'; ?>
                        </span>
                    </div>
                    <div><strong>لديه صلاحية admin:</strong> 
                        <span class="<?php echo $userInfo['has_admin_permission'] ? 'text-green-600' : 'text-red-600'; ?>">
                            <?php echo $userInfo['has_admin_permission'] ? 'نعم' : 'لا'; ?>
                        </span>
                    </div>
                </div>
            </div>
            
            <!-- الأدوار -->
            <div class="border border-gray-200 rounded-lg p-4">
                <h3 class="font-medium text-gray-900 mb-3">الأدوار المعينة</h3>
                <?php if (!empty($userInfo['roles'])): ?>
                    <ul class="space-y-1 text-sm">
                        <?php foreach ($userInfo['roles'] as $role): ?>
                            <li class="flex items-center">
                                <span class="w-2 h-2 bg-green-500 rounded-full ml-2"></span>
                                <?php echo htmlspecialchars($role['name']); ?>
                                <?php if ($role['description']): ?>
                                    <span class="text-gray-500 mr-2">(<?php echo htmlspecialchars($role['description']); ?>)</span>
                                <?php endif; ?>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                <?php else: ?>
                    <p class="text-gray-500 text-sm">لا توجد أدوار معينة</p>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- الصلاحيات -->
        <div class="mt-6 border border-gray-200 rounded-lg p-4">
            <h3 class="font-medium text-gray-900 mb-3">الصلاحيات المتاحة</h3>
            <?php if (!empty($userInfo['permissions'])): ?>
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                    <?php foreach ($userInfo['permissions'] as $permission): ?>
                        <div class="flex items-center text-sm">
                            <span class="w-2 h-2 bg-blue-500 rounded-full ml-2"></span>
                            <?php echo htmlspecialchars($permission); ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <p class="text-gray-500 text-sm">لا توجد صلاحيات متاحة</p>
            <?php endif; ?>
        </div>
        
        <!-- اختبارات الوصول -->
        <div class="mt-6 border border-gray-200 rounded-lg p-4">
            <h3 class="font-medium text-gray-900 mb-3">اختبارات الوصول</h3>
            <div class="space-y-3">
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                    <span>الوصول لملف init_permissions.php</span>
                    <?php 
                    $canAccess = $permissionManager->hasRole($userId, 'super-admin') || $permissionManager->hasRole($userId, 'admin');
                    ?>
                    <span class="<?php echo $canAccess ? 'text-green-600' : 'text-red-600'; ?>">
                        <?php echo $canAccess ? '✓ مسموح' : '✗ غير مسموح'; ?>
                    </span>
                </div>
                
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                    <span>إدارة الصلاحيات</span>
                    <?php $canManagePermissions = $permissionManager->hasPermission($userId, 'manage-permissions'); ?>
                    <span class="<?php echo $canManagePermissions ? 'text-green-600' : 'text-red-600'; ?>">
                        <?php echo $canManagePermissions ? '✓ مسموح' : '✗ غير مسموح'; ?>
                    </span>
                </div>
                
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                    <span>إدارة المستخدمين</span>
                    <?php $canManageUsers = $permissionManager->hasPermission($userId, 'manage-users'); ?>
                    <span class="<?php echo $canManageUsers ? 'text-green-600' : 'text-red-600'; ?>">
                        <?php echo $canManageUsers ? '✓ مسموح' : '✗ غير مسموح'; ?>
                    </span>
                </div>
            </div>
        </div>
        
        <!-- روابط مفيدة -->
        <div class="mt-6 flex space-x-4 space-x-reverse">
            <a href="init_permissions.php" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                <i class="fas fa-cogs ml-2"></i>
                تهيئة الصلاحيات
            </a>
            <a href="permissions.php" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                <i class="fas fa-shield-alt ml-2"></i>
                إدارة الصلاحيات
            </a>
            <a href="/admin/" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700">
                <i class="fas fa-home ml-2"></i>
                العودة للوحة التحكم
            </a>
        </div>
    </div>
</div>

<?php
endLayout();
?>