<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص JavaScript - المنتج 16</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-2xl font-bold mb-6">تشخيص JavaScript - المنتج 16</h1>
        
        <!-- محاكاة التابات -->
        <div class="border-b border-gray-200 mb-8">
            <nav class="product-tabs-nav flex space-x-8 space-x-reverse">
                <button id="description-btn" class="product-tab-button active px-4 py-2 text-sm font-medium border-b-2 border-primary text-primary transition-all duration-300" data-tab="description">
                    الوصف التفصيلي
                </button>
                <button id="specifications-btn" class="product-tab-button px-4 py-2 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 transition-all duration-300" data-tab="specifications">
                    المواصفات
                </button>
                <button id="reviews-btn" class="product-tab-button px-4 py-2 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 transition-all duration-300" data-tab="reviews">
                    التقييمات
                </button>
            </nav>
        </div>
        
        <!-- محتوى التابات -->
        <div class="product-tab-content">
            <div id="description-tab" class="product-tab-pane active">
                <div class="bg-blue-50 p-4 rounded">
                    <h3 class="font-bold">تاب الوصف</h3>
                    <p>هذا هو محتوى تاب الوصف</p>
                </div>
            </div>
            
            <div id="specifications-tab" class="product-tab-pane" style="display: none;">
                <div class="bg-green-50 p-4 rounded">
                    <h3 class="font-bold">تاب المواصفات</h3>
                    <p>هذا هو محتوى تاب المواصفات</p>
                </div>
            </div>
            
            <div id="reviews-tab" class="product-tab-pane" style="display: none;">
                <div class="bg-yellow-50 p-4 rounded">
                    <h3 class="font-bold">تاب التقييمات</h3>
                    <p>هذا هو محتوى تاب التقييمات</p>
                </div>
            </div>
        </div>
        
        <!-- منطقة التشخيص -->
        <div id="debug-area" class="mt-8 p-4 bg-gray-50 rounded">
            <h3 class="font-bold mb-4">معلومات التشخيص:</h3>
            <div id="debug-output"></div>
        </div>
    </div>

    <style>
        .product-tab-button.active {
            color: #059669 !important;
            border-color: #059669 !important;
            background-color: rgba(5, 150, 105, 0.05);
        }
        
        .product-tab-pane {
            transition: all 0.3s ease;
            opacity: 0;
            visibility: hidden;
            height: 0;
            overflow: hidden;
        }
        
        .product-tab-pane.active {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
            height: auto !important;
            overflow: visible !important;
        }
    </style>

    <script>
        function debugLog(message) {
            const debugOutput = document.getElementById('debug-output');
            const timestamp = new Date().toLocaleTimeString();
            debugOutput.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            console.log(`[DEBUG] ${message}`);
        }

        document.addEventListener('DOMContentLoaded', function() {
            debugLog('DOM تم تحميله');
            
            // فحص وجود العناصر
            const tabButtons = document.querySelectorAll('.product-tab-button');
            const tabPanes = document.querySelectorAll('.product-tab-pane');
            
            debugLog(`تم العثور على ${tabButtons.length} أزرار تابات`);
            debugLog(`تم العثور على ${tabPanes.length} ألواح تابات`);
            
            tabButtons.forEach((button, index) => {
                debugLog(`زر ${index + 1}: ID=${button.id}, data-tab=${button.dataset.tab}`);
            });
            
            tabPanes.forEach((pane, index) => {
                debugLog(`لوح ${index + 1}: ID=${pane.id}, display=${getComputedStyle(pane).display}`);
            });
            
            // إضافة مستمعي الأحداث
            tabButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    const tabId = this.dataset.tab;
                    debugLog(`تم النقر على تاب: ${tabId}`);
                    
                    // إخفاء جميع التابات
                    tabButtons.forEach(btn => {
                        btn.classList.remove('active', 'border-primary', 'text-primary');
                        btn.classList.add('border-transparent', 'text-gray-500');
                    });
                    
                    tabPanes.forEach(pane => {
                        pane.classList.remove('active');
                        pane.style.display = 'none';
                        debugLog(`تم إخفاء: ${pane.id}`);
                    });
                    
                    // إظهار التاب المحدد
                    this.classList.add('active', 'border-primary', 'text-primary');
                    this.classList.remove('border-transparent', 'text-gray-500');
                    
                    const targetPane = document.getElementById(tabId + '-tab');
                    if (targetPane) {
                        targetPane.classList.add('active');
                        targetPane.style.display = 'block';
                        debugLog(`تم إظهار: ${tabId}-tab`);
                    } else {
                        debugLog(`خطأ: لم يتم العثور على ${tabId}-tab`);
                    }
                });
            });
            
            debugLog('تم تهيئة التابات بنجاح');
        });
    </script>
</body>
</html>