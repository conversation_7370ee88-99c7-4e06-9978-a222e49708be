<?php
require_once 'config/database.php';

try {
    echo "<h2>فحص هيكل جدول contact_info</h2>";
    
    // التحقق من وجود الجدول
    $stmt = $pdo->query("SHOW TABLES LIKE 'contact_info'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>❌ الجدول contact_info غير موجود!</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ الجدول contact_info موجود</p>";
    
    // عرض هيكل الجدول
    echo "<h3>هيكل الجدول الحالي:</h3>";
    $stmt = $pdo->query("DESCRIBE contact_info");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th style='padding: 8px;'>اسم العمود</th>";
    echo "<th style='padding: 8px;'>النوع</th>";
    echo "<th style='padding: 8px;'>Null</th>";
    echo "<th style='padding: 8px;'>Key</th>";
    echo "<th style='padding: 8px;'>Default</th>";
    echo "<th style='padding: 8px;'>Extra</th>";
    echo "</tr>";
    
    $hasSectonKey = false;
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($column['Default']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>";
        
        if ($column['Field'] === 'section_key') {
            $hasSectonKey = true;
        }
    }
    echo "</table>";
    
    if ($hasSectonKey) {
        echo "<p style='color: green;'>✅ العمود section_key موجود</p>";
    } else {
        echo "<p style='color: red;'>❌ العمود section_key مفقود!</p>";
    }
    
    // عرض البيانات الموجودة
    echo "<h3>البيانات الموجودة في الجدول:</h3>";
    try {
        $stmt = $pdo->query("SELECT * FROM contact_info LIMIT 10");
        $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($data)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background-color: #f0f0f0;'>";
            foreach (array_keys($data[0]) as $header) {
                echo "<th style='padding: 8px;'>" . htmlspecialchars($header) . "</th>";
            }
            echo "</tr>";
            
            foreach ($data as $row) {
                echo "<tr>";
                foreach ($row as $value) {
                    echo "<td style='padding: 8px;'>" . htmlspecialchars(substr($value, 0, 50)) . (strlen($value) > 50 ? '...' : '') . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>لا توجد بيانات في الجدول</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>خطأ في قراءة البيانات: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    // اختبار استعلام section_key
    echo "<h3>اختبار استعلام section_key:</h3>";
    try {
        $stmt = $pdo->prepare("SELECT * FROM contact_info WHERE section_key = ? LIMIT 1");
        $stmt->execute(['address']);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result) {
            echo "<p style='color: green;'>✅ استعلام section_key يعمل بشكل طبيعي</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد بيانات للـ section_key = 'address'</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في استعلام section_key: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ عام: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>