<?php
/**
 * صفحة إدارة الإعدادات
 * Settings Management Page
 */

require_once 'includes/layout.php';

$currentPage = 'settings';
$pageTitle = 'إدارة الإعدادات';
$pageDescription = 'إدارة إعدادات الموقع والنظام العامة';
$breadcrumbs = [
    ['title' => 'الإعدادات']
];
$message = '';
$messageType = '';

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // حفظ الإعدادات العامة
        if (isset($_POST['general_settings'])) {
            setSetting('site_name', $_POST['site_name'], 'string');
            setSetting('site_description', $_POST['site_description'], 'string');
            setSetting('contact_phone', $_POST['contact_phone'], 'string');
            setSetting('contact_email', $_POST['contact_email'], 'string');
            setSetting('whatsapp_number', $_POST['whatsapp_number'], 'string');
            setSetting('address', $_POST['address'], 'string');
            
            // معالجة رفع الشعار
            if (isset($_FILES['site_logo']) && $_FILES['site_logo']['error'] === UPLOAD_ERR_OK) {
                $uploadDir = '../uploads/';
                if (!is_dir($uploadDir)) {
                    mkdir($uploadDir, 0755, true);
                }
                
                $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
                $fileType = $_FILES['site_logo']['type'];
                
                if (in_array($fileType, $allowedTypes)) {
                    $fileExtension = pathinfo($_FILES['site_logo']['name'], PATHINFO_EXTENSION);
                    $fileName = 'logo_' . time() . '.' . $fileExtension;
                    $uploadPath = $uploadDir . $fileName;
                    
                    if (move_uploaded_file($_FILES['site_logo']['tmp_name'], $uploadPath)) {
                        // حذف الشعار القديم إذا كان موجوداً
                        $oldLogo = getSetting('site_logo');
                        if ($oldLogo && file_exists('../' . $oldLogo)) {
                            unlink('../' . $oldLogo);
                        }
                        
                        setSetting('site_logo', 'uploads/' . $fileName, 'string');
                    } else {
                        $message = 'حدث خطأ أثناء رفع الشعار';
                        $messageType = 'error';
                    }
                } else {
                    $message = 'نوع الملف غير مدعوم. يرجى رفع صورة بصيغة JPG, PNG, GIF, WebP أو SVG';
                    $messageType = 'error';
                }
            }
            
            if (empty($message)) {
                $message = 'تم حفظ الإعدادات العامة بنجاح';
                $messageType = 'success';
            }
        }
        
        // حفظ إعدادات التصميم
        if (isset($_POST['design_settings'])) {
            setSetting('primary_color', $_POST['primary_color'], 'string');
            setSetting('secondary_color', $_POST['secondary_color'], 'string');
            setSetting('accent_color', $_POST['accent_color'], 'string');
            setSetting('font_family', $_POST['font_family'], 'string');
            setSetting('font_weight', $_POST['font_weight'], 'string');
            setSetting('font_size', $_POST['font_size'], 'number');
            setSetting('font_url', $_POST['font_url'], 'string');
            
            $message = 'تم حفظ إعدادات التصميم بنجاح';
            $messageType = 'success';
        }
        
        // حفظ إعدادات البريد الإلكتروني
        if (isset($_POST['email_settings'])) {
            setSetting('smtp_host', $_POST['smtp_host'], 'string');
            setSetting('smtp_port', $_POST['smtp_port'], 'number');
            setSetting('smtp_username', $_POST['smtp_username'], 'string');
            setSetting('smtp_password', $_POST['smtp_password'], 'string');
            setSetting('smtp_from_email', $_POST['smtp_from_email'], 'string');
            setSetting('smtp_from_name', $_POST['smtp_from_name'], 'string');
            setSetting('smtp_encryption', $_POST['smtp_encryption'], 'string');
            
            $message = 'تم حفظ إعدادات البريد الإلكتروني بنجاح';
            $messageType = 'success';
        }
        
        // حفظ إعدادات الأمان
        if (isset($_POST['security_settings'])) {
            setSetting('enable_2fa', isset($_POST['enable_2fa']), 'boolean');
            setSetting('session_timeout', $_POST['session_timeout'], 'number');
            setSetting('max_login_attempts', $_POST['max_login_attempts'], 'number');
            setSetting('password_min_length', $_POST['password_min_length'], 'number');
            setSetting('require_strong_password', isset($_POST['require_strong_password']), 'boolean');
            
            $message = 'تم حفظ إعدادات الأمان بنجاح';
            $messageType = 'success';
        }
        
        // حفظ إعدادات النظام
        if (isset($_POST['system_settings'])) {
            setSetting('maintenance_mode', isset($_POST['maintenance_mode']), 'boolean');
            setSetting('debug_mode', isset($_POST['debug_mode']), 'boolean');
            setSetting('cache_enabled', isset($_POST['cache_enabled']), 'boolean');
            setSetting('backup_frequency', $_POST['backup_frequency'], 'string');
            setSetting('timezone', $_POST['timezone'], 'string');
            setSetting('language', $_POST['language'], 'string');
            
            $message = 'تم حفظ إعدادات النظام بنجاح';
            $messageType = 'success';
        }
        
    } catch (Exception $e) {
        $message = 'حدث خطأ أثناء حفظ الإعدادات: ' . $e->getMessage();
        $messageType = 'error';
    }
}

// التأكد من وجود إعدادات الخط الافتراضية
try {
    // إضافة إعداد وزن الخط الافتراضي
    $fontWeightExists = $database->fetch("SELECT id FROM settings WHERE setting_key = 'font_weight'");
    if (!$fontWeightExists) {
        $database->query("
            INSERT INTO settings (setting_key, setting_value, setting_type) 
            VALUES ('font_weight', '400', 'string')
        ");
    }
    
    // إضافة إعداد مقاس الخط الافتراضي
    $fontSizeExists = $database->fetch("SELECT id FROM settings WHERE setting_key = 'font_size'");
    if (!$fontSizeExists) {
        $database->query("
            INSERT INTO settings (setting_key, setting_value, setting_type) 
            VALUES ('font_size', '16', 'number')
        ");
    }
} catch (Exception $e) {
    // تجاهل الأخطاء في حالة وجود الإعدادات مسبقاً
}

// الحصول على التبويب النشط
$activeTab = $_GET['tab'] ?? 'general';

// الحصول على جميع الإعدادات
$settings = getSettings();

startLayout();
showPageHeader();
showMessages();

// عرض رسائل التنبيه المخصصة
if ($message): ?>
<div class="mb-6">
    <div class="<?php echo $messageType === 'success' ? 'bg-green-50 border-green-200 text-green-800' : 'bg-red-50 border-red-200 text-red-800'; ?> border rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <?php if ($messageType === 'success'): ?>
                <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                <?php else: ?>
                <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                </svg>
                <?php endif; ?>
            </div>
            <div class="mr-3">
                <p class="text-sm font-medium"><?php echo htmlspecialchars($message); ?></p>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

                <!-- التبويبات -->
                <div class="bg-white rounded-lg shadow">
                    <div class="border-b border-gray-200">
                        <nav class="-mb-px flex space-x-8 space-x-reverse px-6" aria-label="Tabs">
                            <a href="?tab=general" class="<?php echo $activeTab === 'general' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?> whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                                الإعدادات العامة
                            </a>
                            <a href="?tab=design" class="<?php echo $activeTab === 'design' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?> whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                                التصميم والألوان
                            </a>
                            <a href="?tab=email" class="<?php echo $activeTab === 'email' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?> whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                                البريد الإلكتروني
                            </a>
                            <a href="?tab=security" class="<?php echo $activeTab === 'security' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?> whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                                الأمان
                            </a>
                            <a href="?tab=system" class="<?php echo $activeTab === 'system' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?> whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                                النظام
                            </a>
                        </nav>
                    </div>

                    <div class="p-6">
                        <?php if ($activeTab === 'general'): ?>
                        <!-- الإعدادات العامة -->
                        <form method="POST" enctype="multipart/form-data" class="space-y-6">
                            <input type="hidden" name="general_settings" value="1">
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="site_name" class="block text-sm font-medium text-gray-700 mb-2">اسم الموقع</label>
                                    <input type="text" id="site_name" name="site_name" value="<?php echo htmlspecialchars($settings['site_name'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                </div>
                                
                                <div>
                                    <label for="contact_phone" class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                                    <input type="text" id="contact_phone" name="contact_phone" value="<?php echo htmlspecialchars($settings['contact_phone'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label for="contact_email" class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                                    <input type="email" id="contact_email" name="contact_email" value="<?php echo htmlspecialchars($settings['contact_email'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label for="whatsapp_number" class="block text-sm font-medium text-gray-700 mb-2">رقم الواتساب</label>
                                    <input type="text" id="whatsapp_number" name="whatsapp_number" value="<?php echo htmlspecialchars($settings['whatsapp_number'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                            </div>
                            
                            <div>
                                <label for="site_description" class="block text-sm font-medium text-gray-700 mb-2">وصف الموقع</label>
                                <textarea id="site_description" name="site_description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo htmlspecialchars($settings['site_description'] ?? ''); ?></textarea>
                            </div>
                            
                            <div>
                                <label for="address" class="block text-sm font-medium text-gray-700 mb-2">العنوان</label>
                                <textarea id="address" name="address" rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo htmlspecialchars($settings['address'] ?? ''); ?></textarea>
                            </div>
                            
                            <!-- حقل تحميل الشعار -->
                            <div>
                                <label for="site_logo" class="block text-sm font-medium text-gray-700 mb-2">شعار الموقع</label>
                                <?php if (!empty($settings['site_logo'])): ?>
                                <div class="mb-3">
                                    <img src="../<?php echo htmlspecialchars($settings['site_logo']); ?>" alt="شعار الموقع الحالي" class="h-16 w-auto object-contain border border-gray-300 rounded-md">
                                    <p class="text-sm text-gray-500 mt-1">الشعار الحالي</p>
                                </div>
                                <?php endif; ?>
                                <input type="file" id="site_logo" name="site_logo" accept="image/*" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <p class="text-sm text-gray-500 mt-1">الصيغ المدعومة: JPG, PNG, GIF, WebP, SVG (الحد الأقصى: 2MB)</p>
                            </div>
                            
                            <div class="flex justify-end">
                                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200">
                                    حفظ الإعدادات العامة
                                </button>
                            </div>
                        </form>
                        
                        <?php elseif ($activeTab === 'design'): ?>
                        <!-- إعدادات التصميم -->
                        <form method="POST" class="space-y-6">
                            <input type="hidden" name="design_settings" value="1">
                            
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div>
                                    <label for="primary_color" class="block text-sm font-medium text-gray-700 mb-2">اللون الأساسي</label>
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <input type="color" id="primary_color" name="primary_color" value="<?php echo htmlspecialchars($settings['primary_color'] ?? '#059669'); ?>" class="w-12 h-10 border border-gray-300 rounded cursor-pointer">
                                        <input type="text" value="<?php echo htmlspecialchars($settings['primary_color'] ?? '#059669'); ?>" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" readonly>
                                    </div>
                                </div>
                                
                                <div>
                                    <label for="secondary_color" class="block text-sm font-medium text-gray-700 mb-2">اللون الثانوي</label>
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <input type="color" id="secondary_color" name="secondary_color" value="<?php echo htmlspecialchars($settings['secondary_color'] ?? '#10b981'); ?>" class="w-12 h-10 border border-gray-300 rounded cursor-pointer">
                                        <input type="text" value="<?php echo htmlspecialchars($settings['secondary_color'] ?? '#10b981'); ?>" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" readonly>
                                    </div>
                                </div>
                                
                                <div>
                                    <label for="accent_color" class="block text-sm font-medium text-gray-700 mb-2">لون التمييز</label>
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <input type="color" id="accent_color" name="accent_color" value="<?php echo htmlspecialchars($settings['accent_color'] ?? '#34d399'); ?>" class="w-12 h-10 border border-gray-300 rounded cursor-pointer">
                                        <input type="text" value="<?php echo htmlspecialchars($settings['accent_color'] ?? '#34d399'); ?>" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" readonly>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- إعدادات الخطوط المتقدمة -->
                            <div class="bg-gray-50 p-6 rounded-lg">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4">إعدادات الخطوط</h3>
                                
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                                    <div>
                                        <label for="font_family" class="block text-sm font-medium text-gray-700 mb-2">نوع الخط</label>
                                        <select id="font_family" name="font_family" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            <option value="Noto Kufi Arabic" <?php echo ($settings['font_family'] ?? '') === 'Noto Kufi Arabic' ? 'selected' : ''; ?>>Noto Kufi Arabic - نوتو كوفي عربي</option>
                                            <option value="Cairo" <?php echo ($settings['font_family'] ?? '') === 'Cairo' ? 'selected' : ''; ?>>Cairo - القاهرة</option>
                                            <option value="Amiri" <?php echo ($settings['font_family'] ?? '') === 'Amiri' ? 'selected' : ''; ?>>Amiri - أميري</option>
                                            <option value="Tajawal" <?php echo ($settings['font_family'] ?? '') === 'Tajawal' ? 'selected' : ''; ?>>Tajawal - تجوال</option>
                                            <option value="Almarai" <?php echo ($settings['font_family'] ?? '') === 'Almarai' ? 'selected' : ''; ?>>Almarai - المرعي</option>
                                            <option value="Changa" <?php echo ($settings['font_family'] ?? '') === 'Changa' ? 'selected' : ''; ?>>Changa - تشانغا</option>
                                            <option value="El Messiri" <?php echo ($settings['font_family'] ?? '') === 'El Messiri' ? 'selected' : ''; ?>>El Messiri - المسيري</option>
                                            <option value="Harmattan" <?php echo ($settings['font_family'] ?? '') === 'Harmattan' ? 'selected' : ''; ?>>Harmattan - هرمتان</option>
                                            <option value="IBM Plex Sans Arabic" <?php echo ($settings['font_family'] ?? '') === 'IBM Plex Sans Arabic' ? 'selected' : ''; ?>>IBM Plex Sans Arabic</option>
                                            <option value="Katibeh" <?php echo ($settings['font_family'] ?? '') === 'Katibeh' ? 'selected' : ''; ?>>Katibeh - كاتبة</option>
                                            <option value="Lalezar" <?php echo ($settings['font_family'] ?? '') === 'Lalezar' ? 'selected' : ''; ?>>Lalezar - لالزار</option>
                                            <option value="Lateef" <?php echo ($settings['font_family'] ?? '') === 'Lateef' ? 'selected' : ''; ?>>Lateef - لطيف</option>
                                            <option value="Mada" <?php echo ($settings['font_family'] ?? '') === 'Mada' ? 'selected' : ''; ?>>Mada - مدى</option>
                                            <option value="Markazi Text" <?php echo ($settings['font_family'] ?? '') === 'Markazi Text' ? 'selected' : ''; ?>>Markazi Text - مركزي</option>
                                            <option value="Mirza" <?php echo ($settings['font_family'] ?? '') === 'Mirza' ? 'selected' : ''; ?>>Mirza - ميرزا</option>
                                            <option value="Rakkas" <?php echo ($settings['font_family'] ?? '') === 'Rakkas' ? 'selected' : ''; ?>>Rakkas - رقاص</option>
                                            <option value="Reem Kufi" <?php echo ($settings['font_family'] ?? '') === 'Reem Kufi' ? 'selected' : ''; ?>>Reem Kufi - ريم كوفي</option>
                                            <option value="Scheherazade New" <?php echo ($settings['font_family'] ?? '') === 'Scheherazade New' ? 'selected' : ''; ?>>Scheherazade New - شهرزاد</option>
                                            <option value="Vibes" <?php echo ($settings['font_family'] ?? '') === 'Vibes' ? 'selected' : ''; ?>>Vibes - فايبز</option>
                                        </select>
                                    </div>
                                    
                                    <div>
                                        <label for="font_weight" class="block text-sm font-medium text-gray-700 mb-2">وزن الخط</label>
                                        <select id="font_weight" name="font_weight" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            <option value="300" <?php echo ($settings['font_weight'] ?? '400') === '300' ? 'selected' : ''; ?>>خفيف (300)</option>
                                            <option value="400" <?php echo ($settings['font_weight'] ?? '400') === '400' ? 'selected' : ''; ?>>عادي (400)</option>
                                            <option value="500" <?php echo ($settings['font_weight'] ?? '400') === '500' ? 'selected' : ''; ?>>متوسط (500)</option>
                                            <option value="600" <?php echo ($settings['font_weight'] ?? '400') === '600' ? 'selected' : ''; ?>>نصف عريض (600)</option>
                                            <option value="700" <?php echo ($settings['font_weight'] ?? '400') === '700' ? 'selected' : ''; ?>>عريض (700)</option>
                                            <option value="800" <?php echo ($settings['font_weight'] ?? '400') === '800' ? 'selected' : ''; ?>>عريض جداً (800)</option>
                                        </select>
                                    </div>
                                    
                                    <div>
                                        <label for="font_size" class="block text-sm font-medium text-gray-700 mb-2">مقاس الخط الأساسي</label>
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            <input type="range" id="font_size_range" min="12" max="24" value="<?php echo htmlspecialchars($settings['font_size'] ?? '16'); ?>" class="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                                            <input type="number" id="font_size" name="font_size" min="12" max="24" value="<?php echo htmlspecialchars($settings['font_size'] ?? '16'); ?>" class="w-20 px-2 py-1 border border-gray-300 rounded text-center">
                                            <span class="text-sm text-gray-500">px</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- حقل مخفي لرابط الخط -->
                                <input type="hidden" id="font_url" name="font_url" value="<?php echo htmlspecialchars($settings['font_url'] ?? ''); ?>">
                                
                                <!-- معاينة الخط -->
                                <div class="bg-white p-6 rounded-lg border border-gray-200">
                                    <h4 class="text-sm font-medium text-gray-700 mb-3">معاينة الخط</h4>
                                    <div id="font_preview" class="space-y-3">
                                        <p class="text-2xl">مرحباً بكم في موقع Green Line</p>
                                        <p class="text-lg">شركة رائدة في مجال المكيفات وأنظمة التبريد</p>
                                        <p class="text-base">نحن نقدم أفضل الحلول لتكييف الهواء والتبريد بأحدث التقنيات العالمية</p>
                                        <p class="text-sm">الأرقام: ١٢٣٤٥٦٧٨٩٠ | Numbers: 1234567890</p>
                                        <p class="text-xs">النص الصغير: تفاصيل إضافية ومعلومات مهمة للعملاء</p>
                                    </div>
                                </div>
                                
                                <!-- معلومات إضافية عن الخط -->
                                <div id="font_info" class="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                                    <h5 class="font-medium text-blue-800 mb-2">معلومات الخط المختار</h5>
                                    <p id="font_description" class="text-sm text-blue-700"></p>
                                </div>
                            </div>
                            
                            <div class="flex justify-end">
                                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200">
                                    حفظ إعدادات التصميم
                                </button>
                            </div>
                        </form>
                        
                        <?php elseif ($activeTab === 'email'): ?>
                        <!-- إعدادات البريد الإلكتروني -->
                        <form method="POST" class="space-y-6">
                            <input type="hidden" name="email_settings" value="1">
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="smtp_host" class="block text-sm font-medium text-gray-700 mb-2">خادم SMTP</label>
                                    <input type="text" id="smtp_host" name="smtp_host" value="<?php echo htmlspecialchars($settings['smtp_host'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label for="smtp_port" class="block text-sm font-medium text-gray-700 mb-2">منفذ SMTP</label>
                                    <input type="number" id="smtp_port" name="smtp_port" value="<?php echo htmlspecialchars($settings['smtp_port'] ?? '587'); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label for="smtp_username" class="block text-sm font-medium text-gray-700 mb-2">اسم المستخدم</label>
                                    <input type="text" id="smtp_username" name="smtp_username" value="<?php echo htmlspecialchars($settings['smtp_username'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label for="smtp_password" class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور</label>
                                    <input type="password" id="smtp_password" name="smtp_password" value="<?php echo htmlspecialchars($settings['smtp_password'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label for="smtp_from_email" class="block text-sm font-medium text-gray-700 mb-2">البريد المرسل</label>
                                    <input type="email" id="smtp_from_email" name="smtp_from_email" value="<?php echo htmlspecialchars($settings['smtp_from_email'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label for="smtp_from_name" class="block text-sm font-medium text-gray-700 mb-2">اسم المرسل</label>
                                    <input type="text" id="smtp_from_name" name="smtp_from_name" value="<?php echo htmlspecialchars($settings['smtp_from_name'] ?? ''); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                            </div>
                            
                            <div>
                                <label for="smtp_encryption" class="block text-sm font-medium text-gray-700 mb-2">نوع التشفير</label>
                                <select id="smtp_encryption" name="smtp_encryption" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="tls" <?php echo ($settings['smtp_encryption'] ?? '') === 'tls' ? 'selected' : ''; ?>>TLS</option>
                                    <option value="ssl" <?php echo ($settings['smtp_encryption'] ?? '') === 'ssl' ? 'selected' : ''; ?>>SSL</option>
                                    <option value="none" <?php echo ($settings['smtp_encryption'] ?? '') === 'none' ? 'selected' : ''; ?>>بدون تشفير</option>
                                </select>
                            </div>
                            
                            <div class="flex justify-end">
                                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200">
                                    حفظ إعدادات البريد الإلكتروني
                                </button>
                            </div>
                        </form>
                        
                        <?php elseif ($activeTab === 'security'): ?>
                        <!-- إعدادات الأمان -->
                        
                        <!-- أدوات الأمان السريعة -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                            <h3 class="text-lg font-semibold text-blue-900 mb-4 flex items-center">
                                <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                </svg>
                                أدوات الأمان والمراقبة
                            </h3>
                            <p class="text-blue-700 mb-4">الوصول السريع إلى أدوات مراقبة الأمان وإدارة التهديدات</p>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                <a href="security_dashboard.php" class="bg-white border border-blue-300 rounded-lg p-4 hover:bg-blue-50 transition-colors duration-200 group">
                                    <div class="flex items-center">
                                        <div class="bg-blue-100 rounded-lg p-2 group-hover:bg-blue-200 transition-colors duration-200">
                                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                            </svg>
                                        </div>
                                        <div class="mr-3">
                                            <h4 class="text-sm font-semibold text-gray-900">لوحة الأمان</h4>
                                            <p class="text-xs text-gray-600">مراقبة الأحداث والتنبيهات الأمنية</p>
                                        </div>
                                    </div>
                                </a>
                                
                                <a href="users.php" class="bg-white border border-blue-300 rounded-lg p-4 hover:bg-blue-50 transition-colors duration-200 group">
                                    <div class="flex items-center">
                                        <div class="bg-green-100 rounded-lg p-2 group-hover:bg-green-200 transition-colors duration-200">
                                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                            </svg>
                                        </div>
                                        <div class="mr-3">
                                            <h4 class="text-sm font-semibold text-gray-900">إدارة المستخدمين</h4>
                                            <p class="text-xs text-gray-600">إدارة حسابات المستخدمين والصلاحيات</p>
                                        </div>
                                    </div>
                                </a>
                                
                                <a href="permissions.php" class="bg-white border border-blue-300 rounded-lg p-4 hover:bg-blue-50 transition-colors duration-200 group">
                                    <div class="flex items-center">
                                        <div class="bg-yellow-100 rounded-lg p-2 group-hover:bg-yellow-200 transition-colors duration-200">
                                            <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
                                            </svg>
                                        </div>
                                        <div class="mr-3">
                                            <h4 class="text-sm font-semibold text-gray-900">إدارة الصلاحيات</h4>
                                            <p class="text-xs text-gray-600">تحديد صلاحيات المستخدمين والأدوار</p>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>
                        
                        <form method="POST" class="space-y-6">
                            <input type="hidden" name="security_settings" value="1">
                            
                            <div class="space-y-4">
                                <div class="flex items-center">
                                    <input type="checkbox" id="enable_2fa" name="enable_2fa" <?php echo ($settings['enable_2fa'] ?? false) ? 'checked' : ''; ?> class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <label for="enable_2fa" class="mr-2 block text-sm text-gray-900">تفعيل المصادقة الثنائية</label>
                                </div>
                                
                                <div class="flex items-center">
                                    <input type="checkbox" id="require_strong_password" name="require_strong_password" <?php echo ($settings['require_strong_password'] ?? false) ? 'checked' : ''; ?> class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <label for="require_strong_password" class="mr-2 block text-sm text-gray-900">طلب كلمة مرور قوية</label>
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div>
                                    <label for="session_timeout" class="block text-sm font-medium text-gray-700 mb-2">انتهاء الجلسة (دقيقة)</label>
                                    <input type="number" id="session_timeout" name="session_timeout" value="<?php echo htmlspecialchars($settings['session_timeout'] ?? '30'); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label for="max_login_attempts" class="block text-sm font-medium text-gray-700 mb-2">محاولات تسجيل الدخول</label>
                                    <input type="number" id="max_login_attempts" name="max_login_attempts" value="<?php echo htmlspecialchars($settings['max_login_attempts'] ?? '5'); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label for="password_min_length" class="block text-sm font-medium text-gray-700 mb-2">الحد الأدنى لطول كلمة المرور</label>
                                    <input type="number" id="password_min_length" name="password_min_length" value="<?php echo htmlspecialchars($settings['password_min_length'] ?? '8'); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                            </div>
                            
                            <div class="flex justify-end">
                                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200">
                                    حفظ إعدادات الأمان
                                </button>
                            </div>
                        </form>
                        
                        <?php elseif ($activeTab === 'system'): ?>
                        <!-- إعدادات النظام -->
                        <form method="POST" class="space-y-6">
                            <input type="hidden" name="system_settings" value="1">
                            
                            <div class="space-y-4">
                                <div class="flex items-center">
                                    <input type="checkbox" id="maintenance_mode" name="maintenance_mode" <?php echo ($settings['maintenance_mode'] ?? false) ? 'checked' : ''; ?> class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <label for="maintenance_mode" class="mr-2 block text-sm text-gray-900">وضع الصيانة</label>
                                </div>
                                
                                <div class="flex items-center">
                                    <input type="checkbox" id="debug_mode" name="debug_mode" <?php echo ($settings['debug_mode'] ?? false) ? 'checked' : ''; ?> class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <label for="debug_mode" class="mr-2 block text-sm text-gray-900">وضع التطوير</label>
                                </div>
                                
                                <div class="flex items-center">
                                    <input type="checkbox" id="cache_enabled" name="cache_enabled" <?php echo ($settings['cache_enabled'] ?? false) ? 'checked' : ''; ?> class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <label for="cache_enabled" class="mr-2 block text-sm text-gray-900">تفعيل التخزين المؤقت</label>
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div>
                                    <label for="backup_frequency" class="block text-sm font-medium text-gray-700 mb-2">تكرار النسخ الاحتياطي</label>
                                    <select id="backup_frequency" name="backup_frequency" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="daily" <?php echo ($settings['backup_frequency'] ?? '') === 'daily' ? 'selected' : ''; ?>>يومي</option>
                                        <option value="weekly" <?php echo ($settings['backup_frequency'] ?? '') === 'weekly' ? 'selected' : ''; ?>>أسبوعي</option>
                                        <option value="monthly" <?php echo ($settings['backup_frequency'] ?? '') === 'monthly' ? 'selected' : ''; ?>>شهري</option>
                                        <option value="manual" <?php echo ($settings['backup_frequency'] ?? '') === 'manual' ? 'selected' : ''; ?>>يدوي</option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label for="timezone" class="block text-sm font-medium text-gray-700 mb-2">المنطقة الزمنية</label>
                                    <select id="timezone" name="timezone" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="Asia/Riyadh" <?php echo ($settings['timezone'] ?? '') === 'Asia/Riyadh' ? 'selected' : ''; ?>>الرياض</option>
                                        <option value="Asia/Dubai" <?php echo ($settings['timezone'] ?? '') === 'Asia/Dubai' ? 'selected' : ''; ?>>دبي</option>
                                        <option value="Asia/Kuwait" <?php echo ($settings['timezone'] ?? '') === 'Asia/Kuwait' ? 'selected' : ''; ?>>الكويت</option>
                                        <option value="Asia/Qatar" <?php echo ($settings['timezone'] ?? '') === 'Asia/Qatar' ? 'selected' : ''; ?>>قطر</option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label for="language" class="block text-sm font-medium text-gray-700 mb-2">اللغة</label>
                                    <select id="language" name="language" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="ar" <?php echo ($settings['language'] ?? '') === 'ar' ? 'selected' : ''; ?>>العربية</option>
                                        <option value="en" <?php echo ($settings['language'] ?? '') === 'en' ? 'selected' : ''; ?>>English</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="flex justify-end">
                                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200">
                                    حفظ إعدادات النظام
                                </button>
                            </div>
                        </form>
                        <?php endif; ?>
                    </div>
                </div>

<script>
// تحديث قيم الألوان عند تغييرها
document.addEventListener('DOMContentLoaded', function() {
    const colorInputs = document.querySelectorAll('input[type="color"]');
    
    colorInputs.forEach(input => {
        input.addEventListener('change', function() {
            const textInput = this.parentElement.querySelector('input[type="text"]');
            if (textInput) {
                textInput.value = this.value;
            }
        });
    });
    
    // تحديث رابط الخط عند تغيير نوع الخط
    const fontFamilySelect = document.getElementById('font_family');
    const fontWeightSelect = document.getElementById('font_weight');
    const fontUrlInput = document.getElementById('font_url');
    const fontPreview = document.getElementById('font_preview');
    const fontDescription = document.getElementById('font_description');
    const fontSizeRange = document.getElementById('font_size_range');
    const fontSizeInput = document.getElementById('font_size');
    
    if (fontFamilySelect && fontUrlInput && fontPreview) {
        // روابط الخطوط من Google Fonts مع الأوزان المختلفة
        const fontUrls = {
            'Noto Kufi Arabic': 'https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@100;200;300;400;500;600;700;800;900&display=swap',
            'Cairo': 'https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap',
            'Amiri': 'https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&display=swap',
            'Tajawal': 'https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap',
            'Almarai': 'https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700;800&display=swap',
            'Changa': 'https://fonts.googleapis.com/css2?family=Changa:wght@200;300;400;500;600;700;800&display=swap',
            'El Messiri': 'https://fonts.googleapis.com/css2?family=El+Messiri:wght@400;500;600;700&display=swap',
            'Harmattan': 'https://fonts.googleapis.com/css2?family=Harmattan:wght@400;500;600;700&display=swap',
            'IBM Plex Sans Arabic': 'https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@100;200;300;400;500;600;700&display=swap',
            'Katibeh': 'https://fonts.googleapis.com/css2?family=Katibeh:wght@400&display=swap',
            'Lalezar': 'https://fonts.googleapis.com/css2?family=Lalezar:wght@400&display=swap',
            'Lateef': 'https://fonts.googleapis.com/css2?family=Lateef:wght@200;300;400;500;600;700;800&display=swap',
            'Mada': 'https://fonts.googleapis.com/css2?family=Mada:wght@200;300;400;500;600;700;900&display=swap',
            'Markazi Text': 'https://fonts.googleapis.com/css2?family=Markazi+Text:wght@400;500;600;700&display=swap',
            'Mirza': 'https://fonts.googleapis.com/css2?family=Mirza:wght@400;500;600;700&display=swap',
            'Rakkas': 'https://fonts.googleapis.com/css2?family=Rakkas:wght@400&display=swap',
            'Reem Kufi': 'https://fonts.googleapis.com/css2?family=Reem+Kufi:wght@400;500;600;700&display=swap',
            'Scheherazade New': 'https://fonts.googleapis.com/css2?family=Scheherazade+New:wght@400;500;600;700&display=swap',
            'Vibes': 'https://fonts.googleapis.com/css2?family=Vibes:wght@400&display=swap'
        };
        
        // الأوزان المتاحة لكل خط
        const fontWeights = {
            'Noto Kufi Arabic': ['300', '400', '500', '600', '700', '800'],
            'Cairo': ['300', '400', '500', '600', '700', '800'],
            'Amiri': ['400', '700'],
            'Tajawal': ['300', '400', '500', '700', '800'],
            'Almarai': ['300', '400', '700', '800'],
            'Changa': ['300', '400', '500', '600', '700', '800'],
            'El Messiri': ['400', '500', '600', '700'],
            'Harmattan': ['400', '500', '600', '700'],
            'IBM Plex Sans Arabic': ['300', '400', '500', '600', '700'],
            'Katibeh': ['400'],
            'Lalezar': ['400'],
            'Lateef': ['300', '400', '500', '600', '700', '800'],
            'Mada': ['300', '400', '500', '600', '700'],
            'Markazi Text': ['400', '500', '600', '700'],
            'Mirza': ['400', '500', '600', '700'],
            'Rakkas': ['400'],
            'Reem Kufi': ['400', '500', '600', '700'],
            'Scheherazade New': ['400', '500', '600', '700'],
            'Vibes': ['400']
        };
        
        // معلومات الخطوط
        const fontDescriptions = {
            'Noto Kufi Arabic': 'خط حديث ومتوازن، مناسب للنصوص الطويلة والعناوين. يدعم جميع الأحرف العربية بوضوح عالي.',
            'Cairo': 'خط أنيق ومقروء، مثالي للمواقع الحديثة. يتميز بالبساطة والوضوح في جميع الأحجام.',
            'Amiri': 'خط تقليدي وكلاسيكي مستوحى من الخط النسخي. مناسب للنصوص الرسمية والأدبية.',
            'Tajawal': 'خط بسيط وواضح، سهل القراءة على الشاشات. مناسب للاستخدام في التطبيقات والمواقع.',
            'Almarai': 'خط عصري وأنيق، يجمع بين البساطة والجمال. مثالي للعناوين والنصوص القصيرة.',
            'Changa': 'خط متغير الوزن، يوفر مرونة في التصميم. مناسب للعناوين والنصوص التفاعلية.',
            'El Messiri': 'خط مستوحى من الخط الكوفي، يتميز بالطابع التراثي الحديث.',
            'Harmattan': 'خط بسيط ومقروء، مصمم خصيصاً للاستخدام الرقمي والطباعة.',
            'IBM Plex Sans Arabic': 'خط احترافي من IBM، مصمم للاستخدام في الشركات والتطبيقات التقنية.',
            'Katibeh': 'خط زخرفي مميز، مناسب للعناوين والتصاميم الإبداعية.',
            'Lalezar': 'خط عصري بلمسة فنية، مثالي للعناوين والشعارات.',
            'Lateef': 'خط تقليدي محسن، يجمع بين الأصالة والوضوح الحديث.',
            'Mada': 'خط متعدد الأوزان، مرن ومناسب لجميع أنواع المحتوى.',
            'Markazi Text': 'خط مصمم للنصوص الطويلة، يتميز بالراحة في القراءة.',
            'Mirza': 'خط أنيق بطابع كلاسيكي، مناسب للمحتوى الأدبي والثقافي.',
            'Rakkas': 'خط زخرفي مميز، مثالي للعناوين والتصاميم الفنية.',
            'Reem Kufi': 'خط كوفي حديث، يجمع بين التراث والعصرية.',
            'Scheherazade New': 'خط تقليدي محسن، مناسب للنصوص الطويلة والمحتوى الأدبي.',
            'Vibes': 'خط عصري بلمسة شبابية، مناسب للمحتوى التفاعلي والإبداعي.'
        };
        
        // تحديث أوزان الخط المتاحة
        function updateAvailableWeights() {
            const selectedFont = fontFamilySelect.value;
            const availableWeights = fontWeights[selectedFont] || ['400'];
            const currentWeight = fontWeightSelect.value;
            
            // مسح الخيارات الحالية
            fontWeightSelect.innerHTML = '';
            
            // إضافة الأوزان المتاحة
            const weightLabels = {
                '300': 'خفيف (300)',
                '400': 'عادي (400)',
                '500': 'متوسط (500)',
                '600': 'نصف عريض (600)',
                '700': 'عريض (700)',
                '800': 'عريض جداً (800)'
            };
            
            availableWeights.forEach(weight => {
                const option = document.createElement('option');
                option.value = weight;
                option.textContent = weightLabels[weight] || `وزن ${weight}`;
                option.selected = weight === currentWeight;
                fontWeightSelect.appendChild(option);
            });
            
            // إذا لم يكن الوزن الحالي متاحاً، اختر الوزن الافتراضي
            if (!availableWeights.includes(currentWeight)) {
                fontWeightSelect.value = availableWeights.includes('400') ? '400' : availableWeights[0];
            }
        }
        
        // تحديث الخط والمعاينة
        async function updateFontPreview() {
            const selectedFont = fontFamilySelect.value;
            const selectedWeight = fontWeightSelect.value;
            const fontSize = fontSizeInput.value;
            
            console.log('تحديث معاينة الخط:', { selectedFont, selectedWeight, fontSize });
            
            // تحديث رابط الخط
            if (fontUrls[selectedFont]) {
                fontUrlInput.value = fontUrls[selectedFont];
            }
            
            // تحديث وصف الخط
            if (fontDescriptions[selectedFont]) {
                fontDescription.textContent = fontDescriptions[selectedFont];
            }
            
            try {
                // تحميل الخط وتطبيقه على المعاينة
                await loadFont(selectedFont, fontUrls[selectedFont]);
                
                console.log('تم تحميل الخط، تطبيق المعاينة...');
                
                // تطبيق الخط على المعاينة الرئيسية
                fontPreview.style.fontFamily = `"${selectedFont}", Arial, sans-serif`;
                fontPreview.style.fontWeight = selectedWeight;
                fontPreview.style.fontSize = fontSize + 'px';
                fontPreview.style.fontDisplay = 'swap';
                
                // إجبار إعادة الرسم
                fontPreview.offsetHeight;
                
                // تحديث أحجام النصوص في المعاينة
                const paragraphs = fontPreview.querySelectorAll('p');
                paragraphs.forEach((p, index) => {
                    const multipliers = [1.5, 1.25, 1, 0.875, 0.75]; // للأحجام المختلفة
                    p.style.fontFamily = `"${selectedFont}", Arial, sans-serif`;
                    p.style.fontSize = (fontSize * multipliers[index]) + 'px';
                    p.style.fontWeight = selectedWeight;
                    p.style.fontDisplay = 'swap';
                    
                    // إجبار إعادة الرسم لكل فقرة
                    p.offsetHeight;
                });
                
                console.log('تم تطبيق الخط بنجاح على جميع العناصر');
                
            } catch (error) {
                console.error('خطأ في تحديث معاينة الخط:', error);
            }
        }
        
        // تحميل الخط ديناميكياً
        function loadFont(fontName, fontUrl) {
            return new Promise((resolve, reject) => {
                console.log('محاولة تحميل الخط:', fontName, fontUrl);
                
                // التحقق من وجود الخط مسبقاً
                const existingLink = document.querySelector(`link[href="${fontUrl}"]`);
                if (existingLink) {
                    console.log('الخط موجود مسبقاً:', fontName);
                    // انتظار قصير للتأكد من تحميل الخط
                    setTimeout(() => resolve(), 100);
                    return;
                }
                
                // إنشاء عنصر link لتحميل الخط
                const link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = fontUrl;
                
                link.onload = () => {
                    console.log('تم تحميل الخط بنجاح:', fontName);
                    // انتظار إضافي للتأكد من تطبيق الخط
                    setTimeout(() => resolve(), 200);
                };
                
                link.onerror = () => {
                    console.error('فشل في تحميل الخط:', fontName);
                    // حتى لو فشل التحميل، نكمل العملية
                    resolve();
                };
                
                document.head.appendChild(link);
                
                // timeout للأمان في حالة عدم استجابة الخط
                setTimeout(() => {
                    console.log('انتهت مهلة انتظار تحميل الخط:', fontName);
                    resolve();
                }, 3000);
            });
        }
        
        // ربط الأحداث
        fontFamilySelect.addEventListener('change', function() {
            updateAvailableWeights();
            updateFontPreview();
        });
        
        fontWeightSelect.addEventListener('change', updateFontPreview);
        
        // ربط مقاس الخط
        if (fontSizeRange && fontSizeInput) {
            fontSizeRange.addEventListener('input', function() {
                fontSizeInput.value = this.value;
                updateFontPreview();
            });
            
            fontSizeInput.addEventListener('input', function() {
                fontSizeRange.value = this.value;
                updateFontPreview();
            });
        }
        
        // تطبيق المعاينة عند تحميل الصفحة
        updateAvailableWeights();
        updateFontPreview();
    }
});
</script>

<?php endLayout(); ?>