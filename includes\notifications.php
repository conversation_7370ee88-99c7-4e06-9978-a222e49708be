<?php
/**
 * نظام الإشعارات - البريد الإلكتروني والرسائل النصية
 * Notifications System - Email & SMS
 */

require_once 'database.php';

class NotificationManager {
    private $database;
    private $emailConfig;
    private $smsConfig;
    
    public function __construct() {
        global $database;
        $this->database = $database;
        $this->loadConfigurations();
        $this->createNotificationTables();
    }
    
    /**
     * تحميل إعدادات الإشعارات
     */
    private function loadConfigurations() {
        // إعدادات البريد الإلكتروني
        $this->emailConfig = [
            'smtp_host' => 'smtp.gmail.com',
            'smtp_port' => 587,
            'smtp_username' => '', // يجب تعيينها في الإعدادات
            'smtp_password' => '', // يجب تعيينها في الإعدادات
            'from_email' => '<EMAIL>',
            'from_name' => 'Green Line'
        ];
        
        // إعدادات الرسائل النصية
        $this->smsConfig = [
            'provider' => 'twilio', // أو 'nexmo' أو 'local'
            'api_key' => '', // يجب تعيينها
            'api_secret' => '', // يجب تعيينها
            'from_number' => '+**********' // رقم المرسل
        ];
        
        // تحميل الإعدادات من قاعدة البيانات
        $this->loadDatabaseConfig();
    }
    
    /**
     * تحميل الإعدادات من قاعدة البيانات
     */
    private function loadDatabaseConfig() {
        $settings = $this->database->fetchAll(
            "SELECT setting_key, setting_value FROM settings WHERE setting_key LIKE 'notification_%'"
        );
        
        foreach ($settings as $setting) {
            $key = str_replace('notification_', '', $setting['setting_key']);
            
            if (strpos($key, 'email_') === 0) {
                $emailKey = str_replace('email_', '', $key);
                $this->emailConfig[$emailKey] = $setting['setting_value'];
            } elseif (strpos($key, 'sms_') === 0) {
                $smsKey = str_replace('sms_', '', $key);
                $this->smsConfig[$smsKey] = $setting['setting_value'];
            }
        }
    }
    
    /**
     * إنشاء جداول الإشعارات
     */
    private function createNotificationTables() {
        // جدول قوالب الإشعارات
        $this->database->query("
            CREATE TABLE IF NOT EXISTS notification_templates (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(100) NOT NULL UNIQUE,
                type ENUM('email', 'sms', 'both') NOT NULL,
                subject VARCHAR(255),
                email_body TEXT,
                sms_body TEXT,
                variables JSON,
                status ENUM('active', 'inactive') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ");
        
        // جدول سجل الإشعارات
        $this->database->query("
            CREATE TABLE IF NOT EXISTS notification_logs (
                id INT PRIMARY KEY AUTO_INCREMENT,
                template_name VARCHAR(100),
                recipient_type ENUM('user', 'admin', 'custom'),
                recipient_id INT,
                recipient_email VARCHAR(255),
                recipient_phone VARCHAR(20),
                type ENUM('email', 'sms'),
                subject VARCHAR(255),
                content TEXT,
                status ENUM('pending', 'sent', 'failed', 'cancelled') DEFAULT 'pending',
                error_message TEXT,
                sent_at TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_recipient (recipient_type, recipient_id),
                INDEX idx_status (status),
                INDEX idx_created (created_at)
            )
        ");
        
        // جدول اشتراكات الإشعارات
        $this->database->query("
            CREATE TABLE IF NOT EXISTS notification_subscriptions (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT,
                email VARCHAR(255),
                phone VARCHAR(20),
                notification_type VARCHAR(100),
                channel ENUM('email', 'sms', 'both'),
                status ENUM('active', 'inactive') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE KEY unique_subscription (user_id, notification_type, channel),
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )
        ");
        
        $this->insertDefaultTemplates();
    }
    
    /**
     * إدراج القوالب الافتراضية
     */
    private function insertDefaultTemplates() {
        $templates = [
            [
                'name' => 'welcome_email',
                'type' => 'email',
                'subject' => 'مرحباً بك في Green Line',
                'email_body' => '<h2>مرحباً {{name}}</h2><p>نرحب بك في موقع Green Line. نتمنى لك تجربة رائعة معنا.</p>',
                'variables' => json_encode(['name', 'email'])
            ],
            [
                'name' => 'order_confirmation',
                'type' => 'both',
                'subject' => 'تأكيد الطلب #{{order_id}}',
                'email_body' => '<h2>تم تأكيد طلبك</h2><p>رقم الطلب: {{order_id}}</p><p>المبلغ الإجمالي: {{total}} ريال</p>',
                'sms_body' => 'تم تأكيد طلبك #{{order_id}} بمبلغ {{total}} ريال. شكراً لك.',
                'variables' => json_encode(['order_id', 'total', 'customer_name'])
            ],
            [
                'name' => 'password_reset',
                'type' => 'email',
                'subject' => 'إعادة تعيين كلمة المرور',
                'email_body' => '<h2>إعادة تعيين كلمة المرور</h2><p>اضغط على الرابط التالي لإعادة تعيين كلمة المرور:</p><a href="{{reset_link}}">إعادة تعيين</a>',
                'variables' => json_encode(['name', 'reset_link'])
            ],
            [
                'name' => 'contact_notification',
                'type' => 'email',
                'subject' => 'رسالة جديدة من موقع Green Line',
                'email_body' => '<h2>رسالة جديدة</h2><p><strong>الاسم:</strong> {{name}}</p><p><strong>البريد:</strong> {{email}}</p><p><strong>الرسالة:</strong></p><p>{{message}}</p>',
                'variables' => json_encode(['name', 'email', 'message'])
            ]
        ];
        
        foreach ($templates as $template) {
            $existing = $this->database->fetch(
                "SELECT id FROM notification_templates WHERE name = :name",
                ['name' => $template['name']]
            );
            
            if (!$existing) {
                $this->database->insert('notification_templates', $template);
            }
        }
    }
    
    /**
     * إرسال إشعار
     */
    public function send($templateName, $recipient, $variables = [], $options = []) {
        $template = $this->getTemplate($templateName);
        if (!$template) {
            throw new Exception("Template '{$templateName}' not found");
        }
        
        $results = [];
        
        // تحديد نوع المستقبل
        $recipientData = $this->parseRecipient($recipient);
        
        // إرسال البريد الإلكتروني
        if (in_array($template['type'], ['email', 'both']) && $recipientData['email']) {
            $results['email'] = $this->sendEmail($template, $recipientData, $variables, $options);
        }
        
        // إرسال الرسالة النصية
        if (in_array($template['type'], ['sms', 'both']) && $recipientData['phone']) {
            $results['sms'] = $this->sendSMS($template, $recipientData, $variables, $options);
        }
        
        return $results;
    }
    
    /**
     * تحليل بيانات المستقبل
     */
    private function parseRecipient($recipient) {
        if (is_array($recipient)) {
            return [
                'type' => 'custom',
                'id' => null,
                'email' => $recipient['email'] ?? null,
                'phone' => $recipient['phone'] ?? null,
                'name' => $recipient['name'] ?? 'العميل'
            ];
        }
        
        if (is_numeric($recipient)) {
            // معرف المستخدم
            $user = $this->database->fetch(
                "SELECT id, name, email, phone FROM users WHERE id = :id",
                ['id' => $recipient]
            );
            
            if ($user) {
                return [
                    'type' => 'user',
                    'id' => $user['id'],
                    'email' => $user['email'],
                    'phone' => $user['phone'],
                    'name' => $user['name']
                ];
            }
        }
        
        if (filter_var($recipient, FILTER_VALIDATE_EMAIL)) {
            return [
                'type' => 'custom',
                'id' => null,
                'email' => $recipient,
                'phone' => null,
                'name' => 'العميل'
            ];
        }
        
        throw new Exception('Invalid recipient format');
    }
    
    /**
     * إرسال بريد إلكتروني
     */
    private function sendEmail($template, $recipient, $variables, $options) {
        $subject = $this->replaceVariables($template['subject'], $variables);
        $body = $this->replaceVariables($template['email_body'], $variables);
        
        // تسجيل في قاعدة البيانات
        $logId = $this->database->insert('notification_logs', [
            'template_name' => $template['name'],
            'recipient_type' => $recipient['type'],
            'recipient_id' => $recipient['id'],
            'recipient_email' => $recipient['email'],
            'type' => 'email',
            'subject' => $subject,
            'content' => $body,
            'status' => 'pending'
        ]);
        
        try {
            // استخدام PHPMailer أو mail() function
            $success = $this->sendEmailViaSMTP($recipient['email'], $subject, $body, $options);
            
            if ($success) {
                $this->database->update('notification_logs', 
                    ['status' => 'sent', 'sent_at' => date('Y-m-d H:i:s')],
                    'id = :id',
                    ['id' => $logId]
                );
                return ['success' => true, 'log_id' => $logId];
            } else {
                $this->database->update('notification_logs',
                    ['status' => 'failed', 'error_message' => 'Failed to send email'],
                    'id = :id',
                    ['id' => $logId]
                );
                return ['success' => false, 'error' => 'Failed to send email'];
            }
            
        } catch (Exception $e) {
            $this->database->update('notification_logs',
                ['status' => 'failed', 'error_message' => $e->getMessage()],
                'id = :id',
                ['id' => $logId]
            );
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * إرسال بريد إلكتروني عبر SMTP
     */
    private function sendEmailViaSMTP($to, $subject, $body, $options = []) {
        // إعداد headers
        $headers = [
            'MIME-Version: 1.0',
            'Content-type: text/html; charset=UTF-8',
            'From: ' . $this->emailConfig['from_name'] . ' <' . $this->emailConfig['from_email'] . '>',
            'Reply-To: ' . $this->emailConfig['from_email'],
            'X-Mailer: PHP/' . phpversion()
        ];
        
        // إضافة HTML wrapper
        $htmlBody = $this->wrapEmailHTML($body, $subject);
        
        // محاولة الإرسال
        return mail($to, $subject, $htmlBody, implode("\r\n", $headers));
    }
    
    /**
     * تغليف البريد الإلكتروني بـ HTML
     */
    private function wrapEmailHTML($content, $subject) {
        return "
        <!DOCTYPE html>
        <html dir='rtl' lang='ar'>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>{$subject}</title>
            <style>
                body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #2c5530; color: white; padding: 20px; text-align: center; }
                .content { padding: 30px; background: #f9f9f9; }
                .footer { background: #333; color: white; padding: 15px; text-align: center; font-size: 12px; }
                a { color: #2c5530; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>Green Line</h1>
                </div>
                <div class='content'>
                    {$content}
                </div>
                <div class='footer'>
                    <p>&copy; 2024 Green Line. جميع الحقوق محفوظة.</p>
                </div>
            </div>
        </body>
        </html>
        ";
    }
    
    /**
     * إرسال رسالة نصية
     */
    private function sendSMS($template, $recipient, $variables, $options) {
        $message = $this->replaceVariables($template['sms_body'], $variables);
        
        // تسجيل في قاعدة البيانات
        $logId = $this->database->insert('notification_logs', [
            'template_name' => $template['name'],
            'recipient_type' => $recipient['type'],
            'recipient_id' => $recipient['id'],
            'recipient_phone' => $recipient['phone'],
            'type' => 'sms',
            'content' => $message,
            'status' => 'pending'
        ]);
        
        try {
            $success = $this->sendSMSViaProvider($recipient['phone'], $message, $options);
            
            if ($success) {
                $this->database->update('notification_logs',
                    ['status' => 'sent', 'sent_at' => date('Y-m-d H:i:s')],
                    'id = :id',
                    ['id' => $logId]
                );
                return ['success' => true, 'log_id' => $logId];
            } else {
                $this->database->update('notification_logs',
                    ['status' => 'failed', 'error_message' => 'Failed to send SMS'],
                    'id = :id',
                    ['id' => $logId]
                );
                return ['success' => false, 'error' => 'Failed to send SMS'];
            }
            
        } catch (Exception $e) {
            $this->database->update('notification_logs',
                ['status' => 'failed', 'error_message' => $e->getMessage()],
                'id = :id',
                ['id' => $logId]
            );
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * إرسال رسالة نصية عبر مزود الخدمة
     */
    private function sendSMSViaProvider($phone, $message, $options = []) {
        // هنا يمكن تطبيق مزودي خدمة مختلفين
        switch ($this->smsConfig['provider']) {
            case 'twilio':
                return $this->sendViaTwilio($phone, $message);
            case 'nexmo':
                return $this->sendViaNexmo($phone, $message);
            case 'local':
                return $this->sendViaLocalProvider($phone, $message);
            default:
                // محاكاة الإرسال للاختبار
                error_log("SMS to {$phone}: {$message}");
                return true;
        }
    }
    
    /**
     * إرسال عبر Twilio
     */
    private function sendViaTwilio($phone, $message) {
        // تطبيق Twilio API
        // يتطلب مكتبة Twilio SDK
        return true; // محاكاة
    }
    
    /**
     * إرسال عبر Nexmo
     */
    private function sendViaNexmo($phone, $message) {
        // تطبيق Nexmo API
        return true; // محاكاة
    }
    
    /**
     * إرسال عبر مزود محلي
     */
    private function sendViaLocalProvider($phone, $message) {
        // تطبيق مزود محلي
        return true; // محاكاة
    }
    
    /**
     * استبدال المتغيرات في النص
     */
    private function replaceVariables($text, $variables) {
        foreach ($variables as $key => $value) {
            $text = str_replace('{{' . $key . '}}', $value, $text);
        }
        return $text;
    }
    
    /**
     * الحصول على قالب
     */
    private function getTemplate($name) {
        return $this->database->fetch(
            "SELECT * FROM notification_templates WHERE name = :name AND status = 'active'",
            ['name' => $name]
        );
    }
    
    /**
     * إضافة اشتراك في الإشعارات
     */
    public function subscribe($userId, $notificationType, $channel = 'both') {
        return $this->database->insert('notification_subscriptions', [
            'user_id' => $userId,
            'notification_type' => $notificationType,
            'channel' => $channel,
            'status' => 'active'
        ]);
    }
    
    /**
     * إلغاء اشتراك
     */
    public function unsubscribe($userId, $notificationType, $channel = null) {
        $where = 'user_id = :user_id AND notification_type = :type';
        $params = ['user_id' => $userId, 'type' => $notificationType];
        
        if ($channel) {
            $where .= ' AND channel = :channel';
            $params['channel'] = $channel;
        }
        
        return $this->database->update(
            'notification_subscriptions',
            ['status' => 'inactive'],
            $where,
            $params
        );
    }
    
    /**
     * الحصول على سجل الإشعارات
     */
    public function getLogs($filters = [], $limit = 50, $offset = 0) {
        $where = [];
        $params = [];
        
        if (!empty($filters['recipient_id'])) {
            $where[] = 'recipient_id = :recipient_id';
            $params['recipient_id'] = $filters['recipient_id'];
        }
        
        if (!empty($filters['type'])) {
            $where[] = 'type = :type';
            $params['type'] = $filters['type'];
        }
        
        if (!empty($filters['status'])) {
            $where[] = 'status = :status';
            $params['status'] = $filters['status'];
        }
        
        $whereClause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';
        
        return $this->database->fetchAll(
            "SELECT * FROM notification_logs {$whereClause} ORDER BY created_at DESC LIMIT {$limit} OFFSET {$offset}",
            $params
        );
    }
}

// إنشاء مثيل عام
$notificationManager = new NotificationManager();

/**
 * دوال مساعدة للاستخدام السريع
 */

/**
 * إرسال إشعار ترحيب
 */
function sendWelcomeNotification($userId) {
    global $notificationManager;
    return $notificationManager->send('welcome_email', $userId, [
        'name' => getCurrentUser()['name'] ?? 'العميل'
    ]);
}

/**
 * إرسال تأكيد الطلب
 */
function sendOrderConfirmation($userId, $orderId, $total) {
    global $notificationManager;
    return $notificationManager->send('order_confirmation', $userId, [
        'order_id' => $orderId,
        'total' => number_format($total, 2),
        'customer_name' => getCurrentUser()['name'] ?? 'العميل'
    ]);
}

/**
 * إرسال رابط إعادة تعيين كلمة المرور
 */
function sendPasswordReset($email, $resetLink) {
    global $notificationManager;
    return $notificationManager->send('password_reset', $email, [
        'name' => 'العميل',
        'reset_link' => $resetLink
    ]);
}

/**
 * إرسال إشعار رسالة تواصل جديدة للإدارة
 */
function sendContactNotification($contactData) {
    global $notificationManager;
    
    // إرسال للإدارة
    $adminEmail = '<EMAIL>'; // يجب تعيينه في الإعدادات
    
    return $notificationManager->send('contact_notification', $adminEmail, [
        'name' => $contactData['name'],
        'email' => $contactData['email'],
        'message' => $contactData['message']
    ]);
}

/**
 * إرسال إشعار مخصص
 */
function sendCustomNotification($recipient, $subject, $message, $type = 'email') {
    global $notificationManager;
    
    // إنشاء قالب مؤقت
    $templateData = [
        'name' => 'custom_' . time(),
        'type' => $type,
        'subject' => $subject,
        'email_body' => $message,
        'sms_body' => strip_tags($message)
    ];
    
    // يمكن تحسين هذا بإنشاء قالب مؤقت في قاعدة البيانات
    return $notificationManager->send('custom_' . time(), $recipient, []);
}

?>