# حل مشكلة الخطأ 1304 - PROCEDURE already exists

## وصف المشكلة
عند محاولة استيراد ملف `complete_support_system.sql`، يظهر الخطأ التالي:
```
#1304 - PROCEDURE GetTicketStats already exists
```

هذا يعني أن الإجراء المخزن موجود بالفعل في قاعدة البيانات.

## الحل المقترح

### الطريقة الأولى: استخدام ملف التنظيف

1. **شغل ملف التنظيف أولاً:**
   - افتح phpMyAdmin أو MySQL Workbench
   - اختر قاعدة البيانات `greenline_db`
   - استورد ملف `cleanup_procedures.sql`
   - تأكد من عدم ظهور أخطاء

2. **استورد الملف الرئيسي:**
   - بعد نجاح التنظيف، استورد ملف `complete_support_system.sql`
   - يجب أن يعمل بدون أخطاء الآن

### الطريقة الثانية: الحذف اليدوي

إذا لم تنجح الطريقة الأولى، نفذ الأوامر التالية يدوياً:

```sql
-- اختيار قاعدة البيانات
USE greenline_db;

-- حذف الـ View
DROP VIEW IF EXISTS quick_ticket_stats;

-- حذف الإجراءات المخزنة
DROP PROCEDURE IF EXISTS GetTicketStats;
DROP PROCEDURE IF EXISTS GetDetailedTicketStats;
DROP PROCEDURE IF EXISTS GetDailyTicketStats;
DROP PROCEDURE IF EXISTS GetDetailedTicketStatsDefault;
```

### الطريقة الثالثة: إعادة إنشاء قاعدة البيانات

إذا استمرت المشاكل:

```sql
-- حذف قاعدة البيانات (احذر: سيحذف جميع البيانات!)
DROP DATABASE IF EXISTS greenline_db;

-- إنشاء قاعدة البيانات من جديد
CREATE DATABASE greenline_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- اختيار قاعدة البيانات
USE greenline_db;
```

## التحقق من نجاح العملية

بعد تنفيذ أي من الطرق أعلاه، تحقق من النتيجة:

```sql
-- التحقق من وجود الإجراءات
SELECT ROUTINE_NAME 
FROM INFORMATION_SCHEMA.ROUTINES 
WHERE ROUTINE_SCHEMA = 'greenline_db';

-- التحقق من وجود الجداول
SHOW TABLES LIKE 'support_%';
```

## نصائح إضافية

1. **تأكد من الصلاحيات:**
   - المستخدم يحتاج صلاحيات CREATE, DROP, ALTER
   - تأكد من أن المستخدم له صلاحيات على قاعدة البيانات

2. **تحقق من الجلسات النشطة:**
   ```sql
   SHOW PROCESSLIST;
   ```
   - إذا كانت هناك جلسات تستخدم الإجراءات، أغلقها أولاً

3. **إعادة تشغيل MySQL:**
   - في بعض الحالات، قد تحتاج لإعادة تشغيل خادم MySQL

4. **فحص سجل الأخطاء:**
   - راجع ملف سجل أخطاء MySQL للحصول على تفاصيل أكثر

## الملفات المتعلقة

- `cleanup_procedures.sql` - ملف حذف الإجراءات الموجودة
- `complete_support_system.sql` - الملف الرئيسي للنظام
- `test_database_check.php` - ملف فحص حالة قاعدة البيانات

## في حالة استمرار المشاكل

إذا استمرت المشاكل بعد تجربة جميع الحلول:

1. تحقق من إصدار MySQL (يفضل 5.7 أو أحدث)
2. تأكد من إعدادات `sql_mode`
3. راجع إعدادات الأمان في MySQL
4. تحقق من مساحة القرص المتاحة

---

**ملاحظة:** احرص على عمل نسخة احتياطية من قاعدة البيانات قبل تنفيذ أي من هذه الحلول.