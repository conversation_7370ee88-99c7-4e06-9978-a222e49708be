<?php
/**
 * صفحة الملف الشخصي
 * User Profile Page
 */

$pageTitle = 'الملف الشخصي';
$page = 'profile';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Location: ' . SITE_URL . '/auth/login');
    exit;
}

// جلب بيانات المستخدم الحالي
$currentUser = getCurrentUser();
$userId = $currentUser['id'];

// جلب بيانات المستخدم من قاعدة البيانات
try {
    $database = new Database();
    $user = $database->fetch(
        "SELECT * FROM users WHERE id = :id",
        ['id' => $userId]
    );
    
    if (!$user) {
        header('Location: ' . SITE_URL . '/auth/logout');
        exit;
    }
} catch (Exception $e) {
    $error = 'حدث خطأ في جلب بيانات المستخدم';
}

// معالجة تحديث البيانات
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_profile'])) {
        // تحديث البيانات الشخصية
        $name = sanitizeInput($_POST['name']);
        $phone = sanitizeInput($_POST['phone']);
        $address = sanitizeInput($_POST['address']);
        
        // التحقق من صحة البيانات
        if (empty($name)) {
            $error = 'الاسم مطلوب';
        } else {
            try {
                $database->update('users', [
                    'name' => $name,
                    'phone' => $phone,
                    'address' => $address,
                    'updated_at' => date('Y-m-d H:i:s')
                ], 'id = :id', ['id' => $userId]);
                
                // تحديث بيانات الجلسة
                $_SESSION['user_name'] = $name;
                
                $success = 'تم تحديث البيانات بنجاح';
                
                // إعادة جلب البيانات المحدثة
                $user = $database->fetch(
                    "SELECT * FROM users WHERE id = :id",
                    ['id' => $userId]
                );
            } catch (Exception $e) {
                $error = 'حدث خطأ في تحديث البيانات';
            }
        }
    } elseif (isset($_POST['change_password'])) {
        // تغيير كلمة المرور
        $current_password = $_POST['current_password'];
        $new_password = $_POST['new_password'];
        $confirm_password = $_POST['confirm_password'];
        
        // التحقق من كلمة المرور الحالية
        if (!password_verify($current_password, $user['password'])) {
            $error = 'كلمة المرور الحالية غير صحيحة';
        } elseif (strlen($new_password) < 6) {
            $error = 'كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل';
        } elseif ($new_password !== $confirm_password) {
            $error = 'كلمة المرور الجديدة وتأكيدها غير متطابقين';
        } else {
            try {
                $hashedPassword = password_hash($new_password, PASSWORD_DEFAULT);
                $database->update('users', [
                    'password' => $hashedPassword,
                    'updated_at' => date('Y-m-d H:i:s')
                ], 'id = :id', ['id' => $userId]);
                
                $success = 'تم تغيير كلمة المرور بنجاح';
            } catch (Exception $e) {
                $error = 'حدث خطأ في تغيير كلمة المرور';
            }
        }
    }
}

include 'header.php';
?>

<!-- Page Title Section -->
<section class="bg-primary py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">الملف الشخصي</h1>
            <p class="text-xl text-white/90">إدارة بياناتك الشخصية وإعدادات الحساب</p>
        </div>
    </div>
</section>

<!-- Profile Section -->
<section class="py-16 bg-gray-50 min-h-screen">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">

        <!-- Messages -->
        <?php if ($success): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6" role="alert">
                <span class="block sm:inline"><?php echo htmlspecialchars($success); ?></span>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6" role="alert">
                <span class="block sm:inline"><?php echo htmlspecialchars($error); ?></span>
            </div>
        <?php endif; ?>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            
            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <!-- User Avatar -->
                    <div class="text-center mb-6">
                        <div class="w-24 h-24 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-white text-2xl font-bold">
                                <?php echo strtoupper(substr($user['name'], 0, 1)); ?>
                            </span>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900"><?php echo htmlspecialchars($user['name']); ?></h3>
                        <p class="text-gray-600"><?php echo htmlspecialchars($user['email']); ?></p>
                        <span class="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full mt-2">
                            <?php 
                            $roleNames = [
                                'user' => 'مستخدم',
                                'admin' => 'مدير',
                                'super-admin' => 'مدير عام'
                            ];
                            echo $roleNames[$user['role']] ?? 'مستخدم';
                            ?>
                        </span>
                    </div>

                    <!-- Profile Stats -->
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">تاريخ التسجيل:</span>
                            <span class="text-gray-900 font-medium">
                                <?php echo date('Y/m/d', strtotime($user['created_at'])); ?>
                            </span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">آخر تحديث:</span>
                            <span class="text-gray-900 font-medium">
                                <?php echo $user['updated_at'] ? date('Y/m/d', strtotime($user['updated_at'])) : 'لم يتم التحديث'; ?>
                            </span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">حالة الحساب:</span>
                            <span class="<?php echo $user['is_active'] ? 'text-green-600' : 'text-red-600'; ?> font-medium">
                                <?php echo $user['is_active'] ? 'نشط' : 'غير نشط'; ?>
                            </span>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="mt-6 pt-6 border-t border-gray-200">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">إجراءات سريعة</h4>
                        <div class="space-y-2">
                            <?php if ($user['role'] === 'admin' || $user['role'] === 'super-admin'): ?>
                                <a href="<?php echo ADMIN_URL; ?>" class="block w-full text-center bg-primary text-white py-2 px-4 rounded-lg hover:bg-secondary transition-colors">
                                    <i class="fas fa-tachometer-alt ml-2"></i>
                                    لوحة الإدارة
                                </a>
                            <?php endif; ?>
                            <a href="<?php echo SITE_URL; ?>/auth/logout" class="block w-full text-center bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-colors">
                                <i class="fas fa-sign-out-alt ml-2"></i>
                                تسجيل الخروج
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="lg:col-span-2">
                
                <!-- Profile Information Form -->
                <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">
                        <i class="fas fa-user text-primary ml-2"></i>
                        البيانات الشخصية
                    </h2>
                    
                    <form method="POST" class="space-y-6">
                        <input type="hidden" name="update_profile" value="1">
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل</label>
                                <input type="text" id="name" name="name" value="<?php echo htmlspecialchars($user['name']); ?>" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" required>
                            </div>
                            
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                                <input type="email" id="email" value="<?php echo htmlspecialchars($user['email']); ?>" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-100 cursor-not-allowed" disabled>
                                <p class="text-xs text-gray-500 mt-1">لا يمكن تغيير البريد الإلكتروني</p>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                                <input type="tel" id="phone" name="phone" value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                            </div>
                            
                            <div>
                                <label for="role" class="block text-sm font-medium text-gray-700 mb-2">نوع الحساب</label>
                                <input type="text" value="<?php echo $roleNames[$user['role']] ?? 'مستخدم'; ?>" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-100 cursor-not-allowed" disabled>
                            </div>
                        </div>
                        
                        <div>
                            <label for="address" class="block text-sm font-medium text-gray-700 mb-2">العنوان</label>
                            <textarea id="address" name="address" rows="3" 
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"><?php echo htmlspecialchars($user['address'] ?? ''); ?></textarea>
                        </div>
                        
                        <div class="flex justify-end">
                            <button type="submit" class="bg-primary text-white py-2 px-6 rounded-lg hover:bg-secondary transition-colors">
                                <i class="fas fa-save ml-2"></i>
                                حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Change Password Form -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">
                        <i class="fas fa-lock text-primary ml-2"></i>
                        تغيير كلمة المرور
                    </h2>
                    
                    <form method="POST" class="space-y-6">
                        <input type="hidden" name="change_password" value="1">
                        
                        <div>
                            <label for="current_password" class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور الحالية</label>
                            <input type="password" id="current_password" name="current_password" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" required>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="new_password" class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور الجديدة</label>
                                <input type="password" id="new_password" name="new_password" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" 
                                       minlength="6" required>
                                <p class="text-xs text-gray-500 mt-1">يجب أن تكون 6 أحرف على الأقل</p>
                            </div>
                            
                            <div>
                                <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-2">تأكيد كلمة المرور</label>
                                <input type="password" id="confirm_password" name="confirm_password" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" 
                                       minlength="6" required>
                            </div>
                        </div>
                        
                        <div class="flex justify-end">
                            <button type="submit" class="bg-red-600 text-white py-2 px-6 rounded-lg hover:bg-red-700 transition-colors">
                                <i class="fas fa-key ml-2"></i>
                                تغيير كلمة المرور
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- JavaScript for form validation -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password confirmation validation
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');
    
    function validatePassword() {
        if (newPassword.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity('كلمة المرور غير متطابقة');
        } else {
            confirmPassword.setCustomValidity('');
        }
    }
    
    newPassword.addEventListener('change', validatePassword);
    confirmPassword.addEventListener('keyup', validatePassword);
    
    // Form submission confirmation for password change
    const passwordForm = document.querySelector('form[method="POST"]:has(input[name="change_password"])');
    if (passwordForm) {
        passwordForm.addEventListener('submit', function(e) {
            if (!confirm('هل أنت متأكد من تغيير كلمة المرور؟')) {
                e.preventDefault();
            }
        });
    }
});
</script>

<?php include 'footer.php'; ?>