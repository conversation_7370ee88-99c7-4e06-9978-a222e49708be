<?php
/**
 * نظام حماية CSRF
 * CSRF Protection System
 */

class CSRFProtection {
    private static $tokenName = 'csrf_token';
    private static $tokenLength = 32;
    private static $tokenLifetime = 3600; // ساعة واحدة
    
    /**
     * إنشاء رمز CSRF جديد
     */
    public static function generateToken() {
        $token = bin2hex(random_bytes(self::$tokenLength));
        $timestamp = time();
        
        // حفظ الرمز في الجلسة
        $_SESSION['csrf_tokens'][$token] = $timestamp;
        
        // تنظيف الرموز المنتهية الصلاحية
        self::cleanExpiredTokens();
        
        return $token;
    }
    
    /**
     * التحقق من صحة رمز CSRF
     */
    public static function validateToken($token) {
        if (empty($token)) {
            return false;
        }
        
        // التحقق من وجود الرمز في الجلسة
        if (!isset($_SESSION['csrf_tokens'][$token])) {
            return false;
        }
        
        // التحقق من انتهاء صلاحية الرمز
        $timestamp = $_SESSION['csrf_tokens'][$token];
        if ((time() - $timestamp) > self::$tokenLifetime) {
            unset($_SESSION['csrf_tokens'][$token]);
            return false;
        }
        
        // حذف الرمز بعد الاستخدام (استخدام واحد فقط)
        unset($_SESSION['csrf_tokens'][$token]);
        
        return true;
    }
    
    /**
     * الحصول على رمز CSRF من الطلب
     */
    public static function getTokenFromRequest() {
        // البحث في POST
        if (isset($_POST[self::$tokenName])) {
            return $_POST[self::$tokenName];
        }
        
        // البحث في Headers
        $headers = getallheaders();
        if (isset($headers['X-CSRF-Token'])) {
            return $headers['X-CSRF-Token'];
        }
        
        if (isset($headers['X-Requested-With']) && isset($_GET[self::$tokenName])) {
            return $_GET[self::$tokenName];
        }
        
        return null;
    }
    
    /**
     * التحقق من الطلب الحالي
     */
    public static function validateRequest() {
        // تجاهل طلبات GET
        if ($_SERVER['REQUEST_METHOD'] === 'GET') {
            return true;
        }
        
        $token = self::getTokenFromRequest();
        return self::validateToken($token);
    }
    
    /**
     * إنشاء حقل مخفي للنموذج
     */
    public static function getHiddenField() {
        $token = self::generateToken();
        return '<input type="hidden" name="' . self::$tokenName . '" value="' . htmlspecialchars($token) . '">';
    }
    
    /**
     * الحصول على رمز للاستخدام في JavaScript
     */
    public static function getTokenForJS() {
        return self::generateToken();
    }
    
    /**
     * تنظيف الرموز المنتهية الصلاحية
     */
    private static function cleanExpiredTokens() {
        if (!isset($_SESSION['csrf_tokens'])) {
            $_SESSION['csrf_tokens'] = [];
            return;
        }
        
        $currentTime = time();
        foreach ($_SESSION['csrf_tokens'] as $token => $timestamp) {
            if (($currentTime - $timestamp) > self::$tokenLifetime) {
                unset($_SESSION['csrf_tokens'][$token]);
            }
        }
    }
    
    /**
     * مسح جميع الرموز
     */
    public static function clearAllTokens() {
        $_SESSION['csrf_tokens'] = [];
    }
    
    /**
     * التحقق من الطلب وإظهار خطأ إذا فشل
     */
    public static function requireValidToken($errorMessage = 'طلب غير صحيح. يرجى المحاولة مرة أخرى.') {
        if (!self::validateRequest()) {
            if (isAjaxRequest()) {
                header('Content-Type: application/json');
                http_response_code(403);
                echo json_encode([
                    'success' => false,
                    'error' => $errorMessage,
                    'code' => 'CSRF_TOKEN_INVALID'
                ]);
            } else {
                http_response_code(403);
                die($errorMessage);
            }
            exit;
        }
    }
}

/**
 * دوال مساعدة للاستخدام السريع
 */

/**
 * إنشاء رمز CSRF
 */
function csrf_token() {
    return CSRFProtection::generateToken();
}

/**
 * إنشاء حقل مخفي للنموذج
 */
function csrf_field() {
    return CSRFProtection::getHiddenField();
}

/**
 * التحقق من رمز CSRF
 */
function csrf_validate($token = null) {
    if ($token === null) {
        return CSRFProtection::validateRequest();
    }
    return CSRFProtection::validateToken($token);
}

/**
 * طلب رمز CSRF صحيح أو إظهار خطأ
 */
function csrf_protect($errorMessage = null) {
    CSRFProtection::requireValidToken($errorMessage);
}

/**
 * Middleware للحماية من CSRF
 */
function csrfMiddleware() {
    // تجاهل طلبات GET
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        return;
    }
    
    // تجاهل API endpoints معينة (يمكن تخصيصها)
    $exemptPaths = [
        '/api/webhook',
        '/api/callback'
    ];
    
    $currentPath = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    if (in_array($currentPath, $exemptPaths)) {
        return;
    }
    
    CSRFProtection::requireValidToken();
}

// تطبيق الحماية تلقائياً إذا لم يتم تعطيلها
if (!defined('CSRF_PROTECTION_DISABLED')) {
    // csrfMiddleware(); // يمكن تفعيلها حسب الحاجة
}

?>