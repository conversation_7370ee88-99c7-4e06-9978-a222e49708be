<?php
/**
 * Verify table structure after fix
 */

require_once __DIR__ . '/../config/database.php';

try {
    echo "Verifying contact_messages table structure...\n\n";
    
    // Get current table structure
    $columns = $database->fetchAll("DESCRIBE contact_messages");
    
    echo "Current table structure:\n";
    echo str_repeat("-", 60) . "\n";
    printf("%-15s %-15s %-10s %-10s %-15s\n", "Field", "Type", "Null", "Key", "Default");
    echo str_repeat("-", 60) . "\n";
    
    foreach ($columns as $column) {
        printf("%-15s %-15s %-10s %-10s %-15s\n", 
            $column['Field'], 
            $column['Type'], 
            $column['Null'], 
            $column['Key'], 
            $column['Default'] ?? 'NULL'
        );
    }
    
    echo "\n" . str_repeat("-", 60) . "\n";
    
    // Check specifically for reply and replied_at columns
    $hasReply = false;
    $hasRepliedAt = false;
    
    foreach ($columns as $column) {
        if ($column['Field'] === 'reply') {
            $hasReply = true;
            echo "✓ 'reply' column found: {$column['Type']}\n";
        }
        if ($column['Field'] === 'replied_at') {
            $hasRepliedAt = true;
            echo "✓ 'replied_at' column found: {$column['Type']}\n";
        }
    }
    
    if (!$hasReply) {
        echo "✗ 'reply' column is missing!\n";
    }
    if (!$hasRepliedAt) {
        echo "✗ 'replied_at' column is missing!\n";
    }
    
    if ($hasReply && $hasRepliedAt) {
        echo "\n✅ All required columns are present!\n";
        
        // Test the problematic query
        echo "\nTesting the problematic query...\n";
        try {
            $result = $database->fetch("
                SELECT AVG(TIMESTAMPDIFF(HOUR, created_at, replied_at)) as avg_hours 
                FROM contact_messages 
                WHERE status = 'replied' AND replied_at IS NOT NULL
            ");
            echo "✅ Query executed successfully!\n";
            echo "Average response time: " . ($result['avg_hours'] ?? 0) . " hours\n";
        } catch (Exception $e) {
            echo "✗ Query failed: " . $e->getMessage() . "\n";
        }
    } else {
        echo "\n❌ Missing required columns. Need to fix the table structure.\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>