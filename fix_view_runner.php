<?php
/**
 * أداة إصلاح خطأ SQL 1064 في active_products VIEW
 * Tool to fix SQL Error 1064 in active_products VIEW
 */

require_once 'config/database.php';

// إعدادات الصفحة
header('Content-Type: text/html; charset=UTF-8');

// الحصول على اتصال قاعدة البيانات
$pdo = $db;
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح خطأ SQL 1064 - active_products VIEW</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .btn {
            background: linear-gradient(45deg, #00d2ff, #3a7bd5);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .result {
            background: #f8f9fa;
            border-left: 4px solid #28a745;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .error {
            background: #f8d7da;
            border-left: 4px solid #dc3545;
            color: #721c24;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .code {
            background: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 إصلاح خطأ SQL 1064</h1>
            <p>أداة إصلاح الـ VIEW المسماة active_products</p>
        </div>
        
        <div class="content">
            <?php
            if (isset($_POST['fix_view'])) {
                echo '<h2>🚀 بدء عملية الإصلاح...</h2>';
                
                try {
                    // قراءة ملف SQL
                    $sqlFile = __DIR__ . '/fix_view_quick.sql';
                    if (!file_exists($sqlFile)) {
                        throw new Exception('ملف الإصلاح غير موجود: ' . $sqlFile);
                    }
                    
                    $sqlContent = file_get_contents($sqlFile);
                    $sqlStatements = explode(';', $sqlContent);
                    
                    echo '<div class="result">';
                    echo '<h3>📋 تنفيذ أوامر SQL:</h3>';
                    
                    $successCount = 0;
                    $errorCount = 0;
                    
                    foreach ($sqlStatements as $statement) {
                        $statement = trim($statement);
                        if (empty($statement) || strpos($statement, '--') === 0) {
                            continue;
                        }
                        
                        try {
                            $result = $pdo->exec($statement);
                            echo '<p>✅ تم تنفيذ: ' . substr($statement, 0, 50) . '...</p>';
                            $successCount++;
                        } catch (PDOException $e) {
                            if (strpos($e->getMessage(), 'already exists') !== false || 
                                strpos($e->getMessage(), 'Unknown table') !== false) {
                                echo '<p>⚠️ تحذير: ' . $e->getMessage() . '</p>';
                            } else {
                                echo '<p>❌ خطأ: ' . $e->getMessage() . '</p>';
                                $errorCount++;
                            }
                        }
                    }
                    
                    echo '</div>';
                    
                    // اختبار الـ VIEW
                    echo '<h3>🧪 اختبار الـ VIEW الجديد:</h3>';
                    
                    try {
                        // الحصول على عدد المنتجات النشطة
                        $stmt = $pdo->query("SELECT COUNT(*) as total FROM active_products");
                        $count = $stmt->fetch(PDO::FETCH_ASSOC);
                        $stmt->closeCursor(); // إنهاء الاستعلام الأول
                        
                        echo '<div class="result">';
                        echo '<p>✅ الـ VIEW يعمل بنجاح!</p>';
                        echo '<p>📊 عدد المنتجات النشطة: ' . $count['total'] . '</p>';
                        
                        // عرض عينة من البيانات
                        $stmt2 = $pdo->query("SELECT id, name, category_name, price FROM active_products LIMIT 3");
                        $products = $stmt2->fetchAll(PDO::FETCH_ASSOC);
                        $stmt2->closeCursor(); // إنهاء الاستعلام الثاني
                        
                        if ($products) {
                            echo '<h4>📦 عينة من المنتجات:</h4>';
                            echo '<table border="1" style="width:100%; border-collapse:collapse; margin:10px 0;">';
                            echo '<tr><th>ID</th><th>اسم المنتج</th><th>الفئة</th><th>السعر</th></tr>';
                            foreach ($products as $product) {
                                echo '<tr>';
                                echo '<td>' . htmlspecialchars($product['id']) . '</td>';
                                echo '<td>' . htmlspecialchars($product['name']) . '</td>';
                                echo '<td>' . htmlspecialchars($product['category_name']) . '</td>';
                                echo '<td>' . htmlspecialchars($product['price']) . '</td>';
                                echo '</tr>';
                            }
                            echo '</table>';
                        }
                        echo '</div>';
                        
                    } catch (PDOException $e) {
                        echo '<div class="error">';
                        echo '<p>❌ فشل في اختبار الـ VIEW: ' . $e->getMessage() . '</p>';
                        echo '</div>';
                    }
                    
                    echo '<div class="result">';
                    echo '<h3>🎉 تم الإصلاح بنجاح!</h3>';
                    echo '<p>✅ تم حل خطأ SQL 1064 في active_products VIEW</p>';
                    echo '<p>✅ يمكن الآن استيراد قاعدة البيانات بدون أخطاء</p>';
                    echo '<p>✅ الـ VIEW يعمل بشكل صحيح مع ORDER BY محدث</p>';
                    echo '</div>';
                    
                } catch (Exception $e) {
                    echo '<div class="error">';
                    echo '<h3>❌ حدث خطأ أثناء الإصلاح</h3>';
                    echo '<p>' . $e->getMessage() . '</p>';
                    echo '</div>';
                }
            } else {
                ?>
                <h2>📋 معلومات المشكلة</h2>
                <div class="result">
                    <h3>🐛 الخطأ الحالي:</h3>
                    <div class="code">
                        #1064 - You have an error in your SQL syntax;<br>
                        check the manual that corresponds to your MySQL server version<br>
                        for the right syntax to use near 'AS `DESCdesc` ASC' at line 1
                    </div>
                    
                    <h3>🔍 السبب:</h3>
                    <p>خطأ في ORDER BY clause في الـ VIEW المسماة active_products</p>
                    <p>النص الخاطئ: <code>ORDER BY p.sort_order ASC, p.created_at AS DESCdesc ASC</code></p>
                    <p>النص الصحيح: <code>ORDER BY p.sort_order ASC, p.created_at DESC</code></p>
                    
                    <h3>🛠️ الحل:</h3>
                    <p>سيتم حذف الـ VIEW الخاطئ وإعادة إنشائه بشكل صحيح</p>
                </div>
                
                <form method="post">
                    <button type="submit" name="fix_view" class="btn">🔧 إصلاح المشكلة الآن</button>
                </form>
                
                <h3>📝 ملاحظات مهمة:</h3>
                <ul>
                    <li>هذا الإصلاح آمن ولن يؤثر على البيانات الموجودة</li>
                    <li>سيتم إعادة إنشاء الـ VIEW بشكل صحيح</li>
                    <li>بعد الإصلاح، ستتمكن من استيراد قاعدة البيانات بدون أخطاء</li>
                    <li>يمكنك تشغيل هذا الإصلاح عدة مرات بأمان</li>
                </ul>
                <?php
            }
            ?>
            
            <div style="margin-top: 30px; text-align: center;">
                <a href="test_database_check.php" class="btn">🔍 فحص قاعدة البيانات</a>
                <a href="index.php" class="btn">🏠 العودة للرئيسية</a>
            </div>
        </div>
    </div>
</body>
</html>