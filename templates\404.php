<?php
/**
 * 404 Error Page Template
 * صفحة خطأ 404 - الصفحة غير موجودة
 */

// Set page title and description
$GLOBALS['page_title'] = 'الصفحة غير موجودة - 404';
$GLOBALS['page_description'] = 'عذراً، الصفحة التي تبحث عنها غير موجودة';
?>

<main class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-green-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 text-center">
        <!-- Error Icon -->
        <div class="mx-auto">
            <svg class="mx-auto h-32 w-32 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.5-.9-6.134-2.373M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/>
            </svg>
        </div>

        <!-- Error Content -->
        <div>
            <h1 class="text-9xl font-bold text-gray-300">404</h1>
            <h2 class="mt-4 text-3xl font-bold text-gray-900">الصفحة غير موجودة</h2>
            <p class="mt-4 text-lg text-gray-600">
                عذراً، لا يمكننا العثور على الصفحة التي تبحث عنها.
            </p>
            <p class="mt-2 text-sm text-gray-500">
                قد تكون الصفحة قد تم نقلها أو حذفها أو أن الرابط غير صحيح.
            </p>
        </div>

        <!-- Action Buttons -->
        <div class="mt-8 space-y-4">
            <a href="<?php echo SITE_URL; ?>" 
               class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors">
                <i class="fas fa-home ml-2"></i>
                العودة للصفحة الرئيسية
            </a>
            
            <button onclick="history.back()" 
                    class="w-full flex justify-center py-3 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors">
                <i class="fas fa-arrow-right ml-2"></i>
                العودة للصفحة السابقة
            </button>
        </div>

        <!-- Search Box -->
        <div class="mt-8">
            <div class="relative">
                <input type="text" 
                       id="search-404" 
                       placeholder="ابحث عن منتج أو صفحة..."
                       class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                <button type="button" 
                        onclick="performSearch()"
                        class="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-primary">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>

        <!-- Quick Links -->
        <div class="mt-8">
            <h3 class="text-lg font-medium text-gray-900 mb-4">روابط مفيدة</h3>
            <div class="grid grid-cols-2 gap-4">
                <a href="<?php echo SITE_URL; ?>?page=products" 
                   class="flex items-center justify-center py-2 px-4 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                    <i class="fas fa-box ml-2"></i>
                    المنتجات
                </a>
                <a href="<?php echo SITE_URL; ?>?page=about" 
                   class="flex items-center justify-center py-2 px-4 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                    <i class="fas fa-info-circle ml-2"></i>
                    من نحن
                </a>
                <a href="<?php echo SITE_URL; ?>?page=contact" 
                   class="flex items-center justify-center py-2 px-4 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                    <i class="fas fa-phone ml-2"></i>
                    اتصل بنا
                </a>
                <a href="<?php echo SITE_URL; ?>?page=faq" 
                   class="flex items-center justify-center py-2 px-4 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                    <i class="fas fa-question-circle ml-2"></i>
                    الأسئلة الشائعة
                </a>
            </div>
        </div>

        <!-- Contact Info -->
        <div class="mt-8 p-4 bg-white rounded-lg shadow-sm border">
            <h4 class="text-md font-medium text-gray-900 mb-2">هل تحتاج مساعدة؟</h4>
            <p class="text-sm text-gray-600 mb-3">
                تواصل معنا وسنكون سعداء لمساعدتك
            </p>
            <div class="flex justify-center space-x-4 space-x-reverse">
                <?php 
                $phone = getSetting('contact_phone');
                $whatsapp = getSetting('whatsapp_number');
                $email = getSetting('contact_email');
                ?>
                
                <?php if ($phone): ?>
                <a href="tel:<?php echo $phone; ?>" 
                   class="text-primary hover:text-primary-dark">
                    <i class="fas fa-phone"></i>
                </a>
                <?php endif; ?>
                
                <?php if ($whatsapp): ?>
                <a href="https://wa.me/<?php echo preg_replace('/[^0-9]/', '', $whatsapp); ?>" 
                   target="_blank"
                   class="text-green-600 hover:text-green-700">
                    <i class="fab fa-whatsapp"></i>
                </a>
                <?php endif; ?>
                
                <?php if ($email): ?>
                <a href="mailto:<?php echo $email; ?>" 
                   class="text-blue-600 hover:text-blue-700">
                    <i class="fas fa-envelope"></i>
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</main>

<script>
function performSearch() {
    const searchInput = document.getElementById('search-404');
    const query = searchInput.value.trim();
    
    if (query) {
        window.location.href = `<?php echo SITE_URL; ?>?page=products&search=${encodeURIComponent(query)}`;
    }
}

// Handle Enter key in search
document.getElementById('search-404').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        performSearch();
    }
});

// Add some animation
document.addEventListener('DOMContentLoaded', function() {
    const elements = document.querySelectorAll('.space-y-8 > *');
    elements.forEach((el, index) => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            el.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            el.style.opacity = '1';
            el.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>