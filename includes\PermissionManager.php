<?php
/**
 * دوال مساعدة للصلاحيات والأدوار
 * Helper functions for permissions and roles
 * 
 * ملاحظة: تم نقل كلاس PermissionManager إلى ملف permissions.php لتجنب التعارض
 * Note: PermissionManager class moved to permissions.php to avoid conflicts
 */

// تضمين ملف الصلاحيات الرئيسي
require_once __DIR__ . '/permissions.php';

// تم حذف دالة checkPermission لتجنب التضارب مع middleware.php
// checkPermission function removed to avoid conflict with middleware.php

// تم حذف دالة checkRole لتجنب التضارب مع middleware.php
// checkRole function removed to avoid conflict with middleware.php

/**
 * دالة للتحقق من دور معين
 * Function to require specific role
 */
function requireRoleAccess($role, $redirectUrl = 'index.php') {
    if (!checkRole($role)) {
        $_SESSION['error'] = 'ليس لديك الدور المطلوب للوصول إلى هذه الصفحة';
        header("Location: {$redirectUrl}");
        exit;
    }
}

// تم حذف دالة requireAdminAccess لتجنب التضارب مع middleware.php
// requireAdminAccess function removed to avoid conflict with middleware.php

?>