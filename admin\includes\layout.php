<?php
/**
 * تخطيط موحد لصفحات الأدمن
 * يحتوي على الهيكل الأساسي للصفحة مع الهيدر والسايدبار
 */

// تضمين الملفات المطلوبة
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !hasPermission('admin')) {
    redirect(SITE_URL . '/auth/login?redirect=' . urlencode(SITE_URL . '/admin'));
}

// متغيرات افتراضية إذا لم يتم تعريفها
if (!isset($pageTitle)) {
    $pageTitle = 'لوحة التحكم';
}
if (!isset($currentPage)) {
    $currentPage = 'dashboard';
}
if (!isset($pageDescription)) {
    $pageDescription = '';
}
if (!isset($additionalCSS)) {
    $additionalCSS = [];
}
if (!isset($additionalJS)) {
    $additionalJS = [];
}
if (!isset($breadcrumbs)) {
    $breadcrumbs = [];
}

/**
 * دالة لبدء التخطيط
 */
function startLayout() {
    global $pageTitle, $additionalCSS, $additionalJS, $database;
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title><?php echo htmlspecialchars($pageTitle); ?> - لوحة التحكم</title>
        
        <!-- CSS الأساسي -->
        <script src="https://cdn.tailwindcss.com"></script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        <link rel="stylesheet" href="../assets/css/admin.css">
        <link rel="stylesheet" href="../assets/css/icon-shapes.css">
        
        <!-- CSS إضافي -->
        <?php foreach ($additionalCSS as $css): ?>
            <link rel="stylesheet" href="<?php echo htmlspecialchars($css); ?>">
        <?php endforeach; ?>
        
        <!-- Alpine.js -->
        <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
        
        <!-- JavaScript إضافي -->
        <?php foreach ($additionalJS as $js): ?>
            <script src="<?php echo htmlspecialchars($js); ?>"></script>
        <?php endforeach; ?>
    </head>
    <body class="bg-gray-100" style="overflow: hidden;">
        <div class="flex h-screen">
            <?php include 'sidebar.php'; ?>
            
            <div class="flex-1 flex flex-col overflow-hidden" style="margin-right: 256px;">
                <?php include 'header.php'; ?>
                
                <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6">
                    <div class="max-w-7xl mx-auto">
    <?php
}

/**
 * دالة لعرض رأس الصفحة
 */
function showPageHeader() {
    global $pageTitle, $pageDescription, $breadcrumbs;
    ?>
    <div class="mb-6">
        <?php if (!empty($breadcrumbs)): ?>
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="/admin/" class="text-gray-500 hover:text-gray-700">لوحة التحكم</a>
                    </li>
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <li class="inline-flex items-center">
                            <svg class="w-6 h-6 text-gray-400 mx-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <?php if (isset($breadcrumb['url']) && $index < count($breadcrumbs) - 1): ?>
                                <a href="<?php echo htmlspecialchars($breadcrumb['url']); ?>" class="text-gray-500 hover:text-gray-700">
                                    <?php echo htmlspecialchars($breadcrumb['title']); ?>
                                </a>
                            <?php elseif ($index < count($breadcrumbs) - 1): ?>
                                <span class="text-gray-500">
                                    <?php echo htmlspecialchars($breadcrumb['title']); ?>
                                </span>
                            <?php else: ?>
                                <span class="text-green-600 font-medium">
                                    <?php echo htmlspecialchars($breadcrumb['title']); ?>
                                </span>
                            <?php endif; ?>
                        </li>
                    <?php endforeach; ?>
                </ol>
            </nav>
        <?php endif; ?>
        
        <h1 class="text-3xl font-bold text-gray-900"><?php echo htmlspecialchars($pageTitle); ?></h1>
        <?php if ($pageDescription): ?>
            <p class="text-gray-600 mt-2"><?php echo htmlspecialchars($pageDescription); ?></p>
        <?php endif; ?>
    </div>
    <?php
}

/**
 * دالة لعرض الرسائل (نجاح/خطأ)
 */
function showMessages($msg = null, $err = null, $succ = null) {
    global $message, $error, $success;
    
    // استخدام المعاملات الممررة أو المتغيرات العامة
    $message = $msg ?? $message;
    $error = $err ?? $error;
    $success = $succ ?? $success;
    
    // رسالة النجاح
    if (isset($message) && $message): ?>
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
            <div class="flex items-center">
                <i class="fas fa-check-circle ml-2"></i>
                <?php echo htmlspecialchars($message); ?>
            </div>
        </div>
    <?php endif;
    
    if (isset($success) && $success): ?>
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
            <div class="flex items-center">
                <i class="fas fa-check-circle ml-2"></i>
                <?php echo htmlspecialchars($success); ?>
            </div>
        </div>
    <?php endif;
    
    // رسالة الخطأ
    if (isset($error) && $error): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <div class="flex items-center">
                <i class="fas fa-exclamation-circle ml-2"></i>
                <?php echo htmlspecialchars($error); ?>
            </div>
        </div>
    <?php endif;
}

/**
 * دالة لإنهاء التخطيط
 */
function endLayout() {
    ?>
                    </div>
                </main>
            </div>
        </div>
        
        <!-- JavaScript إضافي في نهاية الصفحة -->
        <script>
            // دوال JavaScript مشتركة
            function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
                return confirm(message);
            }
            
            function previewImage(input, previewId) {
                if (input.files && input.files[0]) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const preview = document.getElementById(previewId);
                        const img = preview.querySelector('img');
                        if (img) {
                            img.src = e.target.result;
                            preview.classList.remove('hidden');
                        }
                    };
                    reader.readAsDataURL(input.files[0]);
                }
            }
            
            // إخفاء الرسائل تلقائياً بعد 5 ثوان
            setTimeout(function() {
                const alerts = document.querySelectorAll('.bg-green-100, .bg-red-100');
                alerts.forEach(function(alert) {
                    alert.style.transition = 'opacity 0.5s';
                    alert.style.opacity = '0';
                    setTimeout(function() {
                        alert.remove();
                    }, 500);
                });
            }, 5000);
        </script>
    </body>
    </html>
    <?php
}

/**
 * دالة مساعدة لإنشاء كارت
 */
function createCard($title, $content, $classes = '') {
    ?>
    <div class="bg-white rounded-lg shadow-md <?php echo $classes; ?>">
        <?php if ($title): ?>
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900"><?php echo htmlspecialchars($title); ?></h3>
            </div>
        <?php endif; ?>
        <div class="p-6">
            <?php echo $content; ?>
        </div>
    </div>
    <?php
}

/**
 * دالة مساعدة لإنشاء تبويبات
 */
function createTabs($tabs, $defaultTab = null) {
    $defaultTab = $defaultTab ?: array_key_first($tabs);
    ?>
    <div class="bg-white rounded-lg shadow-md" x-data="{ activeTab: '<?php echo $defaultTab; ?>' }">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-6">
                <?php foreach ($tabs as $tabId => $tab): ?>
                    <button @click="activeTab = '<?php echo $tabId; ?>'" 
                            :class="activeTab === '<?php echo $tabId; ?>' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                        <?php if (isset($tab['icon'])): ?>
                            <i class="<?php echo $tab['icon']; ?> ml-2"></i>
                        <?php endif; ?>
                        <?php echo htmlspecialchars($tab['title']); ?>
                    </button>
                <?php endforeach; ?>
            </nav>
        </div>
        
        <?php foreach ($tabs as $tabId => $tab): ?>
            <div x-show="activeTab === '<?php echo $tabId; ?>'" class="p-6">
                <?php echo $tab['content']; ?>
            </div>
        <?php endforeach; ?>
    </div>
    <?php
}
?>