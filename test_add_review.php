<?php
require_once 'config/config.php';
require_once 'includes/functions.php';

$product_id = $_GET['product_id'] ?? 11;
$user_id = $_GET['user_id'] ?? null;

if ($_POST) {
    $product_id = $_POST['product_id'];
    $user_id = $_POST['user_id'];
    $rating = $_POST['rating'];
    $title = $_POST['title'];
    $comment = $_POST['comment'];
    
    global $database;
    
    // التحقق من وجود تقييم سابق
    $existing = $database->fetch(
        "SELECT id FROM reviews WHERE product_id = :product_id AND user_id = :user_id",
        ['product_id' => $product_id, 'user_id' => $user_id]
    );
    
    if ($existing) {
        echo "<div style='color: red; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0;'>المستخدم قيّم هذا المنتج من قبل!</div>";
    } else {
        $review_id = $database->insert('reviews', [
            'product_id' => $product_id,
            'user_id' => $user_id,
            'rating' => $rating,
            'title' => $title,
            'comment' => $comment,
            'name' => 'مستخدم تجريبي',
            'email' => '<EMAIL>',
            'is_approved' => 1,
            'created_at' => date('Y-m-d H:i:s')
        ]);
        
        if ($review_id) {
            echo "<div style='color: green; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0;'>تم إضافة التقييم بنجاح!</div>";
            
            // تحديث متوسط التقييم للمنتج
            updateProductRating($product_id);
        } else {
            echo "<div style='color: red; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0;'>فشل في إضافة التقييم</div>";
        }
    }
}

echo "<h1>إضافة تقييم تجريبي</h1>";

// عرض المستخدمين
global $database;
$users = $database->fetchAll("SELECT id, name, email FROM users WHERE is_active = 1");

if (!$users) {
    echo "<p>لا توجد مستخدمين نشطين. <a href='test_users.php'>إنشاء مستخدم تجريبي</a></p>";
    exit;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة تقييم تجريبي</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <form method="POST" class="space-y-4">
            <div>
                <label for="product_id" class="block text-sm font-medium text-gray-700 mb-2">معرف المنتج</label>
                <input type="number" id="product_id" name="product_id" value="<?php echo $product_id; ?>" required 
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
            </div>
            
            <div>
                <label for="user_id" class="block text-sm font-medium text-gray-700 mb-2">المستخدم</label>
                <select id="user_id" name="user_id" required 
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    <option value="">اختر مستخدم</option>
                    <?php foreach ($users as $user): ?>
                        <option value="<?php echo $user['id']; ?>" <?php echo ($user_id == $user['id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($user['name'] . ' (' . $user['email'] . ')'); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div>
                <label for="rating" class="block text-sm font-medium text-gray-700 mb-2">التقييم</label>
                <select id="rating" name="rating" required 
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    <option value="">اختر تقييم</option>
                    <option value="1">1 نجمة</option>
                    <option value="2">2 نجمة</option>
                    <option value="3">3 نجوم</option>
                    <option value="4">4 نجوم</option>
                    <option value="5" selected>5 نجوم</option>
                </select>
            </div>
            
            <div>
                <label for="title" class="block text-sm font-medium text-gray-700 mb-2">عنوان التقييم</label>
                <input type="text" id="title" name="title" value="تقييم تجريبي ممتاز" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
            </div>
            
            <div>
                <label for="comment" class="block text-sm font-medium text-gray-700 mb-2">التعليق</label>
                <textarea id="comment" name="comment" rows="4" required 
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">منتج ممتاز، أنصح به بشدة. جودة عالية وخدمة ممتازة.</textarea>
            </div>
            
            <button type="submit" 
                    class="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                إضافة التقييم
            </button>
        </form>
        
        <div class="mt-6 text-center">
            <a href="test_users.php" class="text-blue-500 hover:text-blue-700">إدارة المستخدمين</a> |
            <a href="test_login.php" class="text-blue-500 hover:text-blue-700">تسجيل الدخول</a> |
            <a href="product_detail.php?id=<?php echo $product_id; ?>&tab=reviews" class="text-blue-500 hover:text-blue-700">صفحة المنتج</a>
        </div>
    </div>
</body>
</html>