<?php
/**
 * صفحة إدارة الصلاحيات والأدوار
 * Permissions and Roles Management Page
 */

// تعريف متغيرات الصفحة
$pageTitle = 'إدارة الصلاحيات والأدوار';
$currentPage = 'permissions';
$pageDescription = 'إدارة صلاحيات النظام والأدوار';
$additionalCSS = [];
$additionalJS = [];

// تعريف مسار التنقل
$breadcrumbs = [
    ['title' => 'المستخدمين', 'url' => '/admin/users.php'],
    ['title' => 'الأدوار والصلاحيات']
];

// تضمين نظام التخطيط
require_once 'includes/layout.php';

// التحقق من الصلاحيات
requirePermission('manage-permissions');

// تضمين مدير الصلاحيات
require_once '../includes/PermissionManager.php';
$permissionManager = new PermissionManager($db);

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'create_permission':
            $name = trim($_POST['name'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $category = trim($_POST['category'] ?? 'general');
            
            if ($name && $description) {
                try {
                    $stmt = $db->prepare("INSERT INTO permissions (name, description, category, is_active) VALUES (?, ?, ?, ?)");
                    $stmt->execute([$name, $description, $category, 1]);
                    $message = 'تم إنشاء الصلاحية بنجاح';
                } catch (Exception $e) {
                    $error = 'خطأ في إنشاء الصلاحية: ' . $e->getMessage();
                }
            } else {
                $error = 'يرجى ملء جميع الحقول المطلوبة';
            }
            break;
            
        case 'create_role':
            $name = trim($_POST['name'] ?? '');
            $displayName = trim($_POST['display_name'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $permissions = $_POST['permissions'] ?? [];
            
            if ($name && $displayName) {
                try {
                    $db->beginTransaction();
                    
                    // إنشاء الدور
                    $stmt = $db->prepare("INSERT INTO roles (name, display_name, description, is_active) VALUES (?, ?, ?, ?)");
                    $stmt->execute([$name, $displayName, $description, 1]);
                    $roleId = $db->lastInsertId();
                    
                    // إضافة الصلاحيات للدور
                    if (!empty($permissions)) {
                        $stmt = $db->prepare("INSERT INTO role_permissions (role_id, permission_id) VALUES (?, ?)");
                        foreach ($permissions as $permissionId) {
                            $stmt->execute([$roleId, $permissionId]);
                        }
                    }
                    
                    $db->commit();
                    $message = 'تم إنشاء الدور بنجاح';
                } catch (Exception $e) {
                    $db->rollBack();
                    $error = 'خطأ في إنشاء الدور: ' . $e->getMessage();
                }
            } else {
                $error = 'يرجى ملء جميع الحقول المطلوبة';
            }
            break;
            
        case 'update_role_permissions':
            $roleId = $_POST['role_id'] ?? null;
            $permissions = $_POST['permissions'] ?? [];
            
            if ($roleId) {
                $result = $permissionManager->updateRolePermissions($roleId, $permissions, getCurrentUserId());
                if ($result['success']) {
                    $message = 'تم تحديث صلاحيات الدور بنجاح';
                } else {
                    $error = $result['error'];
                }
            }
            break;
            
        case 'assign_user_role':
            $userId = $_POST['user_id'] ?? null;
            $roleName = $_POST['role_name'] ?? '';
            
            if ($userId && $roleName) {
                $result = $permissionManager->assignRole($userId, $roleName, getCurrentUserId());
                if ($result['success']) {
                    $message = 'تم تعيين الدور للمستخدم بنجاح';
                } else {
                    $error = $result['error'];
                }
            }
            break;
            
        case 'remove_user_role':
            $userId = $_POST['user_id'] ?? null;
            $roleName = $_POST['role_name'] ?? '';
            
            if ($userId && $roleName) {
                $result = $permissionManager->removeRole($userId, $roleName, getCurrentUserId());
                if ($result['success']) {
                    $message = 'تم إزالة الدور من المستخدم بنجاح';
                } else {
                    $error = $result['error'];
                }
            }
            break;
            
        case 'grant_user_permission':
            $userId = $_POST['user_id'] ?? null;
            $permissionName = $_POST['permission_name'] ?? '';
            
            if ($userId && $permissionName) {
                $result = $permissionManager->grantPermission($userId, $permissionName, getCurrentUserId());
                if ($result['success']) {
                    $message = 'تم منح الصلاحية للمستخدم بنجاح';
                } else {
                    $error = $result['error'];
                }
            }
            break;
            
        case 'revoke_user_permission':
            $userId = $_POST['user_id'] ?? null;
            $permissionName = $_POST['permission_name'] ?? '';
            
            if ($userId && $permissionName) {
                $result = $permissionManager->revokePermission($userId, $permissionName, getCurrentUserId());
                if ($result['success']) {
                    $message = 'تم إلغاء الصلاحية من المستخدم بنجاح';
                } else {
                    $error = $result['error'];
                }
            }
            break;
    }
}

// جلب البيانات
try {
    // جلب الصلاحيات
    $stmt = $db->prepare("SELECT * FROM permissions ORDER BY category, name");
    $stmt->execute();
    $permissions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // جلب الأدوار
    $stmt = $db->prepare("SELECT * FROM roles ORDER BY name");
    $stmt->execute();
    $roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // جلب المستخدمين
    $stmt = $db->prepare("SELECT id, name, email FROM users ORDER BY name");
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $error = 'خطأ في جلب البيانات: ' . $e->getMessage();
    $permissions = [];
    $roles = [];
    $users = [];
}

// تجميع الصلاحيات حسب الفئة
$permissionsByCategory = [];
foreach ($permissions as $permission) {
    $category = $permission['category'] ?? 'general';
    $permissionsByCategory[$category][] = $permission;
}

// بدء التخطيط
startLayout();
showPageHeader();
showMessages();
?>

<div class="space-y-6">
    <!-- إحصائيات سريعة -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">إجمالي الصلاحيات</p>
                    <p class="text-2xl font-semibold text-gray-900"><?= count($permissions) ?></p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-pink-100 rounded-lg">
                    <svg class="w-6 h-6 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">إجمالي الأدوار</p>
                    <p class="text-2xl font-semibold text-gray-900"><?= count($roles) ?></p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 rounded-lg">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">إجمالي المستخدمين</p>
                    <p class="text-2xl font-semibold text-gray-900"><?= count($users) ?></p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 rounded-lg">
                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <p class="text-sm font-medium text-gray-600">فئات الصلاحيات</p>
                    <p class="text-2xl font-semibold text-gray-900"><?= count($permissionsByCategory) ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- تبويبات الإدارة -->
    <div class="bg-white rounded-lg shadow">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                <button onclick="showTab('permissions')" id="tab-permissions" class="tab-button active border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    إدارة الصلاحيات
                </button>
                <button onclick="showTab('roles')" id="tab-roles" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    إدارة الأدوار
                </button>
                <button onclick="showTab('user-permissions')" id="tab-user-permissions" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    صلاحيات المستخدمين
                </button>
            </nav>
        </div>

        <!-- تبويب إدارة الصلاحيات -->
        <div id="permissions-tab" class="tab-content p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- إنشاء صلاحية جديدة -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">إنشاء صلاحية جديدة</h3>
                    <form method="POST" class="space-y-4">
                        <input type="hidden" name="action" value="create_permission">
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">اسم الصلاحية</label>
                            <input type="text" name="name" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="مثال: view-reports">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                            <input type="text" name="description" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="مثال: عرض التقارير">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الفئة</label>
                            <select name="category" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="general">عام</option>
                                <option value="users">المستخدمين</option>
                                <option value="roles">الأدوار</option>
                                <option value="content">المحتوى</option>
                                <option value="products">المنتجات</option>
                                <option value="reviews">المراجعات</option>
                                <option value="reports">التقارير</option>
                                <option value="settings">الإعدادات</option>
                                <option value="admin">الإدارة</option>
                            </select>
                        </div>
                        
                        <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            إنشاء الصلاحية
                        </button>
                    </form>
                </div>
                
                <!-- قائمة الصلاحيات الحالية -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4">الصلاحيات الحالية</h3>
                    <div class="space-y-4 max-h-96 overflow-y-auto">
                        <?php foreach ($permissionsByCategory as $category => $categoryPermissions): ?>
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h4 class="font-medium text-gray-900 mb-2"><?= ucfirst($category) ?></h4>
                                <div class="space-y-2">
                                    <?php foreach ($categoryPermissions as $permission): ?>
                                        <div class="flex items-center justify-between p-2 bg-white rounded border">
                                            <div>
                                                <span class="font-medium text-sm"><?= htmlspecialchars($permission['name']) ?></span>
                                                <p class="text-xs text-gray-600"><?= htmlspecialchars($permission['description']) ?></p>
                                            </div>
                                            <span class="px-2 py-1 text-xs bg-pink-100 text-pink-800 rounded">
                                                <?= $permission['is_active'] ? 'نشط' : 'غير نشط' ?>
                                            </span>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- تبويب إدارة الأدوار -->
        <div id="roles-tab" class="tab-content p-6 hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- إنشاء دور جديد -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">إنشاء دور جديد</h3>
                    <form method="POST" class="space-y-4">
                        <input type="hidden" name="action" value="create_role">
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">اسم الدور (بالإنجليزية)</label>
                            <input type="text" name="name" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="مثال: content-manager">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">اسم الدور للعرض</label>
                            <input type="text" name="display_name" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="مثال: مدير المحتوى">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                            <textarea name="description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="وصف الدور وصلاحياته"></textarea>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الصلاحيات</label>
                            <div class="max-h-48 overflow-y-auto border border-gray-300 rounded-md p-3 space-y-2">
                                <?php foreach ($permissions as $permission): ?>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="permissions[]" value="<?= $permission['id'] ?>" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                        <span class="mr-2 text-sm"><?= htmlspecialchars($permission['description']) ?></span>
                                    </label>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        
                        <button type="submit" class="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                            إنشاء الدور
                        </button>
                    </form>
                </div>
                
                <!-- قائمة الأدوار الحالية -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4">الأدوار الحالية</h3>
                    <div class="space-y-4 max-h-96 overflow-y-auto">
                        <?php foreach ($roles as $role): ?>
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-2">
                                    <h4 class="font-medium text-gray-900"><?= htmlspecialchars($role['display_name']) ?></h4>
                                    <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                                        <?= htmlspecialchars($role['name']) ?>
                                    </span>
                                </div>
                                <p class="text-sm text-gray-600 mb-3"><?= htmlspecialchars($role['description']) ?></p>
                                
                                <!-- صلاحيات الدور -->
                                <?php $rolePermissions = $permissionManager->getRolePermissions($role['id']); ?>
                                <div class="mb-3">
                                    <p class="text-xs font-medium text-gray-700 mb-1">الصلاحيات (<?= count($rolePermissions) ?>):</p>
                                    <div class="flex flex-wrap gap-1">
                                        <?php foreach (array_slice($rolePermissions, 0, 5) as $perm): ?>
                                            <span class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">
                                                <?= htmlspecialchars($perm['name']) ?>
                                            </span>
                                        <?php endforeach; ?>
                                        <?php if (count($rolePermissions) > 5): ?>
                                            <span class="px-2 py-1 text-xs bg-gray-200 text-gray-600 rounded">
                                                +<?= count($rolePermissions) - 5 ?> أخرى
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <button onclick="editRolePermissions(<?= $role['id'] ?>, '<?= htmlspecialchars($role['display_name']) ?>')" class="text-sm bg-yellow-600 text-white px-3 py-1 rounded hover:bg-yellow-700">
                                    تعديل الصلاحيات
                                </button>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- تبويب صلاحيات المستخدمين -->
        <div id="user-permissions-tab" class="tab-content p-6 hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- تعيين أدوار للمستخدمين -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">تعيين دور لمستخدم</h3>
                    <form method="POST" class="space-y-4">
                        <input type="hidden" name="action" value="assign_user_role">
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">المستخدم</label>
                            <select name="user_id" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">اختر المستخدم</option>
                                <?php foreach ($users as $user): ?>
                                    <option value="<?= $user['id'] ?>"><?= htmlspecialchars($user['name']) ?> (<?= htmlspecialchars($user['email']) ?>)</option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الدور</label>
                            <select name="role_name" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">اختر الدور</option>
                                <?php foreach ($roles as $role): ?>
                                    <option value="<?= htmlspecialchars($role['name']) ?>"><?= htmlspecialchars($role['display_name']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            تعيين الدور
                        </button>
                    </form>
                </div>
                
                <!-- قائمة المستخدمين وأدوارهم -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4">المستخدمين وأدوارهم</h3>
                    <div class="space-y-4 max-h-96 overflow-y-auto">
                        <?php foreach ($users as $user): ?>
                            <?php $userRoles = $permissionManager->getUserRoles($user['id']); ?>
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-2">
                                    <h4 class="font-medium text-gray-900"><?= htmlspecialchars($user['name']) ?></h4>
                                    <span class="text-sm text-gray-600"><?= htmlspecialchars($user['email']) ?></span>
                                </div>
                                
                                <div class="mb-3">
                                    <p class="text-xs font-medium text-gray-700 mb-1">الأدوار الحالية:</p>
                                    <div class="flex flex-wrap gap-1">
                                        <?php if (empty($userRoles)): ?>
                                            <span class="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">لا توجد أدوار</span>
                                        <?php else: ?>
                                            <?php foreach ($userRoles as $role): ?>
                                                <span class="px-2 py-1 text-xs bg-pink-100 text-pink-800 rounded">
                                                    <?= htmlspecialchars($role['display_name']) ?>
                                                </span>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <button onclick="manageUserPermissions(<?= $user['id'] ?>, '<?= htmlspecialchars($user['name']) ?>')" class="text-sm bg-purple-600 text-white px-3 py-1 rounded hover:bg-purple-700">
                                    إدارة الصلاحيات
                                </button>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة تعديل صلاحيات الدور -->
<div id="rolePermissionsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-96 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900" id="roleModalTitle">تعديل صلاحيات الدور</h3>
            </div>
            <form method="POST" id="rolePermissionsForm">
                <input type="hidden" name="action" value="update_role_permissions">
                <input type="hidden" name="role_id" id="roleIdInput">
                
                <div class="px-6 py-4 max-h-64 overflow-y-auto">
                    <div class="space-y-2" id="rolePermissionsList">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
                
                <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                    <button type="button" onclick="closeRolePermissionsModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200">
                        إلغاء
                    </button>
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700">
                        حفظ التغييرات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// إدارة التبويبات
function showTab(tabName) {
    // إخفاء جميع التبويبات
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.add('hidden');
    });
    
    // إزالة الفئة النشطة من جميع الأزرار
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('active', 'border-blue-500', 'text-blue-600');
        button.classList.add('border-transparent', 'text-gray-500');
    });
    
    // إظهار التبويب المحدد
    document.getElementById(tabName + '-tab').classList.remove('hidden');
    
    // تفعيل الزر المحدد
    const activeButton = document.getElementById('tab-' + tabName);
    activeButton.classList.add('active', 'border-blue-500', 'text-blue-600');
    activeButton.classList.remove('border-transparent', 'text-gray-500');
}

// تعديل صلاحيات الدور
function editRolePermissions(roleId, roleName) {
    document.getElementById('roleIdInput').value = roleId;
    document.getElementById('roleModalTitle').textContent = 'تعديل صلاحيات: ' + roleName;
    
    // جلب صلاحيات الدور الحالية
    fetch('?ajax=get_role_permissions&role_id=' + roleId)
        .then(response => response.json())
        .then(data => {
            const permissionsList = document.getElementById('rolePermissionsList');
            permissionsList.innerHTML = '';
            
            <?php foreach ($permissions as $permission): ?>
            const permDiv = document.createElement('div');
            permDiv.innerHTML = `
                <label class="flex items-center">
                    <input type="checkbox" name="permissions[]" value="<?= $permission['id'] ?>" 
                           ${data.includes(<?= $permission['id'] ?>) ? 'checked' : ''} 
                           class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                    <span class="mr-2 text-sm"><?= htmlspecialchars($permission['description']) ?></span>
                </label>
            `;
            permissionsList.appendChild(permDiv);
            <?php endforeach; ?>
        });
    
    document.getElementById('rolePermissionsModal').classList.remove('hidden');
}

// إغلاق نافذة تعديل الصلاحيات
function closeRolePermissionsModal() {
    document.getElementById('rolePermissionsModal').classList.add('hidden');
}

// إدارة صلاحيات المستخدم
function manageUserPermissions(userId, userName) {
    // يمكن إضافة نافذة منفصلة لإدارة صلاحيات المستخدم
    alert('سيتم إضافة هذه الميزة قريباً: إدارة صلاحيات ' + userName);
}
</script>

<?php
endLayout();
?>