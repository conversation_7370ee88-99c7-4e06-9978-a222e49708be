<?php
require_once 'config/config.php';
require_once 'includes/functions.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$product_id = (int)($_POST['product_id'] ?? 1);

try {
    // اختبار دالة getProduct
    $product = getProduct($product_id);
    
    if ($product) {
        echo json_encode([
            'success' => true,
            'message' => 'تم العثور على المنتج بنجاح',
            'product' => [
                'id' => $product['id'],
                'name' => $product['name'],
                'is_active' => $product['is_active'],
                'category_name' => $product['category_name'] ?? 'غير محدد'
            ]
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'المنتج غير موجود أو غير نشط'
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في النظام: ' . $e->getMessage()
    ]);
}
?>