<?php
session_start();
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// محاكاة تسجيل دخول المستخدم للاختبار
if (isset($_GET['login']) && $_GET['login'] == '1') {
    $_SESSION['user_id'] = 1;
    $_SESSION['user_name'] = 'مستخدم تجريبي';
    $_SESSION['user_email'] = '<EMAIL>';
    header('Location: test_review_api_fixed.php');
    exit;
}

if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: test_review_api_fixed.php');
    exit;
}

$isLoggedIn = isLoggedIn();
$currentUser = $isLoggedIn ? getCurrentUser() : null;
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API التقييمات المحدث</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .form-group { margin: 10px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-warning:hover { background: #e0a800; }
        .result { margin-top: 10px; padding: 10px; border-radius: 4px; }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        th, td { padding: 8px; text-align: right; border: 1px solid #ddd; }
        th { background: #f8f9fa; }
        .status-indicator { display: inline-block; width: 10px; height: 10px; border-radius: 50%; margin-left: 5px; }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار API التقييمات المحدث</h1>
        
        <!-- حالة تسجيل الدخول -->
        <div class="section <?php echo $isLoggedIn ? 'success' : 'warning'; ?>">
            <h3>حالة تسجيل الدخول</h3>
            <?php if ($isLoggedIn): ?>
                <p><span class="status-indicator status-success"></span>مسجل دخول: <?php echo htmlspecialchars($currentUser['name']); ?> (<?php echo htmlspecialchars($currentUser['email']); ?>)</p>
                <a href="?logout=1" class="btn-danger" style="text-decoration: none; display: inline-block;">تسجيل خروج</a>
            <?php else: ?>
                <p><span class="status-indicator status-error"></span>غير مسجل دخول</p>
                <a href="?login=1" class="btn-success" style="text-decoration: none; display: inline-block;">محاكاة تسجيل دخول</a>
            <?php endif; ?>
        </div>

        <!-- اختبار قاعدة البيانات -->
        <div class="section info">
            <h3>اختبار قاعدة البيانات</h3>
            <?php
            try {
                global $database;
                $pdo = $database->getConnection();
                
                // عدد التقييمات
                $reviewCount = $database->fetch("SELECT COUNT(*) as count FROM reviews");
                echo "<p>✓ الاتصال بقاعدة البيانات ناجح - عدد التقييمات: " . $reviewCount['count'] . "</p>";
                
                // المنتجات المتاحة مع تفاصيل الحالة
                echo "<h4>المنتجات المتاحة:</h4>";
                $products = $database->fetchAll("SELECT id, name, is_active, is_visible FROM products ORDER BY id");
                if ($products) {
                    echo "<table>";
                    echo "<tr><th>ID</th><th>الاسم</th><th>نشط</th><th>مرئي</th><th>متاح للتقييم</th></tr>";
                    foreach ($products as $product) {
                        $isActive = $product['is_active'] == 1;
                        $isVisible = is_null($product['is_visible']) || $product['is_visible'] == 1;
                        $canReview = $isActive && $isVisible;
                        
                        echo "<tr>";
                        echo "<td>" . $product['id'] . "</td>";
                        echo "<td>" . htmlspecialchars($product['name']) . "</td>";
                        echo "<td>" . ($isActive ? '✓' : '✗') . "</td>";
                        echo "<td>" . ($isVisible ? '✓' : '✗') . "</td>";
                        echo "<td>" . ($canReview ? '✓' : '✗') . "</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                } else {
                    echo "<p class='error'>لا توجد منتجات في قاعدة البيانات</p>";
                }
                
                // آخر 3 تقييمات
                echo "<h4>آخر 3 تقييمات:</h4>";
                $reviews = $database->fetchAll("SELECT id, product_id, user_id, rating FROM reviews ORDER BY id DESC LIMIT 3");
                if ($reviews) {
                    foreach ($reviews as $review) {
                        $userType = $review['user_id'] ? $review['user_id'] : 'ضيف';
                        echo "<p>ID: {$review['id']} | المنتج: {$review['product_id']} | المستخدم: {$userType} | التقييم: {$review['rating']}/5</p>";
                    }
                } else {
                    echo "<p>لا توجد تقييمات</p>";
                }
                
            } catch (Exception $e) {
                echo "<p class='error'>✗ خطأ في الاتصال: " . $e->getMessage() . "</p>";
            }
            ?>
        </div>

        <!-- اختبار دالة getProduct -->
        <div class="section">
            <h3>اختبار دالة getProduct</h3>
            <button onclick="testGetProduct()" class="btn-warning">اختبار getProduct</button>
            <div id="getProductResult"></div>
        </div>

        <!-- نموذج اختبار التقييم -->
        <div class="section">
            <h3>نموذج اختبار التقييم</h3>
            <form id="reviewForm">
                <div class="form-group">
                    <label for="product_id">معرف المنتج:</label>
                    <select id="product_id" name="product_id" required>
                        <option value="">اختر منتج</option>
                        <?php
                        $products = $database->fetchAll("SELECT id, name, is_active, is_visible FROM products ORDER BY id");
                        foreach ($products as $product) {
                            $isActive = $product['is_active'] == 1;
                            $isVisible = is_null($product['is_visible']) || $product['is_visible'] == 1;
                            $canReview = $isActive && $isVisible;
                            $status = $canReview ? '' : ' (غير متاح)';
                            echo "<option value='{$product['id']}'" . ($canReview ? '' : ' disabled') . ">{$product['id']} - " . htmlspecialchars($product['name']) . $status . "</option>";
                        }
                        ?>
                    </select>
                </div>
                
                <?php if (!$isLoggedIn): ?>
                <div class="form-group">
                    <label for="customer_name">اسم العميل:</label>
                    <input type="text" id="customer_name" name="customer_name" required>
                </div>
                
                <div class="form-group">
                    <label for="customer_email">البريد الإلكتروني:</label>
                    <input type="email" id="customer_email" name="customer_email" required>
                </div>
                <?php endif; ?>
                
                <div class="form-group">
                    <label for="rating">التقييم:</label>
                    <select id="rating" name="rating" required>
                        <option value="">اختر تقييم</option>
                        <option value="1">1 نجمة</option>
                        <option value="2">2 نجمة</option>
                        <option value="3">3 نجوم</option>
                        <option value="4">4 نجوم</option>
                        <option value="5">5 نجوم</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="review_title">عنوان التقييم (اختياري):</label>
                    <input type="text" id="review_title" name="review_title">
                </div>
                
                <div class="form-group">
                    <label for="review_text">نص التقييم:</label>
                    <textarea id="review_text" name="review_text" rows="4" required></textarea>
                </div>
                
                <button type="submit">إرسال التقييم</button>
                <button type="button" onclick="testAPI()">اختبار API</button>
            </form>
            
            <div id="result"></div>
        </div>
    </div>

    <script>
        // اختبار دالة getProduct
        function testGetProduct() {
            const productId = document.getElementById('product_id').value || 1;
            
            fetch('test_get_product.php?product_id=' + productId)
                .then(response => response.text())
                .then(data => {
                    document.getElementById('getProductResult').innerHTML = 
                        '<div class="result info"><h4>نتيجة اختبار getProduct:</h4><pre>' + data + '</pre></div>';
                })
                .catch(error => {
                    document.getElementById('getProductResult').innerHTML = 
                        '<div class="result error">خطأ: ' + error.message + '</div>';
                });
        }

        // اختبار API
        function testAPI() {
            const formData = new FormData(document.getElementById('reviewForm'));
            
            fetch('api/review.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('result');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="result success">✓ ' + data.message + '</div>';
                    // إعادة تحميل الصفحة بعد 2 ثانية لإظهار التقييم الجديد
                    setTimeout(() => location.reload(), 2000);
                } else {
                    resultDiv.innerHTML = '<div class="result error">✗ ' + data.message + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('result').innerHTML = '<div class="result error">خطأ في الشبكة: ' + error.message + '</div>';
            });
        }

        // إرسال النموذج
        document.getElementById('reviewForm').addEventListener('submit', function(e) {
            e.preventDefault();
            testAPI();
        });
    </script>
</body>
</html>