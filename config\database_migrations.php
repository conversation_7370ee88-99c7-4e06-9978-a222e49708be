<?php
/**
 * Database Migration System
 * Ensures consistent database schema across environments
 */

class DatabaseMigrations {
    private $pdo;
    private $migrations = [];
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->initMigrationsTable();
        $this->defineMigrations();
    }
    
    private function initMigrationsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS migrations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            migration_name VARCHAR(255) NOT NULL UNIQUE,
            executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $this->pdo->exec($sql);
    }
    
    private function defineMigrations() {
        $this->migrations = [
            '001_create_support_files_table' => function() {
                $sql = "CREATE TABLE IF NOT EXISTS support_files (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    title VARCHAR(255) NOT NULL,
                    description TEXT,
                    file_path VARCHAR(500) NOT NULL,
                    file_size INT,
                    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )";
                $this->pdo->exec($sql);
            },
            
            '002_standardize_contact_info_table' => function() {
                // Ensure section_key column exists
                $columns = $this->getTableColumns('contact_info');
                if (!in_array('section_key', $columns)) {
                    if (in_array('section', $columns)) {
                        $this->pdo->exec("ALTER TABLE contact_info CHANGE section section_key VARCHAR(100) NOT NULL");
                    } else {
                        $this->pdo->exec("ALTER TABLE contact_info ADD COLUMN section_key VARCHAR(100) NOT NULL");
                    }
                }
            },
            
            '003_add_indexes_for_performance' => function() {
                $this->pdo->exec("CREATE INDEX IF NOT EXISTS idx_contact_info_section_key ON contact_info(section_key)");
                $this->pdo->exec("CREATE INDEX IF NOT EXISTS idx_support_files_upload_date ON support_files(upload_date)");
                $this->pdo->exec("CREATE INDEX IF NOT EXISTS idx_testimonials_status ON testimonials(status)");
                $this->pdo->exec("CREATE INDEX IF NOT EXISTS idx_faqs_status ON faqs(status)");
            }
        ];
    }
    
    public function runMigrations() {
        $results = [];
        foreach ($this->migrations as $name => $migration) {
            if (!$this->isMigrationExecuted($name)) {
                try {
                    $migration();
                    $this->markMigrationAsExecuted($name);
                    $results[] = "✅ Migration '$name' executed successfully";
                } catch (Exception $e) {
                    $results[] = "❌ Migration '$name' failed: " . $e->getMessage();
                }
            } else {
                $results[] = "⏭️ Migration '$name' already executed";
            }
        }
        return $results;
    }
    
    private function isMigrationExecuted($name) {
        $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM migrations WHERE migration_name = ?");
        $stmt->execute([$name]);
        return $stmt->fetchColumn() > 0;
    }
    
    private function markMigrationAsExecuted($name) {
        $stmt = $this->pdo->prepare("INSERT INTO migrations (migration_name) VALUES (?)");
        $stmt->execute([$name]);
    }
    
    private function getTableColumns($tableName) {
        $stmt = $this->pdo->query("DESCRIBE $tableName");
        return array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');
    }
}

// Usage example
if (basename($_SERVER['PHP_SELF']) === 'database_migrations.php') {
    require_once 'config.php';
    
    $migrations = new DatabaseMigrations($pdo);
    $results = $migrations->runMigrations();
    
    echo "<h2>Database Migration Results</h2>";
    foreach ($results as $result) {
        echo "<p>$result</p>";
    }
}
?>