<?php
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once 'includes/auth.php';

$pageTitle = 'إدارة رسائل التواصل';
$currentPage = 'contact_messages';

// معالجة العمليات
if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'mark_read':
                if (isset($_POST['message_id'])) {
                    $database->update('contact_messages', 
                        ['status' => 'read'], 
                        'id = :id', 
                        ['id' => $_POST['message_id']]
                    );
                    $_SESSION['success'] = 'تم تحديث حالة الرسالة بنجاح';
                }
                break;
                
            case 'mark_unread':
                if (isset($_POST['message_id'])) {
                    $database->update('contact_messages', 
                        ['status' => 'unread'], 
                        'id = :id', 
                        ['id' => $_POST['message_id']]
                    );
                    $_SESSION['success'] = 'تم تحديث حالة الرسالة بنجاح';
                }
                break;
                
            case 'delete':
                if (isset($_POST['message_id'])) {
                    $database->delete('contact_messages', 'id = :id', ['id' => $_POST['message_id']]);
                    $_SESSION['success'] = 'تم حذف الرسالة بنجاح';
                }
                break;
                
            case 'reply':
                if (isset($_POST['message_id']) && isset($_POST['reply_message'])) {
                    $message = $database->fetch("SELECT * FROM contact_messages WHERE id = :id", ['id' => $_POST['message_id']]);
                    if ($message) {
                        // إرسال الرد عبر البريد الإلكتروني
                        $subject = 'رد على رسالتك: ' . $message['subject'];
                        $body = $_POST['reply_message'];
                        
                        // هنا يمكن إضافة كود إرسال البريد الإلكتروني
                        // sendEmail($message['email'], $subject, $body);
                        
                        // تحديث حالة الرسالة
                        $database->update('contact_messages', 
                            ['status' => 'replied', 'reply' => $_POST['reply_message'], 'replied_at' => date('Y-m-d H:i:s')], 
                            'id = :id', 
                            ['id' => $_POST['message_id']]
                        );
                        
                        $_SESSION['success'] = 'تم إرسال الرد بنجاح';
                    }
                }
                break;
        }
        
        // إعادة توجيه لتجنب إعادة الإرسال
        header('Location: ' . $_SERVER['PHP_SELF'] . '?tab=' . ($_GET['tab'] ?? 'all'));
        exit;
    }
}

// جلب المواضيع المتاحة
$subjects = [
    'all' => 'جميع الرسائل',
    'استفسار عام' => 'استفسار عام',
    'طلب عرض سعر' => 'طلب عرض سعر',
    'الدعم الفني' => 'الدعم الفني',
    'شراكة' => 'شراكة',
    'شكوى' => 'شكوى',
    'اقتراح' => 'اقتراح'
];

$currentTab = $_GET['tab'] ?? 'all';

// جلب الرسائل حسب التبويب
$whereClause = '';
$params = [];

if ($currentTab !== 'all') {
    $whereClause = 'WHERE subject = :subject';
    $params['subject'] = $currentTab;
}

$messages = $database->fetchAll("
    SELECT * FROM contact_messages 
    $whereClause 
    ORDER BY created_at DESC
", $params);

// إحصائيات سريعة
$stats = [];
foreach ($subjects as $key => $label) {
    if ($key === 'all') {
        $stats[$key] = $database->fetch("SELECT COUNT(*) as count FROM contact_messages")['count'];
    } else {
        $stats[$key] = $database->fetch("SELECT COUNT(*) as count FROM contact_messages WHERE subject = :subject", ['subject' => $key])['count'];
    }
}

include 'includes/header.php';
?>

<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">إدارة رسائل التواصل</h1>
                    <p class="mt-2 text-gray-600">إدارة ومتابعة رسائل العملاء والاستفسارات</p>
                </div>
                
                <!-- إحصائيات سريعة -->
                <div class="flex space-x-4 space-x-reverse">
                    <div class="bg-white rounded-lg shadow p-4 text-center">
                        <div class="text-2xl font-bold text-blue-600"><?php echo $stats['all']; ?></div>
                        <div class="text-sm text-gray-500">إجمالي الرسائل</div>
                    </div>
                    <div class="bg-white rounded-lg shadow p-4 text-center">
                        <div class="text-2xl font-bold text-red-600">
                            <?php echo $database->fetch("SELECT COUNT(*) as count FROM contact_messages WHERE status = 'unread'")['count']; ?>
                        </div>
                        <div class="text-sm text-gray-500">غير مقروءة</div>
                    </div>
                    <div class="bg-white rounded-lg shadow p-4 text-center">
                        <div class="text-2xl font-bold text-green-600">
                            <?php echo $database->fetch("SELECT COUNT(*) as count FROM contact_messages WHERE status = 'replied'")['count']; ?>
                        </div>
                        <div class="text-sm text-gray-500">تم الرد عليها</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- التبويبات -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8 space-x-reverse px-6" aria-label="Tabs">
                    <?php foreach ($subjects as $key => $label): ?>
                        <a href="?tab=<?php echo $key; ?>" 
                           class="<?php echo $currentTab === $key ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?> whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center">
                            <?php echo $label; ?>
                            <?php if ($stats[$key] > 0): ?>
                                <span class="<?php echo $currentTab === $key ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-900'; ?> ml-2 py-0.5 px-2.5 rounded-full text-xs font-medium">
                                    <?php echo $stats[$key]; ?>
                                </span>
                            <?php endif; ?>
                        </a>
                    <?php endforeach; ?>
                </nav>
            </div>
        </div>

        <!-- قائمة الرسائل -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">
                    <?php echo $subjects[$currentTab]; ?>
                    <?php if (count($messages) > 0): ?>
                        <span class="text-gray-500 text-sm">(<?php echo count($messages); ?> رسالة)</span>
                    <?php endif; ?>
                </h3>
            </div>

            <?php if (empty($messages)): ?>
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">لا توجد رسائل</h3>
                    <p class="mt-1 text-sm text-gray-500">لم يتم العثور على رسائل في هذا القسم.</p>
                </div>
            <?php else: ?>
                <div class="divide-y divide-gray-200">
                    <?php foreach ($messages as $message): ?>
                        <div class="p-6 <?php echo $message['status'] === 'unread' ? 'bg-blue-50' : ''; ?>">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-3 space-x-reverse">
                                        <h4 class="text-lg font-medium text-gray-900"><?php echo htmlspecialchars($message['name']); ?></h4>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php 
                                            echo $message['status'] === 'unread' ? 'bg-red-100 text-red-800' : 
                                                ($message['status'] === 'replied' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'); 
                                        ?>">
                                            <?php 
                                                echo $message['status'] === 'unread' ? 'غير مقروءة' : 
                                                    ($message['status'] === 'replied' ? 'تم الرد' : 'مقروءة'); 
                                            ?>
                                        </span>
                                        <span class="text-sm text-gray-500"><?php echo date('Y/m/d H:i', strtotime($message['created_at'])); ?></span>
                                    </div>
                                    
                                    <div class="mt-2">
                                        <p class="text-sm text-gray-600">
                                            <strong>البريد الإلكتروني:</strong> <?php echo htmlspecialchars($message['email']); ?>
                                        </p>
                                        <?php if (!empty($message['phone'])): ?>
                                            <p class="text-sm text-gray-600">
                                                <strong>الهاتف:</strong> <?php echo htmlspecialchars($message['phone']); ?>
                                            </p>
                                        <?php endif; ?>
                                        <p class="text-sm text-gray-600">
                                            <strong>الموضوع:</strong> <?php echo htmlspecialchars($message['subject']); ?>
                                        </p>
                                    </div>
                                    
                                    <div class="mt-3">
                                        <p class="text-gray-900"><?php echo nl2br(htmlspecialchars($message['message'])); ?></p>
                                    </div>
                                    
                                    <?php if (!empty($message['reply'])): ?>
                                        <div class="mt-4 p-4 bg-green-50 rounded-lg border border-green-200">
                                            <h5 class="text-sm font-medium text-green-800 mb-2">الرد:</h5>
                                            <p class="text-sm text-green-700"><?php echo nl2br(htmlspecialchars($message['reply'])); ?></p>
                                            <p class="text-xs text-green-600 mt-2">تم الرد في: <?php echo date('Y/m/d H:i', strtotime($message['replied_at'])); ?></p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <!-- أزرار العمليات -->
                                <div class="flex space-x-2 space-x-reverse">
                                    <?php if ($message['status'] === 'unread'): ?>
                                        <form method="POST" class="inline">
                                            <input type="hidden" name="action" value="mark_read">
                                            <input type="hidden" name="message_id" value="<?php echo $message['id']; ?>">
                                            <button type="submit" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                                تحديد كمقروءة
                                            </button>
                                        </form>
                                    <?php else: ?>
                                        <form method="POST" class="inline">
                                            <input type="hidden" name="action" value="mark_unread">
                                            <input type="hidden" name="message_id" value="<?php echo $message['id']; ?>">
                                            <button type="submit" class="text-gray-600 hover:text-gray-800 text-sm font-medium">
                                                تحديد كغير مقروءة
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                    
                                    <button onclick="openReplyModal(<?php echo $message['id']; ?>, '<?php echo htmlspecialchars($message['name']); ?>', '<?php echo htmlspecialchars($message['email']); ?>')" 
                                            class="text-green-600 hover:text-green-800 text-sm font-medium">
                                        رد
                                    </button>
                                    
                                    <form method="POST" class="inline" onsubmit="return confirm('هل أنت متأكد من حذف هذه الرسالة؟')">
                                        <input type="hidden" name="action" value="delete">
                                        <input type="hidden" name="message_id" value="<?php echo $message['id']; ?>">
                                        <button type="submit" class="text-red-600 hover:text-red-800 text-sm font-medium">
                                            حذف
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- نافذة الرد -->
<div id="replyModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">إرسال رد</h3>
                <button onclick="closeReplyModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <form method="POST" id="replyForm">
                <input type="hidden" name="action" value="reply">
                <input type="hidden" name="message_id" id="replyMessageId">
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">إلى:</label>
                    <p id="replyTo" class="text-sm text-gray-600"></p>
                </div>
                
                <div class="mb-4">
                    <label for="reply_message" class="block text-sm font-medium text-gray-700 mb-2">الرسالة:</label>
                    <textarea name="reply_message" id="reply_message" rows="6" required
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              placeholder="اكتب ردك هنا..."></textarea>
                </div>
                
                <div class="flex justify-end space-x-3 space-x-reverse">
                    <button type="button" onclick="closeReplyModal()" 
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                        إلغاء
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">
                        إرسال الرد
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function openReplyModal(messageId, name, email) {
    document.getElementById('replyMessageId').value = messageId;
    document.getElementById('replyTo').textContent = name + ' (' + email + ')';
    document.getElementById('reply_message').value = '';
    document.getElementById('replyModal').classList.remove('hidden');
}

function closeReplyModal() {
    document.getElementById('replyModal').classList.add('hidden');
}

// إغلاق النافذة عند النقر خارجها
document.getElementById('replyModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeReplyModal();
    }
});
</script>

<?php include 'includes/footer.php'; ?>