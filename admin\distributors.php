<?php
require_once 'includes/layout.php';

// Check if user is logged in and has admin privileges
if (!isLoggedIn() || !hasPermission('admin')) {
    header('Location: login.php');
    exit;
}

// Create distributors table if it doesn't exist
try {
    $database->query("
        CREATE TABLE IF NOT EXISTS distributors (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(255) NOT NULL,
            region VARCHAR(100) NOT NULL,
            type VARCHAR(50),
            distributor_type ENUM('رئيسي', 'مميز', 'عادي') DEFAULT 'عادي',
            specializations TEXT,
            address TEXT,
            phone VARCHAR(20),
            email VARCHAR(255),
            website VARCHAR(255),
            whatsapp VARCHAR(20),
            working_hours TEXT,
            services TEXT,
            latitude DECIMAL(10, 8),
            longitude DECIMAL(11, 8),
            logo VARCHAR(255),
            is_active TINYINT(1) DEFAULT 1,
            sort_order INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_region (region),
            INDEX idx_active (is_active),
            INDEX idx_sort (sort_order),
            INDEX idx_distributor_type (distributor_type)
        )
    ");
    
    // Check if distributor_type column exists, if not add it
    $columns = $database->fetchAll("SHOW COLUMNS FROM distributors LIKE 'distributor_type'");
    if (empty($columns)) {
        $database->query("ALTER TABLE distributors ADD COLUMN distributor_type ENUM('رئيسي', 'مميز', 'عادي') DEFAULT 'عادي' AFTER type");
        $database->query("ALTER TABLE distributors ADD INDEX idx_distributor_type (distributor_type)");
    }
} catch (Exception $e) {
    // Table creation failed, but continue
}

$pageTitle = 'إدارة الموزعين';
$currentPage = 'distributors';
$pageDescription = 'إدارة وتنظيم جميع الموزعين وأقسام صفحة الموزعين';
$breadcrumbs = [
    ['title' => 'الموزعين']
];

$action = $_POST['action'] ?? $_GET['action'] ?? 'list';
$distributorId = $_GET['id'] ?? null;
$message = $_GET['message'] ?? '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if ($action === 'add') {
            $name = trim($_POST['name'] ?? '');
            $region = trim($_POST['region'] ?? '');
            $city = trim($_POST['city'] ?? '') ?: null;
            $type = trim($_POST['type'] ?? '');
            $distributor_type = trim($_POST['distributor_type'] ?? 'عادي');
            $description = trim($_POST['description'] ?? '') ?: null;
            $specializations = isset($_POST['specializations']) && is_array($_POST['specializations']) 
                ? implode(', ', $_POST['specializations']) 
                : trim($_POST['specializations'] ?? '');
            $address = trim($_POST['address'] ?? '');
            $phone = trim($_POST['phone'] ?? '');
            $email = trim($_POST['email'] ?? '');
            $website = trim($_POST['website'] ?? '');
            $whatsapp = trim($_POST['whatsapp'] ?? '');
            $working_hours = trim($_POST['working_hours'] ?? '');
            // تحويل working_hours إلى JSON إذا لم يكن فارغاً
            if (!empty($working_hours)) {
                $working_hours = json_encode($working_hours, JSON_UNESCAPED_UNICODE);
            } else {
                $working_hours = null;
            }
            $services = trim($_POST['services'] ?? '');
            // تحويل services إلى JSON إذا لم يكن فارغاً
            if (!empty($services)) {
                // التحقق من صحة JSON
                $decoded = json_decode($services, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $services = json_encode($decoded, JSON_UNESCAPED_UNICODE);
                } else {
                    $services = json_encode($services, JSON_UNESCAPED_UNICODE);
                }
            } else {
                $services = null;
            }
            $latitude = !empty($_POST['latitude']) ? floatval($_POST['latitude']) : null;
            $longitude = !empty($_POST['longitude']) ? floatval($_POST['longitude']) : null;
            $location = trim($_POST['location'] ?? '');
            // تحويل location إلى JSON إذا لم يكن فارغاً
            if (!empty($location)) {
                // التحقق من صحة JSON
                $decoded = json_decode($location, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $location = json_encode($decoded, JSON_UNESCAPED_UNICODE);
                } else {
                    $location = json_encode($location, JSON_UNESCAPED_UNICODE);
                }
            } else {
                $location = null;
            }
            $image = trim($_POST['image'] ?? '') ?: null;
            $logo = trim($_POST['logo'] ?? '') ?: null;
            $is_active = isset($_POST['is_active']) ? 1 : 0;
            $sort_order = intval($_POST['sort_order'] ?? 0);
            
            // الحقول الجديدة
            $manager = trim($_POST['manager'] ?? '') ?: null;
            $established = trim($_POST['established'] ?? '') ?: null;
            $rating = !empty($_POST['rating']) ? floatval($_POST['rating']) : null;
            $reviews = !empty($_POST['reviews']) ? intval($_POST['reviews']) : null;
            $certified = isset($_POST['certified']) ? 1 : 0;
            $featured = isset($_POST['featured']) ? 1 : 0;
            
            // معالجة حقول specialties و coverage
            $specialties = trim($_POST['specialties'] ?? '');
            if (!empty($specialties)) {
                $decoded = json_decode($specialties, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $specialties = json_encode($decoded, JSON_UNESCAPED_UNICODE);
                } else {
                    $specialties = json_encode($specialties, JSON_UNESCAPED_UNICODE);
                }
            } else {
                $specialties = null;
            }
            
            $coverage = trim($_POST['coverage'] ?? '');
            if (!empty($coverage)) {
                $decoded = json_decode($coverage, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $coverage = json_encode($decoded, JSON_UNESCAPED_UNICODE);
                } else {
                    $coverage = json_encode($coverage, JSON_UNESCAPED_UNICODE);
                }
            } else {
                $coverage = null;
            }
            
            if (empty($name)) {
                throw new Exception('اسم الموزع مطلوب');
            }
            
            if (empty($region)) {
                throw new Exception('المنطقة مطلوبة');
            }
            
            $database->query(
                "INSERT INTO distributors (name, region, city, type, distributor_type, description, specializations, address, phone, email, website, whatsapp, working_hours, services, latitude, longitude, location, image, logo, is_active, sort_order, manager, established, rating, reviews, certified, featured, specialties, coverage, created_at) 
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())",
                [$name, $region, $city, $type, $distributor_type, $description, $specializations, $address, $phone, $email, $website, $whatsapp, $working_hours, $services, $latitude, $longitude, $location, $image, $logo, $is_active, $sort_order, $manager, $established, $rating, $reviews, $certified, $featured, $specialties, $coverage]
            );
            
            logActivity('تم إضافة موزع جديد: ' . $name, 'distributors');
            $message = 'تم إضافة الموزع بنجاح';
            $action = 'list';
            
        } elseif ($action === 'edit' && $distributorId) {
            $name = trim($_POST['name'] ?? '');
            $region = trim($_POST['region'] ?? '');
            $city = trim($_POST['city'] ?? '') ?: null;
            $type = trim($_POST['type'] ?? '');
            $distributor_type = trim($_POST['distributor_type'] ?? 'عادي');
            $description = trim($_POST['description'] ?? '') ?: null;
            $specializations = isset($_POST['specializations']) && is_array($_POST['specializations']) 
                ? implode(', ', $_POST['specializations']) 
                : trim($_POST['specializations'] ?? '');
            $address = trim($_POST['address'] ?? '');
            $phone = trim($_POST['phone'] ?? '');
            $email = trim($_POST['email'] ?? '');
            $website = trim($_POST['website'] ?? '');
            $whatsapp = trim($_POST['whatsapp'] ?? '');
            $working_hours = trim($_POST['working_hours'] ?? '');
            // تحويل working_hours إلى JSON إذا لم يكن فارغاً
            if (!empty($working_hours)) {
                $working_hours = json_encode($working_hours, JSON_UNESCAPED_UNICODE);
            } else {
                $working_hours = null;
            }
            $services = trim($_POST['services'] ?? '');
            // تحويل services إلى JSON إذا لم يكن فارغاً
            if (!empty($services)) {
                // التحقق من صحة JSON
                $decoded = json_decode($services, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $services = json_encode($decoded, JSON_UNESCAPED_UNICODE);
                } else {
                    $services = json_encode($services, JSON_UNESCAPED_UNICODE);
                }
            } else {
                $services = null;
            }
            $latitude = !empty($_POST['latitude']) ? floatval($_POST['latitude']) : null;
            $longitude = !empty($_POST['longitude']) ? floatval($_POST['longitude']) : null;
            $location = trim($_POST['location'] ?? '');
            // تحويل location إلى JSON إذا لم يكن فارغاً
            if (!empty($location)) {
                // التحقق من صحة JSON
                $decoded = json_decode($location, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $location = json_encode($decoded, JSON_UNESCAPED_UNICODE);
                } else {
                    $location = json_encode($location, JSON_UNESCAPED_UNICODE);
                }
            } else {
                $location = null;
            }
            $image = trim($_POST['image'] ?? '') ?: null;
            $logo = trim($_POST['logo'] ?? '') ?: null;
            $is_active = isset($_POST['is_active']) ? 1 : 0;
            $sort_order = intval($_POST['sort_order'] ?? 0);
            
            // الحقول الجديدة
            $manager = trim($_POST['manager'] ?? '') ?: null;
            $established = trim($_POST['established'] ?? '') ?: null;
            $rating = !empty($_POST['rating']) ? floatval($_POST['rating']) : null;
            $reviews = !empty($_POST['reviews']) ? intval($_POST['reviews']) : null;
            $certified = isset($_POST['certified']) ? 1 : 0;
            $featured = isset($_POST['featured']) ? 1 : 0;
            
            // معالجة حقول specialties و coverage
            $specialties = trim($_POST['specialties'] ?? '');
            if (!empty($specialties)) {
                $decoded = json_decode($specialties, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $specialties = json_encode($decoded, JSON_UNESCAPED_UNICODE);
                } else {
                    $specialties = json_encode($specialties, JSON_UNESCAPED_UNICODE);
                }
            } else {
                $specialties = null;
            }
            
            $coverage = trim($_POST['coverage'] ?? '');
            if (!empty($coverage)) {
                $decoded = json_decode($coverage, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $coverage = json_encode($decoded, JSON_UNESCAPED_UNICODE);
                } else {
                    $coverage = json_encode($coverage, JSON_UNESCAPED_UNICODE);
                }
            } else {
                $coverage = null;
            }
            
            if (empty($name)) {
                throw new Exception('اسم الموزع مطلوب');
            }
            
            if (empty($region)) {
                throw new Exception('المنطقة مطلوبة');
            }
            
            $database->query(
                "UPDATE distributors SET name = ?, region = ?, city = ?, type = ?, distributor_type = ?, description = ?, specializations = ?, address = ?, phone = ?, email = ?, website = ?, whatsapp = ?, working_hours = ?, services = ?, latitude = ?, longitude = ?, location = ?, image = ?, logo = ?, is_active = ?, sort_order = ?, manager = ?, established = ?, rating = ?, reviews = ?, certified = ?, featured = ?, specialties = ?, coverage = ?, updated_at = NOW() 
                 WHERE id = ?",
                [$name, $region, $city, $type, $distributor_type, $description, $specializations, $address, $phone, $email, $website, $whatsapp, $working_hours, $services, $latitude, $longitude, $location, $image, $logo, $is_active, $sort_order, $manager, $established, $rating, $reviews, $certified, $featured, $specialties, $coverage, $distributorId]
            );
            
            logActivity('تم تعديل الموزع: ' . $name, 'distributors');
            $message = 'تم تحديث الموزع بنجاح';
            $action = 'list';
            
        } elseif ($action === 'update_content') {
            // Update page content sections
            $hero_title = trim($_POST['hero_title'] ?? '');
            $hero_subtitle = trim($_POST['hero_subtitle'] ?? '');
            $map_title = trim($_POST['map_title'] ?? '');
            $map_subtitle = trim($_POST['map_subtitle'] ?? '');
            $partnership_title = trim($_POST['partnership_title'] ?? '');
            $partnership_subtitle = trim($_POST['partnership_subtitle'] ?? '');
            $partnership_button1 = trim($_POST['partnership_button1'] ?? '');
            $partnership_button2 = trim($_POST['partnership_button2'] ?? '');
            $stats_title = trim($_POST['stats_title'] ?? '');
            $stats_subtitle = trim($_POST['stats_subtitle'] ?? '');
            $stats_distributors_label = trim($_POST['stats_distributors_label'] ?? '');
            $stats_cities_label = trim($_POST['stats_cities_label'] ?? '');
            $stats_support_label = trim($_POST['stats_support_label'] ?? '');
            $stats_quality_label = trim($_POST['stats_quality_label'] ?? '');
            
            // Update or insert content settings
            $contentSettings = [
                'distributors_hero_title' => $hero_title,
                'distributors_hero_subtitle' => $hero_subtitle,
                'distributors_map_title' => $map_title,
                'distributors_map_subtitle' => $map_subtitle,
                'distributors_partnership_title' => $partnership_title,
                'distributors_partnership_subtitle' => $partnership_subtitle,
                'distributors_partnership_button1' => $partnership_button1,
                'distributors_partnership_button2' => $partnership_button2,
                'distributors_stats_title' => $stats_title,
                'distributors_stats_subtitle' => $stats_subtitle,
                'distributors_stats_distributors_label' => $stats_distributors_label,
                'distributors_stats_cities_label' => $stats_cities_label,
                'distributors_stats_support_label' => $stats_support_label,
                'distributors_stats_quality_label' => $stats_quality_label
            ];
            
            foreach ($contentSettings as $key => $value) {
                $database->query(
                    "INSERT INTO settings (setting_key, setting_value, updated_at) VALUES (?, ?, NOW()) 
                     ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = NOW()",
                    [$key, $value]
                );
            }
            
            logActivity('تم تحديث محتوى صفحة الموزعين', 'distributors');
            $message = 'تم تحديث محتوى الصفحة بنجاح';
            
            // Redirect to show success message
            header('Location: distributors.php?action=content&message=' . urlencode($message));
            exit;
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
        logActivity('خطأ في إدارة الموزعين', 'distributors');
    }
}

// Handle delete action
if ($action === 'delete' && $distributorId) {
    try {
        $distributor = $database->fetch("SELECT name FROM distributors WHERE id = ?", [$distributorId]);
        if ($distributor) {
            $database->query("DELETE FROM distributors WHERE id = ?", [$distributorId]);
            logActivity('تم حذف الموزع: ' . $distributor['name'], 'distributors');
            $message = 'تم حذف الموزع بنجاح';
        }
        $action = 'list';
    } catch (Exception $e) {
        $error = 'حدث خطأ أثناء حذف الموزع';
        logActivity('خطأ في حذف الموزع', 'distributors');
    }
}

// Get data for forms
if ($action === 'edit' && $distributorId) {
    $distributor = $database->fetch("SELECT * FROM distributors WHERE id = ?", [$distributorId]);
    if (!$distributor) {
        $error = 'الموزع غير موجود';
        $action = 'list';
    }
}

// Get distributors list
if ($action === 'list') {
    $search = $_GET['search'] ?? '';
    $regionFilter = $_GET['region_filter'] ?? '';
    $distributorTypeFilter = $_GET['distributor_type_filter'] ?? '';
    $statusFilter = $_GET['status_filter'] ?? '';
    
    $whereConditions = [];
    $params = [];
    
    if (!empty($search)) {
        $whereConditions[] = "(name LIKE ? OR region LIKE ? OR address LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    if (!empty($regionFilter)) {
        $whereConditions[] = "region = ?";
        $params[] = $regionFilter;
    }
    
    if (!empty($distributorTypeFilter)) {
        $whereConditions[] = "distributor_type = ?";
        $params[] = $distributorTypeFilter;
    }
    
    if ($statusFilter !== '') {
        $whereConditions[] = "is_active = ?";
        $params[] = $statusFilter;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    $query = "SELECT * FROM distributors $whereClause ORDER BY 
        CASE distributor_type 
            WHEN 'رئيسي' THEN 1 
            WHEN 'مميز' THEN 2 
            WHEN 'عادي' THEN 3 
            ELSE 4 
        END, 
        region ASC, 
        sort_order ASC, 
        created_at DESC";
    $distributors = $database->fetchAll($query, $params) ?? [];
    
    // Get unique regions for filter
    $regions = $database->fetchAll("SELECT DISTINCT region FROM distributors WHERE region IS NOT NULL AND region != '' ORDER BY region") ?? [];
    
    // Get statistics
    $stats = [
        'total' => $database->fetch("SELECT COUNT(*) as count FROM distributors")['count'] ?? 0,
        'active' => $database->fetch("SELECT COUNT(*) as count FROM distributors WHERE is_active = 1")['count'] ?? 0,
        'primary' => $database->fetch("SELECT COUNT(*) as count FROM distributors WHERE distributor_type = 'رئيسي'")['count'] ?? 0,
        'featured' => $database->fetch("SELECT COUNT(*) as count FROM distributors WHERE distributor_type = 'مميز'")['count'] ?? 0,
        'regular' => $database->fetch("SELECT COUNT(*) as count FROM distributors WHERE distributor_type = 'عادي'")['count'] ?? 0,
        'regions' => $database->fetch("SELECT COUNT(DISTINCT region) as count FROM distributors WHERE region IS NOT NULL AND region != ''")['count'] ?? 0
    ];
    
    // Assign statistics to individual variables for use in HTML
    $totalDistributors = $stats['total'];
    $activeDistributors = $stats['active'];
    $mainDistributors = $stats['primary'];
    $featuredDistributors = $stats['featured'];
    $regularDistributors = $stats['regular'];
    $totalRegions = $stats['regions'];
}

// Get content settings for content management
if ($action === 'content') {
    $contentKeys = [
        'distributors_hero_title',
        'distributors_hero_subtitle',
        'distributors_map_title',
        'distributors_map_subtitle',
        'distributors_partnership_title',
        'distributors_partnership_subtitle',
        'distributors_partnership_button1',
        'distributors_partnership_button2',
        'distributors_stats_title',
        'distributors_stats_subtitle',
        'distributors_stats_distributors_label',
        'distributors_stats_cities_label',
        'distributors_stats_support_label',
        'distributors_stats_quality_label'
    ];
    
    $content = [];
    foreach ($contentKeys as $key) {
        $setting = $database->fetch("SELECT setting_value FROM settings WHERE setting_key = ?", [$key]);
        $content[$key] = $setting ? $setting['setting_value'] : '';
    }
}

startLayout();
showPageHeader();
showMessages($message, $error);

// Create tabs for different sections
$tabs = [
    'list' => [
        'title' => 'قائمة الموزعين',
        'icon' => 'fas fa-list',
        'content' => ''
    ],
    'content' => [
        'title' => 'محتوى الصفحة',
        'icon' => 'fas fa-edit',
        'content' => ''
    ]
];

// Set active tab based on action
$activeTab = in_array($action, ['content', 'update_content']) ? 'content' : 'list';

// Start capturing tab content
ob_start();
?>

<?php if ($activeTab === 'list'): ?>
    <?php if ($action === 'add' || $action === 'edit'): ?>
        <!-- Add/Edit Distributor Form -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900">
                    <?php echo $action === 'add' ? 'إضافة موزع جديد' : 'تعديل الموزع'; ?>
                </h2>
            </div>
            
            <form method="POST" class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Basic Information -->
                    <div class="md:col-span-2">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">المعلومات الأساسية</h3>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">اسم الموزع *</label>
                        <input type="text" name="name" value="<?php echo htmlspecialchars($distributor['name'] ?? ''); ?>" 
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-white mb-2">المنطقة *</label>
                        <input type="text" name="region" value="<?php echo htmlspecialchars($distributor['region'] ?? ''); ?>" 
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">المدينة</label>
                        <input type="text" name="city" value="<?php echo htmlspecialchars($distributor['city'] ?? ''); ?>" 
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">نوع الموزع</label>
                        <select name="type" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">اختر النوع</option>
                            <option value="معتمد" <?php echo ($distributor['type'] ?? '') === 'معتمد' ? 'selected' : ''; ?>>معتمد</option>
                            <option value="وكيل" <?php echo ($distributor['type'] ?? '') === 'وكيل' ? 'selected' : ''; ?>>وكيل</option>
                            <option value="موزع" <?php echo ($distributor['type'] ?? '') === 'موزع' ? 'selected' : ''; ?>>موزع</option>
                            <option value="متجر" <?php echo ($distributor['type'] ?? '') === 'متجر' ? 'selected' : ''; ?>>متجر</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">تصنيف الموزع *</label>
                        <select name="distributor_type" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
                            <option value="عادي" <?php echo ($distributor['distributor_type'] ?? 'عادي') === 'عادي' ? 'selected' : ''; ?>>موزع عادي</option>
                            <option value="رئيسي" <?php echo ($distributor['distributor_type'] ?? '') === 'رئيسي' ? 'selected' : ''; ?>>موزع رئيسي</option>
                            <option value="مميز" <?php echo ($distributor['distributor_type'] ?? '') === 'مميز' ? 'selected' : ''; ?>>موزع مميز</option>
                        </select>
                        <p class="text-xs text-gray-500 mt-1">الموزع الرئيسي والمميز سيظهران بألوان مميزة في الصفحة العامة</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">ترتيب العرض</label>
                        <input type="number" name="sort_order" value="<?php echo htmlspecialchars($distributor['sort_order'] ?? '0'); ?>" 
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    
                    <!-- Contact Information -->
                    <div class="md:col-span-2 mt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">معلومات الاتصال</h3>
                    </div>
                    
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">العنوان</label>
                        <textarea name="address" rows="2" 
                                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"><?php echo htmlspecialchars($distributor['address'] ?? ''); ?></textarea>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                        <input type="text" name="phone" value="<?php echo htmlspecialchars($distributor['phone'] ?? ''); ?>" 
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">رقم الواتساب</label>
                        <input type="text" name="whatsapp" value="<?php echo htmlspecialchars($distributor['whatsapp'] ?? ''); ?>" 
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                        <input type="email" name="email" value="<?php echo htmlspecialchars($distributor['email'] ?? ''); ?>" 
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الموقع الإلكتروني</label>
                        <input type="url" name="website" value="<?php echo htmlspecialchars($distributor['website'] ?? ''); ?>" 
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    
                    <!-- Additional Information -->
                    <div class="md:col-span-2 mt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">معلومات إضافية</h3>
                    </div>
                    
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">ساعات العمل</label>
                        <textarea name="working_hours" rows="2" 
                                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                                  placeholder="مثال: السبت - الخميس: 8:00 ص - 6:00 م"><?php 
                            // إذا كانت working_hours تحتوي على JSON، قم بفك ترميزها لعرضها كنص
                            $working_hours_display = '';
                            if (!empty($distributor['working_hours'])) {
                                $decoded = json_decode($distributor['working_hours'], true);
                                if (json_last_error() === JSON_ERROR_NONE && is_string($decoded)) {
                                    $working_hours_display = $decoded;
                                } else {
                                    $working_hours_display = $distributor['working_hours'];
                                }
                            }
                            echo htmlspecialchars($working_hours_display);
                        ?></textarea>
                    </div>
                    
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                        <textarea name="description" rows="3" 
                                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                                  placeholder="وصف مختصر عن الموزع وخدماته"><?php echo htmlspecialchars($distributor['description'] ?? ''); ?></textarea>
                    </div>
                    
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">التخصصات *</label>
                        <textarea name="specializations" rows="3" 
                                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                                  placeholder="مثال: مكيفات سكنية، مكيفات فنادق، صيانة متخصصة" required><?php echo htmlspecialchars($distributor['specializations'] ?? ''); ?></textarea>
                        <p class="text-xs text-gray-500 mt-1">اكتب التخصصات مفصولة بفواصل (مثل: مكيفات سكنية، مكيفات فنادق، صيانة متخصصة)</p>
                    </div>
                    
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">الخدمات المتاحة</label>
                        <textarea name="services" rows="3" 
                                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                                  placeholder="اكتب الخدمات مفصولة بفواصل، مثال: بيع، صيانة، دعم فني"><?php 
                            // إذا كانت services تحتوي على JSON، قم بفك ترميزها لعرضها بشكل منسق
                            $services_display = '';
                            if (!empty($distributor['services'])) {
                                $decoded = json_decode($distributor['services'], true);
                                if (json_last_error() === JSON_ERROR_NONE) {
                                    $services_display = json_encode($decoded, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                                } else {
                                    $services_display = $distributor['services'];
                                }
                            }
                            echo htmlspecialchars($services_display);
                        ?></textarea>
                        <p class="text-xs text-gray-500 mt-1">اكتب الخدمات مفصولة بفواصل أو بتنسيق JSON (اختياري)</p>
                    </div>
                    
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">التخصصات الجديدة (JSON)</label>
                        <textarea name="specialties" rows="3" 
                                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                                  placeholder='مثال: ["مكيفات سبليت", "أنظمة مركزية", "مكيفات تجارية"]'><?php 
                            // إذا كانت specialties تحتوي على JSON، قم بفك ترميزها لعرضها بشكل منسق
                            $specialties_display = '';
                            if (!empty($distributor['specialties'])) {
                                $decoded = json_decode($distributor['specialties'], true);
                                if (json_last_error() === JSON_ERROR_NONE) {
                                    $specialties_display = json_encode($decoded, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                                } else {
                                    $specialties_display = $distributor['specialties'];
                                }
                            }
                            echo htmlspecialchars($specialties_display);
                        ?></textarea>
                        <p class="text-xs text-gray-500 mt-1">التخصصات بتنسيق JSON (مثل: ["مكيفات سبليت", "أنظمة مركزية"])</p>
                    </div>
                    
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">مناطق التغطية</label>
                        <textarea name="coverage" rows="3" 
                                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                                  placeholder='مثال: ["الرياض", "الخرج", "الدرعية", "القصيم"]'><?php 
                            // إذا كانت coverage تحتوي على JSON، قم بفك ترميزها لعرضها بشكل منسق
                            $coverage_display = '';
                            if (!empty($distributor['coverage'])) {
                                $decoded = json_decode($distributor['coverage'], true);
                                if (json_last_error() === JSON_ERROR_NONE) {
                                    $coverage_display = json_encode($decoded, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                                } else {
                                    $coverage_display = $distributor['coverage'];
                                }
                            }
                            echo htmlspecialchars($coverage_display);
                        ?></textarea>
                        <p class="text-xs text-gray-500 mt-1">المناطق التي يغطيها الموزع بتنسيق JSON</p>
                    </div>
                    
                    <!-- Location -->
                    <div class="md:col-span-2 mt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">الموقع الجغرافي</h3>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">خط العرض (Latitude)</label>
                        <input type="number" step="any" name="latitude" value="<?php echo htmlspecialchars($distributor['latitude'] ?? ''); ?>" 
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">خط الطول (Longitude)</label>
                        <input type="number" step="any" name="longitude" value="<?php echo htmlspecialchars($distributor['longitude'] ?? ''); ?>" 
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">معلومات الموقع (JSON)</label>
                        <textarea name="location" rows="3" 
                                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                                  placeholder='مثال: {"landmark": "بجانب مول الرياض", "directions": "الطابق الثاني"}'><?php 
                            // إذا كانت location تحتوي على JSON، قم بفك ترميزها لعرضها بشكل منسق
                            $location_display = '';
                            if (!empty($distributor['location'])) {
                                $decoded = json_decode($distributor['location'], true);
                                if (json_last_error() === JSON_ERROR_NONE) {
                                    $location_display = json_encode($decoded, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                                } else {
                                    $location_display = $distributor['location'];
                                }
                            }
                            echo htmlspecialchars($location_display);
                        ?></textarea>
                        <p class="text-xs text-gray-500 mt-1">معلومات إضافية عن الموقع بتنسيق JSON (اختياري)</p>
                    </div>
                    
                    <!-- Additional Information -->
                    <div class="md:col-span-2 mt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">معلومات إضافية</h3>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">المدير المسؤول</label>
                        <input type="text" name="manager" value="<?php echo htmlspecialchars($distributor['manager'] ?? ''); ?>" 
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">سنة التأسيس</label>
                        <input type="text" name="established" value="<?php echo htmlspecialchars($distributor['established'] ?? ''); ?>" 
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">التقييم</label>
                        <input type="number" step="0.1" min="0" max="5" name="rating" value="<?php echo htmlspecialchars($distributor['rating'] ?? ''); ?>" 
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">عدد التقييمات</label>
                        <input type="number" min="0" name="reviews" value="<?php echo htmlspecialchars($distributor['reviews'] ?? ''); ?>" 
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">معتمد</label>
                        <div class="flex items-center">
                            <input type="checkbox" name="certified" value="1" 
                                   <?php echo ($distributor['certified'] ?? 0) ? 'checked' : ''; ?> 
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label class="mr-2 text-sm text-gray-700">موزع معتمد</label>
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">مميز</label>
                        <div class="flex items-center">
                            <input type="checkbox" name="featured" value="1" 
                                   <?php echo ($distributor['featured'] ?? 0) ? 'checked' : ''; ?> 
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label class="mr-2 text-sm text-gray-700">موزع مميز</label>
                        </div>
                    </div>
                    
                    <!-- Logo and Status -->
                    <div class="md:col-span-2 mt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">الشعار والحالة</h3>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">رابط الشعار</label>
                        <input type="url" name="logo" value="<?php echo htmlspecialchars($distributor['logo'] ?? ''); ?>" 
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">رابط الصورة</label>
                        <input type="url" name="image" value="<?php echo htmlspecialchars($distributor['image'] ?? ''); ?>" 
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                        <div class="flex items-center">
                            <input type="checkbox" name="is_active" value="1" 
                                   <?php echo ($distributor['is_active'] ?? 1) ? 'checked' : ''; ?> 
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label class="mr-2 text-sm text-gray-700">نشط</label>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 mt-6">
                    <a href="?action=list" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 ml-3">
                        إلغاء
                    </a>
                    <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                        <?php echo $action === 'add' ? 'إضافة الموزع' : 'تحديث الموزع'; ?>
                    </button>
                </div>
            </form>
        </div>
    <?php else: ?>
        <!-- Distributors List -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-900">قائمة الموزعين</h2>
                    <a href="?action=add" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                        إضافة موزع جديد
                    </a>
                </div>
                
                <!-- Filters -->
                <form method="GET" class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-4">
                    <input type="hidden" name="action" value="list">
                    <input type="text" name="search" value="<?php echo htmlspecialchars($search ?? ''); ?>" 
                           placeholder="البحث في الموزعين..." class="border rounded-md px-3 py-2">
                    
                    <select name="region_filter" class="border rounded-md px-3 py-2">
                        <option value="">جميع المناطق</option>
                        <?php foreach ($regions as $region): ?>
                            <option value="<?php echo htmlspecialchars($region['region']); ?>" 
                                    <?php echo ($regionFilter == $region['region']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($region['region']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    
                    <select name="distributor_type_filter" class="border rounded-md px-3 py-2">
                        <option value="">جميع التصنيفات</option>
                        <option value="رئيسي" <?php echo ($distributorTypeFilter ?? '') === 'رئيسي' ? 'selected' : ''; ?>>موزع رئيسي</option>
                        <option value="مميز" <?php echo ($distributorTypeFilter ?? '') === 'مميز' ? 'selected' : ''; ?>>موزع مميز</option>
                        <option value="عادي" <?php echo ($distributorTypeFilter ?? '') === 'عادي' ? 'selected' : ''; ?>>موزع عادي</option>
                    </select>
                    
                    <select name="status_filter" class="border rounded-md px-3 py-2">
                        <option value="">جميع الحالات</option>
                        <option value="1" <?php echo ($statusFilter === '1') ? 'selected' : ''; ?>>نشط</option>
                        <option value="0" <?php echo ($statusFilter === '0') ? 'selected' : ''; ?>>غير نشط</option>
                    </select>
                    
                    <button type="submit" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700">
                        تصفية
                    </button>
                </form>
                
                <!-- Statistics Section -->
                <div class="grid grid-cols-2 md:grid-cols-6 gap-4 mb-6">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-blue-600"><?php echo $totalDistributors; ?></div>
                        <div class="text-sm text-blue-800">إجمالي الموزعين</div>
                    </div>
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-green-600"><?php echo $activeDistributors; ?></div>
                        <div class="text-sm text-green-800">موزع نشط</div>
                    </div>
                    <div class="bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-purple-600"><?php echo $mainDistributors; ?></div>
                        <div class="text-sm text-purple-800">موزع رئيسي</div>
                    </div>
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-yellow-600"><?php echo $featuredDistributors; ?></div>
                        <div class="text-sm text-yellow-800">موزع مميز</div>
                    </div>
                    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-gray-600"><?php echo $regularDistributors; ?></div>
                        <div class="text-sm text-gray-800">موزع عادي</div>
                    </div>
                    <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-indigo-600"><?php echo $totalRegions; ?></div>
                        <div class="text-sm text-indigo-800">منطقة مغطاة</div>
                    </div>
                </div>
            </div>
            
            <!-- Distributors Table -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الاسم</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المنطقة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التصنيف</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المدير</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الهاتف</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                            </tr>
                        </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php if (!empty($distributors)): ?>
                            <?php foreach ($distributors as $distributor): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <?php if (!empty($distributor['logo'])): ?>
                                                <img src="<?php echo htmlspecialchars($distributor['logo']); ?>" 
                                                     alt="<?php echo htmlspecialchars($distributor['name']); ?>"
                                                     class="h-10 w-10 rounded-full object-cover ml-3">
                                            <?php else: ?>
                                                <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center ml-3">
                                                    <i class="fas fa-store text-gray-600"></i>
                                                </div>
                                            <?php endif; ?>
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">
                                                    <?php echo htmlspecialchars($distributor['name']); ?>
                                                </div>
                                                <?php if (!empty($distributor['address'])): ?>
                                                    <div class="text-sm text-gray-500">
                                                        <?php echo htmlspecialchars(substr($distributor['address'], 0, 50)) . (strlen($distributor['address']) > 50 ? '...' : ''); ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo htmlspecialchars($distributor['region']); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                         <?php 
                                         $distributorType = $distributor['distributor_type'] ?? 'عادي';
                                         $badgeClass = 'bg-gray-100 text-gray-800';
                                         if ($distributorType === 'رئيسي') {
                                             $badgeClass = 'bg-blue-100 text-blue-800 border border-blue-300';
                                         } elseif ($distributorType === 'مميز') {
                                             $badgeClass = 'bg-yellow-100 text-yellow-800 border border-yellow-300';
                                         }
                                         ?>
                                         <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $badgeClass; ?>">
                                             موزع <?php echo htmlspecialchars($distributorType); ?>
                                         </span>
                                     </td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                         <?php if (!empty($distributor['manager'])): ?>
                                             <?php echo htmlspecialchars($distributor['manager']); ?>
                                         <?php else: ?>
                                             <span class="text-gray-400">غير محدد</span>
                                         <?php endif; ?>
                                     </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php if (!empty($distributor['phone'])): ?>
                                            <div><?php echo htmlspecialchars($distributor['phone']); ?></div>
                                        <?php endif; ?>
                                        <?php if (!empty($distributor['email'])): ?>
                                            <div class="text-gray-500"><?php echo htmlspecialchars($distributor['email']); ?></div>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php if ($distributor['is_active']): ?>
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                                نشط
                                            </span>
                                        <?php else: ?>
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-pink-100 text-pink-800">
                                                غير نشط
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <a href="?action=edit&id=<?php echo $distributor['id']; ?>" 
                                               class="text-blue-600 hover:text-blue-900 ml-2">تعديل</a>
                                            <a href="?action=delete&id=<?php echo $distributor['id']; ?>" 
                                               onclick="return confirmDelete('هل أنت متأكد من حذف هذا الموزع؟')" 
                                               class="text-red-600 hover:text-red-900">حذف</a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="9" class="px-6 py-4 text-center text-gray-500">
                                    لا توجد موزعون
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    <?php endif; ?>
<?php endif; ?>

<?php if ($activeTab === 'content'): ?>
    <!-- Content Management -->
    <div class="bg-white rounded-lg shadow">
        <div class="p-6 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">إدارة محتوى صفحة الموزعين</h2>
            <p class="text-gray-600 mt-1">تحكم في النصوص والعناوين المعروضة في صفحة الموزعين العامة</p>
        </div>
        
        <form method="POST" class="p-6">
            <input type="hidden" name="action" value="update_content">
            
            <!-- Hero Section -->
            <div class="mb-8">
                <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-star text-yellow-500 ml-2"></i>
                    قسم البانر الرئيسي
                </h3>
                <div class="grid grid-cols-1 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">العنوان الرئيسي</label>
                        <input type="text" name="hero_title" value="<?php echo htmlspecialchars($content['distributors_hero_title'] ?? 'شبكة الموزعين'); ?>" 
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">العنوان الفرعي</label>
                        <textarea name="hero_subtitle" rows="2" 
                                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"><?php echo htmlspecialchars($content['distributors_hero_subtitle'] ?? 'اعثر على أقرب موزع لمنتجاتنا في منطقتك'); ?></textarea>
                    </div>
                </div>
            </div>
            
            <!-- Map Section -->
            <div class="mb-8">
                <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-map text-green-500 ml-2"></i>
                    قسم الخريطة
                </h3>
                <div class="grid grid-cols-1 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">عنوان الخريطة</label>
                        <input type="text" name="map_title" value="<?php echo htmlspecialchars($content['distributors_map_title'] ?? 'خريطة الموزعين'); ?>" 
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">وصف الخريطة</label>
                        <textarea name="map_subtitle" rows="2" 
                                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"><?php echo htmlspecialchars($content['distributors_map_subtitle'] ?? 'انقر على العلامات لعرض تفاصيل الموزع'); ?></textarea>
                    </div>
                </div>
            </div>
            
            <!-- Partnership Section -->
            <div class="mb-8">
                <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-handshake text-blue-500 ml-2"></i>
                    قسم الشراكة
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">عنوان الشراكة</label>
                        <input type="text" name="partnership_title" value="<?php echo htmlspecialchars($content['distributors_partnership_title'] ?? 'هل تريد أن تصبح موزعاً لمنتجاتنا؟'); ?>" 
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">وصف الشراكة</label>
                        <textarea name="partnership_subtitle" rows="2" 
                                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"><?php echo htmlspecialchars($content['distributors_partnership_subtitle'] ?? 'انضم إلى شبكة موزعينا واستفد من فرص النمو والربح'); ?></textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">نص الزر الأول</label>
                        <input type="text" name="partnership_button1" value="<?php echo htmlspecialchars($content['distributors_partnership_button1'] ?? 'طلب شراكة'); ?>" 
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">نص الزر الثاني</label>
                        <input type="text" name="partnership_button2" value="<?php echo htmlspecialchars($content['distributors_partnership_button2'] ?? 'تواصل معنا'); ?>" 
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                </div>
            </div>
            
            <!-- Statistics Section -->
            <div class="mb-8">
                <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-chart-bar text-purple-500 ml-2"></i>
                    قسم الإحصائيات
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">عنوان الإحصائيات</label>
                        <input type="text" name="stats_title" value="<?php echo htmlspecialchars($content['distributors_stats_title'] ?? 'شبكة موزعين قوية'); ?>" 
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">وصف الإحصائيات</label>
                        <textarea name="stats_subtitle" rows="2" 
                                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"><?php echo htmlspecialchars($content['distributors_stats_subtitle'] ?? 'نفخر بشبكة موزعينا الواسعة التي تغطي جميع أنحاء المملكة'); ?></textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">تسمية عدد الموزعين</label>
                        <input type="text" name="stats_distributors_label" value="<?php echo htmlspecialchars($content['distributors_stats_distributors_label'] ?? 'موزع معتمد'); ?>" 
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">تسمية عدد المدن</label>
                        <input type="text" name="stats_cities_label" value="<?php echo htmlspecialchars($content['distributors_stats_cities_label'] ?? 'مدينة ومنطقة'); ?>" 
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">تسمية الدعم</label>
                        <input type="text" name="stats_support_label" value="<?php echo htmlspecialchars($content['distributors_stats_support_label'] ?? 'دعم متواصل'); ?>" 
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">تسمية الجودة</label>
                        <input type="text" name="stats_quality_label" value="<?php echo htmlspecialchars($content['distributors_stats_quality_label'] ?? 'ضمان الجودة'); ?>" 
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                </div>
            </div>
            
            <div class="flex justify-end">
                <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700">
                    حفظ التغييرات
                </button>
            </div>
        </form>
    </div>
<?php endif; ?>

<?php
// Get the content for tabs
if ($activeTab === 'list') {
    $tabs['list']['content'] = ob_get_clean();
    $tabs['content']['content'] = '';
} else {
    $tabs['content']['content'] = ob_get_clean();
    $tabs['list']['content'] = '';
}

// Display tabs
createTabsWithContent($tabs, $activeTab);

/**
 * Helper function to create tabs with content
 */
function createTabsWithContent($tabs, $activeTab) {
    ?>
    <div class="bg-white rounded-lg shadow-md" x-data="{ activeTab: '<?php echo $activeTab; ?>' }">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-6">
                <?php foreach ($tabs as $tabId => $tab): ?>
                    <a href="?action=<?php echo $tabId; ?>" 
                       class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm <?php echo $activeTab === $tabId ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?>">
                        <?php if (isset($tab['icon'])): ?>
                            <i class="<?php echo $tab['icon']; ?> ml-2"></i>
                        <?php endif; ?>
                        <?php echo htmlspecialchars($tab['title']); ?>
                    </a>
                <?php endforeach; ?>
            </nav>
        </div>
        
        <div class="p-6">
            <?php echo $tabs[$activeTab]['content']; ?>
        </div>
    </div>
    <?php
}

endLayout();
?>