<?php
/**
 * نظام تحديد معدل الطلبات
 * Rate Limiting System
 */

class RateLimiter {
    private $database;
    private $defaultLimits = [
        'login' => ['requests' => 5, 'window' => 900], // 5 محاولات في 15 دقيقة
        'register' => ['requests' => 3, 'window' => 3600], // 3 محاولات في ساعة
        'contact' => ['requests' => 10, 'window' => 3600], // 10 رسائل في ساعة
        'api' => ['requests' => 100, 'window' => 3600], // 100 طلب API في ساعة
        'search' => ['requests' => 50, 'window' => 300], // 50 بحث في 5 دقائق
        'review' => ['requests' => 5, 'window' => 3600], // 5 تقييمات في ساعة
        'password_reset' => ['requests' => 3, 'window' => 3600] // 3 طلبات إعادة تعيين في ساعة
    ];
    
    public function __construct() {
        global $database;
        $this->database = $database;
        $this->createRateLimitTable();
    }
    
    /**
     * إنشاء جدول تحديد المعدل
     */
    private function createRateLimitTable() {
        $this->database->query("
            CREATE TABLE IF NOT EXISTS rate_limits (
                id INT PRIMARY KEY AUTO_INCREMENT,
                identifier VARCHAR(255) NOT NULL,
                action VARCHAR(100) NOT NULL,
                requests_count INT DEFAULT 1,
                window_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_request TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                ip_address VARCHAR(45),
                user_agent TEXT,
                INDEX idx_identifier_action (identifier, action),
                INDEX idx_window_start (window_start)
            )
        ");
    }
    
    /**
     * التحقق من تجاوز الحد المسموح
     */
    public function isLimitExceeded($action, $identifier = null, $customLimits = null) {
        $identifier = $identifier ?: $this->getIdentifier();
        $limits = $customLimits ?: ($this->defaultLimits[$action] ?? ['requests' => 60, 'window' => 3600]);
        
        // تنظيف السجلات القديمة
        $this->cleanOldRecords($action, $identifier, $limits['window']);
        
        // الحصول على السجل الحالي
        $record = $this->database->fetch("
            SELECT * FROM rate_limits 
            WHERE identifier = :identifier AND action = :action
            AND window_start > DATE_SUB(NOW(), INTERVAL :window SECOND)
        ", [
            'identifier' => $identifier,
            'action' => $action,
            'window' => $limits['window']
        ]);
        
        if (!$record) {
            // إنشاء سجل جديد
            $this->createNewRecord($action, $identifier);
            return false;
        }
        
        // التحقق من تجاوز الحد
        if ($record['requests_count'] >= $limits['requests']) {
            return true;
        }
        
        // تحديث عدد الطلبات
        $this->updateRequestCount($record['id']);
        return false;
    }
    
    /**
     * تسجيل محاولة جديدة
     */
    public function recordAttempt($action, $identifier = null) {
        $identifier = $identifier ?: $this->getIdentifier();
        
        $record = $this->database->fetch("
            SELECT * FROM rate_limits 
            WHERE identifier = :identifier AND action = :action
            ORDER BY window_start DESC LIMIT 1
        ", [
            'identifier' => $identifier,
            'action' => $action
        ]);
        
        if ($record) {
            $this->updateRequestCount($record['id']);
        } else {
            $this->createNewRecord($action, $identifier);
        }
    }
    
    /**
     * الحصول على معرف المستخدم/IP
     */
    private function getIdentifier() {
        // استخدام معرف المستخدم إذا كان مسجل دخول
        if (isLoggedIn()) {
            return 'user_' . getCurrentUserId();
        }
        
        // استخدام عنوان IP
        return 'ip_' . $this->getClientIP();
    }
    
    /**
     * الحصول على عنوان IP الحقيقي
     */
    private function getClientIP() {
        $ipKeys = ['HTTP_CF_CONNECTING_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = explode(',', $ip)[0];
                }
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * إنشاء سجل جديد
     */
    private function createNewRecord($action, $identifier) {
        $this->database->insert('rate_limits', [
            'identifier' => $identifier,
            'action' => $action,
            'requests_count' => 1,
            'ip_address' => $this->getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
    }
    
    /**
     * تحديث عدد الطلبات
     */
    private function updateRequestCount($recordId) {
        $this->database->query("
            UPDATE rate_limits 
            SET requests_count = requests_count + 1,
                last_request = NOW()
            WHERE id = :id
        ", ['id' => $recordId]);
    }
    
    /**
     * تنظيف السجلات القديمة
     */
    private function cleanOldRecords($action, $identifier, $window) {
        $this->database->delete(
            'rate_limits',
            'identifier = :identifier AND action = :action AND window_start < DATE_SUB(NOW(), INTERVAL :window SECOND)',
            [
                'identifier' => $identifier,
                'action' => $action,
                'window' => $window
            ]
        );
    }
    
    /**
     * الحصول على معلومات الحد الحالي
     */
    public function getLimitInfo($action, $identifier = null) {
        $identifier = $identifier ?: $this->getIdentifier();
        $limits = $this->defaultLimits[$action] ?? ['requests' => 60, 'window' => 3600];
        
        $record = $this->database->fetch("
            SELECT * FROM rate_limits 
            WHERE identifier = :identifier AND action = :action
            AND window_start > DATE_SUB(NOW(), INTERVAL :window SECOND)
        ", [
            'identifier' => $identifier,
            'action' => $action,
            'window' => $limits['window']
        ]);
        
        $remaining = $limits['requests'];
        $resetTime = time() + $limits['window'];
        
        if ($record) {
            $remaining = max(0, $limits['requests'] - $record['requests_count']);
            $resetTime = strtotime($record['window_start']) + $limits['window'];
        }
        
        return [
            'limit' => $limits['requests'],
            'remaining' => $remaining,
            'reset_time' => $resetTime,
            'window' => $limits['window']
        ];
    }
    
    /**
     * إعادة تعيين الحد لمعرف معين
     */
    public function resetLimit($action, $identifier = null) {
        $identifier = $identifier ?: $this->getIdentifier();
        
        return $this->database->delete(
            'rate_limits',
            'identifier = :identifier AND action = :action',
            [
                'identifier' => $identifier,
                'action' => $action
            ]
        );
    }
    
    /**
     * حظر مؤقت للمعرف
     */
    public function temporaryBan($identifier, $duration = 3600) {
        $this->database->insert('rate_limits', [
            'identifier' => $identifier,
            'action' => 'banned',
            'requests_count' => 999999,
            'window_start' => date('Y-m-d H:i:s', time() - $duration + 1),
            'ip_address' => $this->getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
    }
    
    /**
     * التحقق من الحظر
     */
    public function isBanned($identifier = null) {
        $identifier = $identifier ?: $this->getIdentifier();
        
        $record = $this->database->fetch("
            SELECT * FROM rate_limits 
            WHERE identifier = :identifier AND action = 'banned'
            AND window_start > DATE_SUB(NOW(), INTERVAL 1 HOUR)
        ", ['identifier' => $identifier]);
        
        return $record !== null;
    }
    
    /**
     * تنظيف جميع السجلات القديمة
     */
    public function cleanup($olderThanHours = 24) {
        return $this->database->delete(
            'rate_limits',
            'window_start < DATE_SUB(NOW(), INTERVAL :hours HOUR)',
            ['hours' => $olderThanHours]
        );
    }
}

// إنشاء مثيل عام
$rateLimiter = new RateLimiter();

/**
 * دوال مساعدة للاستخدام السريع
 */

/**
 * التحقق من تجاوز الحد
 */
function isRateLimited($action, $identifier = null) {
    global $rateLimiter;
    return $rateLimiter->isLimitExceeded($action, $identifier);
}

/**
 * تسجيل محاولة
 */
function recordRateLimit($action, $identifier = null) {
    global $rateLimiter;
    $rateLimiter->recordAttempt($action, $identifier);
}

/**
 * التحقق من الحد أو إظهار خطأ
 */
function requireRateLimit($action, $errorMessage = null, $identifier = null) {
    global $rateLimiter;
    
    if ($rateLimiter->isLimitExceeded($action, $identifier)) {
        $limitInfo = $rateLimiter->getLimitInfo($action, $identifier);
        $resetTime = date('H:i:s', $limitInfo['reset_time']);
        
        $defaultMessage = "تم تجاوز الحد المسموح. يرجى المحاولة مرة أخرى في {$resetTime}";
        $message = $errorMessage ?: $defaultMessage;
        
        if (isAjaxRequest()) {
            header('Content-Type: application/json');
            http_response_code(429);
            echo json_encode([
                'success' => false,
                'error' => $message,
                'code' => 'RATE_LIMIT_EXCEEDED',
                'limit_info' => $limitInfo
            ]);
        } else {
            http_response_code(429);
            die($message);
        }
        exit;
    }
    
    // تسجيل المحاولة
    $rateLimiter->recordAttempt($action, $identifier);
}

/**
 * Middleware للتحقق من Rate Limiting
 */
function rateLimitMiddleware($action = 'api') {
    requireRateLimit($action);
}

/**
 * إضافة Headers للـ Rate Limiting
 */
function addRateLimitHeaders($action) {
    global $rateLimiter;
    $limitInfo = $rateLimiter->getLimitInfo($action);
    
    header('X-RateLimit-Limit: ' . $limitInfo['limit']);
    header('X-RateLimit-Remaining: ' . $limitInfo['remaining']);
    header('X-RateLimit-Reset: ' . $limitInfo['reset_time']);
}

?>