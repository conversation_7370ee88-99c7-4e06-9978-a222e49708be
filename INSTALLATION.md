# 🛠️ دليل التثبيت - Green Line E-commerce

دليل شامل لتثبيت وإعداد منصة Green Line للتجارة الإلكترونية

## 📋 المتطلبات الأساسية

### متطلبات الخادم
- **نظام التشغيل:** Linux (Ubuntu 20.04+ مفضل) أو Windows Server
- **خادم الويب:** Apache 2.4+ أو Nginx 1.18+
- **PHP:** 7.4 أو أحدث (PHP 8.1+ مُوصى به)
- **قاعدة البيانات:** MySQL 5.7+ أو MariaDB 10.3+
- **Redis:** 5.0+ (اختياري للتخزين المؤقت)
- **Composer:** أحدث إصدار

### امتدادات PHP المطلوبة
```bash
# امتدادات أساسية
php-mysql
php-pdo
php-mbstring
php-json
php-curl
php-openssl
php-zip
php-xml
php-gd

# امتدادات اختيارية
php-redis
php-imagick
php-intl
```

### متطلبات الأجهزة
- **الذاكرة:** 2GB RAM كحد أدنى (4GB+ مُوصى به)
- **المعالج:** 2 نواة كحد أدنى
- **التخزين:** 10GB مساحة فارغة كحد أدنى
- **الشبكة:** اتصال إنترنت مستقر

## 🚀 التثبيت خطوة بخطوة

### الخطوة 1: إعداد البيئة

#### على Ubuntu/Debian:
```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# تثبيت Apache و PHP
sudo apt install apache2 php php-mysql php-mbstring php-json php-curl php-openssl php-zip php-xml php-gd -y

# تثبيت MySQL
sudo apt install mysql-server -y

# تثبيت Redis (اختياري)
sudo apt install redis-server -y

# تثبيت Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
```

#### على CentOS/RHEL:
```bash
# تحديث النظام
sudo yum update -y

# تثبيت Apache و PHP
sudo yum install httpd php php-mysql php-mbstring php-json php-curl php-openssl php-zip php-xml php-gd -y

# تثبيت MySQL
sudo yum install mysql-server -y

# تثبيت Redis (اختياري)
sudo yum install redis -y

# تثبيت Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
```

#### على Windows (XAMPP/WAMP):
1. تحميل وتثبيت XAMPP من [الموقع الرسمي](https://www.apachefriends.org/)
2. تفعيل Apache و MySQL
3. تحميل وتثبيت Composer من [الموقع الرسمي](https://getcomposer.org/)
4. تحميل وتثبيت Redis (اختياري)

### الخطوة 2: تحميل المشروع

```bash
# الانتقال إلى مجلد الويب
cd /var/www/html  # Linux
# أو
cd C:\xampp\htdocs  # Windows

# تحميل المشروع
git clone https://github.com/greenline/ecommerce.git greenline_php
cd greenline_php

# أو تحميل الملف المضغوط
wget https://github.com/greenline/archive/main.zip
unzip main.zip
mv greenline greenline_php
cd greenline_php
```

### الخطوة 3: تثبيت التبعيات

```bash
# تثبيت تبعيات PHP
composer install --no-dev --optimize-autoloader

# إذا كنت في بيئة التطوير
composer install
```

### الخطوة 4: إعداد قاعدة البيانات

#### إنشاء قاعدة البيانات:
```bash
# الدخول إلى MySQL
mysql -u root -p

# إنشاء قاعدة البيانات
CREATE DATABASE greenline_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# إنشاء مستخدم جديد (مُوصى به)
CREATE USER 'greenline_user'@'localhost' IDENTIFIED BY 'strong_password_here';
GRANT ALL PRIVILEGES ON greenline_db.* TO 'greenline_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

#### استيراد هيكل قاعدة البيانات:
```bash
# استيراد الجداول الأساسية
mysql -u greenline_user -p greenline_db < sql/database_schema.sql

# استيراد نظام الصلاحيات المتقدم
mysql -u greenline_user -p greenline_db < sql/permissions_schema.sql
```

### الخطوة 5: إعداد ملف البيئة

```bash
# نسخ ملف الإعدادات
cp .env.example .env

# تحرير الإعدادات
nano .env  # Linux
# أو
notepad .env  # Windows
```

#### إعدادات قاعدة البيانات الأساسية:
```env
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=greenline_db
DB_USERNAME=greenline_user
DB_PASSWORD=strong_password_here
```

#### إعدادات الأمان:
```bash
# إنشاء مفتاح التطبيق
php -r "echo 'APP_KEY=base64:' . base64_encode(random_bytes(32)) . PHP_EOL;"

# إنشاء مفتاح JWT
php -r "echo 'JWT_SECRET=' . bin2hex(random_bytes(32)) . PHP_EOL;"
```

### الخطوة 6: إعداد الصلاحيات

#### على Linux:
```bash
# إعداد مالك الملفات
sudo chown -R www-data:www-data /var/www/html/greenline_php

# إعداد صلاحيات المجلدات
sudo chmod -R 755 /var/www/html/greenline_php
sudo chmod -R 775 storage/
sudo chmod -R 775 uploads/
sudo chmod 644 .env

# إنشاء المجلدات المطلوبة
mkdir -p storage/logs
mkdir -p storage/cache
mkdir -p storage/sessions
mkdir -p uploads/products
mkdir -p uploads/categories
```

#### على Windows:
```cmd
# إنشاء المجلدات المطلوبة
md storage\logs
md storage\cache
md storage\sessions
md uploads\products
md uploads\categories
```

### الخطوة 7: إعداد خادم الويب

#### Apache Configuration:
```apache
# إنشاء Virtual Host
sudo nano /etc/apache2/sites-available/greenline.conf

<VirtualHost *:80>
    ServerName greenline.local
    DocumentRoot /var/www/html/greenline_php
    
    <Directory /var/www/html/greenline_php>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/greenline_error.log
    CustomLog ${APACHE_LOG_DIR}/greenline_access.log combined
</VirtualHost>

# تفعيل الموقع
sudo a2ensite greenline.conf
sudo a2enmod rewrite
sudo systemctl restart apache2
```

#### Nginx Configuration:
```nginx
# إنشاء ملف الإعداد
sudo nano /etc/nginx/sites-available/greenline

server {
    listen 80;
    server_name greenline.local;
    root /var/www/html/greenline_php;
    index index.php index.html;
    
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }
    
    location ~ /\. {
        deny all;
    }
}

# تفعيل الموقع
sudo ln -s /etc/nginx/sites-available/greenline /etc/nginx/sites-enabled/
sudo systemctl restart nginx
```

### الخطوة 8: إعداد Redis (اختياري)

```bash
# تفعيل وبدء Redis
sudo systemctl enable redis-server
sudo systemctl start redis-server

# اختبار Redis
redis-cli ping
# يجب أن يرجع: PONG
```

#### إعدادات Redis في .env:
```env
# Redis Configuration
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DATABASE=0
CACHE_DRIVER=redis
```

### الخطوة 9: إعداد البريد الإلكتروني

#### Gmail SMTP:
```env
# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Green Line"
```

### الخطوة 10: تشغيل الاختبارات

```bash
# إعداد بيانات الاختبار
php tests/run_tests.php --setup

# تشغيل جميع الاختبارات
php tests/run_tests.php

# أو باستخدام Composer
composer run test
```

## 🔧 التحقق من التثبيت

### 1. اختبار الاتصال بقاعدة البيانات:
```bash
php -r "require 'includes/database.php'; echo 'Database connection: ' . (\$database->isConnected() ? 'OK' : 'Failed') . PHP_EOL;"
```

### 2. اختبار Redis (إذا كان مُفعل):
```bash
php -r "require 'includes/redis_cache.php'; \$redis = new RedisCache(); echo 'Redis connection: ' . (\$redis->isConnected() ? 'OK' : 'Failed') . PHP_EOL;"
```

### 3. اختبار الصلاحيات:
```bash
php -r "require 'includes/permissions.php'; \$pm = new PermissionManager(); echo 'Permissions system: OK' . PHP_EOL;"
```

### 4. فتح الموقع في المتصفح:
```
http://localhost/greenline_php
# أو
http://greenline.local
```

## 🛡️ إعدادات الأمان الإضافية

### 1. إعداد HTTPS:
```bash
# تثبيت Let's Encrypt
sudo apt install certbot python3-certbot-apache -y

# الحصول على شهادة SSL
sudo certbot --apache -d yourdomain.com
```

### 2. إعداد Firewall:
```bash
# تفعيل UFW
sudo ufw enable

# السماح بالمنافذ الأساسية
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
```

### 3. تأمين MySQL:
```bash
sudo mysql_secure_installation
```

### 4. إعداد النسخ الاحتياطي:
```bash
# إنشاء سكريبت النسخ الاحتياطي
sudo nano /usr/local/bin/greenline_backup.sh

#!/bin/bash
DATE=$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="/var/backups/greenline"
mkdir -p $BACKUP_DIR

# نسخ احتياطي لقاعدة البيانات
mysqldump -u greenline_user -p greenline_db > $BACKUP_DIR/db_$DATE.sql

# نسخ احتياطي للملفات
tar -czf $BACKUP_DIR/files_$DATE.tar.gz /var/www/html/greenline_php

# حذف النسخ القديمة (أكثر من 30 يوم)
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

# إعداد Cron Job
sudo chmod +x /usr/local/bin/greenline_backup.sh
sudo crontab -e
# إضافة السطر التالي للنسخ الاحتياطي اليومي في الساعة 2 صباحاً
0 2 * * * /usr/local/bin/greenline_backup.sh
```

## 🚨 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### 1. خطأ في الاتصال بقاعدة البيانات:
```bash
# التحقق من حالة MySQL
sudo systemctl status mysql

# إعادة تشغيل MySQL
sudo systemctl restart mysql

# التحقق من إعدادات .env
```

#### 2. مشاكل الصلاحيات:
```bash
# إعادة تعيين صلاحيات الملفات
sudo chown -R www-data:www-data /var/www/html/greenline_php
sudo chmod -R 755 /var/www/html/greenline_php
sudo chmod -R 775 storage/ uploads/
```

#### 3. خطأ في Composer:
```bash
# تحديث Composer
composer self-update

# إعادة تثبيت التبعيات
rm -rf vendor/
composer install
```

#### 4. مشاكل Redis:
```bash
# التحقق من حالة Redis
sudo systemctl status redis-server

# إعادة تشغيل Redis
sudo systemctl restart redis-server

# اختبار الاتصال
redis-cli ping
```

### سجلات الأخطاء:
```bash
# سجلات Apache
sudo tail -f /var/log/apache2/error.log

# سجلات PHP
sudo tail -f /var/log/php7.4-fpm.log

# سجلات التطبيق
tail -f storage/logs/application.log

# سجلات MySQL
sudo tail -f /var/log/mysql/error.log
```

## 📈 تحسين الأداء

### 1. تحسين PHP:
```ini
# في php.ini
memory_limit = 256M
max_execution_time = 300
upload_max_filesize = 50M
post_max_size = 50M
opcache.enable = 1
opcache.memory_consumption = 128
```

### 2. تحسين MySQL:
```ini
# في my.cnf
[mysqld]
innodb_buffer_pool_size = 1G
query_cache_size = 64M
query_cache_type = 1
max_connections = 200
```

### 3. تحسين Redis:
```ini
# في redis.conf
maxmemory 512mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

## 🔄 التحديث

### تحديث المشروع:
```bash
# إنشاء نسخة احتياطية
cp -r /var/www/html/greenline_php /var/backups/greenline_backup_$(date +%Y%m%d)

# تحديث الكود
git pull origin main

# تحديث التبعيات
composer update

# تشغيل الاختبارات
php tests/run_tests.php
```

## 📞 الدعم

إذا واجهت أي مشاكل أثناء التثبيت:

- 📖 راجع [الوثائق الكاملة](README.md)
- 🐛 أبلغ عن الأخطاء في [GitHub Issues](https://github.com/greenline/ecommerce/issues)
- 💬 انضم إلى [مجتمع Discord](https://discord.gg/greenline)
- 📧 راسلنا على: <EMAIL>

---

**تم إعداد هذا الدليل بعناية لضمان تثبيت ناجح وآمن لمنصة Green Line** 🌱