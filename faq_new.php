<?php
require_once 'config/database.php';

// دالة للحصول على الأسئلة الشائعة مع التصفية والبحث
function getFAQsWithFilter($category = 'all', $search = '', $limit = null) {
    global $database;
    
    $sql = "SELECT * FROM FAQs WHERE is_active = 1";
    $params = [];
    
    // تصفية حسب التصنيف
    if ($category && $category !== 'all') {
        $sql .= " AND category = :category";
        $params['category'] = $category;
    }
    
    // البحث في السؤال والجواب
    if (!empty($search)) {
        $sql .= " AND (question LIKE :search OR answer LIKE :search)";
        $params['search'] = '%' . $search . '%';
    }
    
    $sql .= " ORDER BY sort_order ASC, id ASC";
    
    if ($limit) {
        $sql .= " LIMIT :limit";
        $params['limit'] = (int) $limit;
    }
    
    return $database->fetchAll($sql, $params);
}

// دالة للحصول على تصنيفات الأسئلة مع العدد
function getFAQCategoriesWithCount() {
    global $database;
    
    $categories = $database->fetchAll(
        "SELECT category as name, 
                category as slug,
                COUNT(*) as count 
         FROM FAQs 
         WHERE is_active = 1 AND category IS NOT NULL AND category != '' 
         GROUP BY category 
         ORDER BY category"
    );
    
    return $categories;
}

// دالة للحصول على إجمالي عدد الأسئلة
function getTotalFAQsCount() {
    global $database;
    
    $result = $database->fetch("SELECT COUNT(*) as total FROM FAQs WHERE is_active = 1");
    return $result['total'];
}

try {
    $database = new Database();
    
    // معالجة المعاملات
    $category = $_GET['category'] ?? 'all';
    $search = trim($_GET['search'] ?? '');
    
    // جلب البيانات
    $faqs = getFAQsWithFilter($category, $search);
    $categories = getFAQCategoriesWithCount();
    $totalCount = getTotalFAQsCount();
    
} catch (Exception $e) {
    die("خطأ في قاعدة البيانات: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الأسئلة الشائعة - Green Line</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .faq-header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 60px 0;
            text-align: center;
        }
        .search-section {
            background: white;
            padding: 30px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .category-filters {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }
        .category-btn {
            padding: 10px 20px;
            border: 2px solid #28a745;
            background: white;
            color: #28a745;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .category-btn:hover {
            background: #28a745;
            color: white;
            text-decoration: none;
        }
        .category-btn.active {
            background: #28a745;
            color: white;
        }
        .category-count {
            background: rgba(255,255,255,0.2);
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.85em;
        }
        .faq-item {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .faq-item:hover {
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        .faq-question {
            background: #f8f9fa;
            padding: 20px;
            cursor: pointer;
            border: none;
            width: 100%;
            text-align: right;
            font-size: 1.1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .faq-question:hover {
            background: #e9ecef;
        }
        .faq-answer {
            padding: 20px;
            background: white;
            line-height: 1.8;
            color: #555;
            border-top: 1px solid #eee;
        }
        .collapse-icon {
            transition: transform 0.3s ease;
            color: #28a745;
        }
        .collapsed .collapse-icon {
            transform: rotate(180deg);
        }
        .search-box {
            position: relative;
        }
        .search-box input {
            padding-right: 50px;
        }
        .search-box .search-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
        }
        .results-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        .no-results {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        .category-badge {
            background: #28a745;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            margin-bottom: 10px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="faq-header">
        <div class="container">
            <h1><i class="fas fa-question-circle me-3"></i>الأسئلة الشائعة</h1>
            <p class="lead">إجابات شاملة على جميع استفساراتكم حول منتجاتنا وخدماتنا</p>
        </div>
    </div>

    <!-- Search and Filter Section -->
    <div class="search-section">
        <div class="container">
            <!-- Search Box -->
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <form method="GET" action="">
                        <input type="hidden" name="category" value="<?= htmlspecialchars($category) ?>">
                        <div class="search-box">
                            <input type="text" 
                                   name="search" 
                                   value="<?= htmlspecialchars($search) ?>"
                                   placeholder="ابحث في الأسئلة الشائعة..."
                                   class="form-control form-control-lg">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                        <button type="submit" class="btn btn-success mt-2 w-100">
                            <i class="fas fa-search me-2"></i>بحث
                        </button>
                    </form>
                </div>
            </div>

            <!-- Category Filters -->
            <div class="category-filters">
                <a href="?<?= $search ? 'search=' . urlencode($search) : '' ?>" 
                   class="category-btn <?= $category === 'all' ? 'active' : '' ?>">
                    <i class="fas fa-list"></i>
                    جميع الأسئلة
                    <span class="category-count"><?= $totalCount ?></span>
                </a>
                
                <?php foreach ($categories as $cat): ?>
                    <a href="?category=<?= urlencode($cat['slug']) ?><?= $search ? '&search=' . urlencode($search) : '' ?>" 
                       class="category-btn <?= $category === $cat['slug'] ? 'active' : '' ?>">
                        <i class="fas fa-folder"></i>
                        <?= htmlspecialchars($cat['name']) ?>
                        <span class="category-count"><?= $cat['count'] ?></span>
                    </a>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <div class="container my-5">
        <!-- Results Info -->
        <?php if (!empty($search) || $category !== 'all'): ?>
        <div class="results-info">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <?php if (!empty($search)): ?>
                        <strong>نتائج البحث عن:</strong> "<?= htmlspecialchars($search) ?>"
                        <?php if ($category !== 'all'): ?>
                            <strong>في تصنيف:</strong> <?= htmlspecialchars($category) ?>
                        <?php endif; ?>
                    <?php elseif ($category !== 'all'): ?>
                        <strong>عرض أسئلة تصنيف:</strong> <?= htmlspecialchars($category) ?>
                    <?php endif; ?>
                </div>
                <div>
                    <strong><?= count($faqs) ?></strong> نتيجة
                </div>
            </div>
            <?php if (!empty($search) || $category !== 'all'): ?>
                <div class="mt-2">
                    <a href="?" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-times me-1"></i>مسح التصفية
                    </a>
                </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <!-- FAQ Items -->
        <?php if (!empty($faqs)): ?>
            <div class="accordion" id="faqAccordion">
                <?php foreach ($faqs as $index => $faq): ?>
                <div class="faq-item">
                    <button class="faq-question collapsed" 
                            type="button" 
                            data-bs-toggle="collapse" 
                            data-bs-target="#faq<?= $faq['id'] ?>" 
                            aria-expanded="false">
                        <div>
                            <div class="category-badge"><?= htmlspecialchars($faq['category']) ?></div>
                            <div><?= htmlspecialchars($faq['question']) ?></div>
                        </div>
                        <i class="fas fa-chevron-down collapse-icon"></i>
                    </button>
                    
                    <div id="faq<?= $faq['id'] ?>" 
                         class="collapse" 
                         data-bs-parent="#faqAccordion">
                        <div class="faq-answer">
                            <?= nl2br(htmlspecialchars($faq['answer'])) ?>
                            <div class="mt-3 text-muted small">
                                <i class="fas fa-clock me-1"></i>
                                تم الإنشاء: <?= date('d/m/Y', strtotime($faq['created_at'])) ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <!-- No Results -->
            <div class="no-results">
                <i class="fas fa-search fa-3x mb-3"></i>
                <h4>لم نجد أي نتائج</h4>
                <?php if (!empty($search)): ?>
                    <p>لم نجد أي أسئلة تحتوي على "<strong><?= htmlspecialchars($search) ?></strong>"</p>
                    <p>جرب البحث بكلمات مختلفة أو اختر تصنيفاً آخر</p>
                <?php else: ?>
                    <p>لا توجد أسئلة في هذا التصنيف حالياً</p>
                <?php endif; ?>
                <a href="?" class="btn btn-success mt-3">
                    <i class="fas fa-list me-2"></i>عرض جميع الأسئلة
                </a>
            </div>
        <?php endif; ?>

        <!-- Contact Section -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <h4>لم تجد إجابة لسؤالك؟</h4>
                        <p class="text-muted">فريق خدمة العملاء متاح لمساعدتك على مدار الساعة</p>
                        <div class="row">
                            <div class="col-md-4">
                                <i class="fas fa-phone fa-2x text-success mb-2"></i>
                                <h6>اتصل بنا</h6>
                                <p>920001234</p>
                            </div>
                            <div class="col-md-4">
                                <i class="fab fa-whatsapp fa-2x text-success mb-2"></i>
                                <h6>واتساب</h6>
                                <p>966501234567+</p>
                            </div>
                            <div class="col-md-4">
                                <i class="fas fa-envelope fa-2x text-success mb-2"></i>
                                <h6>البريد الإلكتروني</h6>
                                <p><EMAIL></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحسين تجربة البحث
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.querySelector('input[name="search"]');
            const searchForm = document.querySelector('form');
            
            // البحث التلقائي عند الكتابة (مع تأخير)
            let searchTimeout;
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    if (this.value.length >= 2 || this.value.length === 0) {
                        searchForm.submit();
                    }
                }, 1000);
            });
            
            // تحسين تجربة الأكورديون
            const collapseElements = document.querySelectorAll('.collapse');
            collapseElements.forEach(function(element) {
                element.addEventListener('show.bs.collapse', function() {
                    const button = document.querySelector(`[data-bs-target="#${element.id}"]`);
                    button.classList.remove('collapsed');
                });
                
                element.addEventListener('hide.bs.collapse', function() {
                    const button = document.querySelector(`[data-bs-target="#${element.id}"]`);
                    button.classList.add('collapsed');
                });
            });
        });
    </script>
</body>
</html>