<?php

/**
 * كلاس التحقق من صحة بيانات المنتجات
 * يوفر وظائف شاملة للتحقق من صحة البيانات
 */
class ProductValidator {
    
    private $errors = [];
    private $config;
    
    public function __construct() {
        $this->config = include __DIR__ . '/../config/products_config.php';
    }
    
    /**
     * التحقق من صحة بيانات المنتج
     */
    public function validateProduct($data) {
        $this->errors = [];
        
        // التحقق من الاسم
        $this->validateName($data['name'] ?? '');
        
        // التحقق من الوصف
        $this->validateDescription($data['description'] ?? '');
        
        // التحقق من السعر
        $this->validatePrice($data['price'] ?? '');
        
        // التحقق من الفئة
        $this->validateCategory($data['category_id'] ?? '');
        
        // التحقق من المميزات
        if (isset($data['features'])) {
            $this->validateFeatures($data['features']);
        }
        
        // التحقق من المواصفات
        if (isset($data['specifications'])) {
            $this->validateSpecifications($data['specifications']);
        }
        
        // التحقق من الصور
        if (isset($data['images'])) {
            $this->validateImages($data['images']);
        }
        
        // التحقق من الحالة
        $this->validateStatus($data['status'] ?? '');
        
        return empty($this->errors);
    }
    
    /**
     * التحقق من صحة اسم المنتج
     */
    private function validateName($name) {
        if (empty($name)) {
            $this->addError('name', $this->config['messages']['validation']['name_required']);
            return;
        }
        
        if (strlen($name) < 3) {
            $this->addError('name', 'اسم المنتج يجب أن يكون 3 أحرف على الأقل');
        }
        
        if (strlen($name) > 255) {
            $this->addError('name', 'اسم المنتج يجب أن يكون أقل من 255 حرف');
        }
        
        // التحقق من الأحرف المسموحة
        if (!preg_match('/^[\p{Arabic}\p{L}\p{N}\s\-_().]+$/u', $name)) {
            $this->addError('name', 'اسم المنتج يحتوي على أحرف غير مسموحة');
        }
    }
    
    /**
     * التحقق من صحة وصف المنتج
     */
    private function validateDescription($description) {
        if (empty($description)) {
            $this->addError('description', $this->config['messages']['validation']['description_required']);
            return;
        }
        
        if (strlen($description) < 10) {
            $this->addError('description', 'وصف المنتج يجب أن يكون 10 أحرف على الأقل');
        }
        
        if (strlen($description) > 5000) {
            $this->addError('description', 'وصف المنتج يجب أن يكون أقل من 5000 حرف');
        }
    }
    
    /**
     * التحقق من صحة سعر المنتج
     */
    private function validatePrice($price) {
        if (empty($price) && $price !== '0') {
            $this->addError('price', 'سعر المنتج مطلوب');
            return;
        }
        
        if (!is_numeric($price)) {
            $this->addError('price', $this->config['messages']['validation']['price_invalid']);
            return;
        }
        
        $price = floatval($price);
        
        if ($price < 0) {
            $this->addError('price', 'سعر المنتج يجب أن يكون موجباً');
        }
        
        if ($price > 999999.99) {
            $this->addError('price', 'سعر المنتج كبير جداً');
        }
    }
    
    /**
     * التحقق من صحة فئة المنتج
     */
    private function validateCategory($category_id) {
        if (empty($category_id)) {
            $this->addError('category', $this->config['messages']['validation']['category_required']);
            return;
        }
        
        if (!is_numeric($category_id)) {
            $this->addError('category', 'معرف الفئة يجب أن يكون رقماً');
        }
    }
    
    /**
     * التحقق من صحة مميزات المنتج
     */
    private function validateFeatures($features) {
        if (!is_array($features)) {
            $features = json_decode($features, true);
        }
        
        if (!is_array($features)) {
            $this->addError('features', 'تنسيق المميزات غير صحيح');
            return;
        }
        
        $max_features = $this->config['features']['max_features'];
        
        if (count($features) > $max_features) {
            $this->addError('features', "عدد المميزات يجب أن يكون أقل من {$max_features}");
        }
        
        foreach ($features as $index => $feature) {
            if (empty($feature)) {
                $this->addError('features', "الميزة رقم " . ($index + 1) . " فارغة");
            }
            
            if (strlen($feature) > 255) {
                $this->addError('features', "الميزة رقم " . ($index + 1) . " طويلة جداً");
            }
        }
    }
    
    /**
     * التحقق من صحة مواصفات المنتج
     */
    private function validateSpecifications($specifications) {
        if (!is_array($specifications)) {
            $specifications = json_decode($specifications, true);
        }
        
        if (!is_array($specifications)) {
            $this->addError('specifications', 'تنسيق المواصفات غير صحيح');
            return;
        }
        
        $max_specs = $this->config['specifications']['max_specifications'];
        
        if (count($specifications) > $max_specs) {
            $this->addError('specifications', "عدد المواصفات يجب أن يكون أقل من {$max_specs}");
        }
        
        foreach ($specifications as $key => $value) {
            if (empty($key)) {
                $this->addError('specifications', 'اسم المواصفة لا يمكن أن يكون فارغاً');
            }
            
            if (strlen($key) > 100) {
                $this->addError('specifications', 'اسم المواصفة طويل جداً');
            }
            
            if (strlen($value) > 500) {
                $this->addError('specifications', 'قيمة المواصفة طويلة جداً');
            }
        }
    }
    
    /**
     * التحقق من صحة صور المنتج
     */
    private function validateImages($images) {
        if (!is_array($images)) {
            $this->addError('images', 'تنسيق الصور غير صحيح');
            return;
        }
        
        $max_images = $this->config['general']['max_images_per_product'];
        
        if (count($images) > $max_images) {
            $this->addError('images', "عدد الصور يجب أن يكون أقل من {$max_images}");
        }
        
        $allowed_types = $this->config['images']['allowed_types'];
        $max_size = $this->config['images']['max_file_size'];
        
        foreach ($images as $index => $image) {
            if (isset($image['tmp_name']) && is_uploaded_file($image['tmp_name'])) {
                // التحقق من نوع الملف
                $file_info = pathinfo($image['name']);
                $extension = strtolower($file_info['extension'] ?? '');
                
                if (!in_array($extension, $allowed_types)) {
                    $this->addError('images', $this->config['messages']['validation']['image_invalid']);
                }
                
                // التحقق من حجم الملف
                if ($image['size'] > $max_size) {
                    $this->addError('images', $this->config['messages']['validation']['file_too_large']);
                }
                
                // التحقق من صحة الصورة
                if (!getimagesize($image['tmp_name'])) {
                    $this->addError('images', 'الملف ليس صورة صحيحة');
                }
            }
        }
    }
    
    /**
     * التحقق من صحة حالة المنتج
     */
    private function validateStatus($status) {
        $valid_statuses = ['active', 'inactive', 'draft', 'archived'];
        
        if (!in_array($status, $valid_statuses)) {
            $this->addError('status', 'حالة المنتج غير صحيحة');
        }
    }
    
    /**
     * التحقق من صحة البريد الإلكتروني
     */
    public function validateEmail($email) {
        if (empty($email)) {
            return false;
        }
        
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * التحقق من صحة رقم الهاتف
     */
    public function validatePhone($phone) {
        if (empty($phone)) {
            return false;
        }
        
        // إزالة المسافات والأحرف الخاصة
        $phone = preg_replace('/[^0-9+]/', '', $phone);
        
        // التحقق من التنسيق
        return preg_match('/^(\+?[0-9]{10,15})$/', $phone);
    }
    
    /**
     * التحقق من صحة URL
     */
    public function validateUrl($url) {
        if (empty($url)) {
            return false;
        }
        
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }
    
    /**
     * التحقق من صحة التاريخ
     */
    public function validateDate($date, $format = 'Y-m-d') {
        if (empty($date)) {
            return false;
        }
        
        $d = DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) === $date;
    }
    
    /**
     * تنظيف النص من HTML والأحرف الخاصة
     */
    public function sanitizeText($text) {
        // إزالة HTML tags
        $text = strip_tags($text);
        
        // تحويل الأحرف الخاصة
        $text = htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
        
        // إزالة المسافات الزائدة
        $text = trim($text);
        
        return $text;
    }
    
    /**
     * تنظيف HTML المسموح
     */
    public function sanitizeHtml($html, $allowed_tags = '<p><br><strong><em><ul><ol><li>') {
        return strip_tags($html, $allowed_tags);
    }
    
    /**
     * إضافة خطأ
     */
    private function addError($field, $message) {
        if (!isset($this->errors[$field])) {
            $this->errors[$field] = [];
        }
        
        $this->errors[$field][] = $message;
    }
    
    /**
     * الحصول على جميع الأخطاء
     */
    public function getErrors() {
        return $this->errors;
    }
    
    /**
     * الحصول على أخطاء حقل معين
     */
    public function getFieldErrors($field) {
        return $this->errors[$field] ?? [];
    }
    
    /**
     * التحقق من وجود أخطاء
     */
    public function hasErrors() {
        return !empty($this->errors);
    }
    
    /**
     * مسح الأخطاء
     */
    public function clearErrors() {
        $this->errors = [];
    }
    
    /**
     * الحصول على أول خطأ
     */
    public function getFirstError() {
        foreach ($this->errors as $field_errors) {
            if (!empty($field_errors)) {
                return $field_errors[0];
            }
        }
        
        return null;
    }
    
    /**
     * تحويل الأخطاء إلى نص
     */
    public function getErrorsAsString($separator = "\n") {
        $error_messages = [];
        
        foreach ($this->errors as $field => $field_errors) {
            foreach ($field_errors as $error) {
                $error_messages[] = $error;
            }
        }
        
        return implode($separator, $error_messages);
    }
}