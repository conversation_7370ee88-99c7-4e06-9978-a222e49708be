<?php
require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<h2>اعتماد التقييمات الموجودة</h2>";

// اعتماد جميع التقييمات غير المعتمدة
$result = $database->query("UPDATE reviews SET is_approved = 1 WHERE is_approved = 0 AND is_rejected = 0");

if ($result) {
    echo "<p style='color: green;'>تم اعتماد جميع التقييمات بنجاح!</p>";
    
    // تحديث تقييمات جميع المنتجات
    $products = $database->fetchAll("SELECT DISTINCT product_id FROM reviews WHERE is_approved = 1");
    
    foreach ($products as $product) {
        updateProductRating($product['product_id']);
    }
    
    echo "<p style='color: green;'>تم تحديث تقييمات جميع المنتجات!</p>";
} else {
    echo "<p style='color: red;'>حدث خطأ أثناء اعتماد التقييمات</p>";
}

// عرض إحصائيات
$total_reviews = $database->fetch("SELECT COUNT(*) as count FROM reviews")['count'];
$approved_reviews = $database->fetch("SELECT COUNT(*) as count FROM reviews WHERE is_approved = 1")['count'];

echo "<h3>الإحصائيات:</h3>";
echo "<p>إجمالي التقييمات: $total_reviews</p>";
echo "<p>التقييمات المعتمدة: $approved_reviews</p>";

echo "<br><a href='check_reviews.php'>عرض جميع التقييمات</a>";
echo "<br><a href='http://greenline_php.test/products/8'>عرض صفحة المنتج</a>";
?>