<?php
$product_id = $_GET['id'] ?? 0;
$product = getProduct($product_id);

if (!$product) {
    header('HTTP/1.0 404 Not Found');
    include '404.php';
    exit;
}

// التحقق من وجود تقييم سابق للمستخدم
$user_has_reviewed = false;
$user_review = null;
$debug_info = [];
$current_user = null;

if (isLoggedIn()) {
    $current_user = getCurrentUser();
    $debug_info['user_logged_in'] = true;
    $debug_info['user_id'] = $current_user['id'];
    $debug_info['product_id'] = $product_id;

    $user_has_reviewed = hasUserReviewed($product_id, $current_user['id']);
    $debug_info['user_has_reviewed'] = $user_has_reviewed;

    if ($user_has_reviewed) {
        $user_review = getUserReview($product_id, $current_user['id']);
        $debug_info['user_review'] = $user_review;
    }
} else {
    $debug_info['user_logged_in'] = false;
}

$pageTitle = $product['name'];
$page = 'products';
$additionalCSS = '
<link rel="stylesheet" href="' . SITE_URL . '/assets/css/products.css">
<style>
/* Tab Styles */
.product-tab-button {
    transition: all 0.3s ease;
    position: relative;
    outline: none;
}

.product-tab-button:hover {
    color: #059669 !important;
    border-color: #d1d5db !important;
}

.product-tab-button.active {
    color: #059669 !important;
    border-color: #059669 !important;
}

/* Tab Panes */
.product-tab-pane {
    display: none !important;
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
}

.product-tab-pane.active {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Force display for active tabs */
.product-tab-pane[style*="display: block"] {
    opacity: 1 !important;
    visibility: visible !important;
}

/* Specifications styling */
.specifications-list {
    max-height: none;
    overflow: visible;
}

.specification-item {
    padding: 8px 0;
    border-bottom: 1px solid #f3f4f6;
}

.specification-item:last-child {
    border-bottom: none;
}

/* Debug styles (remove in production) */
.debug-info {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
    margin-top: 10px;
    font-size: 12px;
    color: #6c757d;
}

/* Ensure proper spacing */
.product-tab-content {
    min-height: 300px;
}

/* Animation for tab switching */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.product-tab-pane.active {
    animation: fadeIn 0.3s ease-in-out;
}
</style>';

// تضمين المساعد
require_once __DIR__ . '/../includes/ProductHelper.php';
require_once __DIR__ . '/../admin/includes/inc_Pagination.php';

// إعدادات ترقيم الصفحات للتقييمات
$reviews_per_page = 5;
$current_page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;

// جلب البيانات المرتبطة
$product_images = getProductImages($product_id);

// حساب ترقيم الصفحات للتقييمات
$total_reviews = getProductReviewsCount($product_id);
$pagination = calculatePagination($total_reviews, $reviews_per_page, $current_page);

// جلب التقييمات مع الترقيم
$product_reviews = getProductReviews($product_id, $pagination['recordsPerPage'], $pagination['offset']);

$rating_stats = getProductRatingStats($product_id);
$related_products = getProducts([
    'category' => $product['category_id'],
    'exclude' => $product_id,
    'limit' => 4
]);

include 'header.php';
?>

<!-- Breadcrumb -->
<nav class="bg-gray-100 py-4">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <ol class="flex items-center space-x-2 space-x-reverse text-sm">
            <li><a href="<?php echo SITE_URL; ?>" class="text-gray-500 hover:text-primary">الرئيسية</a></li>
            <li class="text-gray-400">/</li>
            <li><a href="<?php echo SITE_URL; ?>/products" class="text-gray-500 hover:text-primary">المنتجات</a></li>
            <?php if ($product['category_name']): ?>
                <li class="text-gray-400">/</li>
                <li><a href="<?php echo SITE_URL; ?>/products?category=<?php echo $product['category_id']; ?>" class="text-gray-500 hover:text-primary"><?php echo htmlspecialchars($product['category_name']); ?></a></li>
            <?php endif; ?>
            <li class="text-gray-400">/</li>
            <li class="text-gray-900 font-medium"><?php echo htmlspecialchars($product['name']); ?></li>
        </ol>
    </div>
</nav>

<!-- Product Details -->
<section class="py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            
            <!-- Product Images -->
            <div class="space-y-4">
                <!-- Main Image -->
                <div class="aspect-w-1 aspect-h-1 bg-gray-200 rounded-lg overflow-hidden">
                    <img id="main-image" 
                         src="<?php echo $product['image'] ?: '/assets/images/default-product.svg'; ?>" 
                         alt="<?php echo htmlspecialchars($product['name']); ?>"
                         class="w-full h-96 object-cover cursor-zoom-in"
                         onclick="openImageModal(this.src)">
                </div>
                
                <!-- Thumbnail Images -->
                <?php if (!empty($product_images)): ?>
                    <div class="grid grid-cols-4 gap-4">
                        <div class="aspect-w-1 aspect-h-1 bg-gray-200 rounded-lg overflow-hidden cursor-pointer border-2 border-primary" 
                             onclick="changeMainImage('<?php echo $product['image']; ?>', this)">
                            <img src="<?php echo $product['image']; ?>" 
                                 alt="<?php echo htmlspecialchars($product['name']); ?>" 
                                 class="w-full h-20 object-cover">
                        </div>
                        <?php foreach ($product_images as $image): ?>
                            <div class="aspect-w-1 aspect-h-1 bg-gray-200 rounded-lg overflow-hidden cursor-pointer border-2 border-transparent hover:border-primary transition-colors" 
                                 onclick="changeMainImage('<?php echo $image['image_url']; ?>', this)">
                                <img src="<?php echo $image['image_url']; ?>" 
                                     alt="<?php echo htmlspecialchars($product['name']); ?>" 
                                     class="w-full h-20 object-cover">
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- Product Info -->
            <div class="space-y-6">
                <!-- Product Title and Category -->
                <div>
                    <?php if ($product['category_name']): ?>
                        <span class="inline-block bg-primary text-white px-3 py-1 rounded-full text-sm font-medium mb-3">
                            <?php echo htmlspecialchars($product['category_name']); ?>
                        </span>
                    <?php endif; ?>
                    
                    <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                        <?php echo htmlspecialchars($product['name']); ?>
                    </h1>
                    
                    <!-- Badges -->
                    <div class="flex items-center space-x-2 space-x-reverse mb-4">
                        <?php if (!empty($product['badge'])): ?>
                            <span class="bg-primary text-white px-3 py-1 rounded-full text-sm font-semibold">
                                <?php echo htmlspecialchars($product['badge']); ?>
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Rating -->
                <?php if ($rating_stats['total_reviews'] > 0): ?>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <div class="flex items-center">
                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                <svg class="w-5 h-5 <?php echo $i <= $rating_stats['average_rating'] ? 'text-yellow-400' : 'text-gray-300'; ?>" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                </svg>
                            <?php endfor; ?>
                        </div>
                        <span class="text-gray-600"><?php echo number_format($rating_stats['average_rating'], 2); ?> من 5</span>
                        <span class="text-gray-400">•</span>
                        <span class="text-gray-600"><?php echo $rating_stats['total_reviews']; ?> تقييم</span>
                    </div>
                <?php endif; ?>
                
                <!-- Description -->
                <?php if (!empty($product['description'])): ?>
                    <div class="prose prose-lg max-w-none">
                        <p class="text-gray-700 leading-relaxed">
                            <?php echo nl2br(htmlspecialchars($product['description'])); ?>
                        </p>
                    </div>
                <?php endif; ?>
                
                <!-- Specifications -->
                <?php if (!empty($product['specifications'])): ?>
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">المواصفات الفنية</h3>
                        <?php echo ProductHelper::renderFeatures($product['specifications'], 'w-5 h-5 text-blue-500 ml-2 flex-shrink-0', 'text-gray-700'); ?>
                    </div>
                <?php endif; ?>
                
                <!-- Features -->
                <?php if (!empty($product['features'])): ?>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">المميزات</h3>
                        <?php echo ProductHelper::renderFeatures($product['features']); ?>
                    </div>
                <?php endif; ?>
                
                <!-- Action Buttons -->
                <div class="space-y-4">
                    <div class="flex flex-col sm:flex-row gap-4">
                        <button onclick="requestQuote(<?php echo $product['id']; ?>)" 
                                class="flex-1 bg-primary hover:bg-secondary text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105">
                            طلب عرض سعر
                        </button>
                        
                        <button onclick="contactUs()" 
                                class="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300">
                            تواصل معنا
                        </button>
                    </div>
                    
                    <div class="flex items-center justify-center space-x-4 space-x-reverse text-sm text-gray-600">
                        <button onclick="toggleWishlist(<?php echo $product['id']; ?>)" class="flex items-center hover:text-primary transition-colors">
                            <svg class="w-5 h-5 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            </svg>
                            إضافة للمفضلة
                        </button>
                        
                        <button onclick="shareProduct()" class="flex items-center hover:text-primary transition-colors">
                            <svg class="w-5 h-5 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                            </svg>
                            مشاركة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Product Tabs -->
<section class="py-12 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Tab Navigation -->
        <div class="border-b border-gray-200 mb-8">
            <nav class="product-tabs-nav flex space-x-8 space-x-reverse">
                <button id="description-btn" class="product-tab-button active px-4 py-2 text-sm font-medium border-b-2 border-primary text-primary transition-all duration-300" data-tab="description">
                    الوصف التفصيلي
                </button>
                <button id="specifications-btn" class="product-tab-button px-4 py-2 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 transition-all duration-300" data-tab="specifications">
                    المواصفات
                </button>
                <button id="reviews-btn" class="product-tab-button px-4 py-2 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 transition-all duration-300" data-tab="reviews">
                    التقييمات (<?php echo $rating_stats['total_reviews']; ?>)
                </button>
            </nav>
        </div>
        
        <!-- Tab Content -->
        <div class="product-tab-content">
            <!-- Description Tab -->
            <div id="description-tab" class="product-tab-pane active">
                <div class="prose prose-lg max-w-none bg-white rounded-lg p-6">
                    <?php if (!empty($product['detailed_description'])): ?>
                        <?php echo nl2br(htmlspecialchars($product['detailed_description'])); ?>
                    <?php else: ?>
                        <?php echo nl2br(htmlspecialchars($product['description'])); ?>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Specifications Tab -->
            <div id="specifications-tab" class="product-tab-pane">
                <div class="bg-white rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">المواصفات الفنية</h3>
                    
                    <?php 
                    // تشخيص البيانات
                    $has_specifications = false;
                    $specifications_to_show = [];
                    
                    if (!empty($product['specifications'])) {
                        if (is_array($product['specifications'])) {
                            $specifications_to_show = $product['specifications'];
                            $has_specifications = true;
                        } elseif (is_string($product['specifications'])) {
                            // محاولة تحويل من JSON
                            $decoded = json_decode($product['specifications'], true);
                            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                                $specifications_to_show = $decoded;
                                $has_specifications = true;
                            } else {
                                // تقسيم النص بناءً على الأسطر
                                $lines = explode("\n", trim($product['specifications']));
                                $specifications_to_show = array_filter(array_map('trim', $lines));
                                $has_specifications = !empty($specifications_to_show);
                            }
                        }
                    }
                    ?>
                    
                    <?php if ($has_specifications): ?>
                        <div class="space-y-3">
                            <?php foreach ($specifications_to_show as $spec): ?>
                                <?php if (!empty(trim($spec))): ?>
                                    <div class="flex items-start">
                                        <svg class="w-5 h-5 text-blue-500 ml-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        <span class="text-gray-700"><?php echo htmlspecialchars(trim($spec)); ?></span>
                                    </div>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-8">
                            <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <p class="text-gray-500">لا توجد مواصفات فنية متاحة لهذا المنتج.</p>
                        </div>
                    <?php endif; ?>
                    

                </div>
            </div>
            
            <!-- Reviews Tab -->
            <div id="reviews-tab" class="product-tab-pane">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    
                    <!-- Rating Statistics -->
                    <div class="lg:col-span-1 order-2 lg:order-1">
                        <div class="bg-white rounded-lg p-6 shadow-sm mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">إحصائيات التقييمات</h3>
                            
                            <!-- Overall Rating -->
                            <div class="flex items-center mb-4">
                                <?php echo generateStarRating($rating_stats['average_rating']); ?>
                                <p class="ms-1 text-sm font-medium text-gray-500 dark:text-gray-400"><?php echo $rating_stats['average_rating']; ?></p>
                                <p class="ms-1 text-sm font-medium text-gray-500 dark:text-gray-400">من</p>
                                <p class="ms-1 text-sm font-medium text-gray-500 dark:text-gray-400">5</p>
                            </div>
                            <p class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-4"><?php echo number_format($rating_stats['total_reviews']); ?> تقييم عالمي</p>
                            
                            <!-- Rating Breakdown -->
                            <?php for ($star = 5; $star >= 1; $star--): ?>
                                <div class="flex items-center mt-4">
                                    <a href="#" class="text-sm font-medium text-blue-600 dark:text-blue-500 hover:underline"><?php echo $star; ?> نجمة</a>
                                    <div class="w-2/4 h-5 mx-4 bg-gray-200 rounded-sm dark:bg-gray-700">
                                        <div class="h-5 bg-yellow-300 rounded-sm" style="width: <?php echo $rating_stats['rating_percentages'][$star]; ?>%"></div>
                                    </div>
                                    <span class="text-sm font-medium text-gray-500 dark:text-gray-400"><?php echo $rating_stats['rating_percentages'][$star]; ?>%</span>
                                </div>
                            <?php endfor; ?>
                        </div>
                        
                        <!-- Add Review Form -->
                        <div class="bg-white rounded-lg p-6 shadow-sm">

                            
                            <!-- Result message area -->
                            <div id="reviewResult" class="mb-4 p-4 rounded-lg hidden">
                                <div id="reviewMessage"></div>
                            </div>
                            
                            <?php if (isLoggedIn()): ?>
                                <?php if ($user_has_reviewed): ?>
                                    <!-- User has already reviewed this product -->
                                    <div class="text-center py-8">
                                        <svg class="mx-auto h-12 w-12 text-green-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <h3 class="text-lg font-semibold text-gray-900 mb-2">لقد قمت بتقييم هذا المنتج من قبل</h3>
                                        <p class="text-gray-600 mb-4">شكراً لك على تقييمك السابق لهذا المنتج</p>
                                        
                                        <?php if ($user_review): ?>
                                            <div class="bg-gray-50 rounded-lg p-4 text-right">
                                                <div class="flex items-center justify-center mb-2">
                                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                                        <svg class="w-5 h-5 <?php echo $i <= $user_review['rating'] ? 'text-yellow-400' : 'text-gray-300'; ?>" fill="currentColor" viewBox="0 0 20 20">
                                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                                        </svg>
                                                    <?php endfor; ?>
                                                    <span class="mr-2 text-sm text-gray-600">(<?php echo $user_review['rating']; ?>/5)</span>
                                                </div>
                                                
                                                <?php if (!empty($user_review['title'])): ?>
                                                    <h4 class="font-semibold text-gray-900 mb-2"><?php echo htmlspecialchars($user_review['title']); ?></h4>
                                                <?php endif; ?>
                                                
                                                <?php if (!empty($user_review['comment'])): ?>
                                                    <p class="text-gray-700 text-sm"><?php echo nl2br(htmlspecialchars($user_review['comment'])); ?></p>
                                                <?php endif; ?>
                                                
                                                <p class="text-xs text-gray-500 mt-2">تاريخ التقييم: <?php echo formatDate($user_review['created_at']); ?></p>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php else: ?>
                                    <!-- Show review form -->
                                    <h3 class="text-lg font-semibold text-gray-900 mb-4">أضف تقييمك</h3>
                                    
                                    <form id="reviewForm" class="space-y-4">
                                        <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                                        <?php if (isLoggedIn() && $current_user): ?>
                                            <input type="hidden" name="user_id" value="<?php echo $current_user['id']; ?>">
                                            <input type="hidden" name="customer_name" value="<?php echo htmlspecialchars($current_user['name']); ?>">
                                            <input type="hidden" name="customer_email" value="<?php echo htmlspecialchars($current_user['email']); ?>">
                                        <?php endif; ?>
                                        <input type="hidden" name="rating" id="rating-input" required>
                                        
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">التقييم *</label>
                                            <div class="flex items-center space-x-1 space-x-reverse">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <button type="button" class="rating-star w-8 h-8 text-gray-300 hover:text-yellow-400 transition-colors" data-rating="<?php echo $i; ?>">
                                                        <svg class="w-full h-full" fill="currentColor" viewBox="0 0 20 20">
                                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                                        </svg>
                                                    </button>
                                                <?php endfor; ?>
                                            </div>
                                        </div>
                                        
                                        <div>
                                            <label for="review_title" class="block text-sm font-medium text-gray-700 mb-2">عنوان المراجعة (اختياري)</label>
                                            <input type="text" id="review_title" name="review_title" 
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                                   placeholder="مثال: منتج ممتاز، أنصح به بشدة">
                                        </div>
                                        
                                        <div>
                                            <label for="review_text" class="block text-sm font-medium text-gray-700 mb-2">التعليق *</label>
                                            <textarea id="review_text" name="review_text" rows="4" required 
                                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                                      placeholder="شاركنا رأيك في هذا المنتج..."></textarea>
                                        </div>
                                        
                                        <button type="submit" id="submitReviewBtn"
                                                class="w-full bg-primary hover:bg-secondary text-white font-medium py-2 px-4 rounded-lg transition-colors duration-300">
                                            <span class="btn-text">إرسال التقييم</span>
                                            <span class="btn-loading hidden">
                                                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                </svg>
                                                جاري الإرسال...
                                            </span>
                                        </button>
                                    </form>
                                <?php endif; ?>
                            <?php else: ?>
                                <div class="text-center py-8">
                                    <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    <p class="text-gray-500 mb-4">يجب تسجيل الدخول لإضافة تقييم</p>
                                    <a href="<?php echo SITE_URL; ?>/login" class="bg-primary hover:bg-secondary text-white px-6 py-2 rounded-lg transition-colors duration-300">
                                        تسجيل الدخول
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Reviews List -->
                    <div class="lg:col-span-2 order-1 lg:order-2">
                        <?php if (!empty($product_reviews)): ?>
                            <div class="space-y-6">
                                <?php foreach ($product_reviews as $review): ?>
                                    <div class="bg-white rounded-lg p-6 shadow-sm">
                                        <div class="flex items-center justify-between mb-4">
                                            <div class="flex items-center">
                                                <div class="w-10 h-10 bg-primary bg-opacity-10 rounded-full flex items-center justify-center ml-3">
                                                    <span class="text-primary font-semibold">
                                                        <?php echo strtoupper(substr($review['name'], 0, 1)); ?>
                                                    </span>
                                                </div>
                                                <div>
                                                    <div class="font-medium text-gray-900"><?php echo htmlspecialchars($review['name']); ?></div>
                                                    <div class="text-sm text-gray-500"><?php echo formatDate($review['created_at']); ?></div>
                                                </div>
                                            </div>
                                            
                                            <div class="flex items-center">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <svg class="w-4 h-4 <?php echo $i <= $review['rating'] ? 'text-yellow-400' : 'text-gray-300'; ?>" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                                    </svg>
                                                <?php endfor; ?>
                                            </div>
                                        </div>
                                        
                                        <?php if (!empty($review['title'])): ?>
                                            <h4 class="text-lg font-semibold text-gray-900 mb-2"><?php echo htmlspecialchars($review['title']); ?></h4>
                                        <?php endif; ?>
                                        
                                        <p class="text-gray-700"><?php echo nl2br(htmlspecialchars($review['comment'])); ?></p>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            
                            <!-- Pagination for Reviews -->
                            <?php if ($pagination['totalPages'] > 1): ?>
                                <div class="mt-8" id="reviews-pagination">
                                    <?php 
                                    // استخدام pagination مع معاملات إضافية للحفاظ على التبويب
                                    $queryParams = ['id' => $product_id];
                                    if (isset($_GET['tab'])) {
                                        $queryParams['tab'] = $_GET['tab'];
                                    }
                                    renderPagination($pagination['currentPage'], $pagination['totalPages'], 'product_detail.php', $queryParams); 
                                    ?>
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="text-center py-8">
                                <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                </svg>
                                <p class="text-gray-500">لا توجد تقييمات لهذا المنتج بعد. كن أول من يقيم!</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Products -->
<?php if (!empty($related_products['products'])): ?>
<section class="py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-2xl md:text-3xl font-bold text-gray-900 mb-8 text-center">منتجات ذات صلة</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <?php foreach ($related_products['products'] as $related_product): ?>
                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="relative">
                        <a href="<?php echo SITE_URL; ?>/products/<?php echo $related_product['id']; ?>">
                            <img src="<?php echo $related_product['image'] ?: '/assets/images/default-product.svg'; ?>" 
                                 alt="<?php echo htmlspecialchars($related_product['name']); ?>" 
                                 class="w-full h-48 object-cover">
                        </a>
                    </div>
                    
                    <div class="p-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                            <a href="<?php echo SITE_URL; ?>/products/<?php echo $related_product['id']; ?>" class="hover:text-primary transition-colors">
                                <?php echo htmlspecialchars($related_product['name']); ?>
                            </a>
                        </h3>
                        
                        <div class="flex items-center justify-between">
                            <a href="<?php echo SITE_URL; ?>/products/<?php echo $related_product['id']; ?>" 
                               class="bg-primary hover:bg-secondary text-white px-4 py-2 rounded-lg transition-colors duration-300 text-sm">
                                عرض التفاصيل
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Image Modal -->
<div id="image-modal" class="fixed inset-0 bg-black bg-opacity-75 z-50 hidden flex items-center justify-center p-4">
    <div class="relative max-w-4xl max-h-full">
        <button onclick="closeImageModal()" class="absolute top-4 right-4 text-white hover:text-gray-300 z-10">
            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
        <img id="modal-image" src="" alt="" class="max-w-full max-h-full object-contain">
    </div>
</div>

<script>
// Tab functionality - Fixed version
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing tabs...');
    
    const tabButtons = document.querySelectorAll('.product-tab-button');
    const tabPanes = document.querySelectorAll('.product-tab-pane');
    
    console.log('Tab buttons found:', tabButtons.length);
    console.log('Tab panes found:', tabPanes.length);
    
    if (tabButtons.length === 0 || tabPanes.length === 0) {
        console.error('Tab elements not found!');
        return;
    }
    
    // Function to switch tabs
    function switchTab(targetTabId) {
        console.log('Switching to tab:', targetTabId);
        
        // Remove active class from all buttons
        tabButtons.forEach(btn => {
            btn.classList.remove('active', 'border-primary', 'text-primary');
            btn.classList.add('border-transparent', 'text-gray-500');
        });
        
        // Hide all tab panes
        tabPanes.forEach(pane => {
            pane.classList.remove('active');
        });
        
        // Find and activate the target button
        const targetButton = document.querySelector(`[data-tab="${targetTabId}"]`);
        if (targetButton) {
            targetButton.classList.add('active', 'border-primary', 'text-primary');
            targetButton.classList.remove('border-transparent', 'text-gray-500');
        }
        
        // Find and show the target pane
        const targetPane = document.getElementById(targetTabId + '-tab');
        if (targetPane) {
            targetPane.classList.add('active');
            console.log('Tab switched successfully to:', targetTabId);
        } else {
            console.error('Target pane not found:', targetTabId + '-tab');
        }
    }
    
    // Add click event to each tab button
    tabButtons.forEach((button) => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const tabId = this.dataset.tab;
            
            if (!tabId) {
                console.error('No data-tab attribute found on button');
                return;
            }
            
            switchTab(tabId);
        });
    });
    
    // Initialize first tab as active
    if (tabButtons.length > 0) {
        const firstTabId = tabButtons[0].dataset.tab;
        if (firstTabId) {
            switchTab(firstTabId);
            console.log('First tab initialized:', firstTabId);
        }
    }
    
    // Rating stars functionality
    const ratingStars = document.querySelectorAll('.rating-star');
    const ratingInput = document.getElementById('rating-input');
    
    if (ratingStars.length > 0 && ratingInput) {
        ratingStars.forEach(star => {
            star.addEventListener('click', function() {
                const rating = parseInt(this.dataset.rating);
                ratingInput.value = rating;
                
                ratingStars.forEach((s, index) => {
                    if (index < rating) {
                        s.classList.remove('text-gray-300');
                        s.classList.add('text-yellow-400');
                    } else {
                        s.classList.remove('text-yellow-400');
                        s.classList.add('text-gray-300');
                    }
                });
            });
            
            star.addEventListener('mouseenter', function() {
                const rating = parseInt(this.dataset.rating);
                
                ratingStars.forEach((s, index) => {
                    if (index < rating) {
                        s.classList.add('text-yellow-400');
                    } else {
                        s.classList.remove('text-yellow-400');
                    }
                });
            });
        });
    }
    
    // Review form submission with AJAX
    const reviewForm = document.getElementById('reviewForm');
    const submitBtn = document.getElementById('submitReviewBtn');
    
    if (reviewForm && submitBtn) {
        // Function to reset button state
        function resetButtonState() {
            const btnText = submitBtn.querySelector('.btn-text');
            const btnLoading = submitBtn.querySelector('.btn-loading');
            
            console.log('Resetting button state...');
            console.log('btnText element:', btnText);
            console.log('btnLoading element:', btnLoading);
            
            if (btnText && btnLoading) {
                btnText.classList.remove('hidden');
                btnLoading.classList.add('hidden');
            } else {
                // Fallback: directly change button text
                submitBtn.innerHTML = 'إرسال التقييم';
            }
            
            submitBtn.disabled = false;
            console.log('Button state reset completed');
        }
        
        reviewForm.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('Form submitted');
            
            // Show loading state
            const btnText = submitBtn.querySelector('.btn-text');
            const btnLoading = submitBtn.querySelector('.btn-loading');
            const resultDiv = document.getElementById('reviewResult');
            const messageDiv = document.getElementById('reviewMessage');
            
            console.log('Elements found:', {
                btnText: !!btnText,
                btnLoading: !!btnLoading,
                resultDiv: !!resultDiv,
                messageDiv: !!messageDiv
            });
            
            if (btnText && btnLoading) {
                btnText.classList.add('hidden');
                btnLoading.classList.remove('hidden');
            } else {
                // Fallback: directly change button text
                submitBtn.innerHTML = 'جاري الإرسال...';
            }
            submitBtn.disabled = true;
            
            // Prepare form data
            const formData = new FormData(reviewForm);
            console.log('Sending request to API...');
            
            // Send AJAX request
            fetch('<?php echo SITE_URL; ?>/api/review.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('Response received:', response);
                console.log('Response status:', response.status);
                console.log('Response ok:', response.ok);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                return response.text().then(text => {
                    console.log('Raw response text:', text);
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        console.error('JSON parse error:', e);
                        throw new Error('Invalid JSON response');
                    }
                });
            })
            .then(data => {
                console.log('Parsed data:', data);
                
                // Always reset button state first
                resetButtonState();
                
                // Show result message
                if (resultDiv && messageDiv) {
                    resultDiv.classList.remove('hidden');
                    
                    if (data.success) {
                        resultDiv.className = 'mb-4 p-4 rounded-lg bg-green-100 border border-green-400 text-green-700';
                        messageDiv.innerHTML = data.message || 'تم إرسال تقييمك بنجاح!';
                        
                        // Reset form
                        reviewForm.reset();
                        const ratingInput = document.getElementById('rating-input');
                        if (ratingInput) ratingInput.value = '';
                        
                        // Reset rating stars
                        document.querySelectorAll('.rating-star').forEach(star => {
                            star.classList.remove('text-yellow-400');
                            star.classList.add('text-gray-300');
                        });
                        
                        // Auto-hide success message after 5 seconds
                        setTimeout(() => {
                            resultDiv.classList.add('hidden');
                        }, 5000);
                        
                        // Reload page after 2 seconds to show new review
                        setTimeout(() => {
                            window.location.reload();
                        }, 2000);
                    } else {
                        resultDiv.className = 'mb-4 p-4 rounded-lg bg-red-100 border border-red-400 text-red-700';
                        messageDiv.innerHTML = data.message || 'حدث خطأ أثناء إرسال التقييم';
                    }
                } else {
                    console.error('Result or message div not found');
                }
            })
            .catch(error => {
                console.error('Fetch error:', error);
                
                // Always reset button state
                resetButtonState();
                
                // Show error message
                if (resultDiv && messageDiv) {
                    resultDiv.classList.remove('hidden');
                    resultDiv.className = 'mb-4 p-4 rounded-lg bg-red-100 border border-red-400 text-red-700';
                    messageDiv.innerHTML = 'حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.';
                } else {
                    alert('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.');
                }
            });
        });
    } else {
        console.error('Review form or submit button not found');
    }
    
    // Auto-hide alerts after 5 seconds
    document.querySelectorAll('.alert, [role="alert"]').forEach(alert => {
        setTimeout(() => {
            alert.style.transition = 'opacity 0.5s ease-out';
            alert.style.opacity = '0';
            setTimeout(() => {
                alert.remove();
            }, 500);
        }, 5000);
    });
    
    // AJAX Pagination for Reviews
    function initReviewsPagination() {
        const paginationContainer = document.getElementById('reviews-pagination');
        if (!paginationContainer) return;
        
        // Add click event to all pagination links
        paginationContainer.addEventListener('click', function(e) {
            const link = e.target.closest('a[href*="page="]');
            if (!link) return;
            
            e.preventDefault();
            
            const url = new URL(link.href);
            const page = url.searchParams.get('page');
            const productId = url.searchParams.get('id');
            
            if (!page || !productId) return;
            
            // Show loading state
            const reviewsContainer = document.getElementById('reviews-tab');
            if (reviewsContainer) {
                reviewsContainer.style.opacity = '0.6';
                reviewsContainer.style.pointerEvents = 'none';
            }
            
            // Fetch new reviews
            fetch(`<?php echo SITE_URL; ?>/api/get_reviews.php?product_id=${productId}&page=${page}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update reviews content
                        updateReviewsContent(data.reviews, data.pagination);
                        
                        // Update URL without page reload
                        const newUrl = new URL(window.location);
                        newUrl.searchParams.set('page', page);
                        newUrl.searchParams.set('tab', 'reviews');
                        window.history.pushState({}, '', newUrl);
                        
                        // Scroll to reviews section
                        document.getElementById('reviews-tab').scrollIntoView({ 
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading reviews:', error);
                    alert('حدث خطأ في تحميل المراجعات');
                })
                .finally(() => {
                    // Hide loading state
                    if (reviewsContainer) {
                        reviewsContainer.style.opacity = '1';
                        reviewsContainer.style.pointerEvents = 'auto';
                    }
                });
        });
    }
    
    // Function to update reviews content
    function updateReviewsContent(reviews, pagination) {
        const reviewsContainer = document.querySelector('#reviews-tab .space-y-6');
        const paginationContainer = document.getElementById('reviews-pagination');
        
        if (!reviewsContainer) return;
        
        // Update reviews list
        if (reviews && reviews.length > 0) {
            let reviewsHtml = '';
            reviews.forEach(review => {
                const stars = '★'.repeat(review.rating) + '☆'.repeat(5 - review.rating);
                const reviewDate = new Date(review.created_at).toLocaleDateString('ar-SA');
                
                reviewsHtml += `
                    <div class="bg-gray-50 rounded-lg p-6">
                        <div class="flex items-start justify-between mb-4">
                            <div>
                                <h4 class="font-semibold text-gray-900">${escapeHtml(review.name)}</h4>
                                <div class="flex items-center mt-1">
                                    <div class="text-yellow-400 text-lg">${stars}</div>
                                    <span class="text-gray-500 text-sm mr-2">${reviewDate}</span>
                                </div>
                            </div>
                        </div>
                        
                        ${review.title ? `<h5 class="font-medium text-gray-900 mb-2">${escapeHtml(review.title)}</h5>` : ''}
                        <p class="text-gray-700">${escapeHtml(review.comment).replace(/\n/g, '<br>')}</p>
                    </div>
                `;
            });
            reviewsContainer.innerHTML = reviewsHtml;
        } else {
            reviewsContainer.innerHTML = `
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                    <p class="text-gray-500">لا توجد تقييمات لهذا المنتج بعد. كن أول من يقيم!</p>
                </div>
            `;
        }
        
        // Update pagination
        if (paginationContainer && pagination && pagination.totalPages > 1) {
            updatePaginationContent(paginationContainer, pagination);
        } else if (paginationContainer) {
            paginationContainer.innerHTML = '';
        }
    }
    
    // Function to update pagination content
    function updatePaginationContent(container, pagination) {
        const productId = new URLSearchParams(window.location.search).get('id');
        let paginationHtml = `
            <div class="bg-white px-4 py-3 flex items-center justify-center border-t border-gray-200 sm:px-6">
                <nav aria-label="Page navigation example">
                    <ul class="flex items-center -space-x-px h-10 text-base">
        `;
        
        // Previous button
        if (pagination.currentPage > 1) {
            paginationHtml += `
                <li>
                    <a href="product_detail.php?id=${productId}&page=${pagination.currentPage - 1}&tab=reviews" class="flex items-center justify-center px-4 h-10 ms-0 leading-tight text-gray-500 bg-white border border-e-0 border-gray-300 rounded-s-lg hover:bg-gray-100 hover:text-gray-700">
                        <span class="sr-only">Previous</span>
                        <svg class="w-3 h-3 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 1 1 5l4 4"/>
                        </svg>
                    </a>
                </li>
            `;
        } else {
            paginationHtml += `
                <li>
                    <span class="flex items-center justify-center px-4 h-10 ms-0 leading-tight text-gray-300 bg-gray-100 border border-e-0 border-gray-300 rounded-s-lg cursor-not-allowed">
                        <span class="sr-only">Previous</span>
                        <svg class="w-3 h-3 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 1 1 5l4 4"/>
                        </svg>
                    </span>
                </li>
            `;
        }
        
        // Page numbers
        const startPage = Math.max(1, pagination.currentPage - 2);
        const endPage = Math.min(pagination.totalPages, pagination.currentPage + 2);
        
        for (let i = startPage; i <= endPage; i++) {
            if (i === pagination.currentPage) {
                paginationHtml += `
                    <li>
                        <a href="product_detail.php?id=${productId}&page=${i}&tab=reviews" aria-current="page" class="z-10 flex items-center justify-center px-4 h-10 leading-tight text-blue-600 border border-blue-300 bg-blue-50 hover:bg-blue-100 hover:text-blue-700">${i}</a>
                    </li>
                `;
            } else {
                paginationHtml += `
                    <li>
                        <a href="product_detail.php?id=${productId}&page=${i}&tab=reviews" class="flex items-center justify-center px-4 h-10 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700">${i}</a>
                    </li>
                `;
            }
        }
        
        // Next button
        if (pagination.currentPage < pagination.totalPages) {
            paginationHtml += `
                <li>
                    <a href="product_detail.php?id=${productId}&page=${pagination.currentPage + 1}&tab=reviews" class="flex items-center justify-center px-4 h-10 leading-tight text-gray-500 bg-white border border-gray-300 rounded-e-lg hover:bg-gray-100 hover:text-gray-700">
                        <span class="sr-only">Next</span>
                        <svg class="w-3 h-3 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                        </svg>
                    </a>
                </li>
            `;
        } else {
            paginationHtml += `
                <li>
                    <span class="flex items-center justify-center px-4 h-10 leading-tight text-gray-300 bg-gray-100 border border-gray-300 rounded-e-lg cursor-not-allowed">
                        <span class="sr-only">Next</span>
                        <svg class="w-3 h-3 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                        </svg>
                    </span>
                </li>
            `;
        }
        
        paginationHtml += `
                    </ul>
                </nav>
            </div>
        `;
        
        container.innerHTML = paginationHtml;
    }
    
    // Helper function to escape HTML
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    // Initialize reviews pagination
    initReviewsPagination();
    
    console.log('Tab initialization complete');
});

// Image functionality
function changeMainImage(imageSrc, thumbnail) {
    const mainImage = document.getElementById('main-image');
    if (mainImage) {
        mainImage.src = imageSrc;
    }
    
    // Update thumbnail borders
    document.querySelectorAll('.aspect-w-1.aspect-h-1').forEach(thumb => {
        thumb.classList.remove('border-primary');
        thumb.classList.add('border-transparent');
    });
    
    if (thumbnail) {
        thumbnail.classList.remove('border-transparent');
        thumbnail.classList.add('border-primary');
    }
}

function openImageModal(imageSrc) {
    document.getElementById('modal-image').src = imageSrc;
    document.getElementById('image-modal').classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function closeImageModal() {
    document.getElementById('image-modal').classList.add('hidden');
    document.body.style.overflow = 'auto';
}

// Action functions
function requestQuote(productId) {
    window.location.href = `<?php echo SITE_URL; ?>/contact?product=${productId}`;
}

function contactUs() {
    window.location.href = '<?php echo SITE_URL; ?>/contact';
}

function toggleWishlist(productId) {
    console.log('Toggle wishlist for product:', productId);
    // Implement wishlist functionality
}

function shareProduct() {
    if (navigator.share) {
        navigator.share({
            title: '<?php echo htmlspecialchars($product['name']); ?>',
            text: '<?php echo htmlspecialchars(substr($product['description'], 0, 100)); ?>',
            url: window.location.href
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(window.location.href).then(() => {
            alert('تم نسخ رابط المنتج');
        });
    }
}

// Close modal on outside click
document.getElementById('image-modal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeImageModal();
    }
});
</script>

<?php include 'footer.php'; ?>