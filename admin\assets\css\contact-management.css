/**
 * CSS مخصص لصفحة إدارة معلومات التواصل
 * Contact Information Management Custom CSS
 */

/* تحسين مظهر التبويبات */
.nav-tabs {
    border-bottom: 2px solid #e5e7eb;
}

.nav-tabs .nav-link {
    border: none;
    border-bottom: 3px solid transparent;
    color: #6b7280;
    font-weight: 500;
    padding: 1rem 1.5rem;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    color: #374151;
    border-bottom-color: #d1d5db;
}

.nav-tabs .nav-link.active {
    color: #3b82f6;
    border-bottom-color: #3b82f6;
    background: none;
}

/* تحسين مظهر النماذج */
.form-control {
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.form-control:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    outline: none;
}

.form-control.is-invalid {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-control.is-valid {
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* تحسين مظهر التسميات */
.form-label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
    display: block;
}

.form-label.required::after {
    content: " *";
    color: #ef4444;
}

/* تحسين مظهر الأزرار */
.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-secondary {
    background: #6b7280;
    color: white;
}

.btn-secondary:hover {
    background: #4b5563;
}

.btn-success {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.btn-success:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

/* تحسين مظهر البطاقات */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
    border-radius: 12px 12px 0 0 !important;
    padding: 1.25rem;
}

.card-body {
    padding: 1.5rem;
}

/* تحسين مظهر معاينة وسائل التواصل الاجتماعي */
.social-links-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.social-link {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.social-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    text-decoration: none;
}

.social-link i {
    margin-left: 0.5rem;
    font-size: 1.1em;
}

/* ألوان مخصصة لكل منصة */
.social-link[href*="facebook"] {
    background: #1877f2;
    color: white;
}

.social-link[href*="twitter"] {
    background: #1da1f2;
    color: white;
}

.social-link[href*="instagram"] {
    background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);
    color: white;
}

.social-link[href*="linkedin"] {
    background: #0077b5;
    color: white;
}

.social-link[href*="youtube"] {
    background: #ff0000;
    color: white;
}

/* تحسين مظهر معاينة الخريطة */
#map_preview {
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    overflow: hidden;
    background: #f8fafc;
    min-height: 250px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#map_preview iframe {
    width: 100%;
    height: 300px;
    border: none;
    border-radius: 8px;
}

#map_preview .placeholder {
    color: #6b7280;
    font-style: italic;
    text-align: center;
}

/* تحسين مظهر الرسائل */
.alert {
    border-radius: 12px;
    border: none;
    padding: 1rem 1.25rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
}

.alert i {
    margin-left: 0.75rem;
    font-size: 1.2em;
}

.alert-success {
    background: linear-gradient(135deg, #d1fae5, #a7f3d0);
    color: #065f46;
    border-right: 4px solid #10b981;
}

.alert-danger {
    background: linear-gradient(135deg, #fee2e2, #fecaca);
    color: #991b1b;
    border-right: 4px solid #ef4444;
}

.alert-info {
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    color: #1e40af;
    border-right: 4px solid #3b82f6;
}

.alert-warning {
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    color: #92400e;
    border-right: 4px solid #f59e0b;
}

/* تحسين مظهر الـ Toast */
.toast {
    border-radius: 12px;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* تحسين مظهر الحقول المطلوبة */
.required-field {
    position: relative;
}

.required-field::after {
    content: "*";
    color: #ef4444;
    position: absolute;
    top: 0;
    right: -10px;
    font-weight: bold;
}

/* تحسين مظهر الشبكة */
.grid-cols-2 {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

.grid-cols-3 {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
}

/* تحسين الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .grid-cols-2,
    .grid-cols-3 {
        grid-template-columns: 1fr;
    }
    
    .nav-tabs {
        overflow-x: auto;
        white-space: nowrap;
    }
    
    .nav-tabs .nav-link {
        display: inline-block;
        min-width: 120px;
        text-align: center;
    }
    
    .social-links-preview {
        flex-direction: column;
    }
    
    .social-link {
        justify-content: center;
    }
}

/* تحسين مظهر الحقول المعطلة */
.form-control:disabled {
    background-color: #f3f4f6;
    color: #6b7280;
    cursor: not-allowed;
}

/* تحسين مظهر مؤشرات التحميل */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسين مظهر الأيقونات */
.icon {
    width: 1.25rem;
    height: 1.25rem;
    display: inline-block;
}

.icon-lg {
    width: 1.5rem;
    height: 1.5rem;
}

.icon-sm {
    width: 1rem;
    height: 1rem;
}

/* تحسين مظهر الفواصل */
.divider {
    border-top: 1px solid #e5e7eb;
    margin: 2rem 0;
}

/* تحسين مظهر النصوص المساعدة */
.help-text {
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 0.25rem;
    line-height: 1.4;
}

/* تحسين مظهر العناوين الفرعية */
.section-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e5e7eb;
}