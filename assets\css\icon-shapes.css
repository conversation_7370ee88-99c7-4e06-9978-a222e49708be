/* أشكال خلفيات الأيقونات */
.icon-shape-rounded {
    border-radius: 0.75rem; /* 12px */
}

.icon-shape-circle {
    border-radius: 50%;
}

.icon-shape-square {
    border-radius: 0;
}

.icon-shape-diamond {
    border-radius: 0;
    transform: rotate(45deg);
    position: relative;
}

.icon-shape-diamond i {
    transform: rotate(-45deg);
    display: block;
}

.icon-shape-hexagon {
    border-radius: 0;
    position: relative;
    background: transparent !important;
    overflow: visible;
}

.icon-shape-hexagon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: inherit;
    clip-path: polygon(30% 0%, 70% 0%, 100% 50%, 70% 100%, 30% 100%, 0% 50%);
    border-radius: 0;
    z-index: 0;
}

.icon-shape-hexagon i {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* تحسينات إضافية للأشكال */
.icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

/* تأثيرات الانتقال */
.icon-container {
    transition: all 0.3s ease;
}

.icon-container:hover {
    transform: scale(1.05);
}

/* تحسين شكل المعين */
.icon-shape-diamond {
    overflow: visible;
}

.icon-shape-diamond::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: inherit;
    transform: rotate(-45deg);
    z-index: -1;
}

/* تحسين الشكل السداسي - إصدار محسن */
.icon-shape-hexagon {
    background: transparent !important;
    position: relative;
}

.icon-shape-hexagon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--icon-bg-color, #10B981);
    clip-path: polygon(30% 0%, 70% 0%, 100% 50%, 70% 100%, 30% 100%, 0% 50%);
    z-index: 0;
}

.icon-shape-hexagon i {
    position: relative;
    z-index: 2;
    width: 100%;
    height: 100%;
    display: flex !important;
    align-items: center;
    justify-content: center;
}