<?php
/**
 * API لجلب صلاحيات المستخدم
 * Get User Permissions API
 */

require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/middleware.php';

// التحقق من تسجيل الدخول وصلاحية الإدارة
requirePermission('manage-users');

header('Content-Type: application/json');

try {
    $userId = $_GET['user_id'] ?? null;
    
    if (!$userId) {
        throw new Exception('معرف المستخدم مطلوب');
    }
    
    // جلب معلومات المستخدم
    $user = $database->fetch("SELECT * FROM users WHERE id = :user_id", ['user_id' => $userId]);
    
    if (!$user) {
        throw new Exception('المستخدم غير موجود');
    }
    
    // جلب صلاحيات المستخدم من خلال أدواره
    $permissions = $database->fetchAll("
        SELECT DISTINCT p.* 
        FROM permissions p
        JOIN role_permissions rp ON p.id = rp.permission_id
        JOIN user_roles ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = :user_id
        ORDER BY p.category, p.name
    ", ['user_id' => $userId]);
    
    echo json_encode([
        'success' => true,
        'user' => $user,
        'permissions' => $permissions
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>