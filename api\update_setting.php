<?php
require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/auth.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Check if user is logged in and is admin
if (!isLoggedIn() || !hasPermission('admin')) {
    http_response_code(403);
    echo json_encode([
        'success' => false,
        'message' => 'غير مصرح لك بالوصول'
    ]);
    exit;
}

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('طريقة الطلب غير مدعومة');
    }

    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['setting_name']) || !isset($input['setting_value'])) {
        throw new Exception('بيانات غير صحيحة');
    }

    $settingKey = trim($input['setting_name']);
    $settingValue = trim($input['setting_value']);
    
    // Validate setting name
    $allowedSettings = ['testimonials_count'];
    if (!in_array($settingKey, $allowedSettings)) {
        throw new Exception('اسم الإعداد غير مدعوم');
    }
    
    // Validate testimonials count
    if ($settingKey === 'testimonials_count') {
        $count = intval($settingValue);
        if ($count < 1 || $count > 20) {
            throw new Exception('عدد التقييمات يجب أن يكون بين 1 و 20');
        }
        $settingValue = $count;
    }

    $database = new Database();
    
    // Check if setting exists
    $existingSetting = $database->fetch(
        "SELECT id FROM settings WHERE setting_key = :key",
        ['key' => $settingKey]
    );
    
    if ($existingSetting) {
        // Update existing setting
        $database->query(
            "UPDATE settings SET setting_value = :value, updated_at = NOW() WHERE setting_key = :key",
            [
                'value' => $settingValue,
                'key' => $settingKey
            ]
        );
    } else {
        // Insert new setting
        $database->query(
            "INSERT INTO settings (setting_key, setting_value, setting_type, created_at, updated_at) VALUES (:key, :value, 'number', NOW(), NOW())",
            [
                'key' => $settingKey,
                'value' => $settingValue
            ]
        );
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'تم حفظ الإعداد بنجاح',
        'setting_name' => $settingKey,
        'setting_value' => $settingValue
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>