<?php
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
$isLoggedIn = isLoggedIn();
$currentUser = $isLoggedIn ? getCurrentUser() : null;

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>تشخيص مشكلة user_id</title>
    <link href='https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css' rel='stylesheet'>
</head>
<body class='bg-gray-100'>
    <div class='container mx-auto px-4 py-8'>
        <h1 class='text-3xl font-bold text-center mb-8'>تشخيص مشكلة user_id</h1>";

// Display current session and user info
echo "<div class='bg-white rounded-lg shadow-md p-6 mb-6'>
        <h2 class='text-xl font-semibold mb-4'>معلومات الجلسة والمستخدم</h2>";

echo "<h3 class='font-bold text-lg mb-2'>بيانات الجلسة (\$_SESSION):</h3>";
echo "<pre class='bg-gray-100 p-4 rounded text-sm overflow-auto'>";
print_r($_SESSION);
echo "</pre>";

echo "<h3 class='font-bold text-lg mb-2 mt-4'>حالة تسجيل الدخول:</h3>";
echo "<p><strong>isLoggedIn():</strong> " . ($isLoggedIn ? 'true' : 'false') . "</p>";

echo "<h3 class='font-bold text-lg mb-2 mt-4'>بيانات المستخدم الحالي:</h3>";
echo "<pre class='bg-gray-100 p-4 rounded text-sm overflow-auto'>";
print_r($currentUser);
echo "</pre>";

echo "</div>";

// Test form submission simulation
if ($_POST && isset($_POST['test_form'])) {
    echo "<div class='bg-white rounded-lg shadow-md p-6 mb-6'>
            <h2 class='text-xl font-semibold mb-4'>نتائج اختبار إرسال النموذج</h2>";
    
    echo "<h3 class='font-bold text-lg mb-2'>البيانات المرسلة (\$_POST):</h3>";
    echo "<pre class='bg-gray-100 p-4 rounded text-sm overflow-auto'>";
    print_r($_POST);
    echo "</pre>";
    
    // Simulate the same logic as review.php
    echo "<h3 class='font-bold text-lg mb-2 mt-4'>معالجة البيانات (محاكاة review.php):</h3>";
    
    $isLoggedIn_test = isLoggedIn();
    $currentUser_test = $isLoggedIn_test ? getCurrentUser() : null;
    
    echo "<p><strong>isLoggedIn في المعالجة:</strong> " . ($isLoggedIn_test ? 'true' : 'false') . "</p>";
    
    if ($isLoggedIn_test) {
        $user_id_from_session = $currentUser_test['id'] ?? null;
        $user_id_from_post = $_POST['user_id'] ?? null;
        
        echo "<p><strong>user_id من getCurrentUser():</strong> " . ($user_id_from_session ?: 'NULL') . "</p>";
        echo "<p><strong>user_id من POST:</strong> " . ($user_id_from_post ?: 'NULL') . "</p>";
        echo "<p><strong>هل يتطابقان؟</strong> " . ($user_id_from_session == $user_id_from_post ? 'نعم' : 'لا') . "</p>";
        
        // Test saving review
        if ($user_id_from_session) {
            echo "<div class='mt-4 p-4 bg-blue-50 border border-blue-200 rounded'>";
            echo "<h4 class='font-bold'>اختبار حفظ التقييم:</h4>";
            
            $product_id = 1;
            $customer_name = $currentUser_test['name'];
            $customer_email = $currentUser_test['email'];
            $rating = 5;
            $review_title = 'اختبار تشخيصي';
            $review_text = 'هذا تقييم تشخيصي لاختبار حفظ user_id';
            
            try {
                $result = saveUserReview($product_id, $user_id_from_session, $customer_name, $customer_email, $rating, $review_text, $review_title);
                
                if ($result) {
                    echo "<p class='text-green-600'>✅ تم حفظ التقييم بنجاح! ID: $result</p>";
                    
                    // Check if user_id was actually saved
                    global $database;
                    $pdo = $database->getConnection();
                    $stmt = $pdo->prepare("SELECT * FROM reviews WHERE id = ?");
                    $stmt->execute([$result]);
                    $savedReview = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    if ($savedReview) {
                        echo "<p><strong>user_id المحفوظ في قاعدة البيانات:</strong> " . ($savedReview['user_id'] ?: 'NULL') . "</p>";
                        echo "<p><strong>هل تم حفظ user_id بشكل صحيح؟</strong> " . ($savedReview['user_id'] == $user_id_from_session ? 'نعم ✅' : 'لا ❌') . "</p>";
                    }
                } else {
                    echo "<p class='text-red-600'>❌ فشل في حفظ التقييم</p>";
                }
            } catch (Exception $e) {
                echo "<p class='text-red-600'>❌ خطأ: " . $e->getMessage() . "</p>";
            }
            
            echo "</div>";
        }
    } else {
        echo "<p class='text-red-600'>المستخدم غير مسجل الدخول</p>";
    }
    
    echo "</div>";
}

// Display recent reviews
echo "<div class='bg-white rounded-lg shadow-md p-6 mb-6'>
        <h2 class='text-xl font-semibold mb-4'>آخر 10 تقييمات في قاعدة البيانات</h2>";

try {
    global $database;
    $pdo = $database->getConnection();
    $stmt = $pdo->prepare("SELECT * FROM reviews ORDER BY created_at DESC LIMIT 10");
    $stmt->execute();
    $reviews = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($reviews)) {
        echo "<div class='overflow-x-auto'>
                <table class='min-w-full table-auto border-collapse border border-gray-300'>
                    <thead>
                        <tr class='bg-gray-50'>
                            <th class='border border-gray-300 px-4 py-2 text-right'>ID</th>
                            <th class='border border-gray-300 px-4 py-2 text-right'>Product ID</th>
                            <th class='border border-gray-300 px-4 py-2 text-right'>User ID</th>
                            <th class='border border-gray-300 px-4 py-2 text-right'>الاسم</th>
                            <th class='border border-gray-300 px-4 py-2 text-right'>البريد</th>
                            <th class='border border-gray-300 px-4 py-2 text-right'>التقييم</th>
                            <th class='border border-gray-300 px-4 py-2 text-right'>التاريخ</th>
                        </tr>
                    </thead>
                    <tbody>";
        
        foreach ($reviews as $review) {
            $userIdClass = $review['user_id'] ? 'text-green-600 font-bold' : 'text-red-600 font-bold';
            echo "<tr>
                    <td class='border border-gray-300 px-4 py-2'>{$review['id']}</td>
                    <td class='border border-gray-300 px-4 py-2'>{$review['product_id']}</td>
                    <td class='border border-gray-300 px-4 py-2 $userIdClass'>" . ($review['user_id'] ?: 'NULL') . "</td>
                    <td class='border border-gray-300 px-4 py-2'>" . htmlspecialchars($review['name']) . "</td>
                    <td class='border border-gray-300 px-4 py-2'>" . htmlspecialchars($review['email']) . "</td>
                    <td class='border border-gray-300 px-4 py-2'>{$review['rating']}/5</td>
                    <td class='border border-gray-300 px-4 py-2'>" . date('Y-m-d H:i', strtotime($review['created_at'])) . "</td>
                  </tr>";
        }
        
        echo "</tbody></table></div>";
    } else {
        echo "<p class='text-gray-500'>لا توجد تقييمات في قاعدة البيانات</p>";
    }
} catch (Exception $e) {
    echo "<p class='text-red-600'>خطأ في قراءة التقييمات: " . $e->getMessage() . "</p>";
}

echo "</div>";

// Test form
if ($isLoggedIn) {
    echo "<div class='bg-white rounded-lg shadow-md p-6 mb-6'>
            <h2 class='text-xl font-semibold mb-4'>اختبار إرسال النموذج</h2>
            <form method='POST' class='space-y-4'>
                <input type='hidden' name='product_id' value='1'>
                <input type='hidden' name='user_id' value='" . ($_SESSION['user_id'] ?? '') . "'>
                <input type='hidden' name='customer_name' value='" . htmlspecialchars($_SESSION['user_name'] ?? '') . "'>
                <input type='hidden' name='customer_email' value='" . htmlspecialchars($_SESSION['user_email'] ?? '') . "'>
                <input type='hidden' name='rating' value='5'>
                <input type='hidden' name='review_title' value='اختبار تشخيصي'>
                <input type='hidden' name='review_text' value='هذا تقييم تشخيصي لاختبار المشكلة'>
                
                <div>
                    <p class='mb-4'>هذا النموذج يحاكي البيانات التي يتم إرسالها من صفحة المنتج</p>
                    <p class='text-sm text-gray-600 mb-4'>
                        <strong>البيانات التي سيتم إرسالها:</strong><br>
                        product_id: 1<br>
                        user_id: " . ($_SESSION['user_id'] ?? 'غير محدد') . "<br>
                        customer_name: " . ($_SESSION['user_name'] ?? 'غير محدد') . "<br>
                        customer_email: " . ($_SESSION['user_email'] ?? 'غير محدد') . "<br>
                        rating: 5<br>
                        review_title: اختبار تشخيصي<br>
                        review_text: هذا تقييم تشخيصي لاختبار المشكلة
                    </p>
                </div>
                
                <button type='submit' name='test_form' class='bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded'>
                    اختبار إرسال النموذج
                </button>
            </form>
          </div>";
} else {
    echo "<div class='bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-6'>
            <strong>تحذير:</strong> يجب تسجيل الدخول أولاً لإجراء الاختبار.
            <a href='test_login_simple.php' class='underline ml-2'>تسجيل الدخول</a>
          </div>";
}

// Navigation
echo "<div class='text-center'>
        <a href='index.php' class='bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded mr-2'>
            العودة للصفحة الرئيسية
        </a>
        <a href='products/1' class='bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded'>
            اختبار في صفحة المنتج
        </a>
      </div>";

echo "</div></body></html>";
?>